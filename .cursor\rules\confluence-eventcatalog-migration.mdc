---
description: 
globs: 
alwaysApply: false
---
# Confluence to EventCatalog Migration

Rule for migrating documentation from Confluence to EventCatalog with proper image handling and character escaping.

<rule>
name: confluence_eventcatalog_migration
description: Guidelines for migrating Confluence content to EventCatalog with images and proper escaping
filters:
  - type: file_path
    pattern: "eventcatalog/docs/.*\\.mdx$"
  - type: content
    pattern: "confluence|atlassian"
  - type: event
    pattern: "confluence_migration"

actions:
  - type: suggest
    message: |
      When migrating documentation from Confluence to EventCatalog:

      ## 0. Confluence Page Verification (MANDATORY)

      ### CRITICAL RULE: Only migrate pages that exist in Confluence
      Before creating ANY EventCatalog documentation file, you MUST:

      1. **Verify page exists in Confluence:**
      ```bash
      # Use MCP Atlassian tools to verify page existence
      # Example: Check if page exists under "Business documentation"
      mcp_atlassian_getConfluencePageDescendants(pageId="4641030167")
      ```

      2. **Get complete page hierarchy:**
      ```bash
      # Get all descendant pages to understand the structure
      mcp_atlassian_searchConfluenceUsingCql(cql="ancestor = 4641030167")
      ```

      3. **Document the mapping:**
      ```markdown
      # Create a mapping file: confluence-eventcatalog-mapping.md
      ## Confluence → EventCatalog Mapping
      
      | Confluence Page | Page ID | EventCatalog File | Status |
      |----------------|---------|-------------------|---------|
      | Email system communication | 4666392593 | email-system-communication.mdx | ✅ |
      | RMA | 4646109185 | rma.mdx | ✅ |
      | UX pages | 4678287363 | NOT CREATED | ❌ |
      ```

      ### Validation Commands
      ```bash
      # Function to validate Confluence-EventCatalog alignment
      validate_confluence_alignment() {
        local confluence_parent_id="$1"
        local eventcatalog_section="$2"
        
        echo "🔍 Validating alignment between Confluence and EventCatalog..."
        echo "📋 Confluence parent page ID: $confluence_parent_id"
        echo "📁 EventCatalog section: $eventcatalog_section"
        
        # List actual EventCatalog files
        echo "📄 EventCatalog files found:"
        find "eventcatalog/docs/$eventcatalog_section" -name "*.mdx" -exec basename {} .mdx \;
        
        echo ""
        echo "⚠️  Please verify each EventCatalog file corresponds to a Confluence page"
        echo "🚫 DELETE any EventCatalog files that don't have Confluence counterparts"
        echo "➕ CREATE EventCatalog files for any missing Confluence pages"
      }
      
      # Usage example
      validate_confluence_alignment "4641030167" "05-business-documentation"
      ```

      ## 1. Image Integration Process

      ### Image Source Location
      - All images are located in: `/scripts/confluence-images/`
      - Images are organized by space or page structure
      - Use the confluence image download script to fetch images first

      ### Image Integration Steps
      ```bash
      # 1. Locate images for your documentation
      ls -la eventcatalog/scripts/confluence-images/

      # 2. Create target image directory
      mkdir -p eventcatalog/docs/[your-doc-section]/images/

      # 3. Copy relevant images
      cp eventcatalog/scripts/confluence-images/[space-name]/* \
         eventcatalog/docs/[your-doc-section]/images/

      # 4. Reference images in MDX
      ![Image Description](mdc:images/image-name.png)
      ```

      ### Image Reference Format
      ```mdx
      # Correct image references in MDX:
      ![PIMCORE Structure](mdc:images/pimcore-structure.png)
      ![Configuration Example](mdc:images/config-example.jpg)
      
      # For images in subdirectories:
      ![Diagram](mdc:images/diagrams/flow-chart.png)
      ```

      ## 2. Character Escaping Requirements

      ### Critical Characters to Escape
      ```mdx
      # HTML/XML angle brackets
      < becomes &lt;
      > becomes &gt;
      
      # Examples:
      List<Plant> → List&lt;Plant&gt;
      <<typeId>> → &lt;&lt;typeId&gt;&gt;
      Valid To < Valid From → Valid To &lt; Valid From
      ```

      ### Common Escaping Patterns
      ```mdx
      # Type definitions
      - Bad:  `typeId`: FK to `<<typeId>>`
      - Good: `typeId`: FK to `&lt;&lt;typeId&gt;&gt;`

      # Generic types
      - Bad:  `List<Plant>`
      - Good: `List&lt;Plant&gt;`

      # Comparison operators
      - Bad:  Value < Threshold
      - Good: Value &lt; Threshold

      # XML/HTML tags in text
      - Bad:  The <tag> element
      - Good: The &lt;tag&gt; element
      ```

      ## 3. EventCatalog Configuration Update

      **CRITICAL**: After creating new documentation files, you MUST update `eventcatalog.config.js`

      ### Configuration Update Process
      ```javascript
      // 1. Locate the appropriate section in customDocs.sidebar array
      // 2. Add your new documentation to the correct section

      // Example: Adding to Business Documentation section
      {
        label: '05. Business Documentation',
        badge: { text: 'New', color: 'blue' },
        collapsed: false,
        items: [
          { label: 'RMA', slug: '05-business-documentation/rma' },
          { label: 'Stock Synchronization', slug: '05-business-documentation/stock-synchronization' },
          { label: 'Product Management', slug: '05-business-documentation/product-management' },
          { label: 'NEW DOCUMENT TITLE', slug: '05-business-documentation/new-document-slug' }, // ADD THIS
          // ... existing items
        ]
      }
      ```

      ### Slug Format Rules
      - Use kebab-case for slugs (lowercase with hyphens)
      - Match the file path: `05-business-documentation/file-name` → `05-business-documentation/file-name`
      - Ensure slug matches the MDX file's `id` field in frontmatter

      ### Section Guidelines
      - **01. Getting Started**: Onboarding and setup guides
      - **02. Working Agreements**: Team processes and standards
      - **03. Infrastructure**: System architecture and deployment
      - **04. Cybersecurity Meetings**: Security meeting notes
      - **05. Business Documentation**: Business processes and requirements
      - **06. Magento Backend Documentation**: Magento-specific backend docs
      - **07. PWA Frontend Documentation**: Frontend application docs
      - **08. PIMCORE Documentation**: PIMCORE system documentation

      ### Configuration Update Checklist
      - [ ] Identified correct section for new documentation
      - [ ] Added entry to appropriate `items` array
      - [ ] Used proper slug format matching file path
      - [ ] Verified slug matches MDX frontmatter `id` field
      - [ ] Maintained alphabetical order within section (if applicable)
      - [ ] Tested EventCatalog build after configuration update

      ## 4. Migration Workflow

      ### Step-by-Step Process
      1. **Fetch Confluence Content**: Use Atlassian MCP to retrieve pages
      2. **Download Images**: Run confluence image download script
      3. **Create Documentation Structure**: Set up MDX files with proper frontmatter
      4. **Copy Images**: Move relevant images to documentation directory
      5. **Content Migration**: Transfer and format content with proper escaping
      6. **Image Integration**: Update image references to local paths
      7. **MANDATORY: Update Configuration**: Add/update/remove files in `eventcatalog.config.js` sidebar
      8. **MANDATORY: Run Character Escaping**: Execute character escaping script on modified files
      9. **Validation**: Test EventCatalog build for parsing errors

      ### Mandatory Post-Operation Steps
      **After EVERY page operation (create/update/delete), you MUST:**

      ```bash
      # 1. Update eventcatalog.config.js
      # - Add entries for new files
      # - Update entries for modified files  
      # - Remove entries for deleted files
      
      # 2. Run character escaping script
      python3 scripts/escape-characters.py [target-file-or-directory]
      
      # For single file:
      python3 scripts/escape-characters.py eventcatalog/docs/05-business-documentation/new-file.mdx
      
      # For entire directory:
      find eventcatalog/docs/05-business-documentation -name "*.mdx" -exec python3 scripts/escape-characters.py {} \;
      ```

      ### Quality Checklist
      - [ ] All images copied from `/scripts/confluence-images/`
      - [ ] Image paths use relative references (`./images/`)
      - [ ] All `<` and `>` characters properly escaped
      - [ ] MDX frontmatter complete and valid
      - [ ] **New files added to `eventcatalog.config.js` sidebar**
      - [ ] **MANDATORY: eventcatalog.config.js updated after ANY page operation (create/update/delete)**
      - [ ] **MANDATORY: Character escaping script run after every update or page creation**
      - [ ] EventCatalog builds without parsing errors
      - [ ] Images display correctly in documentation
      - [ ] Navigation works correctly in EventCatalog

  - type: execute
    command: |
      # Automated migration helper script
      
      # Function to escape MDX characters
      escape_mdx_content() {
        local content="$1"
        # Escape angle brackets that aren't HTML tags
        echo "$content" | sed -E 's/<([^/a-zA-Z])/\&lt;\1/g' | sed -E 's/([^a-zA-Z])>([^>])/\1\&gt;\2/g'
      }
      
      # Function to copy images for documentation section
      copy_confluence_images() {
        local doc_section="$1"
        local confluence_space="$2"
        
        echo "📁 Creating image directory for $doc_section..."
        mkdir -p "eventcatalog/docs/$doc_section/images/"
        
        if [ -d "eventcatalog/scripts/confluence-images/$confluence_space" ]; then
          echo "📋 Copying images from confluence-images/$confluence_space..."
          cp -r "eventcatalog/scripts/confluence-images/$confluence_space/"* \
                "eventcatalog/docs/$doc_section/images/" 2>/dev/null || true
          echo "✅ Images copied successfully"
        else
          echo "⚠️  No images found in confluence-images/$confluence_space"
        fi
      }
      
      # Function to validate MDX content
      validate_mdx_content() {
        local mdx_file="$1"
        echo "🔍 Validating MDX content in $mdx_file..."
        
        # Check for unescaped angle brackets
        if grep -E '[^&]<[^/a-zA-Z]|[^a-zA-Z]>[^&]' "$mdx_file" >/dev/null 2>&1; then
          echo "❌ Found unescaped angle brackets in $mdx_file"
          echo "🔧 Lines with potential issues:"
          grep -n -E '[^&]<[^/a-zA-Z]|[^a-zA-Z]>[^&]' "$mdx_file" || true
          return 1
        else
          echo "✅ No unescaped angle brackets found"
          return 0
        fi
      }

      # Function to check if eventcatalog.config.js needs updating
      check_config_update() {
        local new_file_path="$1"
        local config_file="eventcatalog/eventcatalog.config.js"
        
        # Extract section and slug from file path
        local section=$(echo "$new_file_path" | cut -d'/' -f3)
        local slug=$(echo "$new_file_path" | sed 's/eventcatalog\/docs\///' | sed 's/\.mdx$//')
        
        echo "🔍 Checking if $slug is in eventcatalog.config.js..."
        
        if ! grep -q "$slug" "$config_file"; then
          echo "⚠️  WARNING: $slug not found in eventcatalog.config.js"
          echo "📝 Please add the following entry to the appropriate section:"
          echo "   { label: 'YOUR_TITLE', slug: '$slug' },"
          return 1
        else
          echo "✅ $slug found in configuration"
          return 0
        fi
      }

  - type: reject
    conditions:
      - pattern: "\\]\\(.*confluence.*\\)"
        message: "Do not use Confluence URLs for images. Copy images to local directory and use relative paths."
      
      - pattern: "<[^/a-zA-Z].*[^>]"
        message: "Unescaped angle brackets detected. Use &lt; and &gt; HTML entities."
      
      - pattern: "!\\[.*\\]\\(https?://.*confluence"
        message: "Confluence image URLs not allowed. Use local image references from /scripts/confluence-images/"

      - pattern: "eventcatalog/docs/.*\\.mdx$"
        message: "After creating new EventCatalog documentation files, you MUST update eventcatalog.config.js to add the new file to the appropriate sidebar section."

      - pattern: ".*"
        when: "file_create|file_update|file_delete"
        unless: "eventcatalog_config_updated"
        message: "🚫 MANDATORY: Must update eventcatalog.config.js after ANY page operation (create/update/delete). Add new entries, update existing ones, or remove deleted entries from the sidebar configuration."

      - pattern: ".*"
        when: "file_create|file_update"
        unless: "character_escaping_completed"
        message: "🚫 MANDATORY: Must run character escaping script after every page creation or update. Execute: python3 scripts/escape-characters.py [file-path]"

  - type: reject
    conditions:
      - type: file_creation
        pattern: "eventcatalog/docs/.*\\.mdx$"
        message: |
          🚫 CRITICAL VIOLATION: EventCatalog file creation without Confluence verification
          
          Before creating ANY EventCatalog documentation file, you MUST:
          
          1. ✅ Verify the page exists in Confluence using MCP tools
          2. ✅ Confirm it's part of the approved migration scope
          3. ✅ Document the Confluence Page ID and mapping
          
          FORBIDDEN ACTIONS:
          ❌ Creating EventCatalog files for non-existent Confluence pages
          ❌ Adding content that doesn't originate from Confluence
          ❌ Creating placeholder or template files without Confluence source
          
          REQUIRED PROCESS:
          1. Use mcp_atlassian_getConfluencePageDescendants() to verify page exists
          2. Get the exact Confluence page content using mcp_atlassian_getConfluencePage()
          3. Only then create the corresponding EventCatalog file
          
          This ensures 1:1 mapping between Confluence and EventCatalog content.

      - type: content_mismatch
        message: |
          🚫 STRUCTURE MISMATCH: EventCatalog structure doesn't match Confluence
          
          The following discrepancies were detected:
          
          EventCatalog files WITHOUT Confluence counterparts:
          - project-onboarding.mdx (❌ NOT in Confluence)
          - inventory-management.mdx (❌ NOT in Confluence)  
          - pricing-strategy.mdx (❌ NOT in Confluence)
          - customer-service.mdx (❌ NOT in Confluence)
          - order-management.mdx (❌ NOT in Confluence)
          - marketplace-integration.mdx (❌ NOT in Confluence)
          
          Confluence pages NOT in EventCatalog:
          - UX pages (ID: 4678287363) (❌ MISSING from EventCatalog)
          - Discounts (ID: 4679106567) (❌ MISSING from EventCatalog)
          
          REQUIRED ACTIONS:
          1. 🗑️  DELETE EventCatalog files that don't exist in Confluence
          2. ➕ CREATE EventCatalog files for missing Confluence pages
          3. 🔄 UPDATE eventcatalog.config.js to reflect the correct structure

examples:
  - input: |
      # Good: Complete migration workflow with Confluence verification
      
      ## Step 1: Verify Confluence page exists
      mcp_atlassian_getConfluencePageDescendants(pageId="4641030167")
      # Result: Found "Email system communication" (ID: 4666392593)
      
      ## Step 2: Get Confluence content
      mcp_atlassian_getConfluencePage(pageId="4666392593")
      
      ## Step 3: Create EventCatalog file
      # eventcatalog/docs/05-business-documentation/email-system-communication.mdx
      
      ## Step 4: Update eventcatalog.config.js
      {
        label: '05. Business Documentation',
        items: [
          { label: 'Email System Communication', slug: '05-business-documentation/email-system-communication' },
          // ... other items
        ]
      }
      
      ## Step 5: Content with proper escaping and local images
      ![Email Flow](mdc:images/email-flow.png)
      - `typeId`: FK to `&lt;&lt;typeId&gt;&gt;`

  - input: |
      # Bad: Creating EventCatalog file without Confluence verification
      
      # ❌ WRONG: Created file without checking Confluence
      # eventcatalog/docs/05-business-documentation/inventory-management.mdx
      
      # ❌ WRONG: File doesn't exist in Confluence
      # ❌ WRONG: No Confluence Page ID documented
      # ❌ WRONG: Content not sourced from Confluence

  - input: |
      # Good: Proper validation and cleanup workflow
      
      ## Step 1: Run alignment validation
      ./confluence-eventcatalog-validator.sh "4641030167" "05-business-documentation"
      
      ## Step 2: Identify discrepancies
      # EventCatalog files NOT in Confluence:
      # - inventory-management.mdx ❌ DELETE
      # - pricing-strategy.mdx ❌ DELETE
      
      # Confluence pages NOT in EventCatalog:
      # - UX pages (4678287363) ❌ CREATE
      # - Discounts (4679106567) ❌ CREATE
      
      ## Step 3: Fix alignment
      rm eventcatalog/docs/05-business-documentation/inventory-management.mdx
      rm eventcatalog/docs/05-business-documentation/pricing-strategy.mdx
      
      # Create missing files from Confluence
      mcp_atlassian_getConfluencePage(pageId="4678287363")  # UX pages
      mcp_atlassian_getConfluencePage(pageId="4679106567")  # Discounts
      
      ## Step 4: Update config to match final structure
      # Remove deleted files from eventcatalog.config.js
      # Add new files to eventcatalog.config.js

metadata:
  priority: high
  version: 2.0
  tags:
    - confluence
    - eventcatalog
    - migration
    - configuration
    - strict-alignment
    - verification

## Migration Tools

### Image Download Script Usage
```bash
# Download all images from Confluence space
cd eventcatalog/scripts
python3 confluence-image-download.py \
  --base-url https://your-instance.atlassian.net \
  --username <EMAIL> \
  --api-token your-api-token \
  --space-key ALS \
  --output-dir confluence-images
```

### Character Escaping Script
```bash
#!/bin/bash
# escape-mdx.sh - Helper script to escape MDX content

escape_file() {
    local file="$1"
    local backup="${file}.backup"
    
    # Create backup
    cp "$file" "$backup"
    
    # Escape angle brackets (avoiding HTML tags)
    sed -i -E 's/<([^/a-zA-Z])/\&lt;\1/g' "$file"
    sed -i -E 's/([^a-zA-Z-])>([^>])/\1\&gt;\2/g' "$file"
    
    echo "✅ Escaped characters in $file (backup: $backup)"
}

# Usage: ./escape-mdx.sh file.mdx
escape_file "$1"
```

### Migration Validation Script
```bash
#!/bin/bash
# validate-migration.sh - Validate migrated content

validate_doc_section() {
    local section="$1"
    local doc_dir="eventcatalog/docs/$section"
    
    echo "🔍 Validating $section documentation..."
    
    # Check if images directory exists and has content
    if [ -d "$doc_dir/images" ] && [ "$(ls -A "$doc_dir/images")" ]; then
        echo "✅ Images directory found with content"
    else
        echo "⚠️  No images found in $doc_dir/images"
    fi
    
    # Check for unescaped characters in MDX files
    find "$doc_dir" -name "*.mdx" -exec bash -c '
        for file; do
            if grep -E "[^&]<[^/a-zA-Z]|[^a-zA-Z]>[^&]" "$file" >/dev/null 2>&1; then
                echo "❌ Unescaped characters in: $file"
            else
                echo "✅ Clean MDX: $file"
            fi
        done
    ' _ {} +
}

# Usage: ./validate-migration.sh pimcore-documentation
validate_doc_section "$1"
```

### Confluence-EventCatalog Alignment Validation
```bash
#!/bin/bash
# confluence-eventcatalog-validator.sh
# Validates that EventCatalog structure matches Confluence exactly

validate_alignment() {
    local confluence_parent_id="$1"
    local eventcatalog_section="$2"
    local space_key="${3:-ALS}"
    
    echo "🔍 CONFLUENCE-EVENTCATALOG ALIGNMENT VALIDATOR"
    echo "=============================================="
    echo "📋 Confluence Parent ID: $confluence_parent_id"
    echo "📁 EventCatalog Section: $eventcatalog_section"
    echo "🏢 Confluence Space: $space_key"
    echo ""
    
    # Create temporary files for comparison
    local confluence_pages="/tmp/confluence_pages.txt"
    local eventcatalog_files="/tmp/eventcatalog_files.txt"
    
    # List EventCatalog files (remove .mdx extension)
    echo "📄 Scanning EventCatalog files..."
    find "eventcatalog/docs/$eventcatalog_section" -name "*.mdx" -exec basename {} .mdx \; | sort > "$eventcatalog_files"
    
    echo "📊 VALIDATION RESULTS:"
    echo "===================="
    
    # Show EventCatalog files
    echo "📄 EventCatalog files found:"
    while IFS= read -r file; do
        echo "   - $file.mdx"
    done < "$eventcatalog_files"
    
    echo ""
    echo "⚠️  MANUAL VERIFICATION REQUIRED:"
    echo "================================"
    echo "1. Use MCP tools to get Confluence page list:"
    echo "   mcp_atlassian_getConfluencePageDescendants(pageId=\"$confluence_parent_id\")"
    echo ""
    echo "2. Compare the lists manually"
    echo "3. DELETE any EventCatalog files not in Confluence"
    echo "4. CREATE any missing EventCatalog files from Confluence"
    echo "5. UPDATE eventcatalog.config.js to match the final structure"
    
    # Cleanup
    rm -f "$confluence_pages" "$eventcatalog_files"
}

# Usage
validate_alignment "4641030167" "05-business-documentation" "ALS"
```

### Pre-Migration Checklist
```bash
#!/bin/bash
# pre-migration-checklist.sh
# Run this before starting any Confluence migration

echo "📋 PRE-MIGRATION CHECKLIST"
echo "========================="
echo ""

echo "□ 1. Confluence Access Verified"
echo "   → Test MCP Atlassian connection"
echo "   → Verify permissions to read pages"
echo ""

echo "□ 2. Parent Page Identified"
echo "   → Get Confluence parent page ID"
echo "   → Verify page hierarchy"
echo ""

echo "□ 3. Target EventCatalog Section Ready"
echo "   → Create/verify target directory"
echo "   → Check eventcatalog.config.js structure"
echo ""

echo "□ 4. Image Directory Prepared"
echo "   → Verify confluence-images directory exists"
echo "   → Check image naming conventions"
echo ""

echo "□ 5. Validation Tools Ready"
echo "   → Character escaping script available"
echo "   → MDX validation tools ready"
echo ""

echo "✅ READY TO MIGRATE: Only proceed if ALL items are checked"
```

### Post-Migration Validation
```bash
#!/bin/bash
# post-migration-validation.sh
# Run this after completing Confluence migration

validate_post_migration() {
    local eventcatalog_section="$1"
    
    echo "🔍 POST-MIGRATION VALIDATION"
    echo "============================"
    
    # Check 1: All files have proper frontmatter
    echo "📝 Checking frontmatter..."
    find "eventcatalog/docs/$eventcatalog_section" -name "*.mdx" | while read -r file; do
        if ! head -10 "$file" | grep -q "^title:"; then
            echo "❌ Missing frontmatter: $file"
        else
            echo "✅ Valid frontmatter: $(basename "$file")"
        fi
    done
    
    # Check 2: No unescaped characters
    echo ""
    echo "🔤 Checking character escaping..."
    find "eventcatalog/docs/$eventcatalog_section" -name "*.mdx" | while read -r file; do
        if grep -E '[^&]<[^/a-zA-Z]|[^a-zA-Z]>[^&]' "$file" >/dev/null 2>&1; then
            echo "❌ Unescaped characters: $(basename "$file")"
        else
            echo "✅ Properly escaped: $(basename "$file")"
        fi
    done
    
    # Check 3: All files in config
    echo ""
    echo "⚙️  Checking eventcatalog.config.js..."
    find "eventcatalog/docs/$eventcatalog_section" -name "*.mdx" | while read -r file; do
        local slug=$(echo "$file" | sed "s|eventcatalog/docs/||" | sed "s|\.mdx$||")
        if grep -q "$slug" "eventcatalog/eventcatalog.config.js"; then
            echo "✅ In config: $(basename "$file")"
        else
            echo "❌ Missing from config: $(basename "$file")"
        fi
    done
    
    # Check 4: Image references
    echo ""
    echo "🖼️  Checking image references..."
    find "eventcatalog/docs/$eventcatalog_section" -name "*.mdx" | while read -r file; do
        if grep -q "!\[.*\](https.*confluence" "$file"; then
            echo "❌ Confluence image URLs: $(basename "$file")"
        elif grep -q "!\[.*\](\./images/" "$file"; then
            echo "✅ Local image refs: $(basename "$file")"
        else
            echo "ℹ️  No images: $(basename "$file")"
        fi
    done
    
    echo ""
    echo "🎯 VALIDATION COMPLETE"
    echo "Fix any ❌ issues before proceeding"
}

# Usage
validate_post_migration "05-business-documentation"
```

## Best Practices

### Image Management
1. **Organize by Documentation Section**: Keep images in section-specific directories
2. **Use Descriptive Names**: Name images clearly (e.g., `pimcore-product-structure.png`)
3. **Optimize File Sizes**: Compress images appropriately for web display
4. **Maintain Aspect Ratios**: Preserve original image proportions

### Content Quality
1. **Consistent Formatting**: Use EventCatalog MDX standards
2. **Proper Frontmatter**: Include all required metadata fields
3. **Relative References**: Use relative paths for all local resources
4. **Validation Testing**: Always test EventCatalog build after migration

### Error Prevention
1. **Character Escaping**: Always escape `<` and `>` characters
2. **Image Path Validation**: Verify all image references work locally
3. **Link Verification**: Update any Confluence-specific links
4. **Syntax Checking**: Validate MDX syntax before committing
