# cursor-standard-library
A shared library of Cursor AI validation and transformation rules for use across multiple repositories.

## Using via Git Subtree

### 1. Add the `cursor-standard-library` remote and add the subtree

In your project’s root directory:

```bash
git remote add cursor-standard-library \
  **************:ALSSoftware/cursor-standard-library.git

git subtree add \
  --prefix=.cursor/rules/cursor-standard-library/ \
  cursor-standard-library \
  main \
  --squash
```

### 2. Register a GitHub action to keep the subtree up to date (if desired)