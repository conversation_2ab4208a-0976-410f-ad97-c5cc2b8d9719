---
description: 
globs: 
alwaysApply: true
---
# Git Conventional Commits

Rule for automatically committing changes made by CursorAI using conventional commits format.

<rule>
name: conventional_commits
description: Automatically commit changes made by CursorAI using conventional commits format
filters:
  - type: event
    pattern: "build_success"
  - type: file_change
    pattern: "*"

actions:
  - type: execute
    command: |
      # Extract the change type and scope from the changes
      CHANGE_TYPE=""
      case "$CHANGE_DESCRIPTION" in
        *"add"*|*"create"*|*"implement"*) CHANGE_TYPE="feat";;
        *"fix"*|*"correct"*|*"resolve"*) CHANGE_TYPE="fix";;
        *"refactor"*|*"restructure"*) CHANGE_TYPE="refactor";;
        *"test"*) CHANGE_TYPE="test";;
        *"doc"*|*"comment"*) CHANGE_TYPE="docs";;
        *"style"*|*"format"*) CHANGE_TYPE="style";;
        *"perf"*|*"optimize"*) CHANGE_TYPE="perf";;
        *) CHANGE_TYPE="chore";;
      esac

      # Extract scope from file path
      SCOPE=$(dirname "$FILE" | tr '/' '-')

      # Commit the changes
      git add "$FILE"
      git commit -m "$CHANGE_TYPE($SCOPE): $CHANGE_DESCRIPTION"

  - type: suggest
    message: |
      Changes should be committed using conventional commits format:

      Format: <type>(<scope>): <description>

      Types:
      - feat: A new feature
      - fix: A bug fix
      - docs: Documentation only changes
      - style: Changes that do not affect the meaning of the code
      - refactor: A code change that neither fixes a bug nor adds a feature
      - perf: A code change that improves performance
      - test: Adding missing tests or correcting existing tests
      - chore: Changes to the build process or auxiliary tools

      The scope should be derived from the file path or affected component.
      The description should be clear and concise, written in imperative mood.

  - type: reject
    conditions:
      - pattern: "^(?!feat|fix|refactor|test|docs|style|perf|chore)\\([\\w-]+\\): "
        message: "Commit type must be one of: feat, fix, refactor, test, docs, style, perf, chore"
      - pattern: "^[^(]+\\([^)]*\\): [A-Z]"
        message: "Short description should start with lowercase"
      - pattern: "^[^(]+\\([^)]*\\): .+\\.$"
        message: "Short description should not end with a period"
      - pattern: "^[^(]+\\(.*[A-Z].*\\): "
        message: "Scope should be lowercase"

examples:
  - input: |
      # After adding a new function
      CHANGE_DESCRIPTION="add user authentication function"
      FILE="src/auth/login.ts"
    output: "feat(src-auth): add user authentication function"

  - input: |
      # After fixing a bug
      CHANGE_DESCRIPTION="fix incorrect date parsing"
      FILE="lib/utils/date.js"
    output: "fix(lib-utils): fix incorrect date parsing"

  - input: |
      # Bad: Missing type and scope
      git commit -m "added new feature"

      # Bad: Incorrect type
      git commit -m "feature(domain): add tests"

      # Bad: Uppercase description
      git commit -m "feat(domain): Add new feature"

      # Bad: Period at end
      git commit -m "feat(domain): add new feature."

      # Bad: Uppercase scope
      git commit -m "feat(Domain): add new feature"

      # Good: Complete commit message
      git commit -m "feat(domain): implement user authentication" -m "Add secure user authentication using JWT tokens. This: - Enables user login/registration - Secures API endpoints - Manages user sessions"
  - output: "Valid commit message following standards"

metadata:
  priority: high
  version: 1.0
</rule>
