---
description: 
globs: *.cs
alwaysApply: false
---
# MassTransit Consumer Best Practices

## Rule: masstransit-consumer-best-practices

### Description
Ensure all MassTransit consumers follow best practices for maintainability, observability, and testability.

### Requirements

- **Dependency Injection:**  
  - Consumers must receive dependencies (services, repositories, loggers) via constructor injection.
  - Avoid static access or service locator patterns.

- **Logging:**  
  - Use structured logging (e.g., `ILogger<T>`) to log message receipt, processing, and errors.
  - Log message type, IDs, and relevant context.
  - Don't worry about logging MassTransit properties, a logging filter will ensure that these properties are logged
  - Provide meaningful names to log properties

- **Context Usage:**  
  - Access and use `ConsumeContext<T>` for message metadata (correlation/user IDs, headers).
  - Use context for responding, publishing, or sending follow-up messages.
  - Always use the context that is passed in to create a send endpoint. If you don't, you will lose your `ConversationId` property.

- **Testability:**  
  - Abstract business logic away from the consumer class into services.
  - Use interfaces and dependency injection to allow mocking in tests.
  - Write unit tests for both the consumer and the business logic.

- **Error Handling:**  
  - Allow excpetions to bubble up and be caught by middleware. Only catch when you can resolve the problem. Avoid swallowing exceptions; let MassTransit handle retries/faults.
  - Use MassTransit's built-in retry and fault handling mechanisms. Do not use retry packages such as Polly, those don't provide good logging or observability.

### Examples

```csharp
public class MyEventConsumer : IConsumer<MyEvent>
{
    private readonly IMyService _myService;
    private readonly ILogger<MyEventConsumer> _logger;

    public MyEventConsumer(IMyService myService, ILogger<MyEventConsumer> logger)
    {
        _myService = myService;
        _logger = logger;
    }

    public async Task Consume(ConsumeContext<MyEvent> context)
    {
        _logger.LogInformation("Consuming MyEvent with OrderId {OrderId}", context.Message.OrderId);
   
        var confirmationId = await _myService.HandleEventAsync(context.Message, context.CancellationToken);

        _logger.LogInformation("Consumed MyEvent with OrderId {OrderId} and Notified SystemX. SystemX returned ConfirmationId {ConfirmationId}", context.Message.OrderId, confirmationId);

    }
}
```
