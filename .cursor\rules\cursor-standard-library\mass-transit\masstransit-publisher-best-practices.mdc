---
description: 
globs: *.cs
alwaysApply: false
---
# MassTransit Publisher Best Practices

## Rule: masstransit-publisher-best-practices

### Description
Ensure all MassTransit publishers follow best practices for maintainability, observability, and testability.

### Requirements

- **Dependency Injection:**  
  - Publishers must receive `IPublishEndpoint` or `ISendEndpointProvider` via constructor injection.
  - Avoid static access or service locator patterns.

- **Logging:**  
  - Use structured logging (e.g., `ILogger<T>`) to log publishing attempts, successes, and failures.
  - Log message type, IDs, and relevant context.

- **Context Usage:**  
  - Pass trace IDs and user context (if available) with published messages.
  - Use `SendContext` or `PublishContext` to enrich messages with headers/metadata.

- **Testability:**  
  - Write unit tests to verify that the correct messages are published with the expected content and context.

- **Error Handling:**  
  - Catch and log exceptions during publishing to avoid dropping messages that couldn't be written to the message broker.

### Examples

#### Example: Publishing an Event with IdempotencyKey

```csharp
public class MyEventPublisher : IMyEventPublisher
{
    private readonly IPublishEndpoint _publishEndpoint;
    private readonly ILogger<MyEventPublisher> _logger;

    public MyEventPublisher(IPublishEndpoint publishEndpoint, ILogger<MyEventPublisher> logger)
    {
        _publishEndpoint = publishEndpoint;
        _logger = logger;
    }

    public async Task PublishAsync(MyRequest request, CancellationToken cancellationToken)
    {
        var myEvent = new MyEvent(request.OrderId);

        try
        {
            _logger.LogInformation("Publishing MyRequest with OrderId {OrderId} and IdempodencyKey {IdempodencyKey}", myEvent.OrderId, request.idempodencyKey);
            await _publishEndpoint.Publish(myEvent, cancellationToken, context =>
            {
                context.Headers.Set("IdempodencyKey", idempodencyKey);
                // Add other headers or context as needed
            });

            _logger.LogInformation("Published MyRequest with OrderId {OrderId} and IdempodencyKey {IdempodencyKey}", myEvent.OrderId, request.idempodencyKey);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to publish MyRequest with OrderId {OrderId} and IdempodencyKey {IdempodencyKey}", myEvent.OrderId, request.idempodencyKey);
            throw;
        }
    }
}
```

#### Example: Sending a Command with IdempotencyKey

```csharp
public class OrderCommandPublisher : IOrderCommandPublisher
{
    private readonly ISendEndpointProvider _sendEndpointProvider;
    private readonly ILogger<OrderCommandPublisher> _logger;
    private readonly Config _config;

    public OrderCommandPublisher(ISendEndpointProvider sendEndpointProvider, IOptions<Config> configOptions, ILogger<OrderCommandPublisher> logger)
    {
        _sendEndpointProvider = sendEndpointProvider;
        _config = configOptions.value;
        _logger = logger;
    }

    public async Task SendCreateOrderCommandAsync(MyRequest request, CancellationToken cancellationToken)
    {
        var myCommand = new CreateOrderCommand(request.OrderId);
        var sendEndpoint = await _sendEndpointProvider.GetSendEndpoint(config.ServiceAddress);

        try
        {
            _logger.LogInformation("Sending CreateOrderCommand with OrderId {OrderId} and IdempotencyKey {IdempotencyKey}", myCommand.OrderId, request.idempotencyKey);
            await sendEndpoint.Publish(command, cancellationToken, context =>
            {
                context.Headers.Set("IdempotencyKey", request.idempotencyKey);
            });
            _logger.LogInformation("Sent CreateOrderCommand with OrderId {OrderId} and IdempotencyKey {IdempotencyKey}", myCommand.OrderId, request.idempotencyKey);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send CreateOrderCommand with OrderId {OrderId} and IdempotencyKey {IdempotencyKey}", myCommand.OrderId, request.idempotencyKey);
            throw;
        }
    }
}
```
