name: "Cursor Standard Library Update"
permissions:
  contents: write

# you can also add:
# on:
#   push:             # if you want to auto-sync on every push
#     branches: [main]
on:
  schedule:
    - cron: '0 2 * * *'     # every day at 02:00 UTC
  workflow_dispatch:        # also allow manual runs

jobs:
  sync-subtree:
    runs-on: ubuntu-latest
    steps:
      # 1) Checkout your repo (no credentials needed here)
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      # 2) Configure Git user
      - name: Configure Git user
        run: |
          git config user.name  "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"

      # 3) Spin up ssh-agent and add your deploy key
      - name: Setup SSH key
        uses: webfactory/ssh-agent@v0.5.4
        with:
          ssh-private-key: ${{ secrets.CURSOR_STANDARD_LIBRARY_DEPLOY_KEY }}

      # 4) Ensure the remote is set to the SSH URL
      - name: Add SSH remote for cursor-standard-library
        run: |
          git remote remove cursor-standard-library 2>/dev/null || true
          git remote add cursor-standard-library \
            **************:ALSSoftware/cursor-standard-library.git
          git fetch cursor-standard-library main

      # 5) Merge upstream changes into your subtree prefix
      - name: Merge in subtree (no squash)
        run: |
          git subtree pull \
            --prefix=.cursor/rules/cursor-standard-library \
            cursor-standard-library \
            main \
            --squash

      # 6) Only push if HEAD is now ahead of origin/main
      - name: Push merge commit if present
        run: |
          git fetch origin main
          if [ "$(git rev-parse HEAD)" != "$(git rev-parse origin/main)" ]; then
            echo "🔄 New subtree merge commit detected, pushing back to main"
            git push origin HEAD:main
          else
            echo "✅ No new subtree changes to push"
          fi