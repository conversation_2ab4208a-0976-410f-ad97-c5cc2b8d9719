---
description:
globs:
alwaysApply: false
---
# Rule: domain-subdomain-structure

## Description
When multiple subdomains (e.g., Order, Product) are implemented within a single service project (such as Axon.Domain.Service), the folder structure must reflect clear subdomain boundaries and DDD layers. This enables easy future extraction of subdomains into their own projects or services.

## Requirements
- Each subdomain (e.g., Order, Product) must have its own top-level folder within the service project.
- Inside each subdomain folder, organize code by DDD layers: Domain, Application, Infrastructure, Consumers, etc.
- No cross-subdomain dependencies except via shared contracts/events.
- All subdomain-specific logic, entities, and consumers must reside within their respective subdomain folders.
- Shared code (e.g., contracts, integration events) must remain in shared libraries (e.g., Axon.Contracts).

## Example Structure

/src/Axon.Domain.Service
  /Order
    /Domain
    /Application
    /Infrastructure
    /Consumers
  /Product
    /Domain
    /Application
    /Infrastructure
    /Consumers

## Rationale
- This structure enforces subdomain boundaries and supports DDD best practices.
- It allows for straightforward extraction of a subdomain into a new service or project in the future.
- It prevents code sprawl and cross-subdomain coupling.
