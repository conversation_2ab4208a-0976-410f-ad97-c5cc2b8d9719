---
description: Controller response guidelines with ResponseEnvelopeMiddleware
globs: **/Controllers/*.cs
alwaysApply: false
---
# API Response Guidelines with ResponseEnvelopeMiddleware

Controllers should return simple, unwrapped responses that will be automatically wrapped by the ResponseEnvelopeMiddleware.

<rule>
name: api_response_middleware_aware
description: Controller response guidelines considering automatic response wrapping by ResponseEnvelopeMiddleware
filters:
  # Match controller files
  - type: file_pattern
    pattern: ".*Controller\\.cs$"
  # Match API endpoints
  - type: content
    pattern: "\\[(?:Http(?:Get|Post|Put|Delete|Patch)|ApiController)\\]"

actions:
  - type: suggest
    message: |
      Controllers should return simple responses - the ResponseEnvelopeMiddleware will automatically wrap them:

      1. Success Responses (Middleware wraps automatically):
         ```csharp
         // ✅ Return the DTO directly - middleware wraps it
         return Ok(orderDto);
         return Accepted(new OrderCreatedResponse(orderId));
         return Created($"/api/orders/{id}", order);

         // For empty success responses
         return Ok();
         return NoContent();
         ```

      2. Error Responses (Middleware converts to ApiError format):
         ```csharp
         // ✅ Simple error responses - middleware wraps them
         return BadRequest("Invalid order data");
         return NotFound($"Order {id} not found");
         return Unauthorized("Authentication required");
         return Forbid("Insufficient permissions");

         // For model validation errors (automatic in ConfigureApiBehaviorOptions)
         // The framework automatically returns ApiResponse format for ModelState errors
         ```

      4. Controller Pattern:
         ```csharp
         [HttpPost]
         [ProducesResponseType(typeof(OrderDto), 200)]
         [ProducesResponseType(400)]
         [ProducesResponseType(404)]
         public async Task<ActionResult<OrderDto>> CreateOrder(
             [FromBody] CreateOrderRequest request,
             CancellationToken cancellationToken)
         {
             try
             {
                 // ModelState validation is automatic via ConfigureApiBehaviorOptions

                 var result = await _handler.Handle(request, cancellationToken);

                 if (result == null)
                     return NotFound("Order not found");

                 return Ok(result); // Simple response - middleware wraps it
             }
             catch (ValidationException ex)
             {
                 return BadRequest(ex.Message); // Middleware wraps as ApiError
             }
             catch (NotFoundException ex)
             {
                 return NotFound(ex.Message);
             }
             catch (UnauthorizedException)
             {
                 return Unauthorized(); // Middleware provides default message
             }
             catch (Exception ex)
             {
                 _logger.LogError(ex, "Error processing order");
                 throw; // Let ExceptionHandlingMiddleware handle it
             }
         }
         ```

      6. Middleware Behavior:
         - Automatically wraps 2xx, 4xx, and 5xx JSON responses
         - Detects pre-wrapped ApiResponse format and doesn't double-wrap
         - Skips infrastructure endpoints (/health, /swagger, etc.)
         - Preserves non-JSON responses as-is

  - type: reject
    conditions:
      # Warn against manually wrapping simple success responses
      - pattern: "return\\s+(?:Ok|Accepted)\\s*\\(\\s*ApiResponse(?:<[^>]+>)?\\.SuccessResponse\\s*\\([^)]*\\)\\s*\\)"
        message: "Don't manually wrap success responses. Return the DTO directly - ResponseEnvelopeMiddleware will wrap it automatically."

      # Allow ApiResponse for complex validation scenarios but suggest simpler approach
      - pattern: "return\\s+BadRequest\\s*\\(\\s*ApiResponse(?:<[^>]+>)?\\.ErrorResponse\\s*\\(\\s*\"[^\"]+\"\\s*\\)\\s*\\)"
        message: "Consider returning simple error messages for basic scenarios. Example: return BadRequest(\"Error message\"); The middleware will wrap it."

examples:
  - input: |
      // ❌ Overly complex - manually wrapping responses
      [HttpPost("order-created")]
      public async Task<IActionResult> OrderCreated([FromBody] OrderCreatedQuery request)
      {
          var orderId = await _handler.HandleAsync(request);
          return Accepted(ApiResponse<OrderCreatedResponse>.SuccessResponse(
              new OrderCreatedResponse(orderId)));
      }

      // ✅ Good: Simple response - middleware handles wrapping
      [HttpPost("order-created")]
      public async Task<ActionResult<OrderCreatedResponse>> OrderCreated(
          [FromBody] OrderCreatedQuery request,
          CancellationToken cancellationToken)
      {
          var orderId = await _handler.HandleAsync(request, cancellationToken);
          return Accepted(new OrderCreatedResponse(orderId));
      }

      // ✅ Good: Let exceptions bubble to ExceptionHandlingMiddleware
      [HttpGet("{id}")]
      public async Task<ActionResult<OrderDto>> GetOrder(
          int id,
          CancellationToken cancellationToken)
      {
          var order = await _service.GetOrder(id, cancellationToken);

          if (order == null)
              return NotFound($"Order {id} not found");

          return Ok(order);
      }

      // ✅ Good: Complex validation with ApiResponse (when needed)
      [HttpPost]
      public async Task<IActionResult> UpdateOrder(
          [FromBody] UpdateOrderRequest request)
      {
          var validationErrors = new List<ApiErrorDetail>();

          if (request.Quantity <= 0)
              validationErrors.Add(new ApiErrorDetail("quantity", "Must be positive"));

          if (string.IsNullOrEmpty(request.Email))
              validationErrors.Add(new ApiErrorDetail("email", "Email is required"));

          if (validationErrors.Any())
          {
              // Only use ApiResponse for complex error scenarios
              return BadRequest(ApiResponse<OrderDto>.ValidationErrorResponse(
                  "Validation failed", validationErrors));
          }

          var result = await _handler.Handle(request);
          return Ok(result); // Simple success response
      }
    output: "Controller following response middleware patterns"

metadata:
  priority: high
  version: 1.0
  tags:
    - api
    - controllers
    - middleware
    - response-wrapping
</rule>