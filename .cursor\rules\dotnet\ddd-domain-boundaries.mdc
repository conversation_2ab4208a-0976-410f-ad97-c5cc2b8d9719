---
description: 
globs: *.cs
alwaysApply: false
---
name: Domain boundaries
trigger: file
description: Prevent leaking application/infrastructure logic into domain layer

rules:
  - pattern: using .*HttpClient
    in_path: /Domain/
    then:
      message: "Domain model should not depend on infrastructure concerns like HttpClient."
      severity: error

  - pattern: using .*EntityFramework
    in_path: /Domain/
    then:
      message: "Domain model should not reference EF directly. Use repositories or domain events."
      severity: error
