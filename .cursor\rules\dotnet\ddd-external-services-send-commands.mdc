---
description: 
globs: *.cs
alwaysApply: false
---
# External Services Sends Commands

## Rule: ddd-external-services-send-commands

### Description
When external services are calling into the applicaiton with a webhook, the external API layer should take the incoming request model and convert it into a Command that is sent to a domain services so that it's invariants can be validated. 

Example: When an order already exists in Magento and is being sent to Axon

Think of the API call from Magento as “I assert that Order #12345 now exists with this state” rather than “please create me an order.”
That difference changes the verb you expose in your application layer but doesn't change the overall layering:
| Layer | What happens in this “order already exists” scenario|
| ----- | ----- |
|Anti-corruption adapter (Magento → Ordering) | Receives a webhook (or scheduled pull), deserialises Magento's payload, and sends a command such as RecordExternalOrder into the Ordering bounded context. |
| Ordering application service / aggregate | Handles RecordExternalOrder. <br/>* If the aggregate with the external ID does not exist yet, it creates it and marks the source as “Magento”. <br/>* If it already exists, it performs an idempotent update (e.g., refreshes totals, status). <br/>Either branch can legitimately raise a Domain Event like ExternalOrderRecorded. |
| Outbox / publisher | Translates ExternalOrderRecorded into an Integration Event (e.g., OrderImportedIntegrationEvent) and puts it on MassTransit so that shipping, invoicing, analytics, etc. can react. |

### Rationale

- Business invariants still matter - Even for externally-sourced orders you may enforce limits (e.g., reject duplicated Magento order numbers, validate money amounts, map SKUs).
- Idempotency & reconciliation - A command handler can do proper “create-or-update” logic and log discrepancies.
- Uniform consumption model - Downstream services get the same OrderImportedIntegrationEvent regardless of whether the order originated in your UI or in Magento; they don't need to care.

