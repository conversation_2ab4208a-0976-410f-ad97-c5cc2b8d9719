---
description: 
globs: *.cs
alwaysApply: false
---
name: Enforce Domain-Driven Design Layers
trigger: file
description: Ensure that DDD boundaries are respected.

rules:
  - pattern: using .*Infrastructure
    in_path: /Domain/
    then:
      message: "Domain layer should not reference Infrastructure."
      severity: error

  - pattern: using .*Application
    in_path: /Domain/
    then:
      message: "Domain layer should not depend on Application layer."
      severity: error

  - pattern: using .*Domain
    in_path: /Infrastructure/
    then:
      message: "OK: Infrastructure can depend on Domain."
      severity: info
