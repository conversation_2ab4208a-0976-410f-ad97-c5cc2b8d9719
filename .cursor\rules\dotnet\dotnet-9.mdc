---
description: 
globs: *.csproj
alwaysApply: false
---
# .NET Version Targeting Rule

## Rule: dotnet-9

### Description
All .NET projects in this repository must target .NET 9 for consistency.

### Requirements
- All `<TargetFramework>` elements in `.csproj` files must be set to `net9.0`.
- Do not target preview or non-LTS versions (e.g., `net10.0`).
- New projects must be created with `--framework net9.0` or equivalent.
- CI/CD workflows should install and use .NET 9.x SDK.

### Rationale
- .NET 9 is supported until May 2026
- Using a single version across all projects reduces maintenance burden and build issues.

### Example
```xml
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
  </PropertyGroup>
</Project>
```

