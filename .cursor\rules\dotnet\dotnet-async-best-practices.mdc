---
description: 
globs: *.cs
alwaysApply: false
---
name: Async and cancellation token usage
trigger: file
description: Ensure async methods and tokens are used properly.

rules:
  - pattern: async Task
    if_not_contains:
      - CancellationToken
    then:
      message: "Consider adding a CancellationToken parameter to async methods."
      severity: warning

  - pattern: .Result|.Wait\(
    then:
      message: "Avoid blocking async calls with .Result or .Wait(); this can cause deadlocks."
      severity: error
