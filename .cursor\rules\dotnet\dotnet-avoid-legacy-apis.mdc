---
description: 
globs: *.cs
alwaysApply: false
---
name: Discourage legacy .NET APIs
trigger: file
description: Warn about use of old or problematic APIs.

rules:
  - pattern: System.Web
    then:
      message: "Avoid using System.Web in modern .NET Core projects."
      severity: error

  - pattern: Task.Run
    then:
      message: "Avoid Task.Run unless you need to offload synchronous CPU-bound work."
      severity: warning

  - pattern: var client = new HttpClient
    then:
      message: "Do not instantiate HttpClient directly. Use IHttpClientFactory."
      severity: error
