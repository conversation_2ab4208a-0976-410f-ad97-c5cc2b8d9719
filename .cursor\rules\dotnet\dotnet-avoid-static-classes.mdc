---
description: 
globs: *.cs
alwaysApply: false
---
name: Use Dependency Injection
trigger: file
description: Prefer DI over statics for testability and flexibility.

rules:
  - pattern: static class
    then:
      message: "Static classes limit testability and flexibility. Prefer DI where possible."
      severity: warning

  - pattern: new (Logger|HttpClient|DbContext)\(
    then:
      message: "Avoid direct instantiation of services. Use constructor injection."
      severity: error
