---
description:
globs: **/Controllers/*.cs
alwaysApply: false
---
# .NET Thin API Controllers

Guidelines for maintaining thin, focused API controllers in .NET applications.

<rule>
name: dotnet_thin_api
description: Enforce thin controller pattern and proper separation of concerns in .NET API controllers
filters:
  # Match controller files
  - type: file_pattern
    pattern: ".*Controller\\.cs$"
  # Match API endpoints
  - type: content
    pattern: "\\[(?:Http(?:Get|Post|Put|Delete|Patch)|ApiController)\\]"

actions:
  - type: suggest
    message: |
      Controllers should follow the Thin Controller pattern:

      1. Single Operation per Controller:
         - Each controller should contain ONLY ONE action/endpoint
         - This makes API versioning much easier to manage
         - Name controllers after their specific operation (e.g., OrderCreationController, OrderQueryController)

      2. Primary Responsibilities:
         - Accept HTTP requests
         - Validate inputs
         - Delegate to request handler
         - Return HTTP responses

      3. Controller Methods Should:
         ```csharp
         [HttpPost]
         public async Task<ActionResult<TResponse>> Endpoint(
             [FromBody] TRequest request,
             CancellationToken cancellationToken)
         {
             // Log the incoming request
             _logger.LogInformation("Received request for...");

             // Delegate to request handler
             var result = await _orderCreatedRequestHandler.Handle(request, cancellationToken);

             // Return appropriate response
             return Accepted(result); // or Ok(result) for queries
         }
         ```

      4. Avoid in Controllers:
         - Multiple endpoints/actions (keep one action per controller)
         - Business logic and calculations
         - Direct database operations
         - Infrastructure concerns (message queues, caching)
         - Complex transformations
         - State management
         - ProducesResponseType attributes (middleware handles response wrapping)
         - ModelState validation checks (handled by framework)
         - Try-catch blocks (let exceptions bubble up to middleware)

      5. Proper Dependencies:
         ```csharp
         public class OrderController : ControllerBase
         {
             private readonly IOrderCreatedRequestHandler _orderCreatedRequestHandler;
             // ❌ Don't inject infrastructure
             // private readonly RabbitMQClient _messageQueue;

             public OrderController(IOrderCreatedRequestHandler orderCreatedRequestHandler)
             {
                 _orderCreatedRequestHandler = orderCreatedRequestHandler;
             }
         }
         ```

  - type: reject
    conditions:
      # Reject direct DbContext usage
      - pattern: "DbContext|DbSet<|_context\\."
        message: "Controllers should not directly access DbContext. Use application request handlers instead."

      # Reject direct infrastructure access
      - pattern: "RabbitMQ|MassTransit|IMessageBus"
        message: "Controllers should not directly use message queues or other infrastructure. Delegate to application request handlers."

      # Reject complex business logic
      - pattern: "(?s)public\\s+\\w+\\s+\\w+\\([^)]*\\)\\s*{[^}]{500,}}"
        message: "Controller methods are too complex. Move business logic to application request handlers."

      # Reject ProducesResponseType attributes
      - pattern: "\\[ProducesResponseType"
        message: "Remove ProducesResponseType attributes. The ResponseEnvelopeMiddleware handles response wrapping automatically, making these attributes unnecessary."

      # Reject multiple endpoints in a single controller
      - pattern: "(?s)(\\[Http(Get|Post|Put|Delete|Patch)\\][^\\[]*){2,}"
        message: "Controllers should contain only ONE action/endpoint. Split this controller into separate controllers for each operation to improve versioning and maintainability."

examples:
  - input: |
      // ❌ Bad: Multiple endpoints in one controller
      [ApiController]
      [Route("api/v1/orders")]
      public class OrderController : ControllerBase
      {
          [HttpPost]
          public async Task<IActionResult> CreateOrder(OrderRequest request) { }

          [HttpGet("{id}")]
          public async Task<IActionResult> GetOrder(int id) { }

          [HttpPut("{id}")]
          public async Task<IActionResult> UpdateOrder(int id, OrderRequest request) { }
      }

      // ❌ Bad: Business logic in controller
      [HttpPost]
      public async Task<ActionResult<OrderResponse>> CreateOrder(OrderRequest request)
      {
          var total = request.Items.Sum(i => i.Price * i.Quantity);
          var discount = total > 100 ? 0.1m : 0;
          var order = new Order
          {
              Items = request.Items,
              Total = total * (1 - discount)
          };
          await _context.Orders.AddAsync(order);
          await _context.SaveChangesAsync();
          await _messageQueue.PublishAsync(new OrderCreated(order.Id));
          return Ok(new OrderResponse(order));
      }

      // ✅ Good: Thin controller delegating to request handler
      [HttpPost]
      public async Task<ActionResult<OrderResponse>> CreateOrder(
          [FromBody] OrderRequest request,
          CancellationToken cancellationToken)
      {
          _logger.LogInformation("Received order creation request");
          
          var result = await _orderCreatedRequestHandler.Handle(request, cancellationToken);
          
          _logger.LogInformation("Successfully processed order creation");
          return Accepted(result);
      }

      // ❌ Bad: Using ProducesResponseType attributes
      [HttpGet("{id}")]
      [ProducesResponseType(typeof(OrderDto), 200)]
      [ProducesResponseType(404)]
      public async Task<ActionResult<OrderDto>> GetOrder(int id)
      {
          // ...
      }

      // ✅ Good: No ProducesResponseType attributes needed
      [HttpGet("{id}")]
      public async Task<ActionResult<OrderDto>> GetOrder(
          int id,
          CancellationToken cancellationToken)
      {
          var order = await _orderService.GetOrder(id, cancellationToken);
          if (order == null)
              return NotFound($"Order {id} not found");
          return Ok(order);
      }

      // ✅ Good: Single operation controllers
      [ApiController]
      [Route("api/v1/magento-integration-adapter")]
      public class OrderCreationController : ControllerBase
      {
          private readonly IOrderCreatedRequestHandler _handler;

          public OrderCreationController(IOrderCreatedRequestHandler handler)
          {
              _handler = handler;
          }

          [HttpPost("order-created")]
          public async Task<ActionResult<OrderCreatedResponse>> OrderCreated(
              [FromBody] OrderCreatedQuery request,
              CancellationToken cancellationToken)
          {
              var orderId = await _handler.HandleAsync(request, cancellationToken);
              return Accepted(new OrderCreatedResponse(orderId));
          }
      }

      [ApiController]
      [Route("api/v1/magento-integration-adapter")]
      public class OrderQueryController : ControllerBase
      {
          private readonly IOrderFetchRequestHandler _handler;

          public OrderQueryController(IOrderFetchRequestHandler handler)
          {
              _handler = handler;
          }

          [HttpGet("orders/{orderId:guid}")]
          public async Task<ActionResult<OrderData>> GetOrder(
              Guid orderId,
              CancellationToken cancellationToken)
          {
              var query = new OrderFetchQuery { OrderId = orderId };
              var order = await _handler.HandleAsync(query, cancellationToken);

              if (order == null)
                  return NotFound($"Order {orderId} not found");

              return Ok(order);
          }
      }
    output: "Controller following thin API guidelines"

metadata:
  priority: high
  version: 1.0
  tags:
    - api
    - controllers
    - best-practices
    - architecture
</rule>