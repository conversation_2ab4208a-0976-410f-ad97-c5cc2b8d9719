---
description: 
globs: *.cs
alwaysApply: false
---
# .NET DateTimeOffset Usage Rule

## Rule: dotnet-use-datetimeoffset

### Description
Enforce the use of `DateTimeOffset` instead of `DateTime` to ensure proper timezone handling and prevent timezone-related bugs.

### Requirements
- Use `DateTimeOffset` for all date/time properties and parameters
- Use `DateTimeOffset.UtcNow` instead of `DateTime.UtcNow` or `DateTime.Now`
- Use `DateTimeOffset?` for nullable date/time fields
- Avoid `DateTime` except in very specific legacy scenarios

### Rationale
- `DateTimeOffset` includes timezone information, preventing ambiguity
- Ensures consistent ISO-8601 formatting with timezone in JSON responses
- Prevents timezone conversion issues in distributed systems
- Better for API consistency and international applications
- Avoids "DateTime.Kind" confusion and related bugs

### Examples

#### ❌ Bad: Using DateTime
```csharp
public class Order
{
    public DateTime CreatedAt { get; set; }           // Missing timezone info
    public DateTime? UpdatedAt { get; set; }          // Ambiguous timezone
}

public class OrderService
{
    public void CreateOrder()
    {
        var now = DateTime.Now;                       // Local time ambiguity
        var utcNow = DateTime.UtcNow;                // Better but inconsistent
    }
}
```

#### ✅ Good: Using DateTimeOffset
```csharp
public class Order
{
    public DateTimeOffset CreatedAt { get; set; }    // Includes timezone
    public DateTimeOffset? UpdatedAt { get; set; }   // Clear timezone handling
}

public class OrderService
{
    public void CreateOrder()
    {
        var now = DateTimeOffset.UtcNow;             // Explicit UTC with timezone
        var specificTime = DateTimeOffset.Now;       // Local time with timezone
    }
}
```

#### ✅ JSON Output Comparison
```json
// DateTime (problematic)
{
    "created_at": "2024-03-21T14:30:00"             // Missing timezone
}

// DateTimeOffset (correct)
{
    "created_at": "2024-03-21T14:30:00Z"            // With timezone
}
```

### Exceptions
- Legacy database columns that require `DateTime` for ORM compatibility
- Interacting with third-party APIs that specifically require `DateTime`
- Date-only scenarios where timezone is truly irrelevant (consider using `DateOnly` instead)

### Migration Strategy
When updating existing code:
1. Change property types from `DateTime` to `DateTimeOffset`
2. Update database migrations if needed
3. Update JSON serialization attributes if required
4. Change `DateTime.UtcNow` calls to `DateTimeOffset.UtcNow`
5. Test timezone handling in different environments
