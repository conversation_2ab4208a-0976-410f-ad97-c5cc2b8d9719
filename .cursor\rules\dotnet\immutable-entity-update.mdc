---
description: 
globs: *.cs
alwaysApply: false
---
name: Prevent Mutable Updates
trigger: file
description: Enforce append-only immutable persistence for domain entities.

rules:
  - pattern: context\.Update
    then:
      message: "Avoid DbContext.Update(). Immutability means you should insert a new record version."
      severity: error

  - pattern: context\.Entry\(.*\)\.State = EntityState.Modified
    then:
      message: "Avoid manually marking entities modified. Use versioned writes instead."
      severity: error
