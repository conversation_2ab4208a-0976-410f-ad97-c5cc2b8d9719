---
description: 
globs: *.cs
alwaysApply: false
---
name: Ensure entity version tracking
trigger: file
description: Encourage timestamp and reference tracking for immutable objects.

rules:
  - pattern: class .*Version
    if_not_contains:
      - EffectiveFrom
      - EffectiveTo
      - PreviousVersionId
    then:
      message: "Versioned entities should include `EffectiveFrom`, `EffectiveTo`, and `PreviousVersionId` fields."
      severity: warning
