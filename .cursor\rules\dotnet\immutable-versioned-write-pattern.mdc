---
description: 
globs: *.cs
alwaysApply: false
---
name: Enforce versioned writes
trigger: file
description: Encourage versioned data persistence instead of in-place edits.

rules:
  - pattern: public .*Service
    if_contains:
      - Update
    then:
      message: "Services should not update entities in-place. Instead, write a new version and mark old version inactive/expired."
      severity: warning
