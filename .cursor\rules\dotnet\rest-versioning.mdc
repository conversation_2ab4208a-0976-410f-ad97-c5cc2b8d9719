---
description: 
globs: 
alwaysApply: false
---
name: Contract best practices
trigger: file
description: Ensure good API hygiene for public contracts

rules:
  - pattern: [Route("api/
    if_not_contains:
      - v1
    then:
      message: "Always version your public API routes to avoid breaking clients."
      severity: warning

  - pattern: public record .*Response
    if_not_contains:
      - [DataContract]
    then:
      message: "Response DTOs should use DataContract for forward/backward compatibility."
      severity: info
