---
description: 
globs: *.cs
alwaysApply: false
---
name: Enforce Dependency Inversion Principle
trigger: file
description: Suggest injecting abstractions instead of concrete classes.

rules:
  - pattern: new (SqlConnection|HttpClient|DbContext)\(
    then:
      message: "Avoid directly instantiating infrastructure dependencies. Inject via interfaces and configure DI container."
      severity: error

  - pattern: class .*Controller
    if_not_contains:
      - "constructor"
    then:
      message: "Controllers should depend on abstractions via constructor injection."
      severity: warning
