---
description: 
globs: *.cs
alwaysApply: false
---
name: Enforce Open/Closed Principle
trigger: file
description: Warn when business logic is implemented in a way that requires modification rather than extension.

rules:
  - pattern: switch \(\w+\)
    then:
      message: "Switch statements may violate the Open/Closed Principle. Consider using polymorphism or strategy pattern."
      severity: warning

  - pattern: if \(.+\) \{[^}]*if \(.+\)
    then:
      message: "Nested ifs may signal brittle logic. Consider extracting to strategy classes or specification pattern."
      severity: info
