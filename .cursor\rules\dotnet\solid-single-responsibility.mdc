---
description: 
globs: *.cs
alwaysApply: false
---
name: Enforce Single Responsibility Principle
trigger: file
description: Ensure that each class has a single reason to change.

rules:
  - pattern: class .*Service
    if_contains:
      - "HttpClient"
      - "DbContext"
    then:
      message: "This class appears to do too much (I/O + DB logic). Consider separating responsibilities."
      severity: warning

  - pattern: class .*Manager
    then:
      message: "Avoid ambiguous names like 'Manager' — they often indicate SRP violations. Prefer specific names like 'OrderSubmitter' or 'PaymentProcessor'."
      severity: info
