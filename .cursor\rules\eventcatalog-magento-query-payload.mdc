---
description: 
globs: *.mdx
alwaysApply: false
---
# Magento Query Payload Standards

## Rule Description

All queries produced by the Magento service must use the exact response structure from the corresponding Magento 2 REST API endpoint, with the exception of standard response metadata fields. This ensures consistency between the Magento API and our integration layer while maintaining standard response formatting.

## Requirements

1. **Response Structure**
   - The query response payload MUST match the exact structure of the corresponding Magento 2 API endpoint response for business data
   - Standard response metadata fields are allowed and should be included:
     - `success`: boolean indicating operation success
     - `timestamp`: ISO 8601 timestamp of the response
     - `message`: human-readable response message
   - No other fields should be transformed or renamed from the original Magento response
   - All field types must match the Magento API types exactly

2. **OpenAPI Specification**
   - The `openapi.yml` file MUST reference or replicate the exact schema from the Magento 2 API
   - Schema components should be named to match Magento's naming convention (e.g., `sales-data-order-interface`)
   - Response examples must use real Magento API response format with standard metadata fields
   - Standard response metadata must be documented in the schema

3. **Documentation**
   - Query documentation must include a reference to the corresponding Magento API endpoint
   - Any deviations from the Magento API response must be explicitly documented and justified
   - The documentation must include a link to the relevant Magento 2 API documentation
   - Standard response metadata fields must be documented

## Examples

### Good Example
```yaml
# openapi.yml
components:
  schemas:
    OrderResponse:
      type: object
      properties:
        success:
          type: boolean
          description: Operation success status
        timestamp:
          type: string
          format: date-time
          description: Response timestamp
        message:
          type: string
          description: Response message
        data:
          $ref: '#/components/schemas/sales-data-order-interface'
    sales-data-order-interface:
      type: object
      properties:
        increment_id:
          type: string
          description: Order increment ID
        # ... other fields exactly as they appear in Magento API
```

### Bad Example
```yaml
# openapi.yml - Don't do this
components:
  schemas:
    OrderResponse:
      type: object
      properties:
        id:  # Should be entity_id as in Magento
          type: integer
        orderNumber:  # Should be increment_id as in Magento
          type: string
        status:  # Missing standard response metadata
          type: string
```

## Validation

The following aspects will be validated:
1. Field names match Magento API exactly (except standard metadata)
2. Data types match Magento API exactly
3. Required/optional status matches Magento API
4. Nested object structures match Magento API
5. Array item definitions match Magento API
6. Standard response metadata fields are present and correctly typed

## Implementation Guide

1. **Finding the Correct Magento API Response**
   - Locate the corresponding Magento 2 API endpoint
   - Use the Swagger documentation from Magento
   - Reference the official Magento API documentation

2. **Implementing the Response**
   - Include standard response metadata fields
   - Copy the exact response schema from Magento for business data
   - Maintain all field names and types
   - Include all required and optional fields
   - Preserve nested structure

3. **Documentation Requirements**
   ```mdx
   ---
   # In query documentation (index.mdx)
   specifications:
     - type: openapi
       path: 'openapi.yml'
   ---

   ## Magento API Reference
   This query uses the response format from Magento 2 API endpoint:
   `GET /V1/orders/{orderId}`

   [Link to Magento API Documentation](mdc:https:/devdocs.magento.com/swagger)

   ### Response Format
   All responses include standard metadata:
   - success: Operation success status
   - timestamp: Response timestamp
   - message: Human-readable message
   ```

## Exceptions

Any exceptions to this rule must be:
1. Documented in the query's index.mdx file
2. Include a clear justification for the deviation
3. Approved by the integration team
4. Still maintain data type compatibility
5. Include all standard response metadata fields

## Benefits

- Consistent data structures across the platform
- Easier integration with Magento
- Reduced transformation overhead
- Better maintainability
- Clear documentation trail
- Standardized error handling

## Related Rules
- [EventCatalog Magento Template](mdc:.cursor/rules/eventcatalog-magento-template.mdx)
- [OpenAPI Standards](mdc:.cursor/rules/openapi-standards.mdx)

## Key Requirements

1. **Request Body Structure**
   - Request bodies MUST NOT include metadata fields (`success`, `timestamp`, `message`)
   - Request bodies MUST contain only the business data that matches Magento API structure
   - Example of what NOT to do in request body:
     ```json
     {
       "success": true,
       "timestamp": "2024-03-19T14:30:00Z", 
       "message": "Creating customer",
       "data": { ... }  
     }
     ```
   - Example of correct request body:
     ```json
     {
       "customer_id": 123,
       "email": "<EMAIL>",
       "firstname": "John",
       "lastname": "Doe"
     }
     ```

2. **Response Body Structure**
   - All responses MUST include standard metadata fields:
     - `success`: boolean
     - `timestamp`: ISO 8601 string
     - `message`: string
   - Business data MUST be under `data` property
   - Example of correct response:
     ```json
     {
       "success": true,
       "timestamp": "2024-03-19T14:30:00Z",
       "message": "Customer created successfully",
       "data": {
         "customer_id": 123,
         "email": "<EMAIL>"
       }
     }
     ```

## OpenAPI Implementation

```yaml
# Good Example
components:
  schemas:
    CustomerRequest:  # ✅ Only business data
      type: object
      properties:
        email:
          type: string
        firstname:
          type: string
        lastname:
          type: string

    CustomerResponse:  # ✅ Metadata + data
      type: object
      required:
        - success
        - timestamp
        - message
      properties:
        success:
          type: boolean
        timestamp:
          type: string
          format: date-time
        message:
          type: string
        data:
          $ref: '#/components/schemas/CustomerData'

# Bad Example - DO NOT DO THIS
    BadCustomerRequest:  # ❌ Includes metadata
      type: object
      properties:
        success:
          type: boolean
        timestamp:
          type: string
        message:
          type: string
        data:
          type: object
          properties:
            email:
              type: string
```

## Validation Checklist

For each query's OpenAPI specification:

1. Request Schema:
   - [ ] No `success` field
   - [ ] No `timestamp` field
   - [ ] No `message` field
   - [ ] No `data` wrapper
   - [ ] Matches Magento API structure exactly

2. Response Schema:
   - [ ] Has `success` boolean
   - [ ] Has `timestamp` datetime
   - [ ] Has `message` string
   - [ ] Business data under `data` property

## Implementation Steps

1. Remove metadata fields from all request bodies
2. Keep metadata fields in all responses
3. Update examples to match this pattern
4. Validate against Magento API documentation

## Common Fixes Required

1. In request schemas:
   ```diff
   - success: boolean
   - timestamp: string
   - message: string
   - data: object
   + // Direct business fields from Magento API
   ```

2. In request examples:
   ```diff
   - {
   -   "success": true,
   -   "timestamp": "2024-03-19T14:30:00Z",
   -   "message": "Creating entity",
   -   "data": {
   -     "field": "value"
   -   }
   - }
   + {
   +   "field": "value"
   + }
   ``` 