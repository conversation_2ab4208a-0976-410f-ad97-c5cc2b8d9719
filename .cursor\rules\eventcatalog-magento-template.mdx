---
# Magento Service Documentation Template
# This template combines EventCatalog rules with Magento-specific standards

---
id: {resource-id} # Must be kebab-case
name: {Resource Name}
version: 0.0.1 # Required by EventCatalog rules
summary: |
  {Clear description of the resource}
producers:
  - magento
owners:
  - euvic # or enterprise based on ownership
badges:
  - content: REST API
    backgroundColor: blue
    textColor: white
  - content: Magento 2
    backgroundColor: orange
    textColor: white
  # Add GDPR badge if applicable
  # Add Subdomain badge if applicable
channels:
  - id: magento.{env}.rest.queries
    type: http
    parameters:
      env:
        enum:
          - local
          - dev
          - sit
          - prod
        description: 'Environment to use'
specifications:
  - type: openapi
    path: 'openapi.yml' # Required OpenAPI specification file
---

## Overview

{Detailed description of the resource and its purpose}

## Architecture Diagram
<NodeGraph />

## API Specification
The OpenAPI specification (openapi.yml) is required and must include:
- Complete request/response schemas
- All possible response codes
- Rich descriptions for all fields
- Example requests and responses
- Authentication requirements
- Server configurations

## Implementation Details

### Authentication
- Bearer token authentication required
- Required scopes: [list scopes]
- Rate limiting: {details}

### Request Headers
```
Authorization: Bearer {token}
Content-Type: application/json
Accept: application/json
```

### Response Headers
```
Content-Type: application/json
ETag: "{etag}" # For resources that support concurrency control
```

## Usage Guidelines

### Integration Considerations
- Authentication requirements
- Rate limiting considerations
- Data validation rules
- Concurrency handling (if applicable)

### Data Privacy and Compliance
{Include if handling personal data}
- GDPR compliance requirements
- Data retention policies
- Personal data handling guidelines
- Required consent mechanisms

## Notes
- Important implementation details
- Special considerations
- Integration tips
- Known limitations

## Related Resources
- Link to related queries
- Link to relevant documentation
- Link to API specifications 