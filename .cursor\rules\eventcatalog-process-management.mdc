---
description:
globs:
alwaysApply: false
---
# EventCatalog Process Management

Rule for managing EventCatalog development server instances to prevent conflicts and resource waste.

<rule>
name: eventcatalog_process_management
description: Check for running EventCatalog instance before starting a new one
filters:
  - type: event
    pattern: "eventcatalog_start"
  - type: command
    pattern: "npm run dev"
  - type: content
    pattern: "eventcatalog.*dev"

actions:
  - type: execute
    command: |
      # Check if EventCatalog is already running
      if lsof -i :7500 >/dev/null 2>&1; then
        echo "EventCatalog is already running on port 7500"
        echo "Current EventCatalog processes:"
        ps aux | grep -E "(eventcatalog|astro)" | grep -v grep
        
        read -p "Do you want to terminate the existing instance and start a new one? (y/n) " choice
        if [ "$choice" = "y" ] || [ "$choice" = "Y" ]; then
          echo "Terminating existing EventCatalog instances..."
          pkill -f "eventcatalog" || true
          pkill -f "astro" || true
          sleep 3
          echo "Starting new EventCatalog instance..."
          npm run dev
        else
          echo "Keeping existing EventCatalog instance running"
          echo "Access it at: http://localhost:7500"
          exit 0
        fi
      else
        echo "No EventCatalog instance detected. Starting new instance..."
        npm run dev
      fi

  - type: suggest
    message: |
      Before starting EventCatalog:

      1. **Check for existing instances**:
         ```bash
         # Check if port 7500 is in use
         lsof -i :7500
         
         # Check for EventCatalog processes
         ps aux | grep -E "(eventcatalog|astro)" | grep -v grep
         ```

      2. **Terminate existing instances if needed**:
         ```bash
         # Kill EventCatalog processes
         pkill -f "eventcatalog"
         pkill -f "astro"
         
         # Wait for processes to terminate
         sleep 3
         ```

      3. **Start EventCatalog**:
         ```bash
         # Navigate to project root
         cd /path/to/project
         
         # Start development server
         npm run dev
         ```

      4. **Verify startup**:
         - Check that port 7500 is accessible
         - Verify no parsing errors in console
         - Test documentation pages load correctly

  - type: reject
    conditions:
      - pattern: "multiple.*eventcatalog.*running"
        message: "Multiple EventCatalog instances detected. Please terminate existing instances before starting new ones."
      
      - pattern: "port.*7500.*in.*use"
        message: "Port 7500 is already in use. Check for existing EventCatalog instances."

examples:
  - input: |
      # Good: Check before starting
      if ! lsof -i :7500; then
        npm run dev
      else
        echo "EventCatalog already running"
      fi

  - input: |
      # Bad: Start without checking
      npm run dev  # May cause port conflicts

  - input: |
      # Good: Proper process management
      pkill -f "eventcatalog" && sleep 3 && npm run dev

metadata:
  priority: high
  version: 1.0
  tags:
    - eventcatalog
    - process-management
    - development
  related_rules:
    - eventcatalog
</rule>

## Implementation Guidelines

### Pre-Development Checklist

1. **Process Check**: Always verify no existing EventCatalog instances
2. **Port Verification**: Confirm port 7500 is available
3. **Clean Startup**: Terminate conflicting processes before starting
4. **Monitoring**: Watch for startup errors and parsing issues

### Automation Script

```bash
#!/bin/bash
# eventcatalog-start.sh - Safe EventCatalog startup script

echo "🔍 Checking for existing EventCatalog instances..."

if lsof -i :7500 >/dev/null 2>&1; then
    echo "⚠️  EventCatalog is already running on port 7500"
    echo "📋 Current processes:"
    ps aux | grep -E "(eventcatalog|astro)" | grep -v grep
    
    echo -n "🔄 Restart existing instance? (y/N): "
    read -r choice
    
    if [[ "$choice" =~ ^[Yy]$ ]]; then
        echo "🛑 Terminating existing instances..."
        pkill -f "eventcatalog" || true
        pkill -f "astro" || true
        sleep 3
        echo "🚀 Starting new EventCatalog instance..."
        npm run dev
    else
        echo "✅ Keeping existing instance running"
        echo "🌐 Access at: http://localhost:7500"
    fi
else
    echo "✅ No existing instance found"
    echo "🚀 Starting EventCatalog..."
    npm run dev
fi
```

### Usage

```bash
# Make script executable
chmod +x eventcatalog-start.sh

# Use instead of direct npm run dev
./eventcatalog-start.sh
```

## Benefits

- **Prevents port conflicts** from multiple EventCatalog instances
- **Reduces resource waste** by avoiding duplicate processes
- **Improves development experience** with clear process management
- **Provides safe restart mechanism** for configuration changes
- **Enables monitoring** of EventCatalog process health
