---
description: 
globs: *.mdx
alwaysApply: false
---
ruleset "EventCatalog Documentation" {
  section "Frontmatter Consistency" {
    rule ".mdx files must start with YAML frontmatter containing: id, name, summary, owners." {}
    rule "Services must include receives, sends." {}
  }
  section "ID Format" {
    rule "All id fields in EventCatalog resources (domains, users, teams, etc.) must use kebab-case (lowercase letters, numbers, and hyphens only, e.g., order-management, user-profile)." {
      rationale: "Consistent kebab-case IDs improve readability, prevent errors in linking and referencing, and align with EventCatalog best practices."
      enforcement: "The id field must match the regex: ^[a-z0-9]+(-[a-z0-9]+)*$. Applies to all files in eventcatalog/ that define an id field in frontmatter or JSON/YAML. Violations should be flagged during validation or CI."
      examples: "Valid: id: order-management, id: user-profile-123, id: e-commerce. Invalid: id: OrderManagement, id: user_profile, id: E-Commerce, id: product123!"
      fix: "Convert all id values to lowercase. Replace spaces and underscores with hyphens. Remove any non-alphanumeric, non-hyphen characters."
    }
    rule "Events, commands, and queries must provide a realistic JSON payload example in a code block labeled with title=\"Payload example\"." {}
      regex: "^[a-z0-9]+(-[a-z0-9]+)*$"
    }
  }
  section "Versioning" {
    rule "Every new EventCatalog resource file of type domain, service, channel, query, event, or command must include a version field in its frontmatter or metadata. If not otherwise specified, the default version should be set to 0.0.1." {
      rationale: "Including a version field in these resource types ensures traceability, supports future evolution, and aligns with best practices for resource management."
      enforcement: "When creating a new file of type domain, service, channel, query, event, or command in eventcatalog/, add a version: 0.0.1 field to the frontmatter or metadata unless a different version is explicitly provided. Applies only to these resource types in EventCatalog. Violations should be flagged during validation or CI if the version field is missing or empty."
      examples: "Valid: version: 0.0.1, version: 1.2.3. Invalid: # No version field present, version: "
      fix: "Add version: 0.0.1 to the frontmatter or metadata if not otherwise specified."
    }
    rule "The version field in frontmatter must match the actual version of the resource." {}
  }
  section "Events, Commands, and Queries" {
    rule "Events, commands, and queries must include a channels section specifying channel(s) and parameters." {}
    rule "Events, commands, and queries shall use H2-level (##) section headers for: Overview, Architecture diagram, Payload example, and other relevant sections." {}
    rule "Events and commands must contain a  schema.json file and <SchemaViewer file=\"schema.json\" /> component to display the schema file. There should be a `schemaPath: 'schema.json' property in the frontmatter." {}
    rule "Queries must include an openapi.yml file documenting the request, response, rich descriptions, and example requests. The openapi.yml file must be referenced in the frontmatter under specifications." {}
    rule "Queries should only be attached to http channels." {}
    rule "Events should only be attached to SNS channels." {}
    rule "Commands should only be attached to SQS channels." {}
    rule "Events and commands must include the following sections in this order:"
    example: """
    ## Overview

    ## Architecture Diagram
    <NodeGraph />

    """
    enforcement: "Each event, command, and query file must have these sections and components in the specified order. The schema must be documented in openapi.yml, not schema.json."
    rule "Events, commands, and queries must include a channels section specifying channel(s) and parameters." {}
  }
    rule "Events and commands should include a payload example in this format"
    example: """

    ## Payload example
    ```json
    {
    }
    ```
    """
    enforcement: "Each event and command file must have this section."
    rule "Events, commands, and queries must include a channels section specifying channel(s) and parameters." {}
  }

  section "Badges and Metadata" {
    rule "Use the badges frontmatter property to highlight status, type, or technology (e.g., MassTransit, AWS SNS/SQS, REST, SOAP)." {}
    rule "For events, include badges for the event type and transport technologies." {}
  }
  section "Architecture Visualization" {
    rule "Use <NodeGraph/> or <NodeGraph title=\"...\"/> to visually represent the architecture or relationships." {}
  }
  section "Code Examples" {
    rule "For events, provide code snippets for both producing and consuming the event, using the relevant technology." {}
    rule "Use labeled code blocks (e.g., title=\"Produce event in C#\")." {}
  }
  section "Ownership" {
    rule "The owners field must list all responsible teams or individuals. In most cases this will be euvic or enterprise" {}
  }
  section "Channel Parameters" {
    rule "If a channel uses parameters (e.g., {env}), specify them in the channels frontmatter section. We use: local, dev, sit, and prod" {}
  }
  section "Services need to be linked to domains" {
    rule "Every new service must to be added to the services array in the index.mdx of the domain that it is nested inside of." {
        example:
        services:
        - id: magento
        - id: magento-integration-adapter
    }
  }
  section "Changelog Requirement" {
    rule "Every new Domain or Service must include a changelog.mdx file with the following YAML frontmatter:" {
      example: """
      ---
      createdAt: YYYY-MM-DD
      ---
      """
      enforcement: "The changelog.mdx file must be present in the domain or service directory and contain a createdAt field in the frontmatter with the creation date."
    }
  }
  section "Subdomain badges {
    rule "Every subdomain should get a blue badge indicating that it is a subdomain" {
        example:
        badges:
        - content: Subdomain
            backgroundColor: blue
            textColor: blue
            icon: RectangleGroupIcon
    }
  }
  section "Folder Structure" {
    rule "Each service must have subfolders for queries, events, and specifications." {}
    rule "Each event, command, query, domain, and service must have its own subfolder containing at least an index.mdx. Events and commands should have a schema.json file. Queries shoudl ahve an openapi.yml file." {}
  }
  section "Channel Schema" {
    rule "All channel definition files must include the following fields in the YAML frontmatter: id, name, version, summary, owners, address, protocols, parameters, and badges. The parameters field must specify environment options (e.g., local, dev, sit, prod) and their descriptions. The badges field should indicate the resource is a channel." {
      rationale: "Consistent channel schemas ensure discoverability, clarity, and integration with EventCatalog tooling."
      enforcement: "Channel files missing any of these fields should be flagged during validation or CI."
      example: """
      ---
      id: magento.{env}.rest.queries
      name: Magento Queries REST Channel
      version: 1.0.0
      summary: |
        Channel for all REST queries to Magento
      owners:
        - enterprise
      address: http://localhost:9999
      protocols:
        - http
      parameters:
        env:
          enum:
            - local
            - dev
          description: 'Environment to use'
      badges:
        - content: Channel
          backgroundColor: blue
          textColor: blue
          icon: RectangleGroupIcon
      ---
      """
    }
  }
  section "Process Management" {
    rule "Check for running EventCatalog instance before starting a new one" {
      rationale: "Prevent duplicate EventCatalog instances from running simultaneously, which can cause port conflicts and resource waste."
      enforcement: """
      Before starting EventCatalog in development mode:
      1. Check if port 7500 is in use (default EventCatalog port)
      2. Look for existing 'eventcatalog dev' processes
      3. If an instance is running, terminate it before starting a new one
      """
      example: """
      # Shell script to check and manage EventCatalog instances
      if lsof -i :7500; then
        echo "EventCatalog is already running on port 7500"
        read -p "Do you want to terminate the existing instance? (y/n) " choice
        if [ "$choice" = "y" ]; then
          pkill -f "eventcatalog dev"
          echo "Starting new EventCatalog instance..."
          npm run dev
        else
          echo "Aborting to prevent duplicate instances"
          exit 1
        fi
      else
        echo "Starting EventCatalog..."
        npm run dev
      fi
      """
      fix: """
      1. Use 'lsof -i :7500' to check for running instances
      2. Use 'pkill -f "eventcatalog dev"' to terminate existing instances
      3. Wait a few seconds before starting a new instance
      4. Consider adding a pre-dev script in package.json to automate this check
      """
    }
  }
}
