---
description: 
globs: 
alwaysApply: false
---
# Magento API Documentation Validator

Rule for validating Magento service documentation against official Magento 2 Open Source API documentation.

<rule>
name: magento_api_docs_validator
description: Validates Magento service documentation against official Magento 2 Open Source API documentation using Context7
filters:
  - type: file_change
    pattern: "eventcatalog/domains/adapter/services/magento/**/*.{mdx,json}"

actions:
  - type: execute
    command: |
      # Extract the relevant API endpoint or feature from the changed file
      FILE_NAME=$(basename "$FILE")
      FEATURE_NAME=${FILE_NAME%.*}
      
      # Use Context7 to fetch official Magento 2 documentation
      context7 resolve-library-id "magento2" > /tmp/magento_lib_id
      MAGENTO_LIB_ID=$(cat /tmp/magento_lib_id | grep -o '/magento/magento2')
      
      # Get relevant documentation for the feature
      context7 get-library-docs "$MAGENTO_LIB_ID" --topic "$FEATURE_NAME" > /tmp/magento_docs
      
      # Compare documentation
      if [ -f "$FILE" ]; then
        # For MDX files, extract the documentation content
        if [[ "$FILE" == *.mdx ]]; then
          DOC_CONTENT=$(sed -n '/---/,/---/!p' "$FILE")
        # For JSON files, pretty print the schema
        elif [[ "$FILE" == *.json ]]; then
          DOC_CONTENT=$(jq '.' "$FILE")
        fi
        
        # Check for discrepancies
        DIFF_OUTPUT=$(diff <(echo "$DOC_CONTENT") /tmp/magento_docs)
        
        if [ ! -z "$DIFF_OUTPUT" ]; then
          echo "Documentation discrepancies found for $FEATURE_NAME:"
          echo "$DIFF_OUTPUT"
          
          # Create a report file
          REPORT_FILE="reports/magento_docs_validation_${FEATURE_NAME}.txt"
          mkdir -p reports
          {
            echo "Documentation Validation Report for $FEATURE_NAME"
            echo "================================================"
            echo "File: $FILE"
            echo "Timestamp: $(date)"
            echo ""
            echo "Discrepancies Found:"
            echo "$DIFF_OUTPUT"
            echo ""
            echo "Recommendations:"
            echo "1. Review the differences above"
            echo "2. Update the documentation to match official Magento 2 API specs"
            echo "3. Add any custom implementation details specific to your integration"
          } > "$REPORT_FILE"
          
          # Notify about the report
          echo "Detailed validation report created at: $REPORT_FILE"
        fi
      fi

  - type: suggest
    message: |
      Documentation validation completed. Please ensure:
      
      1. All API endpoints are accurately documented
      2. Request/response schemas match Magento 2 specifications
      3. Custom implementation details are clearly marked
      4. Authentication requirements are specified
      5. Error handling scenarios are documented
      
      Check the validation report for detailed findings.

  - type: reject
    conditions:
      - pattern: "missing_required_fields"
        message: "Required fields are missing in the documentation"
      - pattern: "schema_mismatch"
        message: "Schema does not match Magento 2 API specification"
      - pattern: "invalid_endpoint"
        message: "Invalid or deprecated API endpoint referenced"

examples:
  - input: |
      # Customer creation endpoint documentation
      FILE="eventcatalog/domains/adapter/services/magento/commands/customer-create/index.mdx"
      FEATURE_NAME="customer-create"
    output: |
      Documentation validation report for customer-create endpoint,
      comparing against official Magento 2 API specifications

  - input: |
      # Product schema validation
      FILE="eventcatalog/domains/adapter/services/magento/queries/product-details/schema.json"
      FEATURE_NAME="product-details"
    output: |
      Schema validation report for product-details,
      checking compatibility with Magento 2 API requirements

metadata:
  priority: high
  version: 1.0
  tags:
    - documentation
    - validation
    - magento
    - api
</rule>
