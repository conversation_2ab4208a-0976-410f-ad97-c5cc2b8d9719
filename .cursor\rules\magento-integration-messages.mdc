---
description: 
globs: *.mdx
alwaysApply: false
---
# Magento Integration Messages Documentation Structure

Rule for standardizing the structure and content of Magento integration message documentation.

<rule>
name: magento_integration_messages_structure
description: Standards for documenting the structure, flow, and processing of messages between Magento and integration layer
filters:
  - type: file_path
    pattern: "eventcatalog/domains/adapter/services/(magento|magento-integration-adapter)/queries/.*\\.(mdx|yml)$"
  - type: content
    pattern: "(?s)---.*?---"  # Match frontmatter

actions:
  - type: suggest
    message: |
      When documenting Magento integration messages:

      1. Payload Examples:
         - Do NOT include JSON payload examples in the .mdx documentation files
         - All payload examples MUST be in the openapi.yml file under the appropriate endpoint's requestBody.content.application/json.example
         - The .mdx file should reference the OpenAPI specification for payload details

      2. Documentation Structure (.mdx):
         ```markdown
         ## Overview
         Clear explanation of:
         - When/how the message is triggered
         - Purpose of the message

         ## Architecture diagram
         <NodeGraph />

         ## Query Details
         ### Trigger Point
         - When is it triggered
         - Any prerequisites
         - Part of which workflow

         ### Data Structure
         - Reference to corresponding Magento API endpoint
         - Link to Magento API documentation
         - List of key data components
         (For payload details, see openapi.yml)

         ### Critical Fields
         - List and describe critical fields
         - No JSON examples here

         ## Integration Guidelines
         ### Processing Requirements
         ### Error Handling

         ## Notes
         Technical considerations and important details
         ```

      3. OpenAPI Structure (openapi.yml):
         ```yaml
         paths:
           /endpoint:
             post:
               requestBody:
                 content:
                   application/json:
                     schema:
                       $ref: '#/components/schemas/magento-schema-name'
                     example:
                       # Full JSON example here
         ```

  - type: reject
    conditions:
      - pattern: "```json.*?```"
        message: "JSON payload examples should be in openapi.yml, not in .mdx documentation"
      
      - pattern: "## Response Data"
        message: "Response Data section with examples should not be in .mdx documentation"

      - pattern: "## Payload example"
        message: "Payload examples should be in openapi.yml, not in .mdx documentation"

examples:
  - input: |
      # Good: Reference to OpenAPI
      ### Data Structure
      Uses response format from Magento 2 API endpoint:
      `GET /V1/orders/{orderId}`
      [Magento API Documentation](https://devdocs.magento.com/swagger)

      See openapi.yml for complete payload structure and examples.

  - input: |
      # Bad: JSON example in .mdx
      ```json
      {
        "id": 123,
        "status": "complete"
      }
      ```

metadata:
  priority: high
  version: 1.1
  tags:
    - magento
    - integration
    - documentation
  related_rules:
    - eventcatalog-magento-query-payload
</rule>

This rule focuses on the structure and flow documentation of Magento integration messages, with payload examples strictly confined to OpenAPI specifications.
