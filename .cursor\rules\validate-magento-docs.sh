#!/bin/bash

# Validation script for Magento service documentation
# This script checks for compliance with EventCatalog and Magento-specific standards

echo "Validating Magento service documentation..."

# Base directory for Magento service documentation
BASE_DIR="eventcatalog/domains/adapter/services/magento"

# Check required sections in MDX files
check_mdx_file() {
    local file=$1
    local errors=0
    
    # Check frontmatter
    if ! grep -q "^---$" "$file"; then
        echo "ERROR: $file - Missing frontmatter"
        errors=$((errors + 1))
    fi
    
    # Check required fields
    for field in "id:" "name:" "version:" "summary:" "producers:" "owners:" "badges:" "channels:"; do
        if ! grep -q "^$field" "$file"; then
            echo "ERROR: $file - Missing required field: $field"
            errors=$((errors + 1))
        fi
    done
    
    # Check required sections
    for section in "## Overview" "## Architecture Diagram" "## Schema" "## Implementation Details" "## Error Handling"; do
        if ! grep -q "^$section" "$file"; then
            echo "ERROR: $file - Missing required section: $section"
            errors=$((errors + 1))
        fi
    done
    
    # Check for NodeGraph component
    if ! grep -q "<NodeGraph />" "$file"; then
        echo "ERROR: $file - Missing NodeGraph component"
        errors=$((errors + 1))
    fi
    
    # Check for SchemaViewer component
    if ! grep -q "<SchemaViewer file=\"schema.json\" />" "$file"; then
        echo "ERROR: $file - Missing SchemaViewer component"
        errors=$((errors + 1))
    fi
    
    return $errors
}

# Check schema.json files
check_schema_file() {
    local file=$1
    local errors=0
    
    # Check if file exists
    if [ ! -f "$file" ]; then
        echo "ERROR: Missing schema file: $file"
        return 1
    fi
    
    # Validate JSON syntax
    if ! jq empty "$file" 2>/dev/null; then
        echo "ERROR: $file - Invalid JSON syntax"
        errors=$((errors + 1))
    fi
    
    # Check required schema fields
    if ! jq -e '.["$schema"]' "$file" >/dev/null 2>&1; then
        echo "ERROR: $file - Missing \$schema field"
        errors=$((errors + 1))
    fi
    
    if ! jq -e '.type' "$file" >/dev/null 2>&1; then
        echo "ERROR: $file - Missing type field"
        errors=$((errors + 1))
    fi
    
    if ! jq -e '.properties' "$file" >/dev/null 2>&1; then
        echo "ERROR: $file - Missing properties field"
        errors=$((errors + 1))
    fi
    
    return $errors
}

# Check resource type placement
check_resource_placement() {
    local dir=$1
    local type=$2
    local errors=0
    
    case $type in
        "commands")
            if grep -r "GET" "$dir"; then
                echo "ERROR: $dir - GET operation in commands directory"
                errors=$((errors + 1))
            fi
            ;;
        "queries")
            if grep -r "POST\|PUT\|PATCH\|DELETE" "$dir"; then
                echo "ERROR: $dir - Modification operation in queries directory"
                errors=$((errors + 1))
            fi
            ;;
    esac
    
    return $errors
}

# Main validation loop
total_errors=0

# Validate commands
for cmd_dir in "$BASE_DIR/commands/"*; do
    if [ -d "$cmd_dir" ]; then
        check_mdx_file "$cmd_dir/index.mdx"
        total_errors=$((total_errors + $?))
        
        check_schema_file "$cmd_dir/schema.json"
        total_errors=$((total_errors + $?))
        
        check_resource_placement "$cmd_dir" "commands"
        total_errors=$((total_errors + $?))
    fi
done

# Validate queries
for query_dir in "$BASE_DIR/queries/"*; do
    if [ -d "$query_dir" ]; then
        check_mdx_file "$query_dir/index.mdx"
        total_errors=$((total_errors + $?))
        
        check_schema_file "$query_dir/schema.json"
        total_errors=$((total_errors + $?))
        
        check_resource_placement "$query_dir" "queries"
        total_errors=$((total_errors + $?))
    fi
done

if [ $total_errors -eq 0 ]; then
    echo "✅ All documentation meets the required standards"
    exit 0
else
    echo "❌ Found $total_errors validation errors"
    exit 1
fi 