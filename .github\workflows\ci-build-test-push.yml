# .github/workflows/ci-build.yml
name: CI → Build, Test & Push
permissions:
  contents: read

on:
  push:
    branches:
      - main
    paths:
      - 'src/Axon.Adapter.Api/**'
      - 'src/Axon.Adapter.Api.Tests/**'
      - 'src/Axon.Domain.Service/**'
      - 'src/Axon.Domain.Service.Tests/**'
      - 'src/Axon.Core/**'
      - 'src/Axon.Core.MassTransit/**'
      - 'src/Axon.Contracts/**'
  workflow_dispatch:
    inputs:
      service:
        description: 'Which service to build'
        required: false
        default: 'all'
        type: choice
        options:
          - all
          - Axon.Adapter.Api
          - Axon.Domain.Service

env:
  AWS_ACCOUNT_ID: ${{ vars.AWS_ACCOUNT_ID }}
  AWS_REGION: ${{ vars.AWS_REGION }}

jobs:
  detect-changes:
    runs-on: ubuntu-latest
    outputs:
      Axon_Adapter_Api: ${{ steps.filter.outputs.Axon_Adapter_Api }}
      Axon_Domain_Service: ${{ steps.filter.outputs.Axon_Domain_Service }}
    steps:
      - uses: actions/checkout@v3
      - id: filter
        uses: dorny/paths-filter@v2
        with:
          filters: |
            Axon_Adapter_Api:
              - 'src/Axon.Adapter.Api/**'
              - 'src/Axon.Adapter.Api.Tests/**'
              - 'src/Axon.Core/**'
              - 'src/Axon.Core.MassTransit/**'
              - 'src/Axon.Contracts/**'
            Axon_Domain_Service:
              - 'src/Axon.Domain.Service/**'
              - 'src/Axon.Domain.Service.Tests/**'
              - 'src/Axon.Core/**'
              - 'src/Axon.Core.MassTransit/**'
              - 'src/Axon.Contracts/**'

  build-and-push:
    needs: detect-changes
    runs-on: ubuntu-latest
    strategy:
      matrix:
        service:
          - Axon.Adapter.Api
          - Axon.Domain.Service
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Check if service changed
        id: check
        run: |
          # For workflow_dispatch, check if specific service was selected
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            if [[ "${{ github.event.inputs.service }}" == "all" || "${{ github.event.inputs.service }}" == "${{ matrix.service }}" ]]; then
              echo "should_build=true" >> $GITHUB_OUTPUT
            else
              echo "should_build=false" >> $GITHUB_OUTPUT
            fi
          # For push events, check path changes
          elif [[ "${{ matrix.service }}" == "Axon.Adapter.Api" && "${{ needs.detect-changes.outputs.Axon_Adapter_Api }}" == "true" ]]; then
            echo "should_build=true" >> $GITHUB_OUTPUT
          elif [[ "${{ matrix.service }}" == "Axon.Domain.Service" && "${{ needs.detect-changes.outputs.Axon_Domain_Service }}" == "true" ]]; then
            echo "should_build=true" >> $GITHUB_OUTPUT
          else
            echo "should_build=false" >> $GITHUB_OUTPUT
          fi

      - name: Setup .NET 9
        if: steps.check.outputs.should_build == 'true'
        uses: actions/setup-dotnet@v3
        with:
          dotnet-version: '9.0.x'

      - name: Restore and Build
        if: steps.check.outputs.should_build == 'true'
        working-directory: src/${{ matrix.service }}
        run: |
          dotnet restore
          dotnet build --configuration Release --no-restore
      - name: Test Service
        if: steps.check.outputs.should_build == 'true'
        run: |
          if [[ "${{ matrix.service }}" == "Axon.Adapter.Api" ]]; then
            dotnet test src/Axon.Adapter.Api.Tests --configuration Release --verbosity normal
          elif [[ "${{ matrix.service }}" == "Axon.Domain.Service" ]]; then
            dotnet test src/Axon.Domain.Service.Tests --configuration Release --verbosity normal
          fi

      - name: Configure AWS creds
        if: steps.check.outputs.should_build == 'true'
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Login to ECR
        if: steps.check.outputs.should_build == 'true'
        uses: aws-actions/amazon-ecr-login@v2

      - name: Set up Docker Buildx
        if: steps.check.outputs.should_build == 'true'
        uses: docker/setup-buildx-action@v3

      - name: Build & push Docker image
        if: steps.check.outputs.should_build == 'true'
        id: build
        working-directory: ${{ github.workspace }}
        run: |
          IMAGE_NAME=$(echo ${{ matrix.service }} | tr '[:upper:]' '[:lower:]' | tr '.' '-')
          URI=${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/enterprise-digitalization/evolution/${IMAGE_NAME}
          TAG=${GITHUB_SHA}
          docker buildx build --platform linux/arm64 -t $URI:$TAG -t $URI:latest -f src/${{ matrix.service }}/Dockerfile . --push
          echo "image=$URI:$TAG" >> $GITHUB_OUTPUT
          echo "tag=$TAG" >> $GITHUB_OUTPUT

  deploy-adapter-api:
    needs: [detect-changes, build-and-push]
    if: |
      github.ref == 'refs/heads/main' && 
      (
        (github.event_name == 'push' && needs.detect-changes.outputs.Axon_Adapter_Api == 'true') ||
        (github.event_name == 'workflow_dispatch' && (github.event.inputs.service == 'Axon.Adapter.Api' || github.event.inputs.service == 'all'))
      )
    uses: ./.github/workflows/deploy-to-ecs.yml
    with:
      service: Axon.Adapter.Api
      image_tag: ${{ github.sha }}
      environment: development
    secrets: inherit
