name: CI

on:
  push:
    branches: [ main ]
    paths:
      - 'src/**'
      - '.github/workflows/ci.yml'
  workflow_dispatch:

env:
  AWS_ACCOUNT_ID: ${{ vars.AWS_ACCOUNT_ID }}
  AWS_REGION: ${{ vars.AWS_REGION }}
  DOTNET_VERSION: '9.0.x'

jobs:
  test:
    name: Run Tests
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: ${{ env.DOTNET_VERSION }}
    
    - name: Cache NuGet packages
      uses: actions/cache@v4
      with:
        path: ~/.nuget/packages
        key: ${{ runner.os }}-nuget-${{ hashFiles('**/*.csproj') }}
        restore-keys: |
          ${{ runner.os }}-nuget-
    
    - name: Restore dependencies
      run: dotnet restore
    
    - name: Build
      run: dotnet build --no-restore --configuration Release
    
    - name: Test
      run: dotnet test --no-build --configuration Release --verbosity normal --logger "trx;LogFileName=test-results.trx" --collect:"XPlat Code Coverage"
      env:
        Persistence__UseInMemoryDatabase: "true"
    
    - name: Test Report
      uses: dorny/test-reporter@v1
      if: success() || failure()
      with:
        name: .NET Test Report
        path: '**/*.trx'
        reporter: dotnet-trx

  build-and-push:
    name: Build and Push Docker Images
    needs: test
    runs-on: ubuntu-latest
    if: success()
    permissions:
      contents: read
    strategy:
      matrix:
        service:
          - name: Axon.Adapter.Api
            dockerfile: src/Axon.Adapter.Api/Dockerfile
            ecr_repository: evolution-axon-dev-adapter-api
          - name: Axon.Domain.Service
            dockerfile: src/Axon.Domain.Service/Dockerfile
            ecr_repository: evolution-axon-dev-domain-service
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_REGION }}
    
    - name: Login to Amazon ECR
      uses: aws-actions/amazon-ecr-login@v2
    
    - name: Generate Docker metadata
      id: meta
      run: |
        ECR_REGISTRY=${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com
        ECR_REPOSITORY=${{ matrix.service.ecr_repository }}
        IMAGE_URI=${ECR_REGISTRY}/${ECR_REPOSITORY}
        
        echo "image_uri=${IMAGE_URI}" >> $GITHUB_OUTPUT
        echo "ecr_registry=${ECR_REGISTRY}" >> $GITHUB_OUTPUT
        echo "ecr_repository=${ECR_REPOSITORY}" >> $GITHUB_OUTPUT
    
    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ${{ matrix.service.dockerfile }}
        platforms: linux/arm64
        push: true
        tags: |
          ${{ steps.meta.outputs.image_uri }}:${{ github.sha }}
          ${{ steps.meta.outputs.image_uri }}:latest
          ${{ steps.meta.outputs.image_uri }}:${{ github.ref_name }}
          ${{ steps.meta.outputs.image_uri }}:cache
        cache-from: |
          type=gha,scope=${{ matrix.service.name }}-${{ github.ref_name }}
          type=gha,scope=${{ matrix.service.name }}-main
          type=registry,ref=${{ steps.meta.outputs.image_uri }}:cache
        cache-to: |
          type=gha,mode=max,scope=${{ matrix.service.name }}-${{ github.ref_name }}
          type=registry,ref=${{ steps.meta.outputs.image_uri }}:cache,mode=max
        build-args: |
          BUILD_VERSION=${{ github.sha }}
          BUILD_DATE=${{ github.event.head_commit.timestamp }}
    
    - name: Output image details
      run: |
        echo "### Docker Image Published 🚀" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "**Service:** ${{ matrix.service.name }}" >> $GITHUB_STEP_SUMMARY
        echo "**Image:** \`${{ steps.meta.outputs.image_uri }}:${{ github.sha }}\`" >> $GITHUB_STEP_SUMMARY
        echo "**Tags:**" >> $GITHUB_STEP_SUMMARY
        echo "- \`${{ github.sha }}\`" >> $GITHUB_STEP_SUMMARY
        echo "- \`latest\`" >> $GITHUB_STEP_SUMMARY
        echo "- \`${{ github.ref_name }}\`" >> $GITHUB_STEP_SUMMARY