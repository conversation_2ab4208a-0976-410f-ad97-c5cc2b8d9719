name: Deploy to ECS

on:
  workflow_call:
    inputs:
      service:
        required: true
        type: string
        description: 'Service to deploy (Axon.Adapter.Api or Axon.Domain.Service)'
      image_tag:
        required: true
        type: string
        description: 'Docker image tag (commit SHA)'
      environment:
        required: true
        type: string
        description: 'Environment to deploy to'
        default: 'development'
  workflow_dispatch:
    inputs:
      service:
        description: 'Which service to deploy'
        required: true
        type: choice
        options:
          - Axon.Adapter.Api
          - Axon.Domain.Service
      image_tag:
        description: 'Docker image tag (commit SHA)'
        required: true
        type: string
      environment:
        description: 'Environment to deploy to'
        required: true
        type: choice
        options:
          - development
          - staging
          - production
        default: 'development'

env:
  AWS_ACCOUNT_ID: ${{ vars.AWS_ACCOUNT_ID }}
  AWS_REGION: ${{ vars.AWS_REGION }}

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Set deployment variables
        id: vars
        run: |
          SERVICE_NAME=$(echo ${{ inputs.service }} | tr '[:upper:]' '[:lower:]' | tr '.' '-')
          echo "service_name=${SERVICE_NAME}" >> $GITHUB_OUTPUT
          echo "cluster_name=temp-evolution-foundation-dev-fargate" >> $GITHUB_OUTPUT
          echo "task_family=temp-evolution-dev-${SERVICE_NAME}" >> $GITHUB_OUTPUT
          echo "ecs_service=temp-evolution-dev-${SERVICE_NAME}-service" >> $GITHUB_OUTPUT
          IMAGE_URI="${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/enterprise-digitalization/evolution/${SERVICE_NAME}:${{ inputs.image_tag }}"
          echo "image_uri=${IMAGE_URI}" >> $GITHUB_OUTPUT

      - name: Download current task definition
        id: download-task-def
        run: |
          aws ecs describe-task-definition \
            --task-definition ${{ steps.vars.outputs.task_family }} \
            --query taskDefinition > task-definition.json
          
          # Get the actual container name from the task definition
          CONTAINER_NAME=$(jq -r '.containerDefinitions[0].name' task-definition.json)
          echo "Found container name: ${CONTAINER_NAME}"
          echo "container_name=${CONTAINER_NAME}" >> $GITHUB_OUTPUT

          # Remove fields that shouldn't be in the new revision
          jq 'del(.taskDefinitionArn, .revision, .status, .requiresAttributes, .compatibilities, .registeredAt, .registeredBy, .enableFaultInjection)' \
            task-definition.json > task-definition-clean.json

      - name: Update task definition with new image
        id: task-def
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: task-definition-clean.json
          container-name: ${{ steps.download-task-def.outputs.container_name }}
          image: ${{ steps.vars.outputs.image_uri }}

      - name: Deploy Amazon ECS task definition
        uses: aws-actions/amazon-ecs-deploy-task-definition@v2
        with:
          task-definition: ${{ steps.task-def.outputs.task-definition }}
          service: ${{ steps.vars.outputs.ecs_service }}
          cluster: ${{ steps.vars.outputs.cluster_name }}
          wait-for-service-stability: true

      - name: Verify deployment
        run: |
          echo "Deployment completed for ${{ inputs.service }} with tag ${{ inputs.image_tag }}"
          echo "Service: ${{ steps.vars.outputs.ecs_service }}"
          echo "Cluster: ${{ steps.vars.outputs.cluster_name }}"
          echo "Image: ${{ steps.vars.outputs.image_uri }}"