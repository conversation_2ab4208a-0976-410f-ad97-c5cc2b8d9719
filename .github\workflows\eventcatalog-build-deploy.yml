name: EventCatalog Build and Deploy

on:
  push:
    branches: [ main ]
    paths:
      - 'eventcatalog/**'
      - '.github/workflows/eventcatalog-build-deploy.yml'
  workflow_dispatch:

env:
  AWS_ACCOUNT_ID: ${{ vars.AWS_ACCOUNT_ID }}
  AWS_REGION: ${{ vars.AWS_REGION }}
  ECS_CLUSTER: evolution-core-dev-ecs
  ECS_SERVICE: eventcatalog

jobs:
  build-and-push:
    name: Build and Push EventCatalog
    runs-on: ubuntu-latest
    permissions:
      contents: read
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_REGION }}
    
    - name: Login to Amazon ECR
      uses: aws-actions/amazon-ecr-login@v2
    
    - name: Generate Docker metadata
      id: meta
      run: |
        ECR_REGISTRY=${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com
        ECR_REPOSITORY=evolution-eventcatalog-dev-eventcatalog
        IMAGE_URI=${ECR_REGISTRY}/${ECR_REPOSITORY}
        
        echo "image_uri=${IMAGE_URI}" >> $GITHUB_OUTPUT
        echo "ecr_registry=${ECR_REGISTRY}" >> $GITHUB_OUTPUT
        echo "ecr_repository=${ECR_REPOSITORY}" >> $GITHUB_OUTPUT
    
    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: ./eventcatalog
        file: ./eventcatalog/Dockerfile.server
        platforms: linux/amd64
        push: true
        tags: |
          ${{ steps.meta.outputs.image_uri }}:${{ github.sha }}
          ${{ steps.meta.outputs.image_uri }}:latest
          ${{ steps.meta.outputs.image_uri }}:${{ github.ref_name }}
        cache-from: |
          type=gha,scope=eventcatalog-${{ github.ref_name }}
          type=gha,scope=eventcatalog-main
        cache-to: |
          type=gha,mode=max,scope=eventcatalog-${{ github.ref_name }}
        build-args: |
          BUILD_VERSION=${{ github.sha }}
          BUILD_DATE=${{ github.event.head_commit.timestamp }}
    
    - name: Output image details
      run: |
        echo "### EventCatalog Docker Image Published 🚀" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "**Service:** EventCatalog" >> $GITHUB_STEP_SUMMARY
        echo "**Image:** \`${{ steps.meta.outputs.image_uri }}:${{ github.sha }}\`" >> $GITHUB_STEP_SUMMARY
        echo "**Tags:**" >> $GITHUB_STEP_SUMMARY
        echo "- \`${{ github.sha }}\`" >> $GITHUB_STEP_SUMMARY
        echo "- \`latest\`" >> $GITHUB_STEP_SUMMARY
        echo "- \`${{ github.ref_name }}\`" >> $GITHUB_STEP_SUMMARY

  deploy:
    name: Deploy to ECS
    needs: build-and-push
    runs-on: ubuntu-latest
    if: success()
    environment: development
    permissions:
      contents: read
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_REGION }}
    
    - name: Set deployment variables
      id: vars
      run: |
        echo "cluster_name=evolution-core-dev-ecs" >> $GITHUB_OUTPUT
        echo "task_family=evolution-eventcatalog-dev-eventcatalog" >> $GITHUB_OUTPUT
        echo "ecs_service=eventcatalog" >> $GITHUB_OUTPUT
        IMAGE_URI="${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/evolution-eventcatalog-dev-eventcatalog:${{ github.sha }}"
        echo "image_uri=${IMAGE_URI}" >> $GITHUB_OUTPUT

    - name: Download current task definition
      id: download-task-def
      run: |
        aws ecs describe-task-definition \
          --task-definition ${{ steps.vars.outputs.task_family }} \
          --query taskDefinition > task-definition.json
        
        # Get the actual container name from the task definition (should be oauth2-proxy for the ALB target)
        CONTAINER_NAME=$(jq -r '.containerDefinitions[] | select(.portMappings[].containerPort == 4180) | .name' task-definition.json)
        echo "Found container name for port 4180: ${CONTAINER_NAME}"
        echo "container_name=${CONTAINER_NAME}" >> $GITHUB_OUTPUT

        # Remove fields that shouldn't be in the new revision
        jq 'del(.taskDefinitionArn, .revision, .status, .requiresAttributes, .compatibilities, .registeredAt, .registeredBy, .enableFaultInjection)' \
          task-definition.json > task-definition-clean.json

    - name: Update task definition with new image
      id: task-def
      run: |
        # Update the eventcatalog container image (not oauth2-proxy)
        jq --arg IMAGE "${{ steps.vars.outputs.image_uri }}" \
          '(.containerDefinitions[] | select(.name == "eventcatalog") | .image) = $IMAGE' \
          task-definition-clean.json > task-definition-updated.json
        echo "task-definition=task-definition-updated.json" >> $GITHUB_OUTPUT

    - name: Deploy Amazon ECS task definition
      uses: aws-actions/amazon-ecs-deploy-task-definition@v2
      with:
        task-definition: ${{ steps.task-def.outputs.task-definition }}
        service: ${{ steps.vars.outputs.ecs_service }}
        cluster: ${{ steps.vars.outputs.cluster_name }}
        wait-for-service-stability: true

    - name: Verify deployment
      run: |
        echo "### EventCatalog Deployed Successfully ✅" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "**Service:** EventCatalog" >> $GITHUB_STEP_SUMMARY
        echo "**Cluster:** \`${{ steps.vars.outputs.cluster_name }}\`" >> $GITHUB_STEP_SUMMARY
        echo "**Task Family:** \`${{ steps.vars.outputs.task_family }}\`" >> $GITHUB_STEP_SUMMARY
        echo "**ECS Service:** \`${{ steps.vars.outputs.ecs_service }}\`" >> $GITHUB_STEP_SUMMARY
        echo "**Image:** \`${{ steps.vars.outputs.image_uri }}\`" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "The EventCatalog service has been updated with commit ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY