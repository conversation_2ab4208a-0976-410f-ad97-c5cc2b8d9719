name: Unit Tests

on:
  pull_request:
    types: [opened, synchronize, reopened]

jobs:
  unit-tests:
    name: Unit Tests
    runs-on: ubuntu-latest
    timeout-minutes: 15

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup .NET 9
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: '9.0.x'

    - name: Cache NuGet packages
      uses: actions/cache@v4
      with:
        path: ~/.nuget/packages
        key: ${{ runner.os }}-nuget-${{ hashFiles('**/*.csproj') }}
        restore-keys: |
          ${{ runner.os }}-nuget-

    - name: Restore dependencies
      run: dotnet restore

    - name: Build solution
      run: dotnet build --no-restore --configuration Release

    - name: Run unit tests
      run: |
        dotnet test --no-build --configuration Release \
          --logger "trx;LogFileName=test-results.trx" \
          --logger "console;verbosity=detailed" \
          --results-directory ./TestResults \
          --collect:"XPlat Code Coverage" \
          -- DataCollectionRunSettings.DataCollectors.DataCollector.Configuration.Format=cobertura

    - name: Generate test report
      uses: dorny/test-reporter@v1
      if: always()
      with:
        name: Unit Test Results
        path: 'TestResults/*.trx'
        reporter: dotnet-trx
        fail-on-error: true

    - name: Upload test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: test-results
        path: TestResults/
        retention-days: 7

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v4
      if: always()
      with:
        token: ${{ secrets.CODECOV_TOKEN }}
        directory: ./TestResults
        flags: unittests
        name: codecov-umbrella
        fail_ci_if_error: false