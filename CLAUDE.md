# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Cursor Rules

This repository contains detailed coding conventions and rules in the `.cursor/rules/` directory. Always refer to these rules when:
- Writing new code
- Refactoring existing code
- Making architectural decisions
- Creating documentation

Key rule categories include:
- `dotnet/` - .NET specific conventions (DDD, SOLID, async patterns, etc.)
- `mass-transit/` - MassTransit consumer and publisher best practices
- `git/` - Git commit conventions
- EventCatalog documentation standards

## Build and Test Commands

```bash
# Build entire solution
dotnet build

# Run all tests
dotnet test

# Run specific test project
dotnet test src/Axon.Adapter.Api.Tests/Axon.Adapter.Api.Tests.csproj

# Run with hot reload (preferred for development)
docker-compose up
```

## Architecture Overview

This is a .NET 9.0 distributed architecture implementing Domain-Driven Design (DDD) with CQRS and event-driven patterns.

### Key Projects
- **Axon.Core**: Shared infrastructure (middleware, logging, hosting)
- **Axon.Core.MassTransit**: Message bus integration with RabbitMQ
- **Axon.Contracts**: Shared contracts (Commands, Events, Queries)
- **Axon.Adapter.Api**: Integration adapters for external systems (Magento, SAP)
- **Axon.Domain.Service**: Core business logic and domain models
- **Test Projects**: xUnit tests with Moq for mocking

### Architectural Patterns
1. **CQRS**: Commands, Queries, and Events are separated in Axon.Contracts
2. **Message-Driven**: MassTransit with RabbitMQ for async communication
3. **DDD Layers**: 
   - Domain layer must not reference Infrastructure or Application layers
   - Infrastructure can depend on Domain
   - Application orchestrates between Domain and Infrastructure

### Message Flow Example
1. External system sends HTTP request to Adapter API
2. Adapter publishes Command/Query via MassTransit
3. Domain Service consumes message, processes business logic
4. Domain Service publishes Event
5. Adapter API consumes Event and calls external system

## Development Setup

1. Create `.env.local` file with RabbitMQ and API configurations
2. Run `docker-compose up` for full stack with hot reload
3. Services available at:
   - Adapter API: http://localhost:7501
   - Domain Service: http://localhost:7502
   - RabbitMQ Management: http://localhost:15672

## Testing Conventions

- Use xUnit for all tests
- Follow naming pattern: `{MethodName}_Should{ExpectedBehavior}_When{Condition}`
- Mock external dependencies with Moq
- Test handlers, consumers, and domain logic separately

## Important Conventions (from Cursor Rules)

### Domain-Driven Design
- **Strict Layer Boundaries**: Domain cannot reference Infrastructure/Application
- **Subdomain Structure**: Each subdomain has Domain/Application/Infrastructure/Consumers folders
- **Aggregate Roots**: No public setters; state changes only through methods
- **External Services**: Convert API requests to Commands for domain validation

### .NET 9 Conventions
- **Always use DateTimeOffset** instead of DateTime for timezone safety
- **Async/Await**: All async methods must accept CancellationToken; never use .Result or .Wait()
- **Records**: Prefer C# records for value objects and immutable entities
- **Thin Controllers**: 
  - Only accept requests, delegate to handlers, return responses
  - Do NOT perform ModelState validation checks (handled by framework)
  - Do NOT catch and rethrow exceptions (let them bubble up for middleware)
  - Keep controller actions as simple as possible
- **Structured Logging**: All services must inject and use `ILogger<T>`
- **API Versioning**: Include version in routes (e.g., `/api/v1/`)

### MassTransit Patterns
- **Consumers**: Use constructor injection, structured logging, let exceptions bubble for retry
- **Publishers**: Log before/after publishing, include IdempotencyKey when applicable
- **No External Retry**: Don't use Polly; let MassTransit handle retries

### Git Commits
- Format: `<type>(<scope>): <description>` (e.g., `feat(order): add validation`)
- Types: feat, fix, docs, style, refactor, perf, test, chore
- Commits shoudl be made after each working change
- The dotnet solution should build before any commit is made
- Unit tests should be passing before any commit is made
- No whitespace should be introduced when making additions

### EventCatalog Documentation
- **IDs**: Use kebab-case (e.g., `order-management`)
- **Channels**: Events → SNS, Commands → SQS, Queries → HTTP
- **Required**: schema.json for events/commands, openapi.yml for queries

### SOLID Principles
- Avoid "Manager" classes; use specific names
- Depend on abstractions, not implementations
- Keep interfaces focused and minimal