# OWASP DevSecOps Maturity Model (DSOMM) Assessment

> 📊 **Assessment Date**: 2025-06-16  
> 🏢 **Project**: Axon - Distributed .NET Services  
> 🔗 **DSOMM Reference**: [dsomm.owasp.org](https://dsomm.owasp.org)

## Executive Summary

This assessment evaluates the Axon project against the OWASP DevSecOps Maturity Model (DSOMM). The DSOMM framework consists of 4 main dimensions with 18 sub-dimensions that measure DevSecOps maturity across Build and Deployment, Culture and Organization, Implementation, Information Gathering, and Test and Verification. The project demonstrates strong foundations in containerization, CI/CD, and distributed services architecture, with opportunities for improvement in supply chain security, dynamic testing, and security testing automation.

### DSOMM Maturity Level Definitions

**Level 1 - Basic understanding of security practices**
- Initial security awareness with ad-hoc implementation
- Basic security practices are understood but not consistently applied
- Manual processes with reactive security approach
- Individual teams may implement security independently

**Level 2 - Adoption of basic security practices**
- Security practices become more consistent across teams
- Basic automation of security tasks begins
- Security is considered during development but not fully integrated
- Some standardization of security processes

**Level 3 - High adoption of security practices**
- Security is integrated into the development lifecycle
- Automated security testing and monitoring
- Proactive security measures with defined processes
- Security practices are standardized across the organization

**Level 4 - Very high adoption of security practices**
- Advanced security automation and orchestration
- Security is embedded in culture and all processes
- Continuous security improvement based on metrics
- Organization demonstrates security leadership

**Level 5 - Advanced deployment of security practices at scale**
- Security practices are optimized and continuously evolving
- Full automation with self-healing capabilities
- Security innovation and thought leadership
- Practices can be scaled across large, complex environments

### Maturity Overview

| Domain | Current Level | Target Level |
|--------|--------------|-------------|
| Build and Deployment | Level 2 | Level 4 |
| Culture and Organization | Level 1 | Level 3 |
| Implementation | Level 2 | Level 3 |
| Information Gathering | Level 2 | Level 3 |
| Test and Verification | Level 1 | Level 2 |

---

## 📋 Assessment Legend

| Symbol | Status | Description |
|--------|--------|-------------|
| ✅ | **Implemented** | Implementation meets ALS needs and requirements |
| ❌ | **Not Implemented** | Capability or control is not currently in place |
| 🟡 | **Not in Scope** | Item is not applicable or deemed out of scope for this assessment |

## 📦 Build and Deployment

### Build Process

| Level | Requirement | Status | Evidence |
|-------|------------|--------|----------|
| 1 | Defined build process | ✅ | GitHub Actions CI/CD pipeline |
| 2 | Building & testing in virtual environments | ✅ | Docker containerization |
| 2 | Pinning of artifacts | ✅ | .NET SDK 9.0.0 in global.json; NugetPackages are pinned |
| 2 | SBOM of components | ❌ | Not implemented |
| 3 | Signing of code | 🟡 | |
| 4 | Signing of artifacts | 🟡 | |

### Deployment

| Level | Requirement | Status | Evidence |
|-------|------------|--------|----------|
| 1 | Defined deployment process | ✅ | ECS deployment configuration |
| 1 | Inventory of production components | ✅ | Fargate Tasks defined in IaC |
| 2 | Defined decommissioning process | 🟡 | |
| 2 | Environment depending configuration parameters (secrets) | ✅ | AWS Secrets Manager |
| 2 | Evaluation of the trust of used components | ✅ | NuGet package management |
| 2 | Inventory of production artifacts | ✅ | Container image definitions |
| 3 | Handover of confidential parameters | ✅ | Secrets injected at runtime |
| 3 | Inventory of production dependencies | ❌ | No SBOM for production |
| 3 | Rolling update on deployment | ✅ | ECS deployment strategy |
| 4 | Same artifact for environments | ✅ | Docker image reuse |
| 4 | Usage of feature toggles | 🟡 | |
| 5 | Blue/Green Deployment | 🟡 | |

### Patch Management

| Level | Requirement | Status | Evidence |
|-------|------------|--------|----------|
| 1 | A patch policy is defined | 🟡 | |
| 1 | Automated PRs for patches | ✅ | Dependabot enabled |
| 2 | Automated merge of automated PRs | ❌ | Not configured |
| 2 | Nightly build of images (base images) | 🟡 | |
| 2 | Reduction of the attack surface | ✅ | Minimal dotnet runtime OCI image used |
| 2 | Usage of a maximum lifetime for images | 🟡 | |
| 3 | Automated deployment of automated PRs | 🟡 | |
| 4 | Usage of a short maximum lifetime for images | 🟡 | |

---

## 🎯 Culture and Organization

### Design

| Level | Requirement | Status | Evidence |
|-------|------------|--------|----------|
| 1 | Conduction of simple threat modeling on technical level | ❌ | No threat models found |
| 2 | Information security targets are communicated | ✅ | CLAUDE.md and .cursor security guidelines |
| 2 | Conduction of simple threat modeling on business level | ❌ | Not documented |
| 2 | Creation of simple abuse stories |  | |🟡
| 2 | Creation of threat modeling processes and standards | ❌ | Not established |
| 3 | Conduction of advanced threat modeling | ❌ | No threat models found |
| 4 | Creation of advanced abuse stories | 🟡 | |

### Education & Guidance

| Level | Requirement | Status | Evidence |
|-------|------------|--------|----------|
| 1 | Ad-Hoc Security trainings for software developers | 🟡 | |
| 1 | Security consulting on request | ✅ | Weekly cybersecurity meetings/review with open agenda |
| 2 | Each team has a security champion | ✅ | |
| 2 | Regular security training for all | 🟡 | |
| 2 | Regular security training of security champions | 🟡 | |
| 2 | Reward of good communication | 🟡 | |
| 2 | Security code review | 🟡 | |
| 3 | Conduction of build-it, break-it, fix-it contests | 🟡 | |
| 3 | Office Hours | 🟡 | |
| 3 | Security Coaching | ✅ | Weekly cybersecurity meetings/review with open agenda |
| 3 | Security-Lessoned-Learned | 🟡 | |
| 3 | Simple mob hacking | 🟡 | |
| 4 | Aligning security in teams | 🟡 | |
| 4 | Conduction of collaborative team security checks | 🟡 | |
| 4 | Conduction of war games | 🟡 | |
| 4 | Regular security training for externals | 🟡 | |
| 5 | Conduction of collaborative security checks with developers and system administrators | 🟡 | |

### Process

| Level | Requirement | Status | Evidence |
|-------|------------|--------|----------|
| 1 | Definition of simple BCDR practices for critical components | ❌ | No BCDR documentation |
| 2 | Determining the protection requirement | ❌ | Not documented |
| 3 | Approval by reviewing any new version | 🟡 | |
| 3 | Definition of a change management process | 🟡 | |

---

## 💻 Implementation

### Application Hardening

| Level | Requirement | Status | Evidence |
|-------|------------|--------|----------|
| 1 | App. Hardening Level 1 (50%) | ✅ | Basic hardening implemented |
| 1 | Context-aware output encoding | ✅ | Sanitized logging implementation |
| 1 | Parametrization | ✅ | AWS WAF and parameterized queries in MassTransit |
| 2 | App. Hardening Level 1 | ✅ | Full Level 1 hardening |
| 2 | Containers are running as non-root | ✅ | Docker configuration |
| 3 | App. Hardening Level 2 (75%) | ✅ | Partial Level 2 hardening |
| 3 | Secure headers | ✅ | AWS Application Load Balancer and WAF |
| 4 | App. Hardening Level 2 | ❌ | Not fully implemented |
| 5 | App. Hardening Level 3 | ❌ | Not implemented |

### Development & Source Control

| Level | Requirement | Status | Evidence |
|-------|------------|--------|----------|
| 1 | Versioning | ✅ | Git version control |
| 2 | Require a PR before merging | 🟡 | |
| 3 | Block force pushes | ✅ | `main` branch is protected from deletion and force push |
| 3 | Dismiss stale PR approvals | ❌ | Not configured |
| 3 | Require status checks to pass | ✅ | CI/CD pipeline runs |
| 4 | .gitignore | ✅ | Comprehensive .gitignore |
| 5 | Local development linting & style checks performed | ✅ | Rosyln analyzers provide style output in CI build |

### Infrastructure Hardening

| Level | Requirement | Status | Evidence |
|-------|------------|--------|----------|
| 1 | MFA for admins | ✅ | AWS IAM MFA required |
| 1 | Simple access control for systems | ✅ | AWS IAM policies |
| 1 | Usage of edge encryption at transit | ✅ | HTTPS endpoints |
| 2 | Applications are running in virtualized environments | ✅ | Docker containers |
| 2 | Backup | ❌ | No automated backups |
| 2 | Baseline Hardening of the environment | ❌ | Not documented |
| 2 | Isolated networks for virtual environments | ✅ | Docker network isolation |
| 2 | MFA | ✅ | AWS IAM MFA |
| 2 | Usage of an security account | ✅ | Separate AWS accounts |
| 2 | Usage of encryption at rest | ✅ | AWS EBS encryption |
| 2 | Usage of test and production environments | ✅ | Multiple environment configs |
| 2 | Virtual environments are limited | ✅ | Resource limits defined |
| 3 | Filter outgoing traffic | ❌ | Not implemented |
| 3 | Immutable infrastructure | ✅ | Container-based deployments |
| 3 | Infrastructure as Code | ✅ | OpenTofu and Spacelift in IaC repository |
| 3 | Limitation of system events | ❌ | Not configured |
| 3 | Role based authentication and authorization | ✅ | AWS IAM roles |
| 3 | Usage of internal encryption at transit | ✅ | Usage of VPC endpoints and managed AWS services |
| 3 | Usage of security by default for components | ✅ | Security group defaults |
| 3 | WAF baseline | ✅ | AWS WAF configured |
| 4 | Hardening of the Environment | ❌ | Not complete |
| 4 | Production near environments are used by developers | ✅ | Docker compose for local dev; Dev environment |
| 4 | Usage of a chaos monkey | ❌ | Not implemented |
| 4 | WAF medium | ✅ | AWS WAF rules |
| 5 | Microservice-architecture | ✅ | CQRS with MassTransit |
| 5 | WAF Advanced | ❌ | Basic WAF only |

---

## 📊 Information Gathering

### Logging

| Level | Requirement | Status | Evidence |
|-------|------------|--------|----------|
| 1 | Centralized system logging | ✅ | Serilog configuration |
| 2 | Logging of security events | ❌ | Not specifically implemented |
| 2 | Visualized logging | ❌ | No log visualization |
| 3 | Centralized application logging | ✅ | Structured logging with correlation IDs |
| 5 | Correlation of security events | ❌ | Not implemented |
| 5 | PII logging concept | ✅ | Sanitized logging |

### Monitoring

| Level | Requirement | Status | Evidence |
|-------|------------|--------|----------|
| 1 | Simple application metrics | ✅ | Prometheus endpoints |
| 1 | Simple budget metrics | ❌ | No cost tracking |
| 1 | Simple system metrics | ✅ | HTTP request/response metrics |
| 2 | Alerting | ❌ | Not configured |
| 2 | Monitoring of costs | ❌ | No cost monitoring |
| 2 | Visualized metrics | ❌ | No dashboards found |
| 3 | Advanced availability and stability metrics | ❌ | Basic metrics only |
| 3 | Audit of system events | ❌ | No audit trail |
| 3 | Deactivation of unused metrics | ❌ | Not managed |
| 3 | Grouping of metrics | ❌ | Not organized |
| 3 | Targeted alerting | ❌ | No alerts |
| 4 | Advanced app. metrics | ❌ | Basic metrics only |
| 4 | Coverage and control metrics | ❌ | Not tracked |
| 4 | Defense metrics | ❌ | No security metrics |
| 4 | Screens with metric visualization | ❌ | No displays |
| 5 | Metrics are combined with tests | ❌ | Not integrated |

### Test KPI

| Level | Requirement | Status | Evidence |
|-------|------------|--------|----------|
| 2 | Number of vulnerabilities/severity | ❌ | Not tracked |
| 2 | Number of vulnerabilities/severity/layer | ❌ | Not tracked |
| 2 | Patching mean time to resolution via PR | ❌ | Not measured |
| 3 | Fix rate per repo/product | ❌ | Not tracked |
| 3 | Generation of response statistics | ❌ | Not generated |
| 3 | SLA per criticality | ❌ | No SLAs defined |
| 4 | Patching mean time to resolution via production | ❌ | Not measured |

---

## 🔍 Test and Verification

### Application tests

| Level | Requirement | Status | Evidence |
|-------|------------|--------|----------|
| 2 | Security unit tests for important components | ❌ | No security-specific tests |
| 3 | Security integration tests for important components | ❌ | Not implemented |
| 4 | Smoke Test | ❌ | Not found |
| 5 | High coverage of security related module and integration tests | ❌ | No security test coverage |

### Consolidation

| Level | Requirement | Status | Evidence |
|-------|------------|--------|----------|
| 1 | Simple false positive treatment | ❌ | No process |
| 1 | Treatment of defects with severity high or higher | ❌ | No formal process |
| 2 | Simple visualization of defects | ❌ | No visualization |
| 3 | Fix based on accessibility | ❌ | Not implemented |
| 3 | Integration in development process | ❌ | Not integrated |
| 3 | Integration of vulnerability issues into the development process | ❌ | Not integrated |
| 3 | Treatment of defects per protection requirement | 🟡 | |
| 3 | Treatment of defects with severity middle | 🟡 | |
| 3 | Usage of a vulnerability management system | 🟡 | |
| 4 | Advanced visualization of defects | 🟡 | |
| 4 | Reproducible defect tickets | 🟡 | |
| 5 | Treatment of all defects | 🟡 | |

### Dynamic depth for applications

| Level | Requirement | Status | Evidence |
|-------|------------|--------|----------|
| 2 | Coverage of client side dynamic components | 🟡 | |
| 2 | Simple Scan | ❌ | No DAST implemented |
| 2 | Usage of different roles | ❌ | Not tested |
| 3 | Coverage of hidden endpoints | ❌ | Not scanned |
| 3 | Coverage of more input vectors | ❌ | Not tested |
| 3 | Coverage of sequential operations | ❌ | Not tested |
| 4 | Usage of multiple scanners | 🟡 | |
| 5 | Coverage analysis | ❌ | Not analyzed |
| 5 | Coverage of service to service communication | ❌ | Not tested |

### Dynamic depth for infrastructure

| Level | Requirement | Status | Evidence |
|-------|------------|--------|----------|
| 2 | Test for exposed services | ❌ | No scanning |
| 2 | Test network segmentation | ❌ | Not tested |
| 2 | Test of the configuration of cloud environments | ❌ | Not verified |
| 3 | Test for unauthorized installation | ❌ | Not monitored |
| 3 | Weak password test | ❌ | Not implemented |
| 4 | Load tests | ❌ | Not performed |
| 5 | Test for unused Resources | ❌ | Not checked |

### Static depth for applications

| Level | Requirement | Status | Evidence |
|-------|------------|--------|----------|
| 2 | Software Composition Analysis (server side) | ✅ | NuGet package management |
| 2 | Test for Time to Patch | ❌ | Not measured |
| 2 | Test libyear | ❌ | Not measured |
| 3 | API design validation | ❌ | No validation |
| 3 | Exploit likelihood estimation | ❌ | Not assessed |
| 3 | Local development security checks performed | ❌ | No local checks |
| 3 | Software Composition Analysis (client side) | ❌ | No client-side SCA |
| 3 | Static analysis for important client side components | ❌ | No client analysis |
| 3 | Static analysis for important server side components | ✅ | CodeQL for key components |
| 3 | Test for Patch Deployment Time | ❌ | Not measured |
| 4 | Static analysis for all self written components | ✅ | CodeQL covers all code |
| 4 | Usage of multiple analyzers | ❌ | Only CodeQL |
| 5 | Dead code elimination | ❌ | Not performed |
| 5 | Exclusion of source code duplicates | ❌ | Not checked |
| 5 | Static analysis for all components/libraries | ❌ | Only own code |
| 5 | Stylistic analysis | ✅ | Roslyn analyzers |

### Static depth for infrastructure

| Level | Requirement | Status | Evidence |
|-------|------------|--------|----------|
| 1 | Test for stored secrets | ✅ | GitHub Secret Scanning Alerts |
| 2 | Test cluster deployment resources | ❌ | Not tested |
| 2 | Test for image lifetime | ❌ | Not monitored |
| 2 | Test of virtualized environments | ❌ | Not scanned |
| 2 | Test the cloud configuration | ❌ | No cloud config scanning |
| 2 | Test the definition of virtualized environments | ❌ | Not validated |
| 3 | Analyze logs | ❌ | No log analysis |
| 3 | Test for malware | ❌ | No malware scanning |
| 3 | Test for new image version | ❌ | Not tracked |
| 4 | Correlate known vulnerabilities in infrastructure with new image versions | ❌ | Not correlated |
| 4 | Software Composition Analysis | ❌ | No infrastructure SCA |
| 4 | Test of infrastructure components for known vulnerabilities | ❌ | Not scanned |

### Test-Intensity

| Level | Requirement | Status | Evidence |
|-------|------------|--------|----------|
| 1 | Default settings for intensity | ✅ | Standard CI/CD settings |
| 2 | Regular automated tests | ✅ | CI pipeline runs on every commit |
| 3 | Deactivating of unneeded tests | ❌ | Not optimized |
| 3 | High test intensity | 🟡 | |
| 4 | Creation and application of a testing concept | ❌ | No test strategy document |
---

## 🎯 Priority Recommendations

### 🔴 High Priority (Security & Compliance)
2. **Implement SBOM Generation** - Supply chain security visibility
4. **Create SECURITY.md** - Vulnerability disclosure process

### 🟡 Medium Priority (Process & Quality)
1. **Branch Protection Rules** - Enforce PR reviews and status checks
2. **Security Unit Tests** - Test authentication and authorization
3. **Threat Modeling** - Document security risks and mitigations
4. **Infrastructure as Code** - Version control AWS resources

### 🟢 Low Priority (Maturity Enhancement)
1. **Container Image Scanning** - Vulnerability detection in Docker images
2. **Security Champions** - Designate security focal points per team
3. **Monitoring Dashboards** - Visualize metrics and create alerts
4. **Chaos Engineering** - Test resilience and security

---

## 📈 Maturity Progression Path

```mermaid
graph LR
    A[Current State] --> B[Quick Wins]
    B --> C[Foundation]
    C --> D[Maturity]
    
    B --> B3[Branch Protection]
    
    C --> C1[SBOM Generation]
    C --> C2[Security Tests]
    
    D --> D1[Threat Modeling]
    D --> D2[Security Training]
    D --> D3[Advanced Monitoring]
```
