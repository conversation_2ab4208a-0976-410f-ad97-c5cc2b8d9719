# Evolution - Axon Integration Layer

## Prerequisites
- [Docker](https://www.docker.com/get-started) installed and running
- (Optional for EventCatalog) [Node.js](https://nodejs.org/) >= 18 if you want to run EventCatalog without Docker
- (Optional for database access) [AWS CLI](https://aws.amazon.com/cli/) and [AWS Session Manager Plugin](https://docs.aws.amazon.com/systems-manager/latest/userguide/session-manager-working-with-install-plugin.html)

## Getting Started

### 1. Clone the repository
```sh
<NAME_EMAIL>:ALSSoftware/axon.git
cd axon
```

### 2. Create a .env.local file
Create a `.env.local` file in the project root with the following content (adjust values according to your environment):
```env
# RabbitMQ credentials
RABBITMQ__Host=rabbitmq
RABBITMQ__VirtualHost=/
RABBITMQ__Username=guest
RABBITMQ__Password=guest
RABBITMQ_USER=${RABBITMQ__Username}
RABBITMQ_PASS=${RABBITMQ__Username}

# SAP API configuration (example values - adjust as needed)
SapApi__IntegrationEnabled=true
SapApi__BaseAddress=http://axon-axon-integration-mock-api-1:7600
SapApi__SystemNumber=00
SapApi__Client=100
SapApi__User=your-sap-user
SapApi__Password=your-sap-password
SapApi__Language=EN
SapApi__SystemId=DEV

# Magento API configuration (example values - adjust as needed)
MagentoApi__BaseAddress=http://your-magento-host:your-magento-port
MagentoApi__InboundApiKey=your-inbound-api-key-here
MagentoApi__OutboundApiKey=your-outbound-api-key-here
MagentoApi__RequireInboundAuthentication=false

# LocalStack AWS credentials
AWS_ACCESS_KEY_ID=test
AWS_SECRET_ACCESS_KEY=test

Aws__Region=us-east-2

POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_DB=axon
POSTGRES_USER=axon_user
POSTGRES_PASSWORD=axon_password

Persistence__Type=PostgreSQL

ConnectionStrings__OrderDb="Host=postgres;Port=5432;Database=axon;Username=axon_user;Password=axon_password"
ConnectionStrings__CartDb="Host=postgres;Port=5432;Database=axon;Username=axon_user;Password=axon_password"
ConnectionStrings__SharedDb="Host=postgres;Port=5432;Database=axon;Username=axon_user;Password=axon_password"

```
These variables are used for the strongly-typed RabbitMqOptions and are required for the services to connect to RabbitMQ and LocalStack.

### 3. Start EventCatalog

#### Using Node.js (development mode)
```sh
cd eventcatalog
npm install
npm run dev
```
- The app will be available at the URL shown in your terminal (usually http://localhost:7500).

#### Using Docker
```sh
docker build -t eventcatalog ./eventcatalog
# Serve the static site
# (If you want to use the provided Dockerfile)
docker run -p 7500:80 eventcatalog
```
- The site will be available at http://localhost:7500

### 4. Start Axon Integration Layer Services (Docker Compose)

From the project root:
```sh
docker-compose up
```
This will start:
- Axon.Adapter.Api (http://localhost:7501)
- Axon.Domain.Service (http://localhost:7502)
- RabbitMQ (management UI: http://localhost:15672, broker: amqp://localhost:5672)
- LocalStack (http://localhost:4566)
- PostgreSQL (port: 5432)

> The .NET services will hot-reload on code changes if you edit files in `src/`.

### 5. Stopping Services
To stop all services:
```sh
docker-compose down
```

## Persistence Configuration

The Domain Service supports two persistence modes: In-Memory and PostgreSQL. The persistence type is configured using the `Persistence` section with validation through the IOptions pattern.

### Persistence Modes

#### In-Memory (Default)
- No external database required
- Data is lost when the application restarts
- Ideal for development and testing
- Uses Entity Framework Core's in-memory database provider

#### PostgreSQL
- Production-ready persistence
- Data is persisted across restarts
- Requires PostgreSQL database (included in docker-compose)
- Uses separate schemas for each bounded context:
  - `order` schema for Order context
  - `cart` schema for Cart context
  - `shared` schema for shared data
- Automatic migrations applied on startup

### Validation

The configuration uses Data Annotations for validation:
- **Persistence.Type** must be either "InMemory" or "PostgreSQL" (case-insensitive)
- When using PostgreSQL, all three connection strings are required
- Validation occurs at startup, ensuring the application fails fast with clear error messages

## Database Access via SSM Tunnel

The project includes a script `scripts/ssm-tunnel.sh` that provides secure database access through AWS Systems Manager Session Manager. This script creates an SSH tunnel to connect to Aurora PostgreSQL databases in AWS without exposing them to the public internet.

### Prerequisites for SSM Tunnel

1. **AWS CLI**: Install the AWS CLI v2
   - macOS: `brew install awscli`
   - Linux/Windows: Follow [AWS CLI installation guide](https://docs.aws.amazon.com/cli/latest/userguide/getting-started-install.html)

2. **AWS Session Manager Plugin**: Required for SSM port forwarding
   - macOS: `brew install --cask session-manager-plugin`
   - Other platforms: Follow [Session Manager Plugin installation guide](https://docs.aws.amazon.com/systems-manager/latest/userguide/session-manager-working-with-install-plugin.html)

3. **Additional tools** (usually pre-installed on most systems):
   - `jq` for JSON parsing
   - `nc` (netcat) for port checking
   - `psql` (optional, for PostgreSQL client access)

### Using the SSM Tunnel Script

1. **Configure AWS credentials**:
   ```bash
   aws configure
   # Or use environment variables:
   export AWS_PROFILE=your-profile
   export AWS_REGION=us-east-2
   ```

2. **Run the script**:
   ```bash
   ./scripts/ssm-tunnel.sh
   ```

3. **Select environment**:
   - The script will prompt you to select an environment (dev, qa, or prod)
   - It automatically finds the appropriate bastion instance and database credentials

4. **Connect to the database**:
   - The script displays connection details and commands
   - Default local port is 54321 (configurable via `LOCAL_PORT` environment variable)
   - Connection string format: `postgresql://username:password@localhost:54321/dbname`

### Script Features

- **Automatic discovery**: Finds bastion instances by tags and retrieves database credentials from AWS Secrets Manager
- **Environment-aware**: Supports multiple environments (dev, qa, prod)
- **Color-coded output**: Easy-to-read status messages and error handling
- **Secure**: Uses AWS SSM for tunneling, no SSH keys or direct internet exposure needed
- **Cleanup on exit**: Automatically closes connections when terminated

### Troubleshooting

- **"Could not find bastion instance"**: Ensure EC2 instances are tagged with both "bastion" and the environment name
- **"Could not find database secret"**: Verify the secret exists at `evolution/{environment}/axon/domain-service/postgresql-axondb-connection`
- **"Port already in use"**: Either stop the conflicting process or set a different port: `LOCAL_PORT=5433 ./scripts/ssm-tunnel.sh`

## Additional Notes
- You can run the .NET services individually with `dotnet run --project src/Axon.Adapter.Api` or `src/Axon.Domain.Service` if you prefer not to use Docker Compose.
- For multi-service orchestration, always prefer Docker Compose for consistency.
- When running locally, ensure RabbitMQ is running and update the host to `localhost` in your configuration.
- The port numbers and hostnames in the example configuration are for development. Adjust them according to your environment.

For any issues, please refer to the documentation or reach out to the maintainers.