services:

  axon-adapter-api:
    image: mcr.microsoft.com/dotnet/sdk:9.0
    working_dir: /workspace/src/Axon.Adapter.Api
    command: dotnet watch run --urls http://0.0.0.0:7501
    depends_on:
      - rabbitmq
    env_file:
      - .env.local
    volumes:
      - ./src:/workspace/src
    ports:
      - "7501:7501"

  axon-domain-service:
    image: mcr.microsoft.com/dotnet/sdk:9.0
    working_dir: /workspace/src/Axon.Domain.Service
    command: dotnet watch run --urls http://0.0.0.0:7502
    depends_on:
      - rabbitmq
      - postgres
    env_file:
      - .env.local
    volumes:
      - ./src:/workspace/src
    ports:
      - "7502:7502"

  axon-integration-mock-api:
    image: mcr.microsoft.com/dotnet/sdk:9.0
    working_dir: /workspace/src/Axon.Integration.Mock.Api
    command: dotnet watch run --urls http://0.0.0.0:7600
    volumes:
      - ./src:/workspace/src
    ports:
      - "7600:7600"

  localstack:
    image: localstack/localstack:latest
    env_file:
      - .env.local
    environment:
      - SERVICES=s3,sqs,sns
      - DEBUG=1
    ports:
      - "4566:4566"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock

  rabbitmq:
    image: rabbitmq:3-management
    ports:
      - "5672:5672"
      - "15672:15672"
    env_file:
      - .env.local

  postgres:
    image: postgres:17.4-alpine
    container_name: axon-postgres
    env_file:
      - .env.local
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U axon_user -d axon"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  postgres_data:

