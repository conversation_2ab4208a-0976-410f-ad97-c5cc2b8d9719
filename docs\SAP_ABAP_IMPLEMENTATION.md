# SAP ABAP Implementation Guide

## Overview

This document provides the complete SAP ABAP implementation for pushing events to the Axon adapter API via RFC. The implementation includes BAdI (Business Add-In) for event capture and qRFC (queued Remote Function Call) for guaranteed delivery.

## Architecture

```
SAP Business Process → BAdI → qRFC → Adapter API (RFC Server) → MassTransit → Event Processing
```

## 1. Simplified Architecture (No Database Table Needed)

### Why No Database Table?

The custom event queue table is **not needed** because:

1. **qRFC handles queuing** - SAP's built-in qRFC system manages the queue
2. **Built-in monitoring** - Use standard qRFC monitoring tools (SMQ1, SMQ2)
3. **Simpler implementation** - No custom table management needed
4. **Better performance** - No additional database operations

### qRFC Built-in Features

- ✅ **Automatic queuing** - Events queued in SAP's qRFC system
- ✅ **Retry logic** - Built-in retry mechanisms with configurable intervals
- ✅ **Error handling** - Comprehensive error handling and recovery
- ✅ **Monitoring** - Standard SAP monitoring tools
- ✅ **Persistence** - Events survive system restarts

## 2. BAdI Implementation for Event Capture

### Create BAdI Class

```abap
-- Transaction: SE19
-- BAdI Implementation: ZCL_SAP_EVENT_CAPTURE

CLASS zcl_sap_event_capture DEFINITION
  PUBLIC
  FINAL
  CREATE PUBLIC.

  PUBLIC SECTION.
    INTERFACES if_ex_sd_sales_order_change.
    INTERFACES if_ex_mm_material_change.
    INTERFACES if_ex_sd_customer_change.

  PRIVATE SECTION.
    METHODS: queue_event
      IMPORTING
        event_type TYPE string
        event_data TYPE string.
ENDCLASS.

CLASS zcl_sap_event_capture IMPLEMENTATION.
  
  METHOD if_ex_sd_sales_order_change~change_after_update.
    " Direct qRFC push - no background job needed
    CALL FUNCTION 'ZMM_PUSH_ORDER_STATUS_EVENT'
      IN BACKGROUND TASK
      DESTINATION 'AXON_RFC_SERVER'
      EXPORTING
        order_number = vbak-vbeln
        status       = vbak-gbstk.
  ENDMETHOD.

  METHOD if_ex_mm_material_change~change_after_update.
    " Direct qRFC push - no background job needed
    CALL FUNCTION 'ZMM_PUSH_MATERIAL_UPDATE_EVENT'
      IN BACKGROUND TASK
      DESTINATION 'AXON_RFC_SERVER'
      EXPORTING
        material_number = mara-matnr
        update_type     = 'MATERIAL_UPDATED'.
  ENDMETHOD.

  METHOD if_ex_sd_customer_change~change_after_update.
    " Direct qRFC push - no background job needed
    CALL FUNCTION 'ZMM_PUSH_CUSTOMER_CHANGE_EVENT'
      IN BACKGROUND TASK
      DESTINATION 'AXON_RFC_SERVER'
      EXPORTING
        customer_number = kna1-kunnr
        change_type     = 'CUSTOMER_UPDATED'.
  ENDMETHOD.

  " Note: queue_event method no longer needed with direct qRFC push
  " qRFC handles all queuing and retry logic automatically

ENDCLASS.
```

## 3. qRFC Function for Event Push

### Create qRFC Function

```abap
-- Transaction: SE37
-- Function Name: ZMM_PUSH_EVENT_TO_ADAPTER
-- Function Group: ZMM_EVENTS

FUNCTION zmm_push_event_to_adapter.
*"----------------------------------------------------------------------
*"*"Remote Function Module:
*"  IMPORTING
*"     VALUE(EVENT_TYPE) TYPE  STRING
*"     VALUE(EVENT_DATA) TYPE  STRING
*"  EXPORTING
*"     VALUE(SUCCESS) TYPE  CHAR1
*"     VALUE(MESSAGE) TYPE  STRING
*"  EXCEPTIONS
*"      PROCESSING_ERROR
*"----------------------------------------------------------------------

  DATA: lv_success TYPE char1,
        lv_message TYPE string.

  " Process the event based on type
  CASE event_type.
    WHEN 'ORDER_STATUS_UPDATED'.
      " Call order status event function
      CALL FUNCTION 'ZMM_PUSH_ORDER_STATUS_EVENT'
        EXPORTING
          event_data = event_data
        IMPORTING
          success    = lv_success
          message    = lv_message.

    WHEN 'MATERIAL_UPDATED'.
      " Call material update event function
      CALL FUNCTION 'ZMM_PUSH_MATERIAL_UPDATE_EVENT'
        EXPORTING
          event_data = event_data
        IMPORTING
          success    = lv_success
          message    = lv_message.

    WHEN 'CUSTOMER_CHANGED'.
      " Call customer change event function
      CALL FUNCTION 'ZMM_PUSH_CUSTOMER_CHANGE_EVENT'
        EXPORTING
          event_data = event_data
        IMPORTING
          success    = lv_success
          message    = lv_message.

    WHEN OTHERS.
      lv_success = ''.
      lv_message = 'Unknown event type'.
  ENDCASE.

  " Set export parameters
  success = lv_success.
  message = lv_message.

ENDFUNCTION.
```

## 4. Event-Specific Functions

### Order Status Event Function

```abap
-- Transaction: SE37
-- Function Name: ZMM_PUSH_ORDER_STATUS_EVENT

FUNCTION zmm_push_order_status_event.
*"----------------------------------------------------------------------
*"*"Remote Function Module:
*"  IMPORTING
*"     VALUE(EVENT_DATA) TYPE  STRING
*"  EXPORTING
*"     VALUE(SUCCESS) TYPE  CHAR1
*"     VALUE(MESSAGE) TYPE  STRING
*"----------------------------------------------------------------------

  DATA: lv_order_number TYPE string,
        lv_status TYPE string,
        lv_destination TYPE string.

  " Parse event data (simplified - in production use proper JSON parsing)
  " Extract order number and status from event_data

  " Set RFC destination
  lv_destination = 'AXON_RFC_SERVER'.

  " Call the adapter API RFC server
  CALL FUNCTION 'ZMM_PUSH_ORDER_STATUS_EVENT'
    DESTINATION lv_destination
    EXPORTING
      order_number = lv_order_number
      status       = lv_status
    IMPORTING
      success      = success
      message      = message
    EXCEPTIONS
      communication_failure = 1
      system_failure        = 2
      OTHERS               = 3.

  IF sy-subrc <> 0.
    success = ''.
    message = 'RFC call failed'.
  ENDIF.

ENDFUNCTION.
```

### Material Update Event Function

```abap
-- Transaction: SE37
-- Function Name: ZMM_PUSH_MATERIAL_UPDATE_EVENT

FUNCTION zmm_push_material_update_event.
*"----------------------------------------------------------------------
*"*"Remote Function Module:
*"  IMPORTING
*"     VALUE(EVENT_DATA) TYPE  STRING
*"  EXPORTING
*"     VALUE(SUCCESS) TYPE  CHAR1
*"     VALUE(MESSAGE) TYPE  STRING
*"----------------------------------------------------------------------

  DATA: lv_material_number TYPE string,
        lv_update_type TYPE string,
        lv_destination TYPE string.

  " Parse event data
  " Extract material number and update type from event_data

  " Set RFC destination
  lv_destination = 'AXON_RFC_SERVER'.

  " Call the adapter API RFC server
  CALL FUNCTION 'ZMM_PUSH_MATERIAL_UPDATE_EVENT'
    DESTINATION lv_destination
    EXPORTING
      material_number = lv_material_number
      update_type     = lv_update_type
    IMPORTING
      success         = success
      message         = message
    EXCEPTIONS
      communication_failure = 1
      system_failure        = 2
      OTHERS               = 3.

  IF sy-subrc <> 0.
    success = ''.
    message = 'RFC call failed'.
  ENDIF.

ENDFUNCTION.
```

### Customer Change Event Function

```abap
-- Transaction: SE37
-- Function Name: ZMM_PUSH_CUSTOMER_CHANGE_EVENT

FUNCTION zmm_push_customer_change_event.
*"----------------------------------------------------------------------
*"*"Remote Function Module:
*"  IMPORTING
*"     VALUE(EVENT_DATA) TYPE  STRING
*"  EXPORTING
*"     VALUE(SUCCESS) TYPE  CHAR1
*"     VALUE(MESSAGE) TYPE  STRING
*"----------------------------------------------------------------------

  DATA: lv_customer_number TYPE string,
        lv_change_type TYPE string,
        lv_destination TYPE string.

  " Parse event data
  " Extract customer number and change type from event_data

  " Set RFC destination
  lv_destination = 'AXON_RFC_SERVER'.

  " Call the adapter API RFC server
  CALL FUNCTION 'ZMM_PUSH_CUSTOMER_CHANGE_EVENT'
    DESTINATION lv_destination
    EXPORTING
      customer_number = lv_customer_number
      change_type     = lv_change_type
    IMPORTING
      success         = success
      message         = message
    EXCEPTIONS
      communication_failure = 1
      system_failure        = 2
      OTHERS               = 3.

  IF sy-subrc <> 0.
    success = ''.
    message = 'RFC call failed'.
  ENDIF.

ENDFUNCTION.
```

## 5. Simplified Architecture (No Background Job Needed)

### Why No Background Job?

The background job is **not needed** because:

1. **qRFC is already asynchronous** - It handles queuing and retries automatically
2. **BAdI execution is fast** - Just a function call, not heavy processing
3. **Simpler architecture** - Fewer components to maintain and monitor
4. **Real-time processing** - Events pushed immediately when they occur

### Direct qRFC Push Benefits

- ✅ **Immediate processing** - Events sent as they occur
- ✅ **Built-in reliability** - qRFC handles retries and error recovery
- ✅ **Simpler monitoring** - Use standard qRFC monitoring (SMQ1, SMQ2)
- ✅ **Better performance** - No polling delay
- ✅ **Less complexity** - No custom queue table or background job needed

## 6. Test Function for POC

### Create Test Event Function

```abap
-- Transaction: SE37
-- Function Name: ZMM_PUSH_TEST_EVENT

FUNCTION zmm_push_test_event.
*"----------------------------------------------------------------------
*"*"Remote Function Module:
*"  IMPORTING
*"     VALUE(TEST_MESSAGE) TYPE  STRING DEFAULT 'Test event from SAP'
*"  EXPORTING
*"     VALUE(SUCCESS) TYPE  CHAR1
*"     VALUE(MESSAGE) TYPE  STRING
*"----------------------------------------------------------------------

  DATA: lv_destination TYPE string.

  " Set RFC destination
  lv_destination = 'AXON_RFC_SERVER'.

  " Call the adapter API RFC server
  CALL FUNCTION 'ZMM_PUSH_TEST_EVENT'
    DESTINATION lv_destination
    EXPORTING
      test_message = test_message
    IMPORTING
      success      = success
      message      = message
    EXCEPTIONS
      communication_failure = 1
      system_failure        = 2
      OTHERS               = 3.

  IF sy-subrc <> 0.
    success = ''.
    message = 'RFC call failed'.
  ENDIF.

ENDFUNCTION.
```

## 7. RFC Destination Configuration

### Configure RFC Destination

```abap
-- Transaction: SM59
-- RFC Destination: AXON_RFC_SERVER

-- Connection Type: T (TCP/IP)
-- Target Host: [Adapter API Host]
-- Service No.: [Adapter API Port]
-- System Number: 00
-- Client: 100
-- User: RFC_USER
-- Password: [RFC Password]
-- Language: EN
```

## 8. Monitoring and Troubleshooting

### qRFC Monitoring

```abap
-- Transaction: SMQ1 (qRFC Monitor)
-- View qRFC queue status and retry information

-- Transaction: SMQ2 (qRFC Administration)
-- Manage qRFC destinations and connections
```

### Event Queue Monitoring

```sql
-- Check pending events
SELECT COUNT(*) FROM zmm_event_queue WHERE status = 'PENDING';

-- Check failed events
SELECT * FROM zmm_event_queue WHERE status = 'ERROR' ORDER BY created_at DESC;

-- Check processing statistics
SELECT 
  status,
  COUNT(*) as count,
  AVG(TIMESTAMPDIFF(SECOND, created_at, processed_at)) as avg_processing_time
FROM zmm_event_queue 
WHERE processed_at IS NOT NULL
GROUP BY status;
```

## 9. Security Considerations

### RFC User Authorization

```abap
-- Transaction: SU01
-- Create RFC user with minimal required authorizations

-- Authorizations needed:
-- - RFC authorization
-- - Access to relevant business objects
-- - No dialog login allowed
```

### Network Security

- Configure SAP Gateway for secure connections
- Use SNC (Secure Network Communication) if required
- Implement firewall rules for RFC traffic

## 10. Performance Optimization

### Connection Pooling

```abap
-- Configure RFC destination with connection pooling
-- Set appropriate pool size and timeout values
```

### Batch Processing

```abap
-- Process multiple events in batches
-- Use LUW (Logical Unit of Work) for transaction consistency
```

## Conclusion

This implementation provides a complete SAP ABAP solution for pushing events to the Axon adapter API. The architecture ensures:

1. **Guaranteed Delivery** - qRFC provides built-in reliability
2. **Real-time Processing** - Events are pushed immediately
3. **Error Handling** - Comprehensive error handling and retry logic
4. **Monitoring** - Full visibility into event processing
5. **Scalability** - Background job processing for high throughput

The next step is to implement the actual SAP NCo RFC Server in the adapter API to receive these events. 