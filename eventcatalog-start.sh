#!/bin/bash
# eventcatalog-start.sh - Safe EventCatalog startup script
# This script implements the cursor rule for EventCatalog process management

set -e  # Exit on any error

echo "🔍 Checking for existing EventCatalog instances..."

# Check if port 7500 is in use
if lsof -i :7500 >/dev/null 2>&1; then
    echo "⚠️  EventCatalog is already running on port 7500"
    echo "📋 Current processes:"
    ps aux | grep -E "(eventcatalog|astro)" | grep -v grep | grep -v "$0" || echo "No matching processes found"
    
    echo -n "🔄 Restart existing instance? (y/N): "
    read -r choice
    
    if [[ "$choice" =~ ^[Yy]$ ]]; then
        echo "🛑 Terminating existing instances..."
        pkill -f "eventcatalog" || true
        pkill -f "astro" || true
        sleep 3
        
        # Verify processes are terminated
        if lsof -i :7500 >/dev/null 2>&1; then
            echo "❌ Failed to terminate existing instance. Please manually kill processes on port 7500"
            exit 1
        fi
        
        echo "🚀 Starting new EventCatalog instance..."
        cd "$(dirname "$0")"
        npm run dev
    else
        echo "✅ Keeping existing instance running"
        echo "🌐 Access at: http://localhost:7500"
        exit 0
    fi
else
    echo "✅ No existing instance found"
    echo "🚀 Starting EventCatalog..."
    cd "$(dirname "$0")"
    
    # Check if we're in the right directory (should have package.json)
    if [ ! -f "package.json" ]; then
        echo "❌ package.json not found. Please run this script from the project root directory."
        exit 1
    fi
    
    # Check if npm run dev script exists
    if ! npm run | grep -q "dev"; then
        echo "❌ 'dev' script not found in package.json"
        echo "📋 Available scripts:"
        npm run
        exit 1
    fi
    
    npm run dev
fi 