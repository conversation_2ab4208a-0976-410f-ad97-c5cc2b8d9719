# Evolution Integration Layer Event Catalog

## Overview

### Interactions with the Integration layer

- Channels:
  - **Outside Services:** Any services that live outside of the integration layer will communicate via HTTP (REST) calls in most circumstances. HTTP channels can only have queries flowing across them. This will ensure that they can be imported or exported as Open API Specifications.
  - **Integration Layer services:**: Services that live inside the integration layer will will communicate via the message broker channels (Rabbit or AWS SNS & SQS). These channels will only have commands and events flowing across them. exported into Async API Specifications.
    - Commands will use AWS SQS
    - Events will use AWS SNS

## Requirements

- **Node.js**: >= 18 (LTS recommended)
- **Docker**: Latest version recommended

## Running Locally

### Using Node.js
1. Install dependencies:
   ```bash
   npm install
   ```
2. Start the development server:
   ```bash
   npm run dev
   ```
   The app will be available at the URL shown in your terminal (usually http://localhost:7500).

### Using Docker
- To build and serve the static site:
  ```bash
  docker build -t eventcatalog .
  docker run -p 7500:80 eventcatalog
  ```
  The site will be available at http://localhost:7500.

