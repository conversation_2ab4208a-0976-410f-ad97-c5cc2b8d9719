---
id: aws.sns.{env}.cart-created-event
name: Cart Created Event SNS Channel
version: 0.0.1
summary: |
  SNS channel for publishing Cart Created events to downstream consumers. Used for integration between Axon integration layer and other systems.
owners:
  - enterprise
address: arn:aws:sns:us-east-1:123456789012:cart-created-event-{env}
protocols:
  - sns
parameters:
  env:
    enum:
      - local
      - dev
      - sit
      - prod
    description: 'Deployment environment for the SNS topic.'
badges:
  - content: Channel
    backgroundColor: blue
    textColor: blue
    icon: RectangleGroupIcon
---

## Overview
This channel is used to publish `cart-created-event` events to AWS SNS for consumption by downstream services and systems.

## Example ARN
```
arn:aws:sns:us-east-1:123456789012:cart-created-event-dev
``` 