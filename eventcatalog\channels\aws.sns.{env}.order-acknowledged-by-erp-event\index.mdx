---
id: aws.sns.{env}.order-acknowledged-by-erp-event
name: Order Acknowledged by ERP Event SNS Channel
version: 0.0.1
summary: |
  SNS channel for publishing Order Acknowledged by ERP events to downstream consumers. Used for integration between Axon integration layer and other systems.
owners:
  - enterprise
address: arn:aws:sns:us-east-1:123456789012:order-acknowledged-by-erp-event-{env}
protocols:
  - sns
parameters:
  env:
    enum:
      - local
      - dev
      - sit
      - prod
    description: 'Deployment environment for the SNS topic.'
badges:
  - content: Channel
    backgroundColor: blue
    textColor: blue
    icon: RectangleGroupIcon
---

## Overview
This channel is used to publish `order-acknowledged-by-erp-event` events to AWS SNS for consumption by downstream services and systems.

## Example ARN
```
arn:aws:sns:us-east-1:123456789012:order-acknowledged-event-dev
``` 