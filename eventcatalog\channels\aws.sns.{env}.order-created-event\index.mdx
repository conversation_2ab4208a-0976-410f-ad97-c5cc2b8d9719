---
id: aws.sns.{env}.order-created-event
name: Order Created Event SNS Channel
version: 1.0.0
summary: |
  SNS channel for publishing Order Created events to downstream consumers. Used for integration between Axon integration layer and other systems.
owners:
  - enterprise
address: arn:aws:sns:us-east-1:123456789012:order-created-event-{env}
protocols:
  - sns
parameters:
  env:
    enum:
      - local
      - dev
      - sit
      - prod
    description: 'Deployment environment for the SNS topic.'
badges:
  - content: Channel
    backgroundColor: blue
    textColor: blue
    icon: RectangleGroupIcon
---

## Overview
This channel is used to publish `order-created-event` events to AWS SNS for consumption by downstream services and systems.

## Example ARN
```
arn:aws:sns:us-east-1:123456789012:order-created-event-dev
``` 