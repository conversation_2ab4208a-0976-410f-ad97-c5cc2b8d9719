---
id: aws.sns.{env}.sap-sales-order-created-event
name: SAP Sales Order Created Event SNS Channel
version: 0.0.1
summary: |
  SNS channel for publishing SAP Sales Order Created events to downstream consumers. Used for integration between SAP ECC 6 and other systems.
owners:
  - enterprise
address: arn:aws:sns:us-east-1:123456789012:sap-sales-order-created-event-{env}
protocols:
  - sns
parameters:
  env:
    enum:
      - local
      - dev
      - sit
      - prod
    description: 'Deployment environment for the SNS topic.'
badges:
  - content: Channel
    backgroundColor: blue
    textColor: blue
    icon: RectangleGroupIcon
---

## Overview
This channel is used to publish `sap-sales-order-created-event` events to AWS SNS for consumption by downstream services and systems.

## Example ARN
```
arn:aws:sns:us-east-1:123456789012:sap-sales-order-created-event-dev
``` 