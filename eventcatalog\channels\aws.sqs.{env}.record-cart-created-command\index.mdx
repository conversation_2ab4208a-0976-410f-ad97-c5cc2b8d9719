---
id: aws.sqs.{env}.record-cart-created-command
name: Record Cart Created Command SQS Queue
version: 1.0.0
summary: SQS queue used for sending record-cart-created-command to the Cart Domain Service.
owners:
  - enterprise
address: arn:aws:sns:us-east-1:123456789012:{env}-record-cart-created-command
protocols:
  - sqs
parameters:
  env:
    description: Environment (local, dev, sit, prod)
    required: true
    values:
      - local
      - dev
      - sit
      - prod
badges:
  - content: Channel
    backgroundColor: blue
    textColor: blue
    icon: RectangleGroupIcon
---

## Overview

This SQS queue is used to send the Record Cart Created Command to the Cart Domain Service.

### Example ARN

```
arn:aws:sqs:us-east-1:123456789012:{env}-record-cart-created-command
``` 