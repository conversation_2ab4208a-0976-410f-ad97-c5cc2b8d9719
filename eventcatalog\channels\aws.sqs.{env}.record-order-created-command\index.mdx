---
id: aws.sqs.{env}.record-order-created-command
name: Record Order Created Command SQS Queue
version: 1.0.0
summary: SQS queue used for sending record-order-created-command to the Order Domain Service.
owners:
  - enterprise
address: arn:aws:sns:us-east-1:123456789012:{env}-record-order-created-command
protocols:
  - sqs
parameters:
  env:
    description: Environment (local, dev, sit, prod)
    required: true
    values:
      - local
      - dev
      - sit
      - prod
badges:
  - content: Channel
    backgroundColor: blue
    textColor: blue
    icon: RectangleGroupIcon
---

## Overview

This SQS queue is used to send the Record Order Created Command to the Order Domain Service.

### Example ARN

```
arn:aws:sqs:us-east-1:123456789012:{env}-record-order-created-command
``` 