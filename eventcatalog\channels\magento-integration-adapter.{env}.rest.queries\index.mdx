---
id: magento-integration-adapter.{env}.rest.queries
name: Magento Integration Adapter Queries REST Channel
version: 0.0.1
summary: |
  REST channel for receiving notifications from Magento about various events and state changes
owners:
  - enterprise
address: http://localhost:7501
protocols: 
  - http

parameters:
  env:
    enum:
      - local
      - dev
    description: 'Environment to use'
badges:
  - content: Channel
    backgroundColor: blue
    textColor: blue
    icon: RectangleGroupIcon
---

### Overview
The HTTP REST interface receives notifications from Magento about various events and state changes, such as customer updates, order creation, and cart modifications. These notifications are then processed by the Magento Integration Adapter service.

<ChannelInformation />