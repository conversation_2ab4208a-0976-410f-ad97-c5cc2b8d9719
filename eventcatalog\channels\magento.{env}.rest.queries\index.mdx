---
id: magento.{env}.rest.queries
name: Magento Queries REST Channel
version: 1.0.0
summary: |
  Channel for all REST queries to Magento
owners:
  - enterprise
address: http://localhost:9999
protocols: 
  - http

parameters:
  env:
    enum:
      - local
      - dev
      - sit
      - prod
    description: 'Environment to use'
badges:
  - content: Channel
    backgroundColor: blue
    textColor: blue
    icon: RectangleGroupIcon
---

# Magento REST APIs

## Overview
The HTTP REST interface is a proxy for systems to communicate with Magento via the integration layer. This channel provides access to Magento's core functionalities through RESTful endpoints.

## Authentication
All API requests require authentication using OAuth 2.0. You'll need to include an `Authorization` header with a valid access token:

```http
Authorization: Bearer <your_access_token>
```

## Available Endpoints

### Orders

#### Get Order by ID
```http
GET /V1/orders/{orderId}
```
Retrieves detailed information about a specific order.

**Response Schema**: [E-commerce Order Created Schema](/events/e-commerce-order-created)

#### Update Order Status
```http
POST /V1/orders/{orderId}/status
```
Updates the status of an existing order.

**Query**: [Magento Order Status Update](/channels/magento.{env}.rest.queries/magento-order-status-update)

#### Ship Order
```http
POST /rest/default/V1/order/{orderId}/ship
```
Creates a shipment for a specific order with tracking information.

**Query**: [Magento Ship Order](/channels/magento.{env}.rest.queries/magento-ship-order)

#### List Orders
```http
GET /V1/orders
```
Retrieves a list of orders based on search criteria.

**Query Parameters**:
- `searchCriteria[filterGroups][0][filters][0][field]`: Field to filter by
- `searchCriteria[filterGroups][0][filters][0][value]`: Value to filter by
- `searchCriteria[filterGroups][0][filters][0][conditionType]`: Condition type (eq, like, etc.)
- `searchCriteria[pageSize]`: Number of items per page
- `searchCriteria[currentPage]`: Current page number

### Products

#### Get Product by SKU
```http
GET /V1/products/{sku}
```
Retrieves detailed information about a specific product.

#### List Products
```http
GET /V1/products
```
Retrieves a list of products based on search criteria.

### Customers

#### Get Customer by ID
```http
GET /V1/customers/{customerId}
```
Retrieves detailed information about a specific customer.

**Response Schema**:
```json
{
  "customer": {
    "id": 1,
    "group_id": 1,
    "email": "<EMAIL>",
    "firstname": "John",
    "lastname": "Doe",
    "created_at": "2023-01-01 10:00:00",
    "updated_at": "2023-01-01 10:00:00",
    "addresses": []
  }
}
```

#### Create Customer
```http
POST /V1/customers
```
Creates a new customer account.

**Request Body**:
```json
{
  "customer": {
    "email": "<EMAIL>",
    "firstname": "John",
    "lastname": "Doe",
    "password": "Password123"
  }
}
```

#### Update Customer
```http
PUT /V1/customers/{customerId}
```
Updates customer information.

**Request Body**:
```json
{
  "customer": {
    "email": "<EMAIL>",
    "firstname": "John",
    "lastname": "Doe"
  }
}
```

### Inventory

#### Get Stock Item by SKU
```http
GET /V1/stockItems/{sku}
```
Retrieves stock information for a specific product.

#### Update Stock Item
```http
PUT /V1/products/{sku}/stockItems/{itemId}
```
Updates stock information for a specific product.

**Request Body**:
```json
{
  "stockItem": {
    "qty": 100,
    "is_in_stock": true
  }
}
```

## Error Handling

The API uses standard HTTP status codes:

| Status Code | Description |
|-------------|-------------|
| 200 | Success |
| 400 | Bad Request - Invalid parameters |
| 401 | Unauthorized - Invalid or missing token |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - Resource doesn't exist |
| 500 | Internal Server Error |

Error responses follow this format:
```json
{
  "message": "Error message here",
  "errors": {
    "field_name": ["Error description"]
  }
}
```

## Rate Limiting

- Default rate limit: 100 requests per minute
- Responses include rate limit headers:
  ```http
  X-RateLimit-Limit: 100
  X-RateLimit-Remaining: 95
  X-RateLimit-Reset: 1620000000
  ```

## Best Practices

1. **Pagination**: Always use pagination for list endpoints to avoid timeout issues
2. **Filtering**: Use search criteria to filter results instead of fetching all data
3. **Caching**: Implement caching for frequently accessed data
4. **Bulk Operations**: Use bulk endpoints when processing multiple items
5. **Error Handling**: Always handle error responses appropriately

<ChannelInformation />