---
id: magento-order-status-update
name: Magento Order Status Update
version: 0.0.1
summary: |
  Query to update the status and add comments to a Magento order.
producers:
  - magento-integration-adapter
consumers:
  - magento
owners:
  - enterprise
badges:
  - content: Query
    backgroundColor: blue
    textColor: white
channels:
  - id: magento.{env}.rest.queries
    parameters:
      env: local
schemaPath: 'schema.json'
---

## Overview

The `magento-order-status-update` query allows updating an order's status and adding comments in Magento. This is typically used when the order status changes in the system and needs to be reflected in Magento, along with any relevant comments or notifications.

## Query Details

### Endpoint

```http
POST /V1/orders/{id}/comments
```

### Parameters

- `id` (path parameter, required)
  - Type: integer
  - Description: The order ID to update

### Request Body

```json
{
  "statusHistory": {
    "comment": "string",
    "created_at": "string",
    "entity_name": "string",
    "is_customer_notified": integer,
    "is_visible_on_front": integer,
    "parent_id": integer,
    "status": "string"
  }
}
```

#### Required Fields
- `comment` - Comment text to add to the order
- `is_customer_notified` - Whether to notify the customer (0 or 1)
- `is_visible_on_front` - Whether the comment is visible on the storefront (0 or 1)
- `parent_id` - The order ID this status/comment relates to

#### Optional Fields
- `created_at` - Timestamp when the comment was created
- `entity_id` - Order status history ID
- `entity_name` - Entity name
- `status` - New order status

### Response

The endpoint returns a boolean indicating success or failure:

```json
true
```

### Error Responses

- `401 Unauthorized` - Authentication failed
- `400 Bad Request` - Invalid input data
- Default - Unexpected error

## Usage Examples

### Adding a Comment with Customer Notification

```json
POST /V1/orders/123/comments

{
  "statusHistory": {
    "comment": "Order has been shipped via FedEx",
    "is_customer_notified": 1,
    "is_visible_on_front": 1,
    "parent_id": 123,
    "status": "complete"
  }
}
```

### Adding an Internal Comment

```json
POST /V1/orders/123/comments

{
  "statusHistory": {
    "comment": "Internal note: Order held for fraud check",
    "is_customer_notified": 0,
    "is_visible_on_front": 0,
    "parent_id": 123
  }
}
```

## Security

This endpoint requires proper authentication. All requests must include valid authentication credentials.

## Notes

- The `is_customer_notified` flag determines whether an email notification is sent to the customer
- The `is_visible_on_front` flag controls whether the comment is visible to customers in their order history
- Comments can be added without changing the order status by omitting the `status` field
- The timestamp in `created_at` should be in ISO 8601 format 