{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "required": ["statusHistory"], "properties": {"statusHistory": {"type": "object", "description": "Order status history interface. An order is a document that a web store issues to a customer.", "required": ["comment", "is_customer_notified", "is_visible_on_front", "parent_id"], "properties": {"comment": {"type": "string", "description": "Comment text to add to the order."}, "created_at": {"type": "string", "format": "date-time", "description": "Created-at timestamp."}, "entity_id": {"type": "integer", "description": "Order status history ID."}, "entity_name": {"type": "string", "description": "Entity name."}, "is_customer_notified": {"type": "integer", "enum": [0, 1], "description": "Whether to notify the customer (0 or 1)."}, "is_visible_on_front": {"type": "integer", "enum": [0, 1], "description": "Whether the comment is visible on the storefront (0 or 1)."}, "parent_id": {"type": "integer", "description": "The order ID this status/comment relates to."}, "status": {"type": "string", "description": "New order status."}}}}}