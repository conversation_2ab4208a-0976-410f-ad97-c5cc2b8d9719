---
title: Versioning Working Agreement
summary: Guidelines for versioning commands, events, and queries using semantic versioning and changelog management
---

## Purpose

This working agreement establishes clear guidelines for versioning commands, events, and queries in our EventCatalog to ensure consistent communication of changes and backward compatibility expectations.

## Semantic Versioning Rules

All EventCatalog resources (commands, events, queries) follow **Semantic Versioning (SemVer)** format: `MAJOR.MINOR.PATCH`

### Version Components

- **MAJOR**: Breaking changes that require consumer updates and new version in the `versioned` folder.
- **MINOR**: New functionality added in a backward-compatible manner can be done by incrementing the MINOR version. No need for a new version in the `versioned` folder.
- **PATCH**: Backward-compatible bug fixes and documentation updates

### Version Format
```
MAJOR.MINOR.PATCH
1.2.3
```

## Breaking vs Non-Breaking Changes

### Breaking Changes (MAJOR version bump required)
- **New Version in EventCatalog**: New version in EventCatalog must be created with the breaking changes.
- **New Version in OpenAPI**: New version in OpenAPI must be created with the breaking changes.
- **New Version in Changelog**: New version in Changelog must be created with the breaking changes.

#### Schema Changes
- **Removing fields** from request/response schemas
- **Renaming fields** in request/response schemas
- **Changing field types** (e.g., string → integer)
- **Making optional fields required**
- **Changing field validation rules** (stricter validation)

#### API Changes
- **Removing endpoints**
- **Changing HTTP methods** (POST → PUT)
- **Changing endpoint URLs**
- **Modifying error response formats**

#### Behavioral Changes
- **Changing business logic** that affects expected outcomes
- **Modifying event payload structure**
- **Changing command processing semantics**

#### Examples of Breaking Changes
```yaml
# Breaking: Removing a field
# v1.0.0
properties:
  customer_id: integer
  customer_email: string
  
# v2.0.0 (MAJOR bump)
properties:
  customer_email: string
  # customer_id removed - BREAKING
```

```yaml
# Breaking: Changing field type
# v1.0.0
properties:
  order_total:
    type: string
    
# v2.0.0 (MAJOR bump)
properties:
  order_total:
    type: number  # Changed from string - BREAKING
```

### Non-Breaking Changes (MINOR/PATCH version bump)

#### Safe Schema Changes
- **Adding optional fields** to request/response schemas
- **Making required fields optional**
- **Relaxing validation rules** (less strict validation)
- **Adding new enum values** (if consumers handle unknown values gracefully)

#### Documentation Changes
- **Updating descriptions** and examples
- **Adding usage guidelines**
- **Clarifying field meanings**

#### Implementation Changes
- **Bug fixes** that don't affect schema
- **Performance improvements**
- **Internal refactoring**

#### Examples of Non-Breaking Changes
```yaml
# Non-breaking: Adding optional field
# v1.2.0
properties:
  customer_email: string
  
# v1.3.0 (MINOR bump)
properties:
  customer_email: string
  customer_phone:  # New optional field - NON-BREAKING
    type: string
    required: false
```

## Changelog Requirements

### Pre-1.0.0 Versions
- **All changes require changelog entries**
- Can use `0.x.x` versioning for rapid iteration
- Breaking changes allowed without major version bump
  - A new version in the `versioned` folder is required.

### Post-1.0.0 Versions
- **All changes require changelog entries**
- **Breaking changes require MAJOR version bump**
- **New features require MINOR version bump**
- **Bug fixes require PATCH version bump**

### Changelog Entry Format
```markdown
### YYYY-MM-DD - Version X.Y.Z
Brief description of changes

#### Breaking Changes (if MAJOR version)
- List all breaking changes
- Explain migration path for consumers

#### New Features (if MINOR version)
- List new functionality added

#### Bug Fixes (if PATCH version)
- List bugs fixed

#### Documentation Updates
- List documentation improvements
```

## Implementation Guidelines

### 1. Version Consistency
- **EventCatalog frontmatter** version must match **OpenAPI spec** version
- **Changelog** must document the version being released
- **Git tags** should match the version (optional but recommended)

### 2. Consumer Communication
- **Breaking changes** must be communicated in advance when possible
- **Migration guides** should be provided for major version bumps
- **Deprecation notices** should be added before removing functionality

### 3. Backward Compatibility
- **Support previous major version** for reasonable transition period
- **Maintain old endpoints** during transition when feasible
- **Document migration paths** clearly

## Examples

### Example 1: Adding Optional Field (MINOR)
```markdown
# Before: v1.2.0
# After: v1.3.0

### 2025-01-16 - Version 1.3.0
Added optional customer phone number to order created query

#### New Features
- Added optional `customer_phone` field to order request schema
- Enhanced customer contact information capture
```

### Example 2: Removing Field (MAJOR)
```markdown
# Before: v1.5.0  
# After: v2.0.0

### 2025-01-16 - Version 2.0.0
Removed deprecated customer_legacy_id field

#### Breaking Changes
- Removed `customer_legacy_id` field from order request schema
- Consumers must use `customer_id` field instead
- Migration: Replace all `customer_legacy_id` references with `customer_id`
```

### Example 3: Bug Fix (PATCH)
```markdown
# Before: v1.3.2
# After: v1.3.3

### 2025-01-16 - Version 1.3.3
Fixed order total calculation bug

#### Bug Fixes
- Fixed issue where tax calculation was incorrect for international orders
- Corrected validation logic for postal codes
```

## Version Strategy by Resource Type

### Queries (REST APIs)
- **Major**: Schema changes, endpoint changes
- **Minor**: New optional parameters, new response fields
- **Patch**: Bug fixes, documentation updates

### Events
- **Major**: Payload schema changes, event name changes
- **Minor**: New optional payload fields
- **Patch**: Documentation updates, internal processing fixes

### Commands
- **Major**: Command schema changes, processing behavior changes
- **Minor**: New optional command fields
- **Patch**: Bug fixes, validation improvements

## Enforcement

### Required Checks
1. **Version consistency** across all files
3. **Breaking change** documentation for major versions (unless version < 1.0.0)
4. **Semantic versioning** compliance

### Review Process
- **Major version bumps** require agreement and communicationwith all teams
- **Breaking changes** require consumer impact assessment