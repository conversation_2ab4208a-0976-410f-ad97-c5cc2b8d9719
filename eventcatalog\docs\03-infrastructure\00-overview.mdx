---
title: Overview
summary: |
  Infrastructure domain containing cloud architecture and deployment flows
---

## Overview

The Infrastructure encompasses all cloud architecture, deployment patterns, and infrastructure-as-code configurations used across our services. This includes AWS cloud resources, networking configurations, and security implementations.


### Key Components

### External Access Layer
- DNS Resolution for domain routing
- AWS WAF for web application firewall protection
- Amazon CloudFront for content delivery and caching
- Internet Gateway for VPC internet access

### Network Architecture
- Multi-AZ deployment across three Availability Zones
- Three-tier subnet architecture:
  - Public Subnets (DMZ)
  - Private Subnets (Application Layer)
  - Protected Subnets (Data Layer)
- NAT Gateways for outbound internet access from private subnets

### Application Services
- Application Load Balancer for traffic distribution
- ECS Fargate Cluster for container orchestration
- Amazon MQ Magento for message queuing
- OpenSearch for search and analytics

### Storage and Database
- Amazon EFS for shared file storage
- RDS with writer/reader configuration
- RDS Proxy for connection pooling
- PIM Database
- Valkey Session and Cache stores

### Security
- AWS Client VPN for secure remote access
- Network segmentation with security groups and NACLs
- WAF rules for application security

## Design Considerations

### High Availability
- Multi-AZ deployment ensures service continuity
- Redundant NAT Gateways and load balancers
- Database replication with reader/writer configuration

### Security
- Layered security approach with WAF, security groups, and NACLs
- Private subnets for application and data layers
- VPN access for secure management

### Scalability
- ECS Fargate for container orchestration and scaling
- Auto-scaling groups for compute resources
- CloudFront for content delivery and caching

### Performance
- RDS Proxy for efficient database connection management
- EFS for shared storage across availability zones
- OpenSearch for fast search and analytics capabilities 