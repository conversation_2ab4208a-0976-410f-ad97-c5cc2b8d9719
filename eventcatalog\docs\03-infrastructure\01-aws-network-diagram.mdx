---
title: Network Diagram
summary: Visual representation of the AWS network for Evolution
---

<svg xmlns="http://www.w3.org/2000/svg" style="cursor:pointer;max-width:100%;max-height:1516px;" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="2636px" viewBox="-0.5 -0.5 2636 1516" content="&lt;mxfile host=&quot;app.diagrams.net&quot; agent=&quot;Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/20100101 Firefox/139.0&quot; version=&quot;27.1.4&quot;&gt;&#xA;  &lt;diagram name=&quot;Page-1&quot; id=&quot;9ikrW6oGRq05r8q0t4vx&quot;&gt;&#xA;    &lt;mxGraphModel dx=&quot;3030&quot; dy=&quot;129&quot; grid=&quot;1&quot; gridSize=&quot;10&quot; guides=&quot;1&quot; tooltips=&quot;1&quot; connect=&quot;1&quot; arrows=&quot;1&quot; fold=&quot;1&quot; page=&quot;1&quot; pageScale=&quot;1&quot; pageWidth=&quot;850&quot; pageHeight=&quot;1100&quot; math=&quot;0&quot; shadow=&quot;0&quot;&gt;&#xA;      &lt;root&gt;&#xA;        &lt;mxCell id=&quot;0&quot; /&gt;&#xA;        &lt;mxCell id=&quot;1&quot; parent=&quot;0&quot; /&gt;&#xA;        &lt;mxCell id=&quot;8VBJgbjWb-JPUTvmKhwd-16&quot; value=&quot;Ripon on-prem SAP&quot; style=&quot;image;sketch=0;aspect=fixed;html=1;points=[];align=center;fontSize=12;image=img/lib/mscae/SAP_HANA_on_Azure.svg;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;1172.5&quot; y=&quot;3010&quot; width=&quot;50&quot; height=&quot;25&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;8VBJgbjWb-JPUTvmKhwd-17&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;curved=1;&quot; parent=&quot;1&quot; source=&quot;8VBJgbjWb-JPUTvmKhwd-16&quot; target=&quot;8VBJgbjWb-JPUTvmKhwd-14&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot; value=&quot;&quot; style=&quot;swimlane;startSize=0;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;-30&quot; y=&quot;1590&quot; width=&quot;2455&quot; height=&quot;1330&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;x-eJfxxCqEvTNtFwHw6N-4&quot; value=&quot;Amazon Web Services (us-east-2)&quot; style=&quot;text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;&quot; vertex=&quot;1&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;25&quot; y=&quot;10&quot; width=&quot;210&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;8VBJgbjWb-JPUTvmKhwd-35&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot; source=&quot;0dZXfTm35kw52Q68pXVM-1&quot; target=&quot;0dZXfTm35kw52Q68pXVM-4&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;0dZXfTm35kw52Q68pXVM-1&quot; value=&quot;&amp;lt;div&amp;gt;AWS Route 53 (DNS)&amp;lt;/div&amp;gt;&quot; style=&quot;outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.route_53;fillColor=#F58536;gradientColor=none;&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot; vertex=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;50&quot; y=&quot;677.5&quot; width=&quot;70.5&quot; height=&quot;85.5&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;0dZXfTm35kw52Q68pXVM-4&quot; value=&quot;AWS Web&amp;amp;nbsp;&amp;lt;br&amp;gt;Application&amp;amp;nbsp;&amp;lt;br&amp;gt;Firewall (WAF)&quot; style=&quot;outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.waf;fillColor=#759C3E;gradientColor=none;&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot; vertex=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;200&quot; y=&quot;673.75&quot; width=&quot;76.5&quot; height=&quot;93&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;8VBJgbjWb-JPUTvmKhwd-37&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot; source=&quot;0dZXfTm35kw52Q68pXVM-5&quot; target=&quot;8VBJgbjWb-JPUTvmKhwd-36&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;0dZXfTm35kw52Q68pXVM-5&quot; value=&quot;&amp;lt;div&amp;gt;AWS Cloudfront (CDN)&amp;lt;/div&amp;gt;&quot; style=&quot;outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.cloudfront;fillColor=#F58536;gradientColor=none;&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot; vertex=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;415&quot; y=&quot;982.5&quot; width=&quot;76.5&quot; height=&quot;93&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;0dZXfTm35kw52Q68pXVM-6&quot; value=&quot;Evolution Foundation VPC&quot; style=&quot;sketch=0;outlineConnect=0;gradientColor=none;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_vpc;strokeColor=#879196;fillColor=none;verticalAlign=top;align=left;spacingLeft=30;fontColor=#879196;dashed=0;&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot; vertex=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;600&quot; y=&quot;80&quot; width=&quot;1770&quot; height=&quot;1030&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;0dZXfTm35kw52Q68pXVM-7&quot; value=&quot;Public Subnets (AZ1, AZ2, AZ3)&quot; style=&quot;sketch=0;outlineConnect=0;gradientColor=none;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_subnet;strokeColor=#879196;fillColor=none;verticalAlign=top;align=left;spacingLeft=30;fontColor=#879196;dashed=0;&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot; vertex=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;780&quot; y=&quot;174&quot; width=&quot;350&quot; height=&quot;776&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;8VBJgbjWb-JPUTvmKhwd-10&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot; source=&quot;0dZXfTm35kw52Q68pXVM-8&quot; target=&quot;0dZXfTm35kw52Q68pXVM-11&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;0dZXfTm35kw52Q68pXVM-8&quot; value=&quot;NAT Gateway AZ1&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#8C4FFF;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.nat_gateway;&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot; vertex=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;910&quot; y=&quot;260&quot; width=&quot;78&quot; height=&quot;78&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;8VBJgbjWb-JPUTvmKhwd-9&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot; source=&quot;0dZXfTm35kw52Q68pXVM-9&quot; target=&quot;0dZXfTm35kw52Q68pXVM-14&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;0dZXfTm35kw52Q68pXVM-9&quot; value=&quot;NAT Gateway AZ2&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#8C4FFF;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.nat_gateway;&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot; vertex=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;910&quot; y=&quot;390&quot; width=&quot;78&quot; height=&quot;78&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;8VBJgbjWb-JPUTvmKhwd-7&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot; source=&quot;0dZXfTm35kw52Q68pXVM-10&quot; target=&quot;0dZXfTm35kw52Q68pXVM-16&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;0dZXfTm35kw52Q68pXVM-10&quot; value=&quot;NAT Gateway AZ3&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#8C4FFF;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.nat_gateway;&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot; vertex=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;910&quot; y=&quot;520&quot; width=&quot;78&quot; height=&quot;78&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;0dZXfTm35kw52Q68pXVM-11&quot; value=&quot;Elastic IP 1&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#ED7100;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.elastic_ip_address;&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot; vertex=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;510&quot; y=&quot;289&quot; width=&quot;48&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;0dZXfTm35kw52Q68pXVM-14&quot; value=&quot;Elastic IP 2&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#ED7100;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.elastic_ip_address;&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot; vertex=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;510&quot; y=&quot;419&quot; width=&quot;48&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;0dZXfTm35kw52Q68pXVM-16&quot; value=&quot;Elastic IP 1&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#ED7100;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.elastic_ip_address;&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot; vertex=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;510&quot; y=&quot;549&quot; width=&quot;48&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;0dZXfTm35kw52Q68pXVM-18&quot; value=&quot;External Application&amp;amp;nbsp;&amp;lt;br&amp;gt;Load Balancer (ALB)&quot; style=&quot;outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.application_load_balancer;fillColor=#F58534;gradientColor=none;&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot; vertex=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;914.5&quot; y=&quot;684.25&quot; width=&quot;69&quot; height=&quot;72&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;0dZXfTm35kw52Q68pXVM-27&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot; source=&quot;x-eJfxxCqEvTNtFwHw6N-26&quot; target=&quot;0dZXfTm35kw52Q68pXVM-18&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;0dZXfTm35kw52Q68pXVM-26&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot; source=&quot;0dZXfTm35kw52Q68pXVM-4&quot; target=&quot;x-eJfxxCqEvTNtFwHw6N-26&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint x=&quot;640&quot; y=&quot;720.25&quot; as=&quot;targetPoint&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;0dZXfTm35kw52Q68pXVM-28&quot; value=&quot;Private Subnets (AZ1, AZ2, AZ3)&quot; style=&quot;sketch=0;outlineConnect=0;gradientColor=none;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_subnet;strokeColor=#879196;fillColor=none;verticalAlign=top;align=left;spacingLeft=30;fontColor=#879196;dashed=0;&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot; vertex=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;1150&quot; y=&quot;174&quot; width=&quot;710&quot; height=&quot;776&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;0dZXfTm35kw52Q68pXVM-33&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.25;exitDx=0;exitDy=0;curved=1;&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot; source=&quot;0dZXfTm35kw52Q68pXVM-30&quot; target=&quot;0dZXfTm35kw52Q68pXVM-10&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;8VBJgbjWb-JPUTvmKhwd-20&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.25;exitDx=0;exitDy=0;curved=1;&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot; source=&quot;0dZXfTm35kw52Q68pXVM-30&quot; target=&quot;0dZXfTm35kw52Q68pXVM-9&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;8VBJgbjWb-JPUTvmKhwd-21&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.25;exitDx=0;exitDy=0;curved=1;&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot; source=&quot;0dZXfTm35kw52Q68pXVM-30&quot; target=&quot;0dZXfTm35kw52Q68pXVM-8&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;0dZXfTm35kw52Q68pXVM-30&quot; value=&quot;ECS Fargate Cluster&quot; style=&quot;points=[[0,0],[0.25,0],[0.5,0],[0.75,0],[1,0],[1,0.25],[1,0.5],[1,0.75],[1,1],[0.75,1],[0.5,1],[0.25,1],[0,1],[0,0.75],[0,0.5],[0,0.25]];outlineConnect=0;gradientColor=none;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;container=1;pointerEvents=0;collapsible=0;recursiveResize=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_ec2_instance_contents;strokeColor=#D86613;fillColor=none;verticalAlign=top;align=left;spacingLeft=30;fontColor=#D86613;dashed=0;&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot; vertex=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;1200&quot; y=&quot;210&quot; width=&quot;270&quot; height=&quot;680&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;8VBJgbjWb-JPUTvmKhwd-11&quot; value=&quot;Axon Integration Adapter API&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;fillColor=#ED7100;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.fargate;&quot; parent=&quot;0dZXfTm35kw52Q68pXVM-30&quot; vertex=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;80&quot; y=&quot;320&quot; width=&quot;78&quot; height=&quot;78&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;8VBJgbjWb-JPUTvmKhwd-12&quot; value=&quot;Axon Domain Service&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;fillColor=#ED7100;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.fargate;&quot; parent=&quot;0dZXfTm35kw52Q68pXVM-30&quot; vertex=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;80&quot; y=&quot;450&quot; width=&quot;78&quot; height=&quot;78&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;8VBJgbjWb-JPUTvmKhwd-19&quot; value=&quot;Magento&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;fillColor=#ED7100;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.fargate;&quot; parent=&quot;0dZXfTm35kw52Q68pXVM-30&quot; vertex=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;80&quot; y=&quot;70&quot; width=&quot;78&quot; height=&quot;78&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;8VBJgbjWb-JPUTvmKhwd-18&quot; value=&quot;&amp;lt;div&amp;gt;Pimcore&amp;lt;/div&amp;gt;&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;fillColor=#ED7100;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.fargate;&quot; parent=&quot;0dZXfTm35kw52Q68pXVM-30&quot; vertex=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;80&quot; y=&quot;200&quot; width=&quot;78&quot; height=&quot;78&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;8VBJgbjWb-JPUTvmKhwd-13&quot; value=&quot;Event Catalog&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;fillColor=#ED7100;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.fargate;&quot; parent=&quot;0dZXfTm35kw52Q68pXVM-30&quot; vertex=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;80&quot; y=&quot;570&quot; width=&quot;78&quot; height=&quot;78&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;8VBJgbjWb-JPUTvmKhwd-14&quot; value=&quot;Site-to-Site VPN&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;fillColor=#8C4FFF;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.site_to_site_vpn;&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot; vertex=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;960&quot; y=&quot;1003.5&quot; width=&quot;78&quot; height=&quot;78&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;8VBJgbjWb-JPUTvmKhwd-15&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;curved=1;&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot; source=&quot;8VBJgbjWb-JPUTvmKhwd-14&quot; target=&quot;x-eJfxxCqEvTNtFwHw6N-26&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;Array as=&quot;points&quot;&gt;&#xA;              &lt;mxPoint x=&quot;999&quot; y=&quot;900&quot; /&gt;&#xA;              &lt;mxPoint x=&quot;690&quot; y=&quot;900&quot; /&gt;&#xA;              &lt;mxPoint x=&quot;690&quot; y=&quot;740&quot; /&gt;&#xA;            &lt;/Array&gt;&#xA;            &lt;mxPoint x=&quot;640&quot; y=&quot;740.0769230769233&quot; as=&quot;targetPoint&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;8VBJgbjWb-JPUTvmKhwd-22&quot; value=&quot;&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#C925D1;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.rds_proxy;&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot; vertex=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;1530&quot; y=&quot;338&quot; width=&quot;78&quot; height=&quot;78&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;8VBJgbjWb-JPUTvmKhwd-23&quot; value=&quot;EFS Mount&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#7AA116;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.elastic_file_system_standard;&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot; vertex=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;1530&quot; y=&quot;210&quot; width=&quot;78&quot; height=&quot;78&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;8VBJgbjWb-JPUTvmKhwd-24&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot; source=&quot;8VBJgbjWb-JPUTvmKhwd-19&quot; target=&quot;8VBJgbjWb-JPUTvmKhwd-23&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;8VBJgbjWb-JPUTvmKhwd-25&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot; source=&quot;8VBJgbjWb-JPUTvmKhwd-19&quot; target=&quot;8VBJgbjWb-JPUTvmKhwd-22&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;8VBJgbjWb-JPUTvmKhwd-26&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;curved=1;&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot; source=&quot;8VBJgbjWb-JPUTvmKhwd-12&quot; target=&quot;8VBJgbjWb-JPUTvmKhwd-22&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;8VBJgbjWb-JPUTvmKhwd-28&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;curved=1;&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot; source=&quot;8VBJgbjWb-JPUTvmKhwd-22&quot; target=&quot;8VBJgbjWb-JPUTvmKhwd-27&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;0dZXfTm35kw52Q68pXVM-29&quot; value=&quot;Protected Subnets (AZ1, AZ2, AZ3)&quot; style=&quot;sketch=0;outlineConnect=0;gradientColor=none;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_subnet;strokeColor=#879196;fillColor=none;verticalAlign=top;align=left;spacingLeft=30;fontColor=#879196;dashed=0;&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot; vertex=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;1890&quot; y=&quot;171&quot; width=&quot;420&quot; height=&quot;776&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;8VBJgbjWb-JPUTvmKhwd-27&quot; value=&quot;&amp;lt;div&amp;gt;AWS Aurora Primary Node&amp;lt;/div&amp;gt;&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;fillColor=#C925D1;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.aurora;&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot; vertex=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;1960&quot; y=&quot;338&quot; width=&quot;78&quot; height=&quot;78&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;8VBJgbjWb-JPUTvmKhwd-29&quot; value=&quot;&amp;lt;div&amp;gt;AWS Aurora Read Node&amp;lt;/div&amp;gt;&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;fillColor=#C925D1;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.aurora;&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot; vertex=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;1960&quot; y=&quot;450&quot; width=&quot;78&quot; height=&quot;78&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;8VBJgbjWb-JPUTvmKhwd-34&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot; source=&quot;8VBJgbjWb-JPUTvmKhwd-30&quot; target=&quot;8VBJgbjWb-JPUTvmKhwd-33&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;8VBJgbjWb-JPUTvmKhwd-30&quot; value=&quot;&amp;lt;div&amp;gt;AWS Aurora Read Node&amp;lt;/div&amp;gt;&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;fillColor=#C925D1;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.aurora;&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot; vertex=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;1960&quot; y=&quot;569&quot; width=&quot;78&quot; height=&quot;78&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;8VBJgbjWb-JPUTvmKhwd-31&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;curved=1;&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot; source=&quot;8VBJgbjWb-JPUTvmKhwd-22&quot; target=&quot;8VBJgbjWb-JPUTvmKhwd-29&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;8VBJgbjWb-JPUTvmKhwd-32&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;curved=1;&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot; source=&quot;8VBJgbjWb-JPUTvmKhwd-22&quot; target=&quot;8VBJgbjWb-JPUTvmKhwd-30&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;8VBJgbjWb-JPUTvmKhwd-33&quot; value=&quot;&amp;lt;div&amp;gt;Elasticache&amp;lt;/div&amp;gt;&amp;lt;div&amp;gt;for Valkey&amp;lt;/div&amp;gt;&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#C925D1;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.elasticache_for_valkey;&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot; vertex=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;2170&quot; y=&quot;573.5&quot; width=&quot;78&quot; height=&quot;69&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;8VBJgbjWb-JPUTvmKhwd-36&quot; value=&quot;SPA&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#7AA116;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.bucket;&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot; vertex=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;643&quot; y=&quot;990&quot; width=&quot;75&quot; height=&quot;78&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;8VBJgbjWb-JPUTvmKhwd-39&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;curved=1;&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot; source=&quot;0dZXfTm35kw52Q68pXVM-4&quot; target=&quot;0dZXfTm35kw52Q68pXVM-5&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;x-eJfxxCqEvTNtFwHw6N-1&quot; value=&quot;SNS&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;fillColor=#E7157B;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.sns;&quot; vertex=&quot;1&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;1710&quot; y=&quot;1180&quot; width=&quot;78&quot; height=&quot;78&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;x-eJfxxCqEvTNtFwHw6N-2&quot; value=&quot;SQS&quot; style=&quot;sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;fillColor=#E7157B;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.sqs;&quot; vertex=&quot;1&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;1810&quot; y=&quot;1180&quot; width=&quot;78&quot; height=&quot;78&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;x-eJfxxCqEvTNtFwHw6N-8&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1.002;exitY=0.488;exitDx=0;exitDy=0;exitPerimeter=0;curved=1;&quot; edge=&quot;1&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot; source=&quot;x-eJfxxCqEvTNtFwHw6N-5&quot; target=&quot;x-eJfxxCqEvTNtFwHw6N-2&quot;&gt;&#xA;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;x-eJfxxCqEvTNtFwHw6N-9&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1.01;exitY=0.504;exitDx=0;exitDy=0;exitPerimeter=0;curved=1;&quot; edge=&quot;1&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot; source=&quot;x-eJfxxCqEvTNtFwHw6N-5&quot; target=&quot;x-eJfxxCqEvTNtFwHw6N-1&quot;&gt;&#xA;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;x-eJfxxCqEvTNtFwHw6N-5&quot; value=&quot;&amp;lt;div&amp;gt;&amp;lt;br&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div&amp;gt;&amp;lt;br&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div&amp;gt;&amp;lt;br&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div&amp;gt;&amp;lt;br&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div&amp;gt;&amp;lt;br&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div&amp;gt;&amp;lt;br&amp;gt;&amp;lt;/div&amp;gt;&amp;lt;div&amp;gt;Private Link&amp;lt;br&amp;gt;(SNS &amp;amp;amp; SQS)&amp;lt;/div&amp;gt;&quot; style=&quot;points=[];aspect=fixed;html=1;align=center;shadow=0;dashed=0;fillColor=#FF6A00;strokeColor=none;shape=mxgraph.alibaba_cloud.privatelink;&quot; vertex=&quot;1&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;1630&quot; y=&quot;642.5&quot; width=&quot;56.1&quot; height=&quot;56.699999999999996&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;x-eJfxxCqEvTNtFwHw6N-6&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.535;entryY=-0.044;entryDx=0;entryDy=0;entryPerimeter=0;curved=1;&quot; edge=&quot;1&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot; source=&quot;8VBJgbjWb-JPUTvmKhwd-11&quot; target=&quot;x-eJfxxCqEvTNtFwHw6N-5&quot;&gt;&#xA;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint x=&quot;1693&quot; y=&quot;640&quot; as=&quot;targetPoint&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;x-eJfxxCqEvTNtFwHw6N-7&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.75;exitDx=0;exitDy=0;exitPerimeter=0;curved=1;&quot; edge=&quot;1&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot; source=&quot;8VBJgbjWb-JPUTvmKhwd-12&quot; target=&quot;x-eJfxxCqEvTNtFwHw6N-5&quot;&gt;&#xA;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;x-eJfxxCqEvTNtFwHw6N-13&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;curved=1;&quot; edge=&quot;1&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot; source=&quot;0dZXfTm35kw52Q68pXVM-18&quot; target=&quot;8VBJgbjWb-JPUTvmKhwd-13&quot;&gt;&#xA;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;x-eJfxxCqEvTNtFwHw6N-14&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;curved=1;&quot; edge=&quot;1&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot; source=&quot;0dZXfTm35kw52Q68pXVM-18&quot; target=&quot;8VBJgbjWb-JPUTvmKhwd-18&quot;&gt;&#xA;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;x-eJfxxCqEvTNtFwHw6N-15&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;curved=1;&quot; edge=&quot;1&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot; source=&quot;0dZXfTm35kw52Q68pXVM-18&quot; target=&quot;8VBJgbjWb-JPUTvmKhwd-19&quot;&gt;&#xA;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;x-eJfxxCqEvTNtFwHw6N-19&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;&quot; edge=&quot;1&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot; source=&quot;x-eJfxxCqEvTNtFwHw6N-16&quot; target=&quot;x-eJfxxCqEvTNtFwHw6N-1&quot;&gt;&#xA;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;x-eJfxxCqEvTNtFwHw6N-16&quot; value=&quot;&amp;lt;div&amp;gt;Parts Images&amp;lt;/div&amp;gt;&quot; style=&quot;sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#7AA116;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.bucket;&quot; vertex=&quot;1&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;1460&quot; y=&quot;1180&quot; width=&quot;67.31&quot; height=&quot;70&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;x-eJfxxCqEvTNtFwHw6N-22&quot; value=&quot;Internal Application&amp;amp;nbsp;&amp;lt;br&amp;gt;Load Balancer (ALB)&quot; style=&quot;outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.application_load_balancer;fillColor=#F58534;gradientColor=none;&quot; vertex=&quot;1&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;1641&quot; y=&quot;460&quot; width=&quot;69&quot; height=&quot;72&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;x-eJfxxCqEvTNtFwHw6N-24&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;curved=1;exitX=1;exitY=0.75;exitDx=0;exitDy=0;exitPerimeter=0;&quot; edge=&quot;1&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot; source=&quot;8VBJgbjWb-JPUTvmKhwd-19&quot; target=&quot;x-eJfxxCqEvTNtFwHw6N-22&quot;&gt;&#xA;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;Array as=&quot;points&quot;&gt;&#xA;              &lt;mxPoint x=&quot;1358&quot; y=&quot;490&quot; /&gt;&#xA;            &lt;/Array&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;x-eJfxxCqEvTNtFwHw6N-25&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.25;exitDx=0;exitDy=0;exitPerimeter=0;curved=1;&quot; edge=&quot;1&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot; source=&quot;8VBJgbjWb-JPUTvmKhwd-11&quot; target=&quot;x-eJfxxCqEvTNtFwHw6N-22&quot;&gt;&#xA;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;x-eJfxxCqEvTNtFwHw6N-26&quot; value=&quot;Internet&amp;lt;br&amp;gt;&amp;lt;div&amp;gt;Gateway&amp;lt;/div&amp;gt;&quot; style=&quot;outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.internet_gateway;fillColor=#F58534;gradientColor=none;&quot; vertex=&quot;1&quot; parent=&quot;x-eJfxxCqEvTNtFwHw6N-3&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;750&quot; y=&quot;684.25&quot; width=&quot;69&quot; height=&quot;72&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;x-eJfxxCqEvTNtFwHw6N-21&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;exitPerimeter=0;curved=1;entryX=0.008;entryY=0.518;entryDx=0;entryDy=0;entryPerimeter=0;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;x-eJfxxCqEvTNtFwHw6N-20&quot; target=&quot;x-eJfxxCqEvTNtFwHw6N-16&quot;&gt;&#xA;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;x-eJfxxCqEvTNtFwHw6N-20&quot; value=&quot;Visual SKUs&amp;lt;br&amp;gt;&amp;lt;div&amp;gt;(DAM)&amp;lt;/div&amp;gt;&quot; style=&quot;outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.ec2;fillColor=#F58534;gradientColor=none;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;1340&quot; y=&quot;2976&quot; width=&quot;76.5&quot; height=&quot;93&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;x-eJfxxCqEvTNtFwHw6N-29&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;curved=1;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;x-eJfxxCqEvTNtFwHw6N-28&quot; target=&quot;0dZXfTm35kw52Q68pXVM-1&quot;&gt;&#xA;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;x-eJfxxCqEvTNtFwHw6N-28&quot; value=&quot;Parts King&quot; style=&quot;whiteSpace=wrap;html=1;aspect=fixed;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;-210&quot; y=&quot;2187.5&quot; width=&quot;80&quot; height=&quot;80&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;x-eJfxxCqEvTNtFwHw6N-31&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;curved=1;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;x-eJfxxCqEvTNtFwHw6N-30&quot; target=&quot;0dZXfTm35kw52Q68pXVM-1&quot;&gt;&#xA;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;x-eJfxxCqEvTNtFwHw6N-30&quot; value=&quot;Parts West&quot; style=&quot;whiteSpace=wrap;html=1;aspect=fixed;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;-210&quot; y=&quot;2310&quot; width=&quot;80&quot; height=&quot;80&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;x-eJfxxCqEvTNtFwHw6N-32&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1.023;entryY=0.86;entryDx=0;entryDy=0;entryPerimeter=0;exitX=0;exitY=0.79;exitDx=0;exitDy=0;exitPerimeter=0;curved=1;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;0dZXfTm35kw52Q68pXVM-5&quot; target=&quot;x-eJfxxCqEvTNtFwHw6N-30&quot;&gt;&#xA;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;Array as=&quot;points&quot;&gt;&#xA;              &lt;mxPoint x=&quot;-60&quot; y=&quot;2646&quot; /&gt;&#xA;              &lt;mxPoint x=&quot;-60&quot; y=&quot;2379&quot; /&gt;&#xA;            &lt;/Array&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;x-eJfxxCqEvTNtFwHw6N-33&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.75;entryDx=0;entryDy=0;curved=1;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;0dZXfTm35kw52Q68pXVM-5&quot; target=&quot;x-eJfxxCqEvTNtFwHw6N-28&quot;&gt;&#xA;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;Array as=&quot;points&quot;&gt;&#xA;              &lt;mxPoint x=&quot;-40&quot; y=&quot;2619&quot; /&gt;&#xA;              &lt;mxPoint x=&quot;-40&quot; y=&quot;2248&quot; /&gt;&#xA;            &lt;/Array&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;      &lt;/root&gt;&#xA;    &lt;/mxGraphModel&gt;&#xA;  &lt;/diagram&gt;&#xA;&lt;/mxfile&gt;&#xA;" onclick="(function(svg){var src=window.event.target||window.event.srcElement;while (src!=null&amp;&amp;src.nodeName.toLowerCase()!='a'){src=src.parentNode;}if(src==null){if(svg.wnd!=null&amp;&amp;!svg.wnd.closed){svg.wnd.focus();}else{var r=function(evt){if(evt.data=='ready'&amp;&amp;evt.source==svg.wnd){svg.wnd.postMessage(decodeURIComponent(svg.getAttribute('content')),'*');window.removeEventListener('message',r);}};window.addEventListener('message',r);svg.wnd=window.open('https://viewer.diagrams.net/?client=1&amp;page=0&amp;edit=_blank');}}})(this);"><defs/><g><g data-cell-id="0"><g data-cell-id="1"><g data-cell-id="8VBJgbjWb-JPUTvmKhwd-16"><g><image x="1382" y="1419.5" width="50" height="25" xlink:href="https://app.diagrams.net/img/lib/mscae/SAP_HANA_on_Azure.svg"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 1452px; margin-left: 1408px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">Ripon on-prem SAP</div></div></div></foreignObject><text x="1408" y="1464" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Ripon on...</text></switch></g></g></g><g data-cell-id="8VBJgbjWb-JPUTvmKhwd-17"><g><path d="M 1382.5 1432.54 Q 1300.31 1432.54 1300.31 1237.54 Q 1300.31 1042.54 1224.37 1042.5" fill="none" stroke="#000000" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1219.12 1042.5 L 1226.12 1039 L 1224.37 1042.5 L 1226.12 1046 Z" fill="#000000" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="x-eJfxxCqEvTNtFwHw6N-3"><g><path d="M 180 0 L 180 0 L 2635 0 L 2635 0" fill="#ffffff" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 180 0 L 180 1330 L 2635 1330 L 2635 0" fill="none" stroke="#000000" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke-miterlimit="10" pointer-events="none"/></g><g data-cell-id="x-eJfxxCqEvTNtFwHw6N-4"><g><rect x="205" y="10" width="210" height="30" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 25px; margin-left: 310px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: nowrap; ">Amazon Web Services (us-east-2)</div></div></div></foreignObject><text x="310" y="29" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Amazon Web Services (us-east-2)</text></switch></g></g></g><g data-cell-id="8VBJgbjWb-JPUTvmKhwd-35"><g><path d="M 300.5 720.23 L 340.31 720.23 L 373.63 720.23" fill="none" stroke="#000000" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 378.88 720.23 L 371.88 723.73 L 373.63 720.23 L 371.88 716.73 Z" fill="#000000" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="0dZXfTm35kw52Q68pXVM-1"><g><path d="M 265.14 763 L 250.28 755.48 L 250.28 708.96 L 230 706.68 L 230 695.18 L 234.53 692.9 L 250.28 696.7 L 250.28 685.02 L 265.14 677.5 L 280.1 685.02 L 280.1 720.26 L 300.5 720.24 L 300.5 730.27 L 296.75 731.06 L 280.1 729.59 L 280.1 755.48 Z" fill="#f58536" style="fill: light-dark(rgb(245, 133, 54), rgb(189, 92, 24));" stroke="none" pointer-events="all"/><path d="M 234.53 705.42 L 230 706.68 L 230 695.18 L 234.53 692.9 Z M 265.14 763 L 250.28 755.48 L 250.28 708.96 L 254.36 709.42 L 259.58 708.61 L 259.58 698.93 L 250.28 696.7 L 250.28 685.02 L 265.14 677.5 Z M 296.75 731.06 L 270.68 728.68 L 270.68 720.26 L 296.75 720.26 Z" fill-opacity="0.3" fill="#000000" style="fill: light-dark(rgb(0, 0, 0), rgb(237, 237, 237));" stroke="none" pointer-events="all"/><path d="M 254.36 709.42 L 230 706.68 L 234.53 705.42 L 259.58 708.61 Z" fill-opacity="0.5" fill="#000000" style="fill: light-dark(rgb(0, 0, 0), rgb(237, 237, 237));" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 770px; margin-left: 265px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: nowrap; "><div>AWS Route 53 (DNS)</div></div></div></div></foreignObject><text x="265" y="782" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">AWS Route 53...</text></switch></g></g></g><g data-cell-id="0dZXfTm35kw52Q68pXVM-4"><g><path d="M 456.5 747.45 L 418.26 766.75 L 380 747.47 L 380 725.66 L 385.83 725.45 L 391.01 725.9 L 391.01 731.53 L 402.1 733.52 L 402.1 681.88 L 418.26 673.75 L 434.41 681.88 L 434.41 733.5 L 445.4 731.53 L 445.4 725.9 L 450.65 725.45 L 456.5 725.68 Z" fill="#759c3e" style="fill: light-dark(rgb(117, 156, 62), rgb(95, 129, 48));" stroke="none" pointer-events="all"/><path d="M 451.56 726.23 L 445.4 725.9 L 450.65 725.45 L 456.5 725.68 Z M 429.7 736.56 L 429.7 734.33 L 445.4 731.53 L 451.56 732.1 Z M 418.26 729.51 L 406.71 728.37 L 418.26 727.47 L 429.71 728.37 Z M 391.01 725.9 L 384.83 726.23 L 380 725.66 L 385.83 725.45 Z M 406.7 736.53 L 384.83 732.07 L 391.01 731.53 L 406.7 734.33 Z" fill-opacity="0.3" fill="#ffffff" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));" stroke="none" pointer-events="all"/><path d="M 451.56 732.1 L 445.4 731.53 L 445.4 725.9 L 451.56 726.23 Z M 418.26 766.75 L 380 747.47 L 380 725.66 L 384.83 726.23 L 384.83 732.07 L 406.7 736.53 L 406.7 734.33 L 402.1 733.52 L 402.1 681.88 L 418.26 673.75 L 418.26 727.47 L 406.71 728.37 L 418.26 729.51 Z" fill-opacity="0.3" fill="#000000" style="fill: light-dark(rgb(0, 0, 0), rgb(237, 237, 237));" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 774px; margin-left: 418px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: nowrap; ">AWS Web <br />Application <br />Firewall (WAF)</div></div></div></foreignObject><text x="418" y="786" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">AWS Web...</text></switch></g></g></g><g data-cell-id="8VBJgbjWb-JPUTvmKhwd-37"><g><path d="M 671.5 1029 L 816.64 1029" fill="none" stroke="#000000" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 821.89 1029 L 814.89 1032.5 L 816.64 1029 L 814.89 1025.5 Z" fill="#000000" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="0dZXfTm35kw52Q68pXVM-5"><g><path d="M 671.5 1021.05 L 654.83 1022.09 L 644.65 1021.02 L 644.65 1018.81 L 633.3 1019.94 L 621.76 1018.82 L 621.76 1021.03 L 611.93 1022.07 L 595 1021.06 L 595 1001.83 L 604.2 997.18 L 614.83 999.98 L 614.83 991.82 L 633.3 982.5 L 651.56 991.72 L 651.56 1000 L 662.39 997.17 L 671.5 1001.81 Z M 633.3 1075.5 L 614.83 1066.18 L 614.83 1057.99 L 604.2 1060.83 L 595 1056.17 L 595 1036.95 L 611.76 1035.93 L 621.76 1036.91 L 621.76 1039.18 L 633.3 1038.06 L 644.65 1039.18 L 644.65 1036.91 L 654.83 1035.93 L 671.5 1036.98 L 671.5 1056.26 L 662.39 1060.83 L 651.56 1057.96 L 651.56 1066.25 Z" fill="#f58536" style="fill: light-dark(rgb(245, 133, 54), rgb(189, 92, 24));" stroke="none" pointer-events="all"/><path d="M 644.65 1021.02 L 644.65 1018.81 L 651.56 1018.13 L 651.56 1000 L 662.39 997.17 L 662.39 1019.7 Z M 614.83 1018.14 L 614.83 991.82 L 633.3 982.5 L 633.3 1015.45 Z M 604.2 1019.7 L 595 1021.06 L 595 1001.83 L 604.2 997.18 Z M 604.2 1060.83 L 595 1056.17 L 595 1036.95 L 604.2 1038.3 Z M 633.3 1075.5 L 614.83 1066.18 L 614.83 1039.86 L 633.3 1042.55 Z M 662.39 1060.83 L 651.56 1057.96 L 651.56 1039.87 L 644.65 1039.18 L 644.65 1036.91 L 662.39 1038.32 Z" fill-opacity="0.3" fill="#000000" style="fill: light-dark(rgb(0, 0, 0), rgb(237, 237, 237));" stroke="none" pointer-events="all"/><path d="M 644.65 1036.91 L 654.83 1035.93 L 671.5 1036.98 L 662.39 1038.32 Z M 614.83 1039.86 L 633.3 1038.06 L 651.56 1039.87 L 633.3 1042.55 Z M 604.2 1038.3 L 595 1036.95 L 611.76 1035.93 L 621.76 1036.91 Z" fill-opacity="0.3" fill="#ffffff" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));" stroke="none" pointer-events="all"/><path d="M 621.76 1021.03 L 611.93 1022.07 L 595 1021.06 L 604.2 1019.7 Z M 651.56 1018.13 L 633.3 1019.94 L 614.83 1018.14 L 633.3 1015.45 Z M 671.5 1021.05 L 654.83 1022.09 L 644.65 1021.02 L 662.39 1019.7 Z" fill-opacity="0.5" fill="#000000" style="fill: light-dark(rgb(0, 0, 0), rgb(237, 237, 237));" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 1083px; margin-left: 633px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: nowrap; "><div>AWS Cloudfront (CDN)</div></div></div></div></foreignObject><text x="633" y="1095" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">AWS Cloudfron...</text></switch></g></g></g><g data-cell-id="0dZXfTm35kw52Q68pXVM-6"><g><path d="M 780 80 L 2550 80 L 2550 1110 L 780 1110 Z" fill="none" stroke="#879196" style="stroke: light-dark(rgb(135, 145, 150), rgb(106, 115, 119));" stroke-miterlimit="10" pointer-events="all"/><path d="M 790.59 86.65 C 790.53 86.65 790.48 86.65 790.42 86.65 L 790.42 86.65 C 789.11 86.68 788.03 87.24 787.14 88.25 C 787.13 88.25 787.13 88.25 787.13 88.25 C 786.2 89.36 785.87 90.52 785.96 91.73 C 784.81 92.06 784.12 92.92 783.76 93.74 C 783.75 93.75 783.75 93.76 783.74 93.78 C 783.33 95.05 783.68 96.36 784.24 97.16 C 784.25 97.17 784.25 97.17 784.26 97.18 C 784.94 98.05 785.97 98.53 787.02 98.53 L 798.17 98.53 C 799.19 98.53 800.07 98.16 800.8 97.37 C 801.25 96.94 801.49 96.29 801.58 95.59 C 801.67 94.9 801.61 94.16 801.32 93.55 C 801.31 93.54 801.31 93.53 801.31 93.52 C 800.8 92.62 799.95 91.81 798.76 91.64 C 798.74 90.79 798.28 89.99 797.68 89.56 C 797.67 89.55 797.66 89.55 797.65 89.54 C 797.01 89.18 796.4 89.14 795.91 89.3 C 795.6 89.4 795.36 89.56 795.14 89.74 C 794.51 88.36 793.43 87.18 791.81 86.79 C 791.81 86.79 791.81 86.79 791.81 86.79 C 791.38 86.7 790.97 86.65 790.59 86.65 Z M 790.43 87.38 C 790.8 87.38 791.2 87.43 791.64 87.53 C 793.16 87.89 794.15 89.07 794.66 90.48 C 794.71 90.6 794.81 90.69 794.94 90.72 C 795.07 90.74 795.2 90.7 795.29 90.61 C 795.54 90.34 795.83 90.11 796.14 90.01 C 796.44 89.91 796.78 89.92 797.26 90.18 C 797.67 90.49 798.11 91.31 798.03 91.9 C 798.01 92.01 798.05 92.12 798.12 92.2 C 798.19 92.28 798.29 92.33 798.39 92.33 C 799.46 92.34 800.16 93.02 800.64 93.88 C 800.85 94.3 800.91 94.92 800.84 95.5 C 800.76 96.07 800.53 96.59 800.28 96.83 C 800.27 96.84 800.27 96.85 800.26 96.85 C 799.65 97.53 799.03 97.78 798.17 97.78 L 787.02 97.78 C 786.2 97.78 785.39 97.41 784.85 96.73 C 784.44 96.13 784.14 95.02 784.46 94.02 C 784.79 93.27 785.36 92.55 786.41 92.36 C 786.6 92.32 786.74 92.14 786.71 91.94 C 786.56 90.79 786.8 89.81 787.7 88.74 C 788.49 87.85 789.33 87.39 790.43 87.38 Z M 792.2 90.7 C 791.77 90.7 791.4 90.93 791.13 91.21 C 790.85 91.5 790.64 91.85 790.64 92.25 L 790.64 92.71 L 790.14 92.71 C 790.04 92.71 789.94 92.75 789.87 92.82 C 789.8 92.89 789.76 92.98 789.76 93.08 L 789.76 95.7 C 789.76 95.8 789.8 95.89 789.87 95.96 C 789.94 96.03 790.04 96.07 790.14 96.07 L 794.16 96.07 C 794.26 96.07 794.35 96.03 794.42 95.96 C 794.49 95.89 794.53 95.8 794.53 95.7 L 794.53 93.08 C 794.53 92.98 794.49 92.89 794.42 92.82 C 794.35 92.75 794.26 92.71 794.16 92.71 L 793.68 92.71 L 793.68 92.25 C 793.68 91.84 793.47 91.47 793.21 91.2 C 792.94 90.92 792.61 90.7 792.2 90.7 Z M 792.2 91.45 C 792.29 91.45 792.5 91.54 792.67 91.72 C 792.83 91.89 792.93 92.11 792.93 92.25 L 792.93 92.71 L 791.39 92.71 L 791.39 92.25 C 791.39 92.15 791.49 91.91 791.66 91.74 C 791.83 91.56 792.06 91.45 792.2 91.45 Z M 790.51 93.46 L 793.78 93.46 L 793.78 95.32 L 790.51 95.32 Z M 780 105 L 780 80 L 805 80 L 805 105 Z" fill="#879196" style="fill: light-dark(rgb(135, 145, 150), rgb(106, 115, 119));" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1738px; height: 1px; padding-top: 87px; margin-left: 812px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; color: #879196; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#879196, #6a7377); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Evolution Foundation VPC</div></div></div></foreignObject><text x="812" y="99" fill="#879196" font-family="&quot;Helvetica&quot;" font-size="12px">Evolution Foundation VPC</text></switch></g></g></g><g data-cell-id="0dZXfTm35kw52Q68pXVM-7"><g><path d="M 960 174 L 1310 174 L 1310 950 L 960 950 Z" fill="none" stroke="#879196" style="stroke: light-dark(rgb(135, 145, 150), rgb(106, 115, 119));" stroke-miterlimit="10" pointer-events="all"/><path d="M 970.56 178.12 C 969.34 178.12 968.28 178.59 967.59 179.19 C 967.59 179.19 967.59 179.2 967.59 179.2 C 966.32 180.33 965.76 181.75 965.87 183.2 C 965 183.47 964.41 183.96 964 184.55 C 964 184.56 963.99 184.56 963.99 184.57 C 963.36 185.58 963.4 186.72 963.63 187.54 C 963.63 187.55 963.63 187.55 963.63 187.56 C 964.24 189.35 965.79 190.07 967 190.07 L 971.18 190.07 L 971.18 189.32 L 967 189.32 C 966.08 189.32 964.86 188.81 964.35 187.33 C 964.17 186.67 964.15 185.75 964.62 184.97 C 965.01 184.42 965.45 184.03 966.33 183.84 C 966.52 183.8 966.65 183.61 966.62 183.42 C 966.42 182.09 966.88 180.84 968.09 179.76 L 968.09 179.76 C 968.92 179.03 970.53 178.47 972.27 179.23 C 973.4 179.81 974.24 180.81 974.6 181.97 C 974.64 182.1 974.75 182.2 974.88 182.23 C 975.02 182.25 975.16 182.2 975.25 182.09 C 975.48 181.8 975.77 181.57 976.09 181.49 C 976.4 181.41 976.77 181.45 977.27 181.78 C 977.97 182.29 978.03 182.89 978 183.47 C 977.99 183.58 978.03 183.68 978.11 183.76 C 978.18 183.83 978.29 183.87 978.39 183.87 C 979.12 183.83 979.74 184.17 980.4 185.05 C 980.65 185.47 980.83 186.07 980.84 186.65 C 980.85 187.25 980.68 187.83 980.24 188.28 C 980.24 188.29 980.23 188.29 980.23 188.3 C 979.63 188.99 979.06 189.32 978.14 189.32 L 973.87 189.32 L 973.87 190.07 L 978.14 190.07 C 979.26 190.07 980.1 189.59 980.79 188.8 C 981.38 188.18 981.6 187.39 981.59 186.65 C 981.58 185.9 981.36 185.2 981.04 184.66 C 981.03 184.64 981.03 184.63 981.02 184.62 C 980.36 183.74 979.59 183.25 978.74 183.16 C 978.71 182.51 978.5 181.74 977.7 181.17 C 977.7 181.17 977.7 181.17 977.7 181.17 C 977.06 180.73 976.43 180.63 975.9 180.77 C 975.58 180.85 975.32 181.02 975.09 181.21 C 974.6 180.08 973.74 179.13 972.6 178.55 C 972.59 178.55 972.58 178.55 972.58 178.54 C 972.07 178.32 971.57 178.2 971.09 178.14 C 970.91 178.12 970.74 178.12 970.56 178.12 Z M 970.87 183.73 L 970.87 184.11 L 970.87 186.97 L 972.16 186.97 L 972.16 192.93 L 967.64 192.93 L 967.64 191.71 L 964.35 191.71 L 964.35 192.08 L 964.35 194.94 L 967.64 194.94 L 967.64 193.68 L 977.45 193.68 L 977.45 194.94 L 980.74 194.94 L 980.74 191.71 L 977.45 191.71 L 977.45 192.08 L 977.45 192.93 L 972.91 192.93 L 972.91 186.97 L 974.16 186.97 L 974.16 183.73 Z M 971.62 184.48 L 973.41 184.48 L 973.41 186.22 L 971.62 186.22 Z M 965.1 192.46 L 966.89 192.46 L 966.89 194.19 L 965.1 194.19 Z M 978.2 192.46 L 979.99 192.46 L 979.99 194.19 L 978.2 194.19 Z M 960 199 L 960 174 L 985 174 L 985 199 Z" fill="#879196" style="fill: light-dark(rgb(135, 145, 150), rgb(106, 115, 119));" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 318px; height: 1px; padding-top: 181px; margin-left: 992px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; color: #879196; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#879196, #6a7377); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Public Subnets (AZ1, AZ2, AZ3)</div></div></div></foreignObject><text x="992" y="193" fill="#879196" font-family="&quot;Helvetica&quot;" font-size="12px">Public Subnets (AZ1, AZ2, AZ3)</text></switch></g></g></g><g data-cell-id="8VBJgbjWb-JPUTvmKhwd-10"><g><path d="M 1090 299 L 744.37 299" fill="none" stroke="#000000" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 739.12 299 L 746.12 295.5 L 744.37 299 L 746.12 302.5 Z" fill="#000000" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="0dZXfTm35kw52Q68pXVM-8"><g><rect x="1090" y="260" width="78" height="78" fill="none" stroke="none" pointer-events="all"/><path d="M 1110.61 320.13 L 1110.61 315.1 L 1113.75 317.61 Z M 1109.94 310.02 C 1109.41 309.6 1108.68 309.51 1108.07 309.81 C 1107.45 310.11 1107.06 310.73 1107.06 311.41 L 1107.06 323.82 C 1107.06 324.5 1107.45 325.12 1108.07 325.41 C 1108.31 325.53 1108.58 325.59 1108.84 325.59 C 1109.23 325.59 1109.62 325.46 1109.94 325.2 L 1117.7 319 C 1118.12 318.66 1118.36 318.15 1118.36 317.61 C 1118.36 317.07 1118.12 316.57 1117.7 316.23 Z M 1152.49 304.03 L 1152.49 297.07 L 1155.96 300.55 Z M 1159.72 299.3 L 1151.97 291.54 C 1151.46 291.03 1150.7 290.88 1150.04 291.16 C 1149.37 291.43 1148.94 292.08 1148.94 292.79 L 1148.94 298.78 L 1135.42 298.78 L 1135.42 281.94 C 1135.42 280.96 1134.63 280.17 1133.65 280.17 L 1120.01 280.17 L 1120.01 283.71 L 1131.88 283.71 L 1131.88 298.78 L 1119.69 298.78 L 1119.69 302.32 L 1131.88 302.32 L 1131.88 317.39 L 1120.01 317.39 L 1120.01 320.94 L 1133.65 320.94 C 1134.63 320.94 1135.42 320.14 1135.42 319.16 L 1135.42 302.32 L 1148.94 302.32 L 1148.94 308.31 C 1148.94 309.02 1149.38 309.67 1150.04 309.94 C 1150.26 310.04 1150.49 310.08 1150.72 310.08 C 1151.18 310.08 1151.63 309.9 1151.97 309.56 L 1159.72 301.8 C 1160.42 301.11 1160.42 299.99 1159.72 299.3 Z M 1110.61 303.07 L 1110.61 298.03 L 1113.75 300.55 Z M 1109.94 292.96 C 1109.41 292.53 1108.68 292.45 1108.07 292.75 C 1107.45 293.04 1107.06 293.66 1107.06 294.35 L 1107.06 306.76 C 1107.06 307.44 1107.45 308.06 1108.07 308.35 C 1108.31 308.47 1108.58 308.53 1108.84 308.53 C 1109.23 308.53 1109.62 308.4 1109.94 308.14 L 1117.7 301.93 C 1118.12 301.6 1118.36 301.09 1118.36 300.55 C 1118.36 300.01 1118.12 299.5 1117.7 299.17 Z M 1110.61 284.45 L 1110.61 279.42 L 1113.75 281.94 Z M 1109.94 274.35 C 1109.41 273.92 1108.68 273.84 1108.07 274.14 C 1107.45 274.43 1107.06 275.05 1107.06 275.73 L 1107.06 288.14 C 1107.06 288.82 1107.45 289.44 1108.07 289.74 C 1108.31 289.86 1108.58 289.91 1108.84 289.91 C 1109.23 289.91 1109.62 289.78 1109.94 289.53 L 1117.7 283.32 C 1118.12 282.99 1118.36 282.48 1118.36 281.94 C 1118.36 281.4 1118.12 280.89 1117.7 280.55 Z M 1129 334.45 C 1109.45 334.45 1093.55 318.55 1093.55 299 C 1093.55 279.45 1109.45 263.55 1129 263.55 C 1148.55 263.55 1164.45 279.45 1164.45 299 C 1164.45 318.55 1148.55 334.45 1129 334.45 Z M 1129 260 C 1107.49 260 1090 277.49 1090 299 C 1090 320.5 1107.49 338 1129 338 C 1150.5 338 1168 320.5 1168 299 C 1168 277.49 1150.5 260 1129 260 Z" fill="#8c4fff" style="fill: light-dark(rgb(140, 79, 255), rgb(177, 125, 255));" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 345px; margin-left: 1129px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">NAT Gateway AZ1</div></div></div></foreignObject><text x="1129" y="357" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">NAT Gateway A...</text></switch></g></g></g><g data-cell-id="8VBJgbjWb-JPUTvmKhwd-9"><g><path d="M 1090 429 L 744.37 429" fill="none" stroke="#000000" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 739.12 429 L 746.12 425.5 L 744.37 429 L 746.12 432.5 Z" fill="#000000" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="0dZXfTm35kw52Q68pXVM-9"><g><rect x="1090" y="390" width="78" height="78" fill="none" stroke="none" pointer-events="all"/><path d="M 1110.61 450.13 L 1110.61 445.1 L 1113.75 447.61 Z M 1109.94 440.02 C 1109.41 439.6 1108.68 439.51 1108.07 439.81 C 1107.45 440.11 1107.06 440.73 1107.06 441.41 L 1107.06 453.82 C 1107.06 454.5 1107.45 455.12 1108.07 455.41 C 1108.31 455.53 1108.58 455.59 1108.84 455.59 C 1109.23 455.59 1109.62 455.46 1109.94 455.2 L 1117.7 449 C 1118.12 448.66 1118.36 448.15 1118.36 447.61 C 1118.36 447.07 1118.12 446.57 1117.7 446.23 Z M 1152.49 434.03 L 1152.49 427.07 L 1155.96 430.55 Z M 1159.72 429.3 L 1151.97 421.54 C 1151.46 421.03 1150.7 420.88 1150.04 421.16 C 1149.37 421.43 1148.94 422.08 1148.94 422.79 L 1148.94 428.78 L 1135.42 428.78 L 1135.42 411.94 C 1135.42 410.96 1134.63 410.17 1133.65 410.17 L 1120.01 410.17 L 1120.01 413.71 L 1131.88 413.71 L 1131.88 428.78 L 1119.69 428.78 L 1119.69 432.32 L 1131.88 432.32 L 1131.88 447.39 L 1120.01 447.39 L 1120.01 450.94 L 1133.65 450.94 C 1134.63 450.94 1135.42 450.14 1135.42 449.16 L 1135.42 432.32 L 1148.94 432.32 L 1148.94 438.31 C 1148.94 439.02 1149.38 439.67 1150.04 439.94 C 1150.26 440.04 1150.49 440.08 1150.72 440.08 C 1151.18 440.08 1151.63 439.9 1151.97 439.56 L 1159.72 431.8 C 1160.42 431.11 1160.42 429.99 1159.72 429.3 Z M 1110.61 433.07 L 1110.61 428.03 L 1113.75 430.55 Z M 1109.94 422.96 C 1109.41 422.53 1108.68 422.45 1108.07 422.75 C 1107.45 423.04 1107.06 423.66 1107.06 424.35 L 1107.06 436.76 C 1107.06 437.44 1107.45 438.06 1108.07 438.35 C 1108.31 438.47 1108.58 438.53 1108.84 438.53 C 1109.23 438.53 1109.62 438.4 1109.94 438.14 L 1117.7 431.93 C 1118.12 431.6 1118.36 431.09 1118.36 430.55 C 1118.36 430.01 1118.12 429.5 1117.7 429.17 Z M 1110.61 414.45 L 1110.61 409.42 L 1113.75 411.94 Z M 1109.94 404.35 C 1109.41 403.92 1108.68 403.84 1108.07 404.14 C 1107.45 404.43 1107.06 405.05 1107.06 405.73 L 1107.06 418.14 C 1107.06 418.82 1107.45 419.44 1108.07 419.74 C 1108.31 419.86 1108.58 419.91 1108.84 419.91 C 1109.23 419.91 1109.62 419.78 1109.94 419.53 L 1117.7 413.32 C 1118.12 412.99 1118.36 412.48 1118.36 411.94 C 1118.36 411.4 1118.12 410.89 1117.7 410.55 Z M 1129 464.45 C 1109.45 464.45 1093.55 448.55 1093.55 429 C 1093.55 409.45 1109.45 393.55 1129 393.55 C 1148.55 393.55 1164.45 409.45 1164.45 429 C 1164.45 448.55 1148.55 464.45 1129 464.45 Z M 1129 390 C 1107.49 390 1090 407.49 1090 429 C 1090 450.5 1107.49 468 1129 468 C 1150.5 468 1168 450.5 1168 429 C 1168 407.49 1150.5 390 1129 390 Z" fill="#8c4fff" style="fill: light-dark(rgb(140, 79, 255), rgb(177, 125, 255));" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 475px; margin-left: 1129px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">NAT Gateway AZ2</div></div></div></foreignObject><text x="1129" y="487" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">NAT Gateway A...</text></switch></g></g></g><g data-cell-id="8VBJgbjWb-JPUTvmKhwd-7"><g><path d="M 1090 559 L 744.37 559" fill="none" stroke="#000000" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 739.12 559 L 746.12 555.5 L 744.37 559 L 746.12 562.5 Z" fill="#000000" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="0dZXfTm35kw52Q68pXVM-10"><g><rect x="1090" y="520" width="78" height="78" fill="none" stroke="none" pointer-events="all"/><path d="M 1110.61 580.13 L 1110.61 575.1 L 1113.75 577.61 Z M 1109.94 570.02 C 1109.41 569.6 1108.68 569.51 1108.07 569.81 C 1107.45 570.11 1107.06 570.73 1107.06 571.41 L 1107.06 583.82 C 1107.06 584.5 1107.45 585.12 1108.07 585.41 C 1108.31 585.53 1108.58 585.59 1108.84 585.59 C 1109.23 585.59 1109.62 585.46 1109.94 585.2 L 1117.7 579 C 1118.12 578.66 1118.36 578.15 1118.36 577.61 C 1118.36 577.07 1118.12 576.57 1117.7 576.23 Z M 1152.49 564.03 L 1152.49 557.07 L 1155.96 560.55 Z M 1159.72 559.3 L 1151.97 551.54 C 1151.46 551.03 1150.7 550.88 1150.04 551.16 C 1149.37 551.43 1148.94 552.08 1148.94 552.79 L 1148.94 558.78 L 1135.42 558.78 L 1135.42 541.94 C 1135.42 540.96 1134.63 540.17 1133.65 540.17 L 1120.01 540.17 L 1120.01 543.71 L 1131.88 543.71 L 1131.88 558.78 L 1119.69 558.78 L 1119.69 562.32 L 1131.88 562.32 L 1131.88 577.39 L 1120.01 577.39 L 1120.01 580.94 L 1133.65 580.94 C 1134.63 580.94 1135.42 580.14 1135.42 579.16 L 1135.42 562.32 L 1148.94 562.32 L 1148.94 568.31 C 1148.94 569.02 1149.38 569.67 1150.04 569.94 C 1150.26 570.04 1150.49 570.08 1150.72 570.08 C 1151.18 570.08 1151.63 569.9 1151.97 569.56 L 1159.72 561.8 C 1160.42 561.11 1160.42 559.99 1159.72 559.3 Z M 1110.61 563.07 L 1110.61 558.03 L 1113.75 560.55 Z M 1109.94 552.96 C 1109.41 552.53 1108.68 552.45 1108.07 552.75 C 1107.45 553.04 1107.06 553.66 1107.06 554.35 L 1107.06 566.76 C 1107.06 567.44 1107.45 568.06 1108.07 568.35 C 1108.31 568.47 1108.58 568.53 1108.84 568.53 C 1109.23 568.53 1109.62 568.4 1109.94 568.14 L 1117.7 561.93 C 1118.12 561.6 1118.36 561.09 1118.36 560.55 C 1118.36 560.01 1118.12 559.5 1117.7 559.17 Z M 1110.61 544.45 L 1110.61 539.42 L 1113.75 541.94 Z M 1109.94 534.35 C 1109.41 533.92 1108.68 533.84 1108.07 534.14 C 1107.45 534.43 1107.06 535.05 1107.06 535.73 L 1107.06 548.14 C 1107.06 548.82 1107.45 549.44 1108.07 549.74 C 1108.31 549.86 1108.58 549.91 1108.84 549.91 C 1109.23 549.91 1109.62 549.78 1109.94 549.53 L 1117.7 543.32 C 1118.12 542.99 1118.36 542.48 1118.36 541.94 C 1118.36 541.4 1118.12 540.89 1117.7 540.55 Z M 1129 594.45 C 1109.45 594.45 1093.55 578.55 1093.55 559 C 1093.55 539.45 1109.45 523.55 1129 523.55 C 1148.55 523.55 1164.45 539.45 1164.45 559 C 1164.45 578.55 1148.55 594.45 1129 594.45 Z M 1129 520 C 1107.49 520 1090 537.49 1090 559 C 1090 580.5 1107.49 598 1129 598 C 1150.5 598 1168 580.5 1168 559 C 1168 537.49 1150.5 520 1129 520 Z" fill="#8c4fff" style="fill: light-dark(rgb(140, 79, 255), rgb(177, 125, 255));" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 605px; margin-left: 1129px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">NAT Gateway AZ3</div></div></div></foreignObject><text x="1129" y="617" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">NAT Gateway A...</text></switch></g></g></g><g data-cell-id="0dZXfTm35kw52Q68pXVM-11"><g><rect x="690" y="289" width="48" height="20" fill="none" stroke="none" pointer-events="all"/><path d="M 735.2 299.14 L 735.2 298.86 L 735.34 299 Z M 699.69 306.52 C 695.55 306.52 692.17 303.15 692.17 299 C 692.17 294.85 695.55 291.48 699.69 291.48 C 703.84 291.48 707.22 294.85 707.22 299 C 707.22 303.15 703.84 306.52 699.69 306.52 Z M 737.61 298.26 L 730.62 290.73 L 729.03 292.21 L 734.33 297.91 L 709.32 297.91 C 708.78 293.08 704.67 289.31 699.69 289.31 C 694.35 289.31 690 293.65 690 299 C 690 304.35 694.35 308.69 699.69 308.69 C 704.67 308.69 708.78 304.92 709.32 300.09 L 734.33 300.09 L 729.03 305.79 L 730.62 307.27 L 737.61 299.74 C 738 299.32 738 298.68 737.61 298.26 Z" fill="#ed7100" style="fill: light-dark(rgb(237, 113, 0), rgb(216, 109, 12));" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 316px; margin-left: 714px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">Elastic IP 1</div></div></div></foreignObject><text x="714" y="328" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Elastic...</text></switch></g></g></g><g data-cell-id="0dZXfTm35kw52Q68pXVM-14"><g><rect x="690" y="419" width="48" height="20" fill="none" stroke="none" pointer-events="all"/><path d="M 735.2 429.14 L 735.2 428.86 L 735.34 429 Z M 699.69 436.52 C 695.55 436.52 692.17 433.15 692.17 429 C 692.17 424.85 695.55 421.48 699.69 421.48 C 703.84 421.48 707.22 424.85 707.22 429 C 707.22 433.15 703.84 436.52 699.69 436.52 Z M 737.61 428.26 L 730.62 420.73 L 729.03 422.21 L 734.33 427.91 L 709.32 427.91 C 708.78 423.08 704.67 419.31 699.69 419.31 C 694.35 419.31 690 423.65 690 429 C 690 434.35 694.35 438.69 699.69 438.69 C 704.67 438.69 708.78 434.92 709.32 430.09 L 734.33 430.09 L 729.03 435.79 L 730.62 437.27 L 737.61 429.74 C 738 429.32 738 428.68 737.61 428.26 Z" fill="#ed7100" style="fill: light-dark(rgb(237, 113, 0), rgb(216, 109, 12));" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 446px; margin-left: 714px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">Elastic IP 2</div></div></div></foreignObject><text x="714" y="458" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Elastic...</text></switch></g></g></g><g data-cell-id="0dZXfTm35kw52Q68pXVM-16"><g><rect x="690" y="549" width="48" height="20" fill="none" stroke="none" pointer-events="all"/><path d="M 735.2 559.14 L 735.2 558.86 L 735.34 559 Z M 699.69 566.52 C 695.55 566.52 692.17 563.15 692.17 559 C 692.17 554.85 695.55 551.48 699.69 551.48 C 703.84 551.48 707.22 554.85 707.22 559 C 707.22 563.15 703.84 566.52 699.69 566.52 Z M 737.61 558.26 L 730.62 550.73 L 729.03 552.21 L 734.33 557.91 L 709.32 557.91 C 708.78 553.08 704.67 549.31 699.69 549.31 C 694.35 549.31 690 553.65 690 559 C 690 564.35 694.35 568.69 699.69 568.69 C 704.67 568.69 708.78 564.92 709.32 560.09 L 734.33 560.09 L 729.03 565.79 L 730.62 567.27 L 737.61 559.74 C 738 559.32 738 558.68 737.61 558.26 Z" fill="#ed7100" style="fill: light-dark(rgb(237, 113, 0), rgb(216, 109, 12));" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 576px; margin-left: 714px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">Elastic IP 1</div></div></div></foreignObject><text x="714" y="588" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Elastic...</text></switch></g></g></g><g data-cell-id="0dZXfTm35kw52Q68pXVM-18"><g><path d="M 1128.84 756.25 C 1110.61 756.25 1094.5 741.34 1094.5 722.05 L 1094.5 718.13 C 1094.5 702.52 1107.57 684.25 1129.1 684.25 C 1148.04 684.25 1163.5 699.7 1163.5 718.46 L 1163.48 721.55 C 1163.48 741.2 1147.8 756.25 1128.84 756.25 Z" fill="#f58534" style="fill: light-dark(rgb(245, 133, 52), rgb(189, 92, 23));" stroke="none" pointer-events="all"/><path d="M 1163.5 718.46 L 1163.48 721.55 C 1163.48 741.22 1147.8 756.25 1128.84 756.25 C 1110.61 756.25 1094.5 741.33 1094.5 722.05 L 1094.5 718.13 C 1094.5 736.19 1107.7 753.31 1129.34 753.31 C 1148.76 753.31 1163.5 737.31 1163.5 718.46 Z" fill-opacity="0.3" fill="#000000" style="fill: light-dark(rgb(0, 0, 0), rgb(237, 237, 237));" stroke="none" pointer-events="all"/><rect x="1094.5" y="684.25" width="0" height="0" fill="none" stroke="#000000" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" pointer-events="all"/><path d="M 1102.93 737.46 L 1102.93 727.23 L 1107.12 727.23 L 1107.12 718.39 L 1113.58 718.39 L 1113.58 710.45 L 1128.09 710.45 L 1128.09 703.45 L 1118.93 703.45 L 1118.93 690.89 L 1139.06 690.89 L 1139.06 703.45 L 1129.91 703.45 L 1129.91 710.45 L 1144.05 710.45 L 1144.05 718.39 L 1150.46 718.39 L 1150.46 727.23 L 1154.64 727.23 L 1154.64 737.46 L 1144.44 737.46 L 1144.44 727.23 L 1148.61 727.23 L 1148.61 720.22 L 1137.56 720.22 L 1137.56 727.23 L 1141.73 727.23 L 1141.73 737.46 L 1131.53 737.46 L 1131.53 727.23 L 1135.72 727.23 L 1135.72 718.39 L 1142.2 718.39 L 1142.2 712.3 L 1115.42 712.3 L 1115.42 718.39 L 1121.86 718.39 L 1121.86 727.23 L 1126.05 727.23 L 1126.05 737.46 L 1115.83 737.46 L 1115.83 727.23 L 1120.01 727.23 L 1120.01 720.22 L 1108.95 720.22 L 1108.95 727.23 L 1113.14 727.23 L 1113.14 737.46 Z" fill="#ffffff" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 763px; margin-left: 1129px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: nowrap; ">External Application <br />Load Balancer (ALB)</div></div></div></foreignObject><text x="1129" y="775" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">External Ap...</text></switch></g></g></g><g data-cell-id="0dZXfTm35kw52Q68pXVM-27"><g><path d="M 999 720.25 L 1088.13 720.25" fill="none" stroke="#000000" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1093.38 720.25 L 1086.38 723.75 L 1088.13 720.25 L 1086.38 716.75 Z" fill="#000000" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="0dZXfTm35kw52Q68pXVM-26"><g><path d="M 456.5 720.25 L 923.63 720.25" fill="none" stroke="#000000" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 928.88 720.25 L 921.88 723.75 L 923.63 720.25 L 921.88 716.75 Z" fill="#000000" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="0dZXfTm35kw52Q68pXVM-28"><g><path d="M 1330 174 L 2040 174 L 2040 950 L 1330 950 Z" fill="none" stroke="#879196" style="stroke: light-dark(rgb(135, 145, 150), rgb(106, 115, 119));" stroke-miterlimit="10" pointer-events="all"/><path d="M 1340.56 178.12 C 1339.34 178.12 1338.28 178.59 1337.59 179.19 C 1337.59 179.19 1337.59 179.2 1337.59 179.2 C 1336.32 180.33 1335.76 181.75 1335.87 183.2 C 1335 183.47 1334.41 183.96 1334 184.55 C 1334 184.56 1333.99 184.56 1333.99 184.57 C 1333.36 185.58 1333.4 186.72 1333.63 187.54 C 1333.63 187.55 1333.63 187.55 1333.63 187.56 C 1334.24 189.35 1335.79 190.07 1337 190.07 L 1341.18 190.07 L 1341.18 189.32 L 1337 189.32 C 1336.08 189.32 1334.86 188.81 1334.35 187.33 C 1334.17 186.67 1334.15 185.75 1334.62 184.97 C 1335.01 184.42 1335.45 184.03 1336.33 183.84 C 1336.52 183.8 1336.65 183.61 1336.62 183.42 C 1336.42 182.09 1336.88 180.84 1338.09 179.76 L 1338.09 179.76 C 1338.92 179.03 1340.53 178.47 1342.27 179.23 C 1343.4 179.81 1344.24 180.81 1344.6 181.97 C 1344.64 182.1 1344.75 182.2 1344.88 182.23 C 1345.02 182.25 1345.16 182.2 1345.25 182.09 C 1345.48 181.8 1345.77 181.57 1346.09 181.49 C 1346.4 181.41 1346.77 181.45 1347.27 181.78 C 1347.97 182.29 1348.03 182.89 1348 183.47 C 1347.99 183.58 1348.03 183.68 1348.11 183.76 C 1348.18 183.83 1348.29 183.87 1348.39 183.87 C 1349.12 183.83 1349.74 184.17 1350.4 185.05 C 1350.65 185.47 1350.83 186.07 1350.84 186.65 C 1350.85 187.25 1350.68 187.83 1350.24 188.28 C 1350.24 188.29 1350.23 188.29 1350.23 188.3 C 1349.63 188.99 1349.06 189.32 1348.14 189.32 L 1343.87 189.32 L 1343.87 190.07 L 1348.14 190.07 C 1349.26 190.07 1350.1 189.59 1350.79 188.8 C 1351.38 188.18 1351.6 187.39 1351.59 186.65 C 1351.58 185.9 1351.36 185.2 1351.04 184.66 C 1351.03 184.64 1351.03 184.63 1351.02 184.62 C 1350.36 183.74 1349.59 183.25 1348.74 183.16 C 1348.71 182.51 1348.5 181.74 1347.7 181.17 C 1347.7 181.17 1347.7 181.17 1347.7 181.17 C 1347.06 180.73 1346.43 180.63 1345.9 180.77 C 1345.58 180.85 1345.32 181.02 1345.09 181.21 C 1344.6 180.08 1343.74 179.13 1342.6 178.55 C 1342.59 178.55 1342.58 178.55 1342.58 178.54 C 1342.07 178.32 1341.57 178.2 1341.09 178.14 C 1340.91 178.12 1340.74 178.12 1340.56 178.12 Z M 1340.87 183.73 L 1340.87 184.11 L 1340.87 186.97 L 1342.16 186.97 L 1342.16 192.93 L 1337.64 192.93 L 1337.64 191.71 L 1334.35 191.71 L 1334.35 192.08 L 1334.35 194.94 L 1337.64 194.94 L 1337.64 193.68 L 1347.45 193.68 L 1347.45 194.94 L 1350.74 194.94 L 1350.74 191.71 L 1347.45 191.71 L 1347.45 192.08 L 1347.45 192.93 L 1342.91 192.93 L 1342.91 186.97 L 1344.16 186.97 L 1344.16 183.73 Z M 1341.62 184.48 L 1343.41 184.48 L 1343.41 186.22 L 1341.62 186.22 Z M 1335.1 192.46 L 1336.89 192.46 L 1336.89 194.19 L 1335.1 194.19 Z M 1348.2 192.46 L 1349.99 192.46 L 1349.99 194.19 L 1348.2 194.19 Z M 1330 199 L 1330 174 L 1355 174 L 1355 199 Z" fill="#879196" style="fill: light-dark(rgb(135, 145, 150), rgb(106, 115, 119));" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 678px; height: 1px; padding-top: 181px; margin-left: 1362px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; color: #879196; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#879196, #6a7377); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Private Subnets (AZ1, AZ2, AZ3)</div></div></div></foreignObject><text x="1362" y="193" fill="#879196" font-family="&quot;Helvetica&quot;" font-size="12px">Private Subnets (AZ1, AZ2, AZ3)</text></switch></g></g></g><g data-cell-id="0dZXfTm35kw52Q68pXVM-33"><g><path d="M 1380 380 Q 1274 380.08 1274 469.54 Q 1274 559 1174.37 559" fill="none" stroke="#000000" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1169.12 559 L 1176.12 555.5 L 1174.37 559 L 1176.12 562.5 Z" fill="#000000" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="8VBJgbjWb-JPUTvmKhwd-20"><g><path d="M 1380 380 Q 1274 380.08 1274 404.54 Q 1274 429 1174.37 429" fill="none" stroke="#000000" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1169.12 429 L 1176.12 425.5 L 1174.37 429 L 1176.12 432.5 Z" fill="#000000" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="8VBJgbjWb-JPUTvmKhwd-21"><g><path d="M 1380 380 Q 1274 380.08 1274 339.54 Q 1274 299 1174.37 299" fill="none" stroke="#000000" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1169.12 299 L 1176.12 295.5 L 1174.37 299 L 1176.12 302.5 Z" fill="#000000" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="0dZXfTm35kw52Q68pXVM-30"><g><path d="M 1380 210 L 1650 210 L 1650 890 L 1380 890 Z" fill="none" stroke="#d86613" style="stroke: light-dark(rgb(216, 102, 19), rgb(216, 118, 46));" stroke-miterlimit="10" pointer-events="none"/><path d="M 1380 210 L 1405 210 L 1405 235 L 1380 235 Z M 1401.07 218.37 L 1401.07 217.66 L 1399.39 217.66 L 1399.39 215.61 L 1397.25 215.61 L 1397.25 213.93 L 1396.54 213.93 L 1396.54 215.61 L 1395 215.61 L 1395 213.93 L 1394.29 213.93 L 1394.29 215.61 L 1392.77 215.61 L 1392.77 213.93 L 1392.06 213.93 L 1392.06 215.61 L 1390.53 215.61 L 1390.53 213.93 L 1389.81 213.93 L 1389.81 215.61 L 1388.29 215.61 L 1388.29 213.93 L 1387.57 213.93 L 1387.57 215.61 L 1385.61 215.61 L 1385.61 217.66 L 1383.93 217.66 L 1383.93 218.37 L 1385.61 218.37 L 1385.61 219.9 L 1383.93 219.9 L 1383.93 220.61 L 1385.61 220.61 L 1385.61 222.14 L 1383.93 222.14 L 1383.93 222.86 L 1385.61 222.86 L 1385.61 224.39 L 1383.93 224.39 L 1383.93 225.1 L 1385.61 225.1 L 1385.61 226.63 L 1383.93 226.63 L 1383.93 227.34 L 1385.61 227.34 L 1385.61 229.39 L 1387.57 229.39 L 1387.57 231.07 L 1388.29 231.07 L 1388.29 229.39 L 1389.81 229.39 L 1389.81 231.07 L 1390.53 231.07 L 1390.53 229.39 L 1392.06 229.39 L 1392.06 231.07 L 1392.77 231.07 L 1392.77 229.39 L 1394.29 229.39 L 1394.29 231.07 L 1395 231.07 L 1395 229.39 L 1396.53 229.39 L 1396.53 231.07 L 1397.24 231.07 L 1397.24 229.39 L 1399.39 229.39 L 1399.39 227.34 L 1401.07 227.34 L 1401.07 226.63 L 1399.39 226.63 L 1399.39 225.1 L 1401.07 225.1 L 1401.07 224.39 L 1399.39 224.39 L 1399.39 222.86 L 1401.07 222.86 L 1401.07 222.14 L 1399.39 222.14 L 1399.39 220.61 L 1401.07 220.61 L 1401.07 219.9 L 1399.39 219.9 L 1399.39 218.37 Z M 1398.68 228.68 L 1386.32 228.68 L 1386.32 216.32 L 1398.68 216.32 Z" fill="#d86613" style="fill: light-dark(rgb(216, 102, 19), rgb(216, 118, 46));" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 238px; height: 1px; padding-top: 217px; margin-left: 1412px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; color: #D86613; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#D86613, #d8762e); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">ECS Fargate Cluster</div></div></div></foreignObject><text x="1412" y="229" fill="#D86613" font-family="&quot;Helvetica&quot;" font-size="12px">ECS Fargate Cluster</text></switch></g></g><g data-cell-id="8VBJgbjWb-JPUTvmKhwd-11"><g><path d="M 1460 530 L 1538 530 L 1538 608 L 1460 608 Z" fill="#ed7100" style="fill: light-dark(rgb(237, 113, 0), rgb(216, 109, 12));" stroke="none" pointer-events="all"/><path d="M 1513.68 588.61 L 1513.68 582.68 L 1517.35 581.22 L 1517.35 587.14 Z M 1507.56 581.22 L 1511.23 582.68 L 1511.23 588.61 L 1507.56 587.14 Z M 1500.22 597.17 L 1500.22 591.24 L 1503.89 589.78 L 1503.89 595.7 Z M 1494.11 589.78 L 1497.78 591.24 L 1497.78 597.17 L 1494.11 595.7 Z M 1486.77 588.61 L 1486.77 582.68 L 1490.44 581.22 L 1490.44 587.14 Z M 1480.65 581.22 L 1484.32 582.68 L 1484.32 588.61 L 1480.65 587.14 Z M 1485.55 578.28 L 1488.37 579.41 L 1485.55 580.54 L 1482.72 579.41 Z M 1499 586.84 L 1501.82 587.97 L 1499 589.1 L 1496.18 587.97 Z M 1512.45 578.28 L 1515.28 579.41 L 1512.45 580.54 L 1509.63 579.41 Z M 1519.02 578.27 L 1512.91 575.83 C 1512.62 575.71 1512.29 575.71 1512 575.83 L 1505.88 578.27 C 1505.42 578.46 1505.12 578.91 1505.12 579.41 L 1505.12 586.65 L 1499.45 584.39 C 1499.16 584.27 1498.84 584.27 1498.55 584.39 L 1492.88 586.65 L 1492.88 579.41 C 1492.88 578.91 1492.58 578.46 1492.12 578.27 L 1486 575.83 C 1485.71 575.71 1485.38 575.71 1485.09 575.83 L 1478.98 578.27 C 1478.51 578.46 1478.21 578.91 1478.21 579.41 L 1478.21 587.97 C 1478.21 588.47 1478.51 588.92 1478.98 589.11 L 1485.09 591.55 C 1485.24 591.61 1485.39 591.64 1485.55 591.64 C 1485.7 591.64 1485.86 591.61 1486 591.55 L 1491.66 589.29 L 1491.66 596.53 C 1491.66 597.03 1491.97 597.48 1492.43 597.67 L 1498.55 600.11 C 1498.69 600.17 1498.85 600.2 1499 600.2 C 1499.15 600.2 1499.31 600.17 1499.45 600.11 L 1505.57 597.67 C 1506.03 597.48 1506.34 597.03 1506.34 596.53 L 1506.34 589.29 L 1512 591.55 C 1512.14 591.61 1512.3 591.64 1512.45 591.64 C 1512.61 591.64 1512.76 591.61 1512.91 591.55 L 1519.02 589.11 C 1519.49 588.92 1519.79 588.47 1519.79 587.97 L 1519.79 579.41 C 1519.79 578.91 1519.49 578.46 1519.02 578.27 Z M 1529.58 563.43 C 1529.58 570.58 1513.82 574.44 1499 574.44 C 1484.18 574.44 1468.42 570.58 1468.42 563.43 C 1468.42 560.02 1472.21 557.02 1479.08 554.97 L 1479.78 557.32 C 1474.28 558.95 1470.87 561.3 1470.87 563.43 C 1470.87 567.48 1482.42 572 1499 572 C 1515.58 572 1527.13 567.48 1527.13 563.43 C 1527.13 561.3 1523.72 558.95 1518.22 557.32 L 1518.92 554.97 C 1525.79 557.02 1529.58 560.02 1529.58 563.43 Z M 1499 540.36 L 1511.49 545.16 L 1499 549.97 L 1486.51 545.16 Z M 1511.94 564.54 C 1509.63 565.57 1505.76 566.77 1500.22 566.94 L 1500.22 552.12 L 1513.68 546.95 L 1513.68 561.86 C 1513.68 563.02 1513 564.07 1511.94 564.54 Z M 1484.32 561.86 L 1484.32 546.95 L 1497.78 552.12 L 1497.78 566.94 C 1492.24 566.77 1488.37 565.57 1486.06 564.54 C 1485 564.07 1484.32 563.02 1484.32 561.86 Z M 1485.06 566.77 C 1487.75 567.98 1492.36 569.41 1499 569.41 C 1505.64 569.41 1510.25 567.98 1512.93 566.77 C 1514.87 565.92 1516.12 563.99 1516.12 561.86 L 1516.12 545.16 C 1516.12 544.66 1515.81 544.2 1515.34 544.02 L 1499.44 537.91 C 1499.16 537.8 1498.84 537.8 1498.56 537.91 L 1482.66 544.02 C 1482.19 544.2 1481.88 544.66 1481.88 545.16 L 1481.88 561.86 C 1481.88 563.99 1483.13 565.92 1485.06 566.77 Z" fill="#ffffff" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 615px; margin-left: 1499px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">Axon Integration Adapter API</div></div></div></foreignObject><text x="1499" y="627" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Axon Integrat...</text></switch></g></g></g><g data-cell-id="8VBJgbjWb-JPUTvmKhwd-12"><g><path d="M 1460 660 L 1538 660 L 1538 738 L 1460 738 Z" fill="#ed7100" style="fill: light-dark(rgb(237, 113, 0), rgb(216, 109, 12));" stroke="none" pointer-events="all"/><path d="M 1513.68 718.61 L 1513.68 712.68 L 1517.35 711.22 L 1517.35 717.14 Z M 1507.56 711.22 L 1511.23 712.68 L 1511.23 718.61 L 1507.56 717.14 Z M 1500.22 727.17 L 1500.22 721.24 L 1503.89 719.78 L 1503.89 725.7 Z M 1494.11 719.78 L 1497.78 721.24 L 1497.78 727.17 L 1494.11 725.7 Z M 1486.77 718.61 L 1486.77 712.68 L 1490.44 711.22 L 1490.44 717.14 Z M 1480.65 711.22 L 1484.32 712.68 L 1484.32 718.61 L 1480.65 717.14 Z M 1485.55 708.28 L 1488.37 709.41 L 1485.55 710.54 L 1482.72 709.41 Z M 1499 716.84 L 1501.82 717.97 L 1499 719.1 L 1496.18 717.97 Z M 1512.45 708.28 L 1515.28 709.41 L 1512.45 710.54 L 1509.63 709.41 Z M 1519.02 708.27 L 1512.91 705.83 C 1512.62 705.71 1512.29 705.71 1512 705.83 L 1505.88 708.27 C 1505.42 708.46 1505.12 708.91 1505.12 709.41 L 1505.12 716.65 L 1499.45 714.39 C 1499.16 714.27 1498.84 714.27 1498.55 714.39 L 1492.88 716.65 L 1492.88 709.41 C 1492.88 708.91 1492.58 708.46 1492.12 708.27 L 1486 705.83 C 1485.71 705.71 1485.38 705.71 1485.09 705.83 L 1478.98 708.27 C 1478.51 708.46 1478.21 708.91 1478.21 709.41 L 1478.21 717.97 C 1478.21 718.47 1478.51 718.92 1478.98 719.11 L 1485.09 721.55 C 1485.24 721.61 1485.39 721.64 1485.55 721.64 C 1485.7 721.64 1485.86 721.61 1486 721.55 L 1491.66 719.29 L 1491.66 726.53 C 1491.66 727.03 1491.97 727.48 1492.43 727.67 L 1498.55 730.11 C 1498.69 730.17 1498.85 730.2 1499 730.2 C 1499.15 730.2 1499.31 730.17 1499.45 730.11 L 1505.57 727.67 C 1506.03 727.48 1506.34 727.03 1506.34 726.53 L 1506.34 719.29 L 1512 721.55 C 1512.14 721.61 1512.3 721.64 1512.45 721.64 C 1512.61 721.64 1512.76 721.61 1512.91 721.55 L 1519.02 719.11 C 1519.49 718.92 1519.79 718.47 1519.79 717.97 L 1519.79 709.41 C 1519.79 708.91 1519.49 708.46 1519.02 708.27 Z M 1529.58 693.43 C 1529.58 700.58 1513.82 704.44 1499 704.44 C 1484.18 704.44 1468.42 700.58 1468.42 693.43 C 1468.42 690.02 1472.21 687.02 1479.08 684.97 L 1479.78 687.32 C 1474.28 688.95 1470.87 691.3 1470.87 693.43 C 1470.87 697.48 1482.42 702 1499 702 C 1515.58 702 1527.13 697.48 1527.13 693.43 C 1527.13 691.3 1523.72 688.95 1518.22 687.32 L 1518.92 684.97 C 1525.79 687.02 1529.58 690.02 1529.58 693.43 Z M 1499 670.36 L 1511.49 675.16 L 1499 679.97 L 1486.51 675.16 Z M 1511.94 694.54 C 1509.63 695.57 1505.76 696.77 1500.22 696.94 L 1500.22 682.12 L 1513.68 676.95 L 1513.68 691.86 C 1513.68 693.02 1513 694.07 1511.94 694.54 Z M 1484.32 691.86 L 1484.32 676.95 L 1497.78 682.12 L 1497.78 696.94 C 1492.24 696.77 1488.37 695.57 1486.06 694.54 C 1485 694.07 1484.32 693.02 1484.32 691.86 Z M 1485.06 696.77 C 1487.75 697.98 1492.36 699.41 1499 699.41 C 1505.64 699.41 1510.25 697.98 1512.93 696.77 C 1514.87 695.92 1516.12 693.99 1516.12 691.86 L 1516.12 675.16 C 1516.12 674.66 1515.81 674.2 1515.34 674.02 L 1499.44 667.91 C 1499.16 667.8 1498.84 667.8 1498.56 667.91 L 1482.66 674.02 C 1482.19 674.2 1481.88 674.66 1481.88 675.16 L 1481.88 691.86 C 1481.88 693.99 1483.13 695.92 1485.06 696.77 Z" fill="#ffffff" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 745px; margin-left: 1499px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">Axon Domain Service</div></div></div></foreignObject><text x="1499" y="757" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Axon Domain S...</text></switch></g></g></g><g data-cell-id="8VBJgbjWb-JPUTvmKhwd-19"><g><path d="M 1460 280 L 1538 280 L 1538 358 L 1460 358 Z" fill="#ed7100" style="fill: light-dark(rgb(237, 113, 0), rgb(216, 109, 12));" stroke="none" pointer-events="all"/><path d="M 1513.68 338.61 L 1513.68 332.68 L 1517.35 331.22 L 1517.35 337.14 Z M 1507.56 331.22 L 1511.23 332.68 L 1511.23 338.61 L 1507.56 337.14 Z M 1500.22 347.17 L 1500.22 341.24 L 1503.89 339.78 L 1503.89 345.7 Z M 1494.11 339.78 L 1497.78 341.24 L 1497.78 347.17 L 1494.11 345.7 Z M 1486.77 338.61 L 1486.77 332.68 L 1490.44 331.22 L 1490.44 337.14 Z M 1480.65 331.22 L 1484.32 332.68 L 1484.32 338.61 L 1480.65 337.14 Z M 1485.55 328.28 L 1488.37 329.41 L 1485.55 330.54 L 1482.72 329.41 Z M 1499 336.84 L 1501.82 337.97 L 1499 339.1 L 1496.18 337.97 Z M 1512.45 328.28 L 1515.28 329.41 L 1512.45 330.54 L 1509.63 329.41 Z M 1519.02 328.27 L 1512.91 325.83 C 1512.62 325.71 1512.29 325.71 1512 325.83 L 1505.88 328.27 C 1505.42 328.46 1505.12 328.91 1505.12 329.41 L 1505.12 336.65 L 1499.45 334.39 C 1499.16 334.27 1498.84 334.27 1498.55 334.39 L 1492.88 336.65 L 1492.88 329.41 C 1492.88 328.91 1492.58 328.46 1492.12 328.27 L 1486 325.83 C 1485.71 325.71 1485.38 325.71 1485.09 325.83 L 1478.98 328.27 C 1478.51 328.46 1478.21 328.91 1478.21 329.41 L 1478.21 337.97 C 1478.21 338.47 1478.51 338.92 1478.98 339.11 L 1485.09 341.55 C 1485.24 341.61 1485.39 341.64 1485.55 341.64 C 1485.7 341.64 1485.86 341.61 1486 341.55 L 1491.66 339.29 L 1491.66 346.53 C 1491.66 347.03 1491.97 347.48 1492.43 347.67 L 1498.55 350.11 C 1498.69 350.17 1498.85 350.2 1499 350.2 C 1499.15 350.2 1499.31 350.17 1499.45 350.11 L 1505.57 347.67 C 1506.03 347.48 1506.34 347.03 1506.34 346.53 L 1506.34 339.29 L 1512 341.55 C 1512.14 341.61 1512.3 341.64 1512.45 341.64 C 1512.61 341.64 1512.76 341.61 1512.91 341.55 L 1519.02 339.11 C 1519.49 338.92 1519.79 338.47 1519.79 337.97 L 1519.79 329.41 C 1519.79 328.91 1519.49 328.46 1519.02 328.27 Z M 1529.58 313.43 C 1529.58 320.58 1513.82 324.44 1499 324.44 C 1484.18 324.44 1468.42 320.58 1468.42 313.43 C 1468.42 310.02 1472.21 307.02 1479.08 304.97 L 1479.78 307.32 C 1474.28 308.95 1470.87 311.3 1470.87 313.43 C 1470.87 317.48 1482.42 322 1499 322 C 1515.58 322 1527.13 317.48 1527.13 313.43 C 1527.13 311.3 1523.72 308.95 1518.22 307.32 L 1518.92 304.97 C 1525.79 307.02 1529.58 310.02 1529.58 313.43 Z M 1499 290.36 L 1511.49 295.16 L 1499 299.97 L 1486.51 295.16 Z M 1511.94 314.54 C 1509.63 315.57 1505.76 316.77 1500.22 316.94 L 1500.22 302.12 L 1513.68 296.95 L 1513.68 311.86 C 1513.68 313.02 1513 314.07 1511.94 314.54 Z M 1484.32 311.86 L 1484.32 296.95 L 1497.78 302.12 L 1497.78 316.94 C 1492.24 316.77 1488.37 315.57 1486.06 314.54 C 1485 314.07 1484.32 313.02 1484.32 311.86 Z M 1485.06 316.77 C 1487.75 317.98 1492.36 319.41 1499 319.41 C 1505.64 319.41 1510.25 317.98 1512.93 316.77 C 1514.87 315.92 1516.12 313.99 1516.12 311.86 L 1516.12 295.16 C 1516.12 294.66 1515.81 294.2 1515.34 294.02 L 1499.44 287.91 C 1499.16 287.8 1498.84 287.8 1498.56 287.91 L 1482.66 294.02 C 1482.19 294.2 1481.88 294.66 1481.88 295.16 L 1481.88 311.86 C 1481.88 313.99 1483.13 315.92 1485.06 316.77 Z" fill="#ffffff" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 365px; margin-left: 1499px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">Magento</div></div></div></foreignObject><text x="1499" y="377" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Magento</text></switch></g></g></g><g data-cell-id="8VBJgbjWb-JPUTvmKhwd-18"><g><path d="M 1460 410 L 1538 410 L 1538 488 L 1460 488 Z" fill="#ed7100" style="fill: light-dark(rgb(237, 113, 0), rgb(216, 109, 12));" stroke="none" pointer-events="all"/><path d="M 1513.68 468.61 L 1513.68 462.68 L 1517.35 461.22 L 1517.35 467.14 Z M 1507.56 461.22 L 1511.23 462.68 L 1511.23 468.61 L 1507.56 467.14 Z M 1500.22 477.17 L 1500.22 471.24 L 1503.89 469.78 L 1503.89 475.7 Z M 1494.11 469.78 L 1497.78 471.24 L 1497.78 477.17 L 1494.11 475.7 Z M 1486.77 468.61 L 1486.77 462.68 L 1490.44 461.22 L 1490.44 467.14 Z M 1480.65 461.22 L 1484.32 462.68 L 1484.32 468.61 L 1480.65 467.14 Z M 1485.55 458.28 L 1488.37 459.41 L 1485.55 460.54 L 1482.72 459.41 Z M 1499 466.84 L 1501.82 467.97 L 1499 469.1 L 1496.18 467.97 Z M 1512.45 458.28 L 1515.28 459.41 L 1512.45 460.54 L 1509.63 459.41 Z M 1519.02 458.27 L 1512.91 455.83 C 1512.62 455.71 1512.29 455.71 1512 455.83 L 1505.88 458.27 C 1505.42 458.46 1505.12 458.91 1505.12 459.41 L 1505.12 466.65 L 1499.45 464.39 C 1499.16 464.27 1498.84 464.27 1498.55 464.39 L 1492.88 466.65 L 1492.88 459.41 C 1492.88 458.91 1492.58 458.46 1492.12 458.27 L 1486 455.83 C 1485.71 455.71 1485.38 455.71 1485.09 455.83 L 1478.98 458.27 C 1478.51 458.46 1478.21 458.91 1478.21 459.41 L 1478.21 467.97 C 1478.21 468.47 1478.51 468.92 1478.98 469.11 L 1485.09 471.55 C 1485.24 471.61 1485.39 471.64 1485.55 471.64 C 1485.7 471.64 1485.86 471.61 1486 471.55 L 1491.66 469.29 L 1491.66 476.53 C 1491.66 477.03 1491.97 477.48 1492.43 477.67 L 1498.55 480.11 C 1498.69 480.17 1498.85 480.2 1499 480.2 C 1499.15 480.2 1499.31 480.17 1499.45 480.11 L 1505.57 477.67 C 1506.03 477.48 1506.34 477.03 1506.34 476.53 L 1506.34 469.29 L 1512 471.55 C 1512.14 471.61 1512.3 471.64 1512.45 471.64 C 1512.61 471.64 1512.76 471.61 1512.91 471.55 L 1519.02 469.11 C 1519.49 468.92 1519.79 468.47 1519.79 467.97 L 1519.79 459.41 C 1519.79 458.91 1519.49 458.46 1519.02 458.27 Z M 1529.58 443.43 C 1529.58 450.58 1513.82 454.44 1499 454.44 C 1484.18 454.44 1468.42 450.58 1468.42 443.43 C 1468.42 440.02 1472.21 437.02 1479.08 434.97 L 1479.78 437.32 C 1474.28 438.95 1470.87 441.3 1470.87 443.43 C 1470.87 447.48 1482.42 452 1499 452 C 1515.58 452 1527.13 447.48 1527.13 443.43 C 1527.13 441.3 1523.72 438.95 1518.22 437.32 L 1518.92 434.97 C 1525.79 437.02 1529.58 440.02 1529.58 443.43 Z M 1499 420.36 L 1511.49 425.16 L 1499 429.97 L 1486.51 425.16 Z M 1511.94 444.54 C 1509.63 445.57 1505.76 446.77 1500.22 446.94 L 1500.22 432.12 L 1513.68 426.95 L 1513.68 441.86 C 1513.68 443.02 1513 444.07 1511.94 444.54 Z M 1484.32 441.86 L 1484.32 426.95 L 1497.78 432.12 L 1497.78 446.94 C 1492.24 446.77 1488.37 445.57 1486.06 444.54 C 1485 444.07 1484.32 443.02 1484.32 441.86 Z M 1485.06 446.77 C 1487.75 447.98 1492.36 449.41 1499 449.41 C 1505.64 449.41 1510.25 447.98 1512.93 446.77 C 1514.87 445.92 1516.12 443.99 1516.12 441.86 L 1516.12 425.16 C 1516.12 424.66 1515.81 424.2 1515.34 424.02 L 1499.44 417.91 C 1499.16 417.8 1498.84 417.8 1498.56 417.91 L 1482.66 424.02 C 1482.19 424.2 1481.88 424.66 1481.88 425.16 L 1481.88 441.86 C 1481.88 443.99 1483.13 445.92 1485.06 446.77 Z" fill="#ffffff" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 495px; margin-left: 1499px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; "><div>Pimcore</div></div></div></div></foreignObject><text x="1499" y="507" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Pimcore</text></switch></g></g></g><g data-cell-id="8VBJgbjWb-JPUTvmKhwd-13"><g><path d="M 1460 780 L 1538 780 L 1538 858 L 1460 858 Z" fill="#ed7100" style="fill: light-dark(rgb(237, 113, 0), rgb(216, 109, 12));" stroke="none" pointer-events="all"/><path d="M 1513.68 838.61 L 1513.68 832.68 L 1517.35 831.22 L 1517.35 837.14 Z M 1507.56 831.22 L 1511.23 832.68 L 1511.23 838.61 L 1507.56 837.14 Z M 1500.22 847.17 L 1500.22 841.24 L 1503.89 839.78 L 1503.89 845.7 Z M 1494.11 839.78 L 1497.78 841.24 L 1497.78 847.17 L 1494.11 845.7 Z M 1486.77 838.61 L 1486.77 832.68 L 1490.44 831.22 L 1490.44 837.14 Z M 1480.65 831.22 L 1484.32 832.68 L 1484.32 838.61 L 1480.65 837.14 Z M 1485.55 828.28 L 1488.37 829.41 L 1485.55 830.54 L 1482.72 829.41 Z M 1499 836.84 L 1501.82 837.97 L 1499 839.1 L 1496.18 837.97 Z M 1512.45 828.28 L 1515.28 829.41 L 1512.45 830.54 L 1509.63 829.41 Z M 1519.02 828.27 L 1512.91 825.83 C 1512.62 825.71 1512.29 825.71 1512 825.83 L 1505.88 828.27 C 1505.42 828.46 1505.12 828.91 1505.12 829.41 L 1505.12 836.65 L 1499.45 834.39 C 1499.16 834.27 1498.84 834.27 1498.55 834.39 L 1492.88 836.65 L 1492.88 829.41 C 1492.88 828.91 1492.58 828.46 1492.12 828.27 L 1486 825.83 C 1485.71 825.71 1485.38 825.71 1485.09 825.83 L 1478.98 828.27 C 1478.51 828.46 1478.21 828.91 1478.21 829.41 L 1478.21 837.97 C 1478.21 838.47 1478.51 838.92 1478.98 839.11 L 1485.09 841.55 C 1485.24 841.61 1485.39 841.64 1485.55 841.64 C 1485.7 841.64 1485.86 841.61 1486 841.55 L 1491.66 839.29 L 1491.66 846.53 C 1491.66 847.03 1491.97 847.48 1492.43 847.67 L 1498.55 850.11 C 1498.69 850.17 1498.85 850.2 1499 850.2 C 1499.15 850.2 1499.31 850.17 1499.45 850.11 L 1505.57 847.67 C 1506.03 847.48 1506.34 847.03 1506.34 846.53 L 1506.34 839.29 L 1512 841.55 C 1512.14 841.61 1512.3 841.64 1512.45 841.64 C 1512.61 841.64 1512.76 841.61 1512.91 841.55 L 1519.02 839.11 C 1519.49 838.92 1519.79 838.47 1519.79 837.97 L 1519.79 829.41 C 1519.79 828.91 1519.49 828.46 1519.02 828.27 Z M 1529.58 813.43 C 1529.58 820.58 1513.82 824.44 1499 824.44 C 1484.18 824.44 1468.42 820.58 1468.42 813.43 C 1468.42 810.02 1472.21 807.02 1479.08 804.97 L 1479.78 807.32 C 1474.28 808.95 1470.87 811.3 1470.87 813.43 C 1470.87 817.48 1482.42 822 1499 822 C 1515.58 822 1527.13 817.48 1527.13 813.43 C 1527.13 811.3 1523.72 808.95 1518.22 807.32 L 1518.92 804.97 C 1525.79 807.02 1529.58 810.02 1529.58 813.43 Z M 1499 790.36 L 1511.49 795.16 L 1499 799.97 L 1486.51 795.16 Z M 1511.94 814.54 C 1509.63 815.57 1505.76 816.77 1500.22 816.94 L 1500.22 802.12 L 1513.68 796.95 L 1513.68 811.86 C 1513.68 813.02 1513 814.07 1511.94 814.54 Z M 1484.32 811.86 L 1484.32 796.95 L 1497.78 802.12 L 1497.78 816.94 C 1492.24 816.77 1488.37 815.57 1486.06 814.54 C 1485 814.07 1484.32 813.02 1484.32 811.86 Z M 1485.06 816.77 C 1487.75 817.98 1492.36 819.41 1499 819.41 C 1505.64 819.41 1510.25 817.98 1512.93 816.77 C 1514.87 815.92 1516.12 813.99 1516.12 811.86 L 1516.12 795.16 C 1516.12 794.66 1515.81 794.2 1515.34 794.02 L 1499.44 787.91 C 1499.16 787.8 1498.84 787.8 1498.56 787.91 L 1482.66 794.02 C 1482.19 794.2 1481.88 794.66 1481.88 795.16 L 1481.88 811.86 C 1481.88 813.99 1483.13 815.92 1485.06 816.77 Z" fill="#ffffff" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 865px; margin-left: 1499px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">Event Catalog</div></div></div></foreignObject><text x="1499" y="877" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Event Catalog</text></switch></g></g></g></g><g data-cell-id="8VBJgbjWb-JPUTvmKhwd-14"><g><path d="M 1140 1003.5 L 1218 1003.5 L 1218 1081.5 L 1140 1081.5 Z" fill="#8c4fff" style="fill: light-dark(rgb(140, 79, 255), rgb(177, 125, 255));" stroke="none" pointer-events="all"/><path d="M 1176.44 1051.41 C 1176.44 1053.14 1177.85 1054.55 1179.58 1054.55 C 1181.31 1054.55 1182.72 1053.14 1182.72 1051.41 C 1182.72 1049.68 1181.31 1048.27 1179.58 1048.27 C 1177.85 1048.27 1176.44 1049.68 1176.44 1051.41 Z M 1174.24 1051.41 C 1174.24 1048.47 1176.64 1046.07 1179.58 1046.07 C 1182.52 1046.07 1184.92 1048.47 1184.92 1051.41 C 1184.92 1054.04 1183.01 1056.22 1180.5 1056.66 L 1180.5 1063.86 L 1178.3 1063.86 L 1178.3 1056.58 C 1175.98 1056 1174.24 1053.91 1174.24 1051.41 Z M 1160.83 1071.5 L 1197.98 1071.5 L 1197.98 1042.66 L 1160.83 1042.66 Z M 1193.18 1040.46 L 1200.18 1040.46 L 1200.18 1073.7 L 1158.63 1073.7 L 1158.63 1040.46 L 1166.06 1040.46 L 1166.06 1034.13 L 1168.26 1034.13 L 1168.26 1040.46 L 1190.98 1040.46 L 1190.98 1034.13 L 1193.18 1034.13 Z M 1200.01 1023.33 L 1198.48 1024.91 L 1202.8 1029.09 L 1155.18 1029.09 L 1159.37 1024.9 L 1157.82 1023.34 L 1150.83 1030.33 L 1157.99 1037.25 L 1159.52 1035.67 L 1154.99 1031.29 L 1203.02 1031.29 L 1198.63 1035.68 L 1200.18 1037.24 L 1207.17 1030.25 Z M 1168.26 1026.69 L 1166.06 1026.69 L 1166.06 1025.5 C 1166.06 1018.73 1170.79 1013.03 1177.32 1011.96 C 1181.31 1011.3 1185.34 1012.4 1188.39 1014.98 C 1191.43 1017.57 1193.18 1021.34 1193.18 1025.33 L 1193.18 1026.69 L 1190.98 1026.69 L 1190.98 1025.33 C 1190.98 1021.99 1189.52 1018.83 1186.97 1016.66 C 1184.42 1014.5 1181.03 1013.58 1177.67 1014.13 C 1172.31 1015.02 1168.26 1019.9 1168.26 1025.5 Z" fill="#ffffff" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 1089px; margin-left: 1179px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">Site-to-Site VPN</div></div></div></foreignObject><text x="1179" y="1101" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Site-to-Site...</text></switch></g></g></g><g data-cell-id="8VBJgbjWb-JPUTvmKhwd-15"><g><path d="M 1179.08 1003.5 Q 1179.08 900.08 1024.54 900.08 Q 870 900.08 870 820.08 Q 870 740.08 923.63 740.08" fill="none" stroke="#000000" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 928.88 740.08 L 921.88 743.58 L 923.63 740.08 L 921.88 736.58 Z" fill="#000000" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="8VBJgbjWb-JPUTvmKhwd-22"><g><rect x="1710" y="338" width="78" height="78" fill="none" stroke="none" pointer-events="all"/><path d="M 1784.89 387.8 L 1784.89 381.13 C 1781.86 382.91 1777.04 383.83 1772.24 383.83 C 1767.45 383.83 1762.62 382.91 1759.6 381.13 L 1759.6 387.8 C 1759.6 389.15 1764.06 391.78 1772.24 391.78 C 1780.42 391.78 1784.89 389.15 1784.89 387.8 Z M 1784.89 398.68 L 1784.89 392.2 C 1781.86 393.98 1777.04 394.89 1772.24 394.89 C 1767.45 394.89 1762.62 393.98 1759.6 392.2 L 1759.6 398.68 C 1759.6 400.03 1764.06 402.65 1772.24 402.65 C 1780.42 402.65 1784.89 400.03 1784.89 398.68 Z M 1784.89 408.39 L 1784.89 403.07 C 1781.86 404.85 1777.04 405.77 1772.24 405.77 C 1767.45 405.77 1762.62 404.85 1759.6 403.07 L 1759.6 408.39 C 1759.6 409.73 1764.06 412.36 1772.24 412.36 C 1780.42 412.36 1784.89 409.73 1784.89 408.39 Z M 1759.6 376.74 C 1759.6 378.09 1764.06 380.72 1772.24 380.72 C 1780.42 380.72 1784.89 378.09 1784.89 376.74 C 1784.89 375.39 1780.42 372.76 1772.24 372.76 C 1764.06 372.76 1759.6 375.39 1759.6 376.74 Z M 1788 376.74 L 1788 408.39 C 1788 413.04 1780.07 415.47 1772.24 415.47 C 1764.41 415.47 1756.48 413.04 1756.48 408.39 L 1756.48 376.74 C 1756.48 372.08 1764.41 369.65 1772.24 369.65 C 1780.07 369.65 1788 372.08 1788 376.74 Z M 1773.78 355.49 C 1773.72 355.95 1773.6 356.28 1773.41 356.48 C 1773.22 356.68 1772.94 356.77 1772.57 356.77 C 1772.36 356.77 1772.13 356.75 1771.86 356.71 L 1771.86 358.07 C 1772.3 358.22 1772.74 358.3 1773.16 358.3 C 1773.72 358.3 1774.17 358.13 1774.51 357.81 C 1774.86 357.48 1775.13 356.96 1775.34 356.23 L 1777.83 347.53 L 1775.75 347.53 L 1774.65 353.08 L 1773.51 347.53 L 1771.37 347.53 L 1773.81 355.22 Z M 1770.82 355.08 L 1768.62 351.28 L 1770.7 347.53 L 1768.71 347.53 L 1767.69 349.85 L 1766.58 347.53 L 1764.42 347.53 L 1766.5 351.28 L 1764.39 355.08 L 1766.38 355.08 L 1767.42 352.68 L 1768.65 355.08 Z M 1760.09 353.15 C 1759.92 352.76 1759.84 352.15 1759.84 351.31 C 1759.84 350.47 1759.92 349.86 1760.09 349.46 C 1760.25 349.07 1760.51 348.87 1760.88 348.87 C 1761.24 348.87 1761.51 349.07 1761.67 349.46 C 1761.83 349.86 1761.91 350.47 1761.91 351.31 C 1761.91 352.15 1761.83 352.76 1761.67 353.15 C 1761.51 353.54 1761.24 353.73 1760.88 353.73 C 1760.51 353.73 1760.25 353.54 1760.09 353.15 Z M 1763.13 354.23 C 1763.66 353.55 1763.93 352.57 1763.93 351.31 C 1763.93 350.04 1763.66 349.06 1763.13 348.37 C 1762.6 347.68 1761.85 347.34 1760.88 347.34 C 1759.91 347.34 1759.15 347.68 1758.62 348.37 C 1758.09 349.06 1757.83 350.04 1757.83 351.31 C 1757.83 352.57 1758.09 353.55 1758.62 354.23 C 1759.15 354.92 1759.91 355.27 1760.88 355.27 C 1761.85 355.27 1762.6 354.92 1763.13 354.23 Z M 1755.11 355.08 L 1755.11 349.57 C 1755.54 349.33 1755.98 349.21 1756.45 349.21 C 1756.7 349.21 1756.96 349.24 1757.24 349.29 L 1757.24 347.5 C 1757.08 347.47 1756.92 347.46 1756.76 347.46 C 1756.44 347.46 1756.13 347.54 1755.84 347.71 C 1755.55 347.88 1755.26 348.14 1754.96 348.51 L 1754.74 347.53 L 1753.09 347.53 L 1753.09 355.08 Z M 1747.43 346.38 L 1748.39 346.38 C 1748.77 346.38 1749.06 346.52 1749.27 346.81 C 1749.47 347.09 1749.57 347.51 1749.57 348.06 C 1749.57 348.61 1749.47 349.03 1749.27 349.32 C 1749.06 349.6 1748.77 349.75 1748.39 349.75 L 1747.43 349.75 Z M 1748.64 351.28 C 1749.54 351.28 1750.27 350.99 1750.8 350.42 C 1751.34 349.84 1751.6 349.06 1751.6 348.06 C 1751.6 347.07 1751.34 346.28 1750.8 345.71 C 1750.27 345.13 1749.54 344.84 1748.64 344.84 L 1745.41 344.84 L 1745.41 355.08 L 1747.43 355.08 L 1747.43 351.28 Z M 1737.55 350.94 C 1737.95 351.16 1738.24 351.37 1738.41 351.58 C 1738.59 351.8 1738.67 352.05 1738.67 352.36 C 1738.67 352.77 1738.55 353.09 1738.31 353.31 C 1738.06 353.53 1737.7 353.64 1737.23 353.64 C 1736.91 353.64 1736.51 353.6 1736.06 353.5 C 1735.6 353.41 1735.19 353.29 1734.83 353.16 L 1734.83 354.71 C 1735.14 354.88 1735.53 355.03 1735.99 355.14 C 1736.45 355.24 1736.91 355.3 1737.38 355.3 C 1738.44 355.3 1739.27 355.02 1739.87 354.48 C 1740.48 353.93 1740.78 353.19 1740.78 352.25 C 1740.78 351.77 1740.71 351.36 1740.57 351.02 C 1740.42 350.68 1740.2 350.38 1739.9 350.1 C 1739.6 349.83 1739.2 349.55 1738.69 349.27 L 1737.89 348.84 C 1737.46 348.61 1737.17 348.39 1737.02 348.2 C 1736.86 348 1736.79 347.75 1736.79 347.44 C 1736.79 347.08 1736.91 346.79 1737.14 346.59 C 1737.38 346.38 1737.7 346.28 1738.11 346.28 C 1738.72 346.28 1739.42 346.42 1740.22 346.72 L 1740.22 345.15 C 1739.45 344.8 1738.68 344.62 1737.91 344.62 C 1737.29 344.62 1736.74 344.74 1736.26 344.98 C 1735.79 345.23 1735.41 345.57 1735.13 346.02 C 1734.86 346.47 1734.72 346.99 1734.72 347.59 C 1734.72 348.24 1734.88 348.79 1735.2 349.24 C 1735.51 349.7 1736.03 350.12 1736.76 350.51 Z M 1729.5 346.42 C 1730.16 346.42 1730.65 346.7 1730.95 347.24 C 1731.27 347.79 1731.42 348.64 1731.42 349.81 L 1731.42 350.1 C 1731.42 351.25 1731.26 352.11 1730.95 352.66 C 1730.63 353.22 1730.16 353.5 1729.52 353.5 L 1728.65 353.5 L 1728.65 346.42 Z M 1729.74 355.08 C 1730.98 355.08 1731.92 354.64 1732.57 353.78 C 1733.22 352.91 1733.54 351.64 1733.54 349.95 C 1733.54 348.28 1733.21 347.01 1732.55 346.14 C 1731.9 345.28 1730.93 344.84 1729.66 344.84 L 1726.58 344.84 L 1726.58 355.08 Z M 1720.75 346.38 L 1721.7 346.38 C 1722.09 346.38 1722.38 346.52 1722.58 346.81 C 1722.78 347.09 1722.88 347.51 1722.88 348.06 C 1722.88 348.61 1722.78 349.03 1722.58 349.32 C 1722.38 349.6 1722.09 349.75 1721.7 349.75 L 1720.75 349.75 Z M 1725.45 355.08 L 1723.38 350.97 C 1723.88 350.73 1724.26 350.35 1724.52 349.85 C 1724.78 349.35 1724.92 348.75 1724.92 348.06 C 1724.92 347.07 1724.65 346.28 1724.11 345.71 C 1723.58 345.13 1722.86 344.84 1721.96 344.84 L 1718.73 344.84 L 1718.73 355.08 L 1720.75 355.08 L 1720.75 351.28 L 1721.54 351.28 L 1723.33 355.08 Z M 1755.13 415.9 L 1720.03 415.9 C 1714.5 415.9 1710 411.4 1710 405.87 L 1710 348.13 C 1710 342.6 1714.5 338.1 1720.03 338.1 L 1777.77 338.1 C 1783.3 338.1 1787.8 342.6 1787.8 348.13 L 1787.8 370.78 L 1784.69 370.78 L 1784.69 348.13 C 1784.69 344.32 1781.59 341.21 1777.77 341.21 L 1720.03 341.21 C 1716.22 341.21 1713.11 344.32 1713.11 348.13 L 1713.11 405.87 C 1713.11 409.68 1716.22 412.79 1720.03 412.79 L 1755.13 412.79 Z" fill="#c925d1" style="fill: light-dark(rgb(201, 37, 209), rgb(255, 124, 255));" stroke="none" pointer-events="all"/></g></g><g data-cell-id="8VBJgbjWb-JPUTvmKhwd-23"><g><rect x="1710" y="210" width="78" height="78" fill="none" stroke="none" pointer-events="all"/><path d="M 1779.14 277.36 L 1779.14 237.75 C 1779.14 234.63 1777.56 231.27 1774.09 231.27 L 1770.27 231.27 L 1770.27 234.82 L 1774.09 234.82 C 1775.5 234.82 1775.59 237.73 1775.59 237.75 L 1775.59 275.59 L 1713.55 275.59 L 1713.55 237.77 C 1713.56 237.07 1713.84 234.82 1716.82 234.82 L 1718.86 234.82 L 1718.86 231.27 L 1716.82 231.27 C 1711.82 231.27 1710 235.15 1710 237.75 L 1710 277.36 C 1710 278.34 1710.79 279.14 1711.77 279.14 L 1777.36 279.14 C 1778.34 279.14 1779.14 278.34 1779.14 277.36 Z M 1751.39 233.05 C 1751.39 231.82 1751.79 228.99 1752.21 227.73 L 1759.64 227.73 L 1759.64 224.18 L 1751.23 224.18 C 1750.89 224.18 1750.57 224.28 1750.29 224.45 C 1748.74 225.42 1748.16 229.05 1747.96 231.27 L 1729.5 231.27 L 1729.5 234.82 L 1749.62 234.82 C 1750.59 234.82 1751.39 234.02 1751.39 233.05 Z M 1749 264.95 C 1749 266.91 1750.59 268.5 1752.55 268.5 C 1754.5 268.5 1756.09 266.91 1756.09 264.95 C 1756.09 263 1754.5 261.41 1752.55 261.41 C 1750.59 261.41 1749 263 1749 264.95 Z M 1745.45 264.95 C 1745.45 261.04 1748.63 257.86 1752.55 257.86 C 1756.46 257.86 1759.64 261.04 1759.64 264.95 C 1759.64 268.87 1756.46 272.05 1752.55 272.05 C 1748.63 272.05 1745.45 268.87 1745.45 264.95 Z M 1753.32 250.77 L 1750.08 244.76 L 1746.85 250.77 Z M 1756.29 254.32 L 1743.88 254.32 C 1743.26 254.32 1742.68 253.99 1742.36 253.45 C 1742.04 252.92 1742.02 252.25 1742.32 251.71 L 1748.52 240.18 C 1749.14 239.03 1751.03 239.03 1751.64 240.18 L 1757.85 251.71 C 1758.14 252.25 1758.13 252.92 1757.81 253.45 C 1757.49 253.99 1756.91 254.32 1756.29 254.32 Z M 1727.73 263.18 L 1733.05 263.18 L 1733.05 257.86 L 1727.73 257.86 Z M 1734.82 254.32 C 1735.8 254.32 1736.59 255.11 1736.59 256.09 L 1736.59 264.95 C 1736.59 265.93 1735.8 266.73 1734.82 266.73 L 1725.95 266.73 C 1724.97 266.73 1724.18 265.93 1724.18 264.95 L 1724.18 256.09 C 1724.18 255.11 1724.97 254.32 1725.95 254.32 Z M 1782.82 241.92 L 1782.64 245.46 C 1783.64 245.51 1784.45 246.36 1784.45 247.36 L 1784.45 284.45 L 1722.41 284.45 L 1722.41 282.68 L 1718.86 282.68 L 1718.86 286.23 L 1718.86 286.23 C 1718.87 287.21 1719.66 288 1720.64 288 L 1786.23 288 C 1787.21 288 1788 287.21 1788 286.23 L 1788 247.36 C 1788 244.45 1785.73 242.06 1782.82 241.92 Z M 1741.91 216.03 L 1741.91 227.73 L 1745.45 227.73 L 1745.45 216.08 L 1747.75 218.35 L 1750.25 215.83 L 1744.88 210.51 C 1744.55 210.18 1744.1 210 1743.63 210 L 1743.62 210 C 1743.15 210 1742.7 210.19 1742.37 210.52 L 1737.1 215.84 L 1739.62 218.34 Z M 1717.59 239.6 L 1722.89 245.04 C 1723.22 245.38 1723.67 245.58 1724.15 245.58 L 1724.16 245.58 C 1724.63 245.58 1725.09 245.39 1725.42 245.05 L 1730.77 239.61 L 1728.23 237.12 L 1725.95 239.44 L 1725.95 224.18 L 1722.41 224.18 L 1722.41 239.47 L 1720.13 237.13 Z M 1760.89 237.11 L 1763.18 239.4 L 1763.18 224.18 L 1766.73 224.18 L 1766.73 239.4 L 1769.02 237.11 L 1771.53 239.62 L 1766.21 244.94 C 1765.86 245.28 1765.41 245.45 1764.95 245.45 C 1764.5 245.45 1764.05 245.28 1763.7 244.94 L 1758.38 239.62 Z" fill="#7aa116" style="fill: light-dark(rgb(122, 161, 22), rgb(97, 130, 11));" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 295px; margin-left: 1749px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">EFS Mount</div></div></div></foreignObject><text x="1749" y="307" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">EFS Mount</text></switch></g></g></g><g data-cell-id="8VBJgbjWb-JPUTvmKhwd-24"><g><path d="M 1538 319 L 1624 319 L 1624 249 L 1703.63 249" fill="none" stroke="#000000" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1708.88 249 L 1701.88 252.5 L 1703.63 249 L 1701.88 245.5 Z" fill="#000000" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="8VBJgbjWb-JPUTvmKhwd-25"><g><path d="M 1538 319 L 1624 319 L 1624 377 L 1703.63 377" fill="none" stroke="#000000" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1708.88 377 L 1701.88 380.5 L 1703.63 377 L 1701.88 373.5 Z" fill="#000000" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="8VBJgbjWb-JPUTvmKhwd-26"><g><path d="M 1538 699 Q 1749.08 699 1749.08 422.27" fill="none" stroke="#000000" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1749.08 417.02 L 1752.58 424.02 L 1749.08 422.27 L 1745.58 424.02 Z" fill="#000000" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="8VBJgbjWb-JPUTvmKhwd-28"><g><path d="M 1788 377 Q 1788 377 2133.63 377" fill="none" stroke="#000000" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2138.88 377 L 2131.88 380.5 L 2133.63 377 L 2131.88 373.5 Z" fill="#000000" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="0dZXfTm35kw52Q68pXVM-29"><g><path d="M 2070 171 L 2490 171 L 2490 947 L 2070 947 Z" fill="none" stroke="#879196" style="stroke: light-dark(rgb(135, 145, 150), rgb(106, 115, 119));" stroke-miterlimit="10" pointer-events="all"/><path d="M 2080.56 175.12 C 2079.34 175.12 2078.28 175.59 2077.59 176.19 C 2077.59 176.19 2077.59 176.2 2077.59 176.2 C 2076.32 177.33 2075.76 178.75 2075.87 180.2 C 2075 180.47 2074.41 180.96 2074 181.55 C 2074 181.56 2073.99 181.56 2073.99 181.57 C 2073.36 182.58 2073.4 183.72 2073.63 184.54 C 2073.63 184.55 2073.63 184.55 2073.63 184.56 C 2074.24 186.35 2075.79 187.07 2077 187.07 L 2081.18 187.07 L 2081.18 186.32 L 2077 186.32 C 2076.08 186.32 2074.86 185.81 2074.35 184.33 C 2074.17 183.67 2074.15 182.75 2074.62 181.97 C 2075.01 181.42 2075.45 181.03 2076.33 180.84 C 2076.52 180.8 2076.65 180.61 2076.62 180.42 C 2076.42 179.09 2076.88 177.84 2078.09 176.76 L 2078.09 176.76 C 2078.92 176.03 2080.53 175.47 2082.27 176.23 C 2083.4 176.81 2084.24 177.81 2084.6 178.97 C 2084.64 179.1 2084.75 179.2 2084.88 179.23 C 2085.02 179.25 2085.16 179.2 2085.25 179.09 C 2085.48 178.8 2085.77 178.57 2086.09 178.49 C 2086.4 178.41 2086.77 178.45 2087.27 178.78 C 2087.97 179.29 2088.03 179.89 2088 180.47 C 2087.99 180.58 2088.03 180.68 2088.11 180.76 C 2088.18 180.83 2088.29 180.87 2088.39 180.87 C 2089.12 180.83 2089.74 181.17 2090.4 182.05 C 2090.65 182.47 2090.83 183.07 2090.84 183.65 C 2090.85 184.25 2090.68 184.83 2090.24 185.28 C 2090.24 185.29 2090.23 185.29 2090.23 185.3 C 2089.63 185.99 2089.06 186.32 2088.14 186.32 L 2083.87 186.32 L 2083.87 187.07 L 2088.14 187.07 C 2089.26 187.07 2090.1 186.59 2090.79 185.8 C 2091.38 185.18 2091.6 184.39 2091.59 183.65 C 2091.58 182.9 2091.36 182.2 2091.04 181.66 C 2091.03 181.64 2091.03 181.63 2091.02 181.62 C 2090.36 180.74 2089.59 180.25 2088.74 180.16 C 2088.71 179.51 2088.5 178.74 2087.7 178.17 C 2087.7 178.17 2087.7 178.17 2087.7 178.17 C 2087.06 177.73 2086.43 177.63 2085.9 177.77 C 2085.58 177.85 2085.32 178.02 2085.09 178.21 C 2084.6 177.08 2083.74 176.13 2082.6 175.55 C 2082.59 175.55 2082.58 175.55 2082.58 175.54 C 2082.07 175.32 2081.57 175.2 2081.09 175.14 C 2080.91 175.12 2080.74 175.12 2080.56 175.12 Z M 2080.87 180.73 L 2080.87 181.11 L 2080.87 183.97 L 2082.16 183.97 L 2082.16 189.93 L 2077.64 189.93 L 2077.64 188.71 L 2074.35 188.71 L 2074.35 189.08 L 2074.35 191.94 L 2077.64 191.94 L 2077.64 190.68 L 2087.45 190.68 L 2087.45 191.94 L 2090.74 191.94 L 2090.74 188.71 L 2087.45 188.71 L 2087.45 189.08 L 2087.45 189.93 L 2082.91 189.93 L 2082.91 183.97 L 2084.16 183.97 L 2084.16 180.73 Z M 2081.62 181.48 L 2083.41 181.48 L 2083.41 183.22 L 2081.62 183.22 Z M 2075.1 189.46 L 2076.89 189.46 L 2076.89 191.19 L 2075.1 191.19 Z M 2088.2 189.46 L 2089.99 189.46 L 2089.99 191.19 L 2088.2 191.19 Z M 2070 196 L 2070 171 L 2095 171 L 2095 196 Z" fill="#879196" style="fill: light-dark(rgb(135, 145, 150), rgb(106, 115, 119));" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 388px; height: 1px; padding-top: 178px; margin-left: 2102px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; color: #879196; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#879196, #6a7377); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Protected Subnets (AZ1, AZ2, AZ3)</div></div></div></foreignObject><text x="2102" y="190" fill="#879196" font-family="&quot;Helvetica&quot;" font-size="12px">Protected Subnets (AZ1, AZ2, AZ3)</text></switch></g></g></g><g data-cell-id="8VBJgbjWb-JPUTvmKhwd-27"><g><path d="M 2140 338 L 2218 338 L 2218 416 L 2140 416 Z" fill="#c925d1" style="fill: light-dark(rgb(201, 37, 209), rgb(255, 124, 255));" stroke="none" pointer-events="all"/><path d="M 2184.48 353.38 L 2181.24 353.38 L 2181.24 351.21 L 2184.48 351.21 L 2184.48 347.95 L 2186.63 347.95 L 2186.63 351.21 L 2189.87 351.21 L 2189.87 353.38 L 2186.63 353.38 L 2186.63 356.64 L 2184.48 356.64 Z M 2197.42 365.32 L 2194.19 365.32 L 2194.19 363.15 L 2197.42 363.15 L 2197.42 359.89 L 2199.58 359.89 L 2199.58 363.15 L 2202.82 363.15 L 2202.82 365.32 L 2199.58 365.32 L 2199.58 368.58 L 2197.42 368.58 Z M 2191.8 399.63 C 2189.74 394.39 2184.99 389.61 2179.78 387.53 C 2184.99 385.46 2189.74 380.68 2191.8 375.44 C 2193.86 380.68 2198.62 385.46 2203.82 387.53 C 2198.62 389.61 2193.86 394.39 2191.8 399.63 Z M 2209.12 386.45 C 2201.53 386.45 2192.88 377.74 2192.88 370.11 C 2192.88 369.51 2192.4 369.02 2191.8 369.02 C 2191.21 369.02 2190.73 369.51 2190.73 370.11 C 2190.73 377.74 2182.07 386.45 2174.49 386.45 C 2173.89 386.45 2173.41 386.94 2173.41 387.53 C 2173.41 388.14 2173.89 388.62 2174.49 388.62 C 2182.07 388.62 2190.73 397.33 2190.73 404.96 C 2190.73 405.56 2191.21 406.05 2191.8 406.05 C 2192.4 406.05 2192.88 405.56 2192.88 404.96 C 2192.88 397.33 2201.53 388.62 2209.12 388.62 C 2209.72 388.62 2210.2 388.14 2210.2 387.53 C 2210.2 386.94 2209.72 386.45 2209.12 386.45 Z M 2149.96 365.06 C 2153.1 367.36 2159.2 368.58 2165.06 368.58 C 2170.92 368.58 2177.02 367.36 2180.16 365.06 L 2180.16 375.46 C 2178.61 377.54 2172.89 379.59 2165.28 379.59 C 2156.51 379.59 2149.96 376.83 2149.96 374.37 Z M 2165.06 356.64 C 2174.42 356.64 2180.16 359.48 2180.16 361.52 C 2180.16 363.56 2174.42 366.41 2165.06 366.41 C 2155.7 366.41 2149.96 363.56 2149.96 361.52 C 2149.96 359.48 2155.7 356.64 2165.06 356.64 Z M 2180.16 397.38 C 2180.16 399.88 2173.7 402.68 2165.05 402.68 C 2156.41 402.68 2149.96 399.88 2149.96 397.38 L 2149.96 390.44 C 2153.14 392.87 2159.36 394.15 2165.34 394.15 C 2169.49 394.15 2173.51 393.56 2176.64 392.49 L 2175.95 390.44 C 2173.03 391.43 2169.26 391.98 2165.34 391.98 C 2156.53 391.98 2149.96 389.23 2149.96 386.76 L 2149.96 378.05 C 2153.13 380.48 2159.33 381.76 2165.28 381.76 C 2171.65 381.76 2177.12 380.44 2180.16 378.35 L 2180.16 381.6 L 2182.32 381.6 L 2182.32 361.52 C 2182.32 356.94 2173.43 354.46 2165.06 354.46 C 2157.03 354.46 2148.54 356.75 2147.87 360.98 L 2147.8 360.98 L 2147.8 397.38 C 2147.8 402.23 2156.69 404.85 2165.05 404.85 C 2173.42 404.85 2182.32 402.23 2182.32 397.38 L 2182.32 393.55 L 2180.16 393.55 Z" fill="#ffffff" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 423px; margin-left: 2179px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; "><div>AWS Aurora Primary Node</div></div></div></div></foreignObject><text x="2179" y="435" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">AWS Aurora Pr...</text></switch></g></g></g><g data-cell-id="8VBJgbjWb-JPUTvmKhwd-29"><g><path d="M 2140 450 L 2218 450 L 2218 528 L 2140 528 Z" fill="#c925d1" style="fill: light-dark(rgb(201, 37, 209), rgb(255, 124, 255));" stroke="none" pointer-events="all"/><path d="M 2184.48 465.38 L 2181.24 465.38 L 2181.24 463.21 L 2184.48 463.21 L 2184.48 459.95 L 2186.63 459.95 L 2186.63 463.21 L 2189.87 463.21 L 2189.87 465.38 L 2186.63 465.38 L 2186.63 468.64 L 2184.48 468.64 Z M 2197.42 477.32 L 2194.19 477.32 L 2194.19 475.15 L 2197.42 475.15 L 2197.42 471.89 L 2199.58 471.89 L 2199.58 475.15 L 2202.82 475.15 L 2202.82 477.32 L 2199.58 477.32 L 2199.58 480.58 L 2197.42 480.58 Z M 2191.8 511.63 C 2189.74 506.39 2184.99 501.61 2179.78 499.53 C 2184.99 497.46 2189.74 492.68 2191.8 487.44 C 2193.86 492.68 2198.62 497.46 2203.82 499.53 C 2198.62 501.61 2193.86 506.39 2191.8 511.63 Z M 2209.12 498.45 C 2201.53 498.45 2192.88 489.74 2192.88 482.11 C 2192.88 481.51 2192.4 481.02 2191.8 481.02 C 2191.21 481.02 2190.73 481.51 2190.73 482.11 C 2190.73 489.74 2182.07 498.45 2174.49 498.45 C 2173.89 498.45 2173.41 498.94 2173.41 499.53 C 2173.41 500.14 2173.89 500.62 2174.49 500.62 C 2182.07 500.62 2190.73 509.33 2190.73 516.96 C 2190.73 517.56 2191.21 518.05 2191.8 518.05 C 2192.4 518.05 2192.88 517.56 2192.88 516.96 C 2192.88 509.33 2201.53 500.62 2209.12 500.62 C 2209.72 500.62 2210.2 500.14 2210.2 499.53 C 2210.2 498.94 2209.72 498.45 2209.12 498.45 Z M 2149.96 477.06 C 2153.1 479.36 2159.2 480.58 2165.06 480.58 C 2170.92 480.58 2177.02 479.36 2180.16 477.06 L 2180.16 487.46 C 2178.61 489.54 2172.89 491.59 2165.28 491.59 C 2156.51 491.59 2149.96 488.83 2149.96 486.37 Z M 2165.06 468.64 C 2174.42 468.64 2180.16 471.48 2180.16 473.52 C 2180.16 475.56 2174.42 478.41 2165.06 478.41 C 2155.7 478.41 2149.96 475.56 2149.96 473.52 C 2149.96 471.48 2155.7 468.64 2165.06 468.64 Z M 2180.16 509.38 C 2180.16 511.88 2173.7 514.68 2165.05 514.68 C 2156.41 514.68 2149.96 511.88 2149.96 509.38 L 2149.96 502.44 C 2153.14 504.87 2159.36 506.15 2165.34 506.15 C 2169.49 506.15 2173.51 505.56 2176.64 504.49 L 2175.95 502.44 C 2173.03 503.43 2169.26 503.98 2165.34 503.98 C 2156.53 503.98 2149.96 501.23 2149.96 498.76 L 2149.96 490.05 C 2153.13 492.48 2159.33 493.76 2165.28 493.76 C 2171.65 493.76 2177.12 492.44 2180.16 490.35 L 2180.16 493.6 L 2182.32 493.6 L 2182.32 473.52 C 2182.32 468.94 2173.43 466.46 2165.06 466.46 C 2157.03 466.46 2148.54 468.75 2147.87 472.98 L 2147.8 472.98 L 2147.8 509.38 C 2147.8 514.23 2156.69 516.85 2165.05 516.85 C 2173.42 516.85 2182.32 514.23 2182.32 509.38 L 2182.32 505.55 L 2180.16 505.55 Z" fill="#ffffff" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 535px; margin-left: 2179px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; "><div>AWS Aurora Read Node</div></div></div></div></foreignObject><text x="2179" y="547" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">AWS Aurora Re...</text></switch></g></g></g><g data-cell-id="8VBJgbjWb-JPUTvmKhwd-34"><g><path d="M 2218 608 L 2345.3 608" fill="none" stroke="#000000" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2350.55 608 L 2343.55 611.5 L 2345.3 608 L 2343.55 604.5 Z" fill="#000000" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="8VBJgbjWb-JPUTvmKhwd-30"><g><path d="M 2140 569 L 2218 569 L 2218 647 L 2140 647 Z" fill="#c925d1" style="fill: light-dark(rgb(201, 37, 209), rgb(255, 124, 255));" stroke="none" pointer-events="all"/><path d="M 2184.48 584.38 L 2181.24 584.38 L 2181.24 582.21 L 2184.48 582.21 L 2184.48 578.95 L 2186.63 578.95 L 2186.63 582.21 L 2189.87 582.21 L 2189.87 584.38 L 2186.63 584.38 L 2186.63 587.64 L 2184.48 587.64 Z M 2197.42 596.32 L 2194.19 596.32 L 2194.19 594.15 L 2197.42 594.15 L 2197.42 590.89 L 2199.58 590.89 L 2199.58 594.15 L 2202.82 594.15 L 2202.82 596.32 L 2199.58 596.32 L 2199.58 599.58 L 2197.42 599.58 Z M 2191.8 630.63 C 2189.74 625.39 2184.99 620.61 2179.78 618.53 C 2184.99 616.46 2189.74 611.68 2191.8 606.44 C 2193.86 611.68 2198.62 616.46 2203.82 618.53 C 2198.62 620.61 2193.86 625.39 2191.8 630.63 Z M 2209.12 617.45 C 2201.53 617.45 2192.88 608.74 2192.88 601.11 C 2192.88 600.51 2192.4 600.02 2191.8 600.02 C 2191.21 600.02 2190.73 600.51 2190.73 601.11 C 2190.73 608.74 2182.07 617.45 2174.49 617.45 C 2173.89 617.45 2173.41 617.94 2173.41 618.53 C 2173.41 619.14 2173.89 619.62 2174.49 619.62 C 2182.07 619.62 2190.73 628.33 2190.73 635.96 C 2190.73 636.56 2191.21 637.05 2191.8 637.05 C 2192.4 637.05 2192.88 636.56 2192.88 635.96 C 2192.88 628.33 2201.53 619.62 2209.12 619.62 C 2209.72 619.62 2210.2 619.14 2210.2 618.53 C 2210.2 617.94 2209.72 617.45 2209.12 617.45 Z M 2149.96 596.06 C 2153.1 598.36 2159.2 599.58 2165.06 599.58 C 2170.92 599.58 2177.02 598.36 2180.16 596.06 L 2180.16 606.46 C 2178.61 608.54 2172.89 610.59 2165.28 610.59 C 2156.51 610.59 2149.96 607.83 2149.96 605.37 Z M 2165.06 587.64 C 2174.42 587.64 2180.16 590.48 2180.16 592.52 C 2180.16 594.56 2174.42 597.41 2165.06 597.41 C 2155.7 597.41 2149.96 594.56 2149.96 592.52 C 2149.96 590.48 2155.7 587.64 2165.06 587.64 Z M 2180.16 628.38 C 2180.16 630.88 2173.7 633.68 2165.05 633.68 C 2156.41 633.68 2149.96 630.88 2149.96 628.38 L 2149.96 621.44 C 2153.14 623.87 2159.36 625.15 2165.34 625.15 C 2169.49 625.15 2173.51 624.56 2176.64 623.49 L 2175.95 621.44 C 2173.03 622.43 2169.26 622.98 2165.34 622.98 C 2156.53 622.98 2149.96 620.23 2149.96 617.76 L 2149.96 609.05 C 2153.13 611.48 2159.33 612.76 2165.28 612.76 C 2171.65 612.76 2177.12 611.44 2180.16 609.35 L 2180.16 612.6 L 2182.32 612.6 L 2182.32 592.52 C 2182.32 587.94 2173.43 585.46 2165.06 585.46 C 2157.03 585.46 2148.54 587.75 2147.87 591.98 L 2147.8 591.98 L 2147.8 628.38 C 2147.8 633.23 2156.69 635.85 2165.05 635.85 C 2173.42 635.85 2182.32 633.23 2182.32 628.38 L 2182.32 624.55 L 2180.16 624.55 Z" fill="#ffffff" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 654px; margin-left: 2179px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; "><div>AWS Aurora Read Node</div></div></div></div></foreignObject><text x="2179" y="666" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">AWS Aurora Re...</text></switch></g></g></g><g data-cell-id="8VBJgbjWb-JPUTvmKhwd-31"><g><path d="M 1788 377 Q 1964 377 1964 433 Q 1964 489 2133.63 489" fill="none" stroke="#000000" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2138.88 489 L 2131.88 492.5 L 2133.63 489 L 2131.88 485.5 Z" fill="#000000" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="8VBJgbjWb-JPUTvmKhwd-32"><g><path d="M 1788 377 Q 1964 377 1964 492.54 Q 1964 608.08 2133.63 608" fill="none" stroke="#000000" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2138.88 608 L 2131.88 611.5 L 2133.63 608 L 2131.88 604.5 Z" fill="#000000" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="8VBJgbjWb-JPUTvmKhwd-33"><g><rect x="2350" y="573.5" width="78" height="69" fill="none" stroke="none" pointer-events="all"/><path d="M 2388.82 591.59 C 2379.45 591.59 2369.41 593.9 2368.7 598.95 L 2368.64 598.95 L 2368.64 634.57 C 2368.64 639.78 2378.88 642.5 2389 642.5 C 2399.12 642.5 2409.36 639.78 2409.36 634.57 L 2409.36 598.95 L 2409.29 598.95 C 2408.57 594.12 2398.65 591.59 2388.82 591.59 Z M 2388.82 594.98 C 2400.14 594.98 2405.97 598.16 2405.97 599.52 C 2405.97 600.87 2400.14 604.05 2388.82 604.05 C 2377.96 604.05 2372.03 601.05 2372.03 599.52 C 2372.03 597.98 2377.96 594.98 2388.82 594.98 Z M 2401.33 617.65 C 2403.21 617.13 2404.75 616.53 2405.97 615.87 L 2405.97 623.33 C 2405.97 623.63 2405.03 625 2400.43 626.25 L 2401.33 629.53 C 2403.21 629.01 2404.75 628.42 2405.97 627.75 L 2405.97 634.57 C 2405.97 636.17 2399.53 639.11 2389 639.11 C 2378.48 639.11 2372.03 636.17 2372.03 634.57 L 2372.03 627.76 C 2373.25 628.43 2374.79 629.03 2376.67 629.54 L 2377.57 626.27 C 2372.97 625.01 2372.03 623.63 2372.03 623.33 L 2372.03 615.88 C 2373.25 616.55 2374.79 617.15 2376.67 617.66 L 2377.57 614.39 C 2372.97 613.13 2372.03 611.75 2372.03 611.45 L 2372.03 604.16 C 2375.9 606.39 2382.51 607.45 2388.82 607.45 C 2395.44 607.45 2402.1 606.29 2405.97 604.07 L 2405.97 611.45 C 2405.97 611.75 2405.03 613.12 2400.43 614.37 Z M 2384.47 590.47 L 2384.47 583.68 L 2379.38 583.68 L 2379.38 590.47 L 2375.99 590.47 L 2375.99 581.98 C 2375.99 581.05 2376.75 580.29 2377.69 580.29 L 2386.17 580.29 C 2387.11 580.29 2387.87 581.05 2387.87 581.98 L 2387.87 590.47 Z M 2398.62 590.47 L 2398.62 583.68 L 2393.53 583.68 L 2393.53 590.47 L 2390.13 590.47 L 2390.13 581.98 C 2390.13 581.05 2390.89 580.29 2391.83 580.29 L 2400.31 580.29 C 2401.25 580.29 2402.01 581.05 2402.01 581.98 L 2402.01 590.47 Z M 2412.76 602.35 L 2412.76 583.68 L 2407.67 583.68 L 2407.67 592.17 L 2404.27 592.17 L 2404.27 581.98 C 2404.27 581.05 2405.03 580.29 2405.97 580.29 L 2414.45 580.29 C 2415.39 580.29 2416.15 581.05 2416.15 581.98 L 2416.15 604.04 C 2416.15 604.98 2415.39 605.74 2414.45 605.74 L 2411.06 605.74 L 2411.06 602.35 Z M 2365.24 602.35 L 2366.94 602.35 L 2366.94 605.74 L 2363.55 605.74 C 2362.61 605.74 2361.85 604.98 2361.85 604.04 L 2361.85 581.98 C 2361.85 581.05 2362.61 580.29 2363.55 580.29 L 2372.03 580.29 C 2372.97 580.29 2373.73 581.05 2373.73 581.98 L 2373.73 592.17 L 2370.33 592.17 L 2370.33 583.68 L 2365.24 583.68 Z M 2422.09 586.23 C 2422.09 587.63 2423.23 588.77 2424.64 588.77 C 2425.57 588.77 2426.33 589.53 2426.33 590.47 L 2426.33 617.62 C 2426.33 618.56 2425.57 619.32 2424.64 619.32 L 2411.06 619.32 L 2411.06 615.92 L 2417.85 615.92 L 2417.85 612.53 L 2411.06 612.53 L 2411.06 609.14 L 2419.54 609.14 C 2420.48 609.14 2421.24 609.9 2421.24 610.83 L 2421.24 615.92 L 2422.94 615.92 L 2422.94 591.92 C 2420.49 591.19 2418.7 588.91 2418.7 586.23 C 2418.7 583.54 2420.49 581.27 2422.94 580.54 L 2422.94 576.89 L 2355.06 576.89 L 2355.06 580.54 C 2357.51 581.27 2359.3 583.54 2359.3 586.23 C 2359.3 588.91 2357.51 591.19 2355.06 591.92 L 2355.06 615.92 L 2356.76 615.92 L 2356.76 610.83 C 2356.76 609.9 2357.52 609.14 2358.46 609.14 L 2366.94 609.14 L 2366.94 612.53 L 2360.15 612.53 L 2360.15 615.92 L 2366.94 615.92 L 2366.94 619.32 L 2353.36 619.32 C 2352.43 619.32 2351.67 618.56 2351.67 617.62 L 2351.67 590.47 C 2351.67 589.53 2352.43 588.77 2353.36 588.77 C 2354.77 588.77 2355.91 587.63 2355.91 586.23 C 2355.91 584.82 2354.77 583.68 2353.36 583.68 C 2352.43 583.68 2351.67 582.92 2351.67 581.98 L 2351.67 575.2 C 2351.67 574.26 2352.43 573.5 2353.36 573.5 L 2424.64 573.5 C 2425.57 573.5 2426.33 574.26 2426.33 575.2 L 2426.33 581.98 C 2426.33 582.92 2425.57 583.68 2424.64 583.68 C 2423.23 583.68 2422.09 584.82 2422.09 586.23 Z M 2398.33 612.87 L 2398.33 613.54 L 2391.25 632.22 C 2391.25 632.72 2390.73 632.89 2390.38 632.89 L 2387.62 632.89 C 2387.1 632.89 2386.93 632.72 2386.75 632.22 L 2379.67 613.54 L 2379.67 612.87 C 2379.67 612.53 2379.84 612.53 2380.19 612.53 L 2382.78 612.53 C 2383.12 612.53 2383.47 612.7 2383.64 613.03 L 2389 629.19 L 2394.36 613.03 C 2394.36 612.7 2394.7 612.53 2395.22 612.53 L 2397.81 612.53 C 2398.16 612.53 2398.33 612.53 2398.33 612.87 Z" fill="#c925d1" style="fill: light-dark(rgb(201, 37, 209), rgb(255, 124, 255));" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 649px; margin-left: 2389px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; "><div>Elasticache</div><div>for Valkey</div></div></div></div></foreignObject><text x="2389" y="661" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Elasticache...</text></switch></g></g></g><g data-cell-id="8VBJgbjWb-JPUTvmKhwd-36"><g><rect x="823" y="990" width="75" height="78" fill="none" stroke="none" pointer-events="all"/><path d="M 889.4 1032.77 L 889.8 1029.82 C 892.96 1031.75 893.94 1032.86 894.22 1033.33 C 893.38 1033.58 891.69 1033.36 889.4 1032.77 Z M 858.06 1022.06 C 858.06 1021.97 858.14 1021.89 858.23 1021.89 C 858.26 1021.89 858.28 1021.91 858.3 1021.92 L 858.17 1022.18 C 858.12 1022.17 858.06 1022.13 858.06 1022.06 Z M 882.34 1058.69 C 882.33 1058.77 882.33 1058.85 882.33 1058.92 C 882.33 1059.2 881.65 1060.24 878.67 1061.42 C 877.34 1061.95 875.77 1062.42 874 1062.82 C 869.44 1063.88 863.84 1064.46 858.23 1064.46 C 843.24 1064.46 834.14 1060.62 834.14 1058.92 C 834.14 1058.85 834.14 1058.77 834.12 1058.69 L 827.33 1008.21 C 827.77 1008.46 828.23 1008.71 828.73 1008.94 C 828.87 1009.01 829.02 1009.07 829.16 1009.14 C 829.59 1009.34 830.03 1009.53 830.49 1009.71 C 830.69 1009.79 830.89 1009.87 831.09 1009.94 C 831.58 1010.13 832.09 1010.31 832.61 1010.48 C 832.76 1010.53 832.91 1010.58 833.06 1010.63 C 833.74 1010.84 834.43 1011.04 835.15 1011.23 C 835.35 1011.28 835.56 1011.33 835.76 1011.38 C 836.31 1011.52 836.87 1011.66 837.45 1011.79 C 837.7 1011.84 837.95 1011.9 838.2 1011.95 C 838.84 1012.09 839.51 1012.22 840.18 1012.34 C 840.32 1012.37 840.46 1012.39 840.61 1012.42 C 841.42 1012.56 842.25 1012.69 843.09 1012.82 C 843.33 1012.85 843.57 1012.88 843.81 1012.91 C 844.45 1013 845.09 1013.08 845.75 1013.16 C 846.01 1013.19 846.28 1013.22 846.55 1013.25 C 847.33 1013.33 848.13 1013.41 848.93 1013.48 C 849.02 1013.49 849.12 1013.5 849.21 1013.5 C 850.1 1013.58 851 1013.64 851.9 1013.69 C 852.16 1013.7 852.42 1013.71 852.67 1013.73 C 853.35 1013.76 854.03 1013.79 854.71 1013.81 C 854.98 1013.82 855.24 1013.83 855.51 1013.83 C 856.42 1013.85 857.33 1013.87 858.23 1013.87 C 859.13 1013.87 860.04 1013.85 860.95 1013.83 C 861.22 1013.83 861.48 1013.82 861.75 1013.81 C 862.43 1013.79 863.11 1013.76 863.79 1013.73 C 864.05 1013.71 864.3 1013.7 864.56 1013.69 C 865.46 1013.64 866.36 1013.58 867.25 1013.5 C 867.35 1013.5 867.45 1013.48 867.54 1013.48 C 868.34 1013.41 869.13 1013.33 869.91 1013.25 C 870.19 1013.22 870.45 1013.19 870.72 1013.16 C 871.37 1013.08 872.01 1013 872.65 1012.91 C 872.89 1012.88 873.14 1012.85 873.38 1012.82 C 874.22 1012.69 875.04 1012.56 875.86 1012.42 C 876 1012.39 876.15 1012.37 876.29 1012.34 C 876.96 1012.22 877.62 1012.09 878.27 1011.95 C 878.52 1011.9 878.77 1011.84 879.02 1011.79 C 879.59 1011.66 880.15 1011.52 880.71 1011.38 C 880.91 1011.33 881.12 1011.28 881.32 1011.23 C 882.04 1011.04 882.73 1010.84 883.4 1010.63 C 883.56 1010.58 883.7 1010.53 883.85 1010.48 C 884.38 1010.31 884.89 1010.13 885.38 1009.94 C 885.58 1009.87 885.78 1009.79 885.97 1009.71 C 886.44 1009.53 886.88 1009.34 887.31 1009.14 C 887.45 1009.07 887.6 1009.01 887.74 1008.95 C 888.23 1008.71 888.7 1008.46 889.14 1008.21 L 885.97 1031.77 C 879.53 1029.7 870.48 1025.95 861.92 1021.88 C 861.83 1019.92 860.22 1018.35 858.23 1018.35 C 856.19 1018.35 854.52 1020.02 854.52 1022.06 C 854.52 1024.11 856.19 1025.77 858.23 1025.77 C 859.04 1025.77 859.78 1025.51 860.39 1025.07 C 868.06 1028.75 877.85 1033.1 885.49 1035.3 Z M 858.23 993.54 C 876.68 993.54 889.67 999.26 889.91 1002.43 L 889.87 1002.8 C 889.84 1002.91 889.77 1003.02 889.71 1003.13 C 889.66 1003.23 889.63 1003.31 889.56 1003.41 C 889.48 1003.52 889.37 1003.63 889.27 1003.74 C 889.18 1003.84 889.1 1003.94 888.99 1004.03 C 888.87 1004.14 888.72 1004.25 888.57 1004.36 C 888.44 1004.47 888.33 1004.57 888.17 1004.67 C 888.01 1004.78 887.82 1004.89 887.64 1005 C 887.46 1005.1 887.3 1005.21 887.11 1005.31 C 886.91 1005.42 886.68 1005.52 886.47 1005.63 C 886.25 1005.74 886.04 1005.84 885.8 1005.95 C 885.57 1006.05 885.31 1006.15 885.07 1006.25 C 884.8 1006.36 884.55 1006.47 884.27 1006.57 C 884.01 1006.67 883.71 1006.77 883.44 1006.86 C 883.13 1006.97 882.84 1007.07 882.52 1007.18 C 882.22 1007.27 881.89 1007.36 881.58 1007.45 C 881.24 1007.55 880.92 1007.65 880.56 1007.75 C 880.22 1007.84 879.86 1007.93 879.5 1008.02 C 879.13 1008.11 878.78 1008.2 878.4 1008.29 C 878.01 1008.38 877.6 1008.46 877.2 1008.54 C 876.82 1008.62 876.44 1008.7 876.04 1008.78 C 875.61 1008.86 875.16 1008.93 874.72 1009.01 C 874.31 1009.08 873.91 1009.15 873.49 1009.22 C 873.03 1009.29 872.54 1009.35 872.06 1009.42 C 871.63 1009.48 871.21 1009.54 870.76 1009.6 C 870.25 1009.66 869.7 1009.71 869.16 1009.77 C 868.73 1009.81 868.31 1009.86 867.87 1009.9 C 867.27 1009.96 866.65 1010 866.04 1010.04 C 865.63 1010.07 865.23 1010.11 864.81 1010.14 C 864.14 1010.17 863.43 1010.2 862.74 1010.23 C 862.35 1010.24 861.98 1010.27 861.59 1010.28 C 860.5 1010.31 859.37 1010.33 858.23 1010.33 C 857.09 1010.33 855.97 1010.31 854.87 1010.28 C 854.48 1010.27 854.1 1010.24 853.71 1010.23 C 853.02 1010.2 852.33 1010.17 851.66 1010.14 C 851.21 1010.11 850.79 1010.07 850.36 1010.04 C 849.77 1010 849.17 1009.96 848.59 1009.9 C 848.15 1009.86 847.73 1009.81 847.3 1009.77 C 846.76 1009.71 846.22 1009.66 845.7 1009.6 C 845.26 1009.54 844.84 1009.48 844.41 1009.42 C 843.93 1009.35 843.44 1009.29 842.97 1009.22 C 842.53 1009.15 842.12 1009.07 841.7 1009 C 841.27 1008.93 840.84 1008.86 840.43 1008.78 C 840.02 1008.7 839.64 1008.62 839.24 1008.53 C 838.85 1008.45 838.44 1008.37 838.07 1008.29 C 837.68 1008.2 837.33 1008.11 836.96 1008.02 C 836.61 1007.93 836.24 1007.84 835.9 1007.75 C 835.54 1007.65 835.22 1007.55 834.88 1007.45 C 834.56 1007.36 834.24 1007.27 833.94 1007.18 C 833.62 1007.07 833.32 1006.97 833.02 1006.86 C 832.74 1006.76 832.45 1006.67 832.2 1006.57 C 831.91 1006.47 831.66 1006.36 831.39 1006.25 C 831.15 1006.15 830.89 1006.05 830.66 1005.95 C 830.43 1005.84 830.22 1005.74 830 1005.63 C 829.79 1005.52 829.56 1005.42 829.36 1005.31 C 829.17 1005.21 829 1005.1 828.83 1004.99 C 828.65 1004.89 828.45 1004.78 828.29 1004.67 C 828.14 1004.57 828.02 1004.46 827.89 1004.36 C 827.75 1004.25 827.59 1004.14 827.48 1004.03 C 827.37 1003.93 827.29 1003.83 827.2 1003.74 C 827.1 1003.63 826.98 1003.52 826.91 1003.41 C 826.84 1003.31 826.81 1003.22 826.76 1003.12 C 826.7 1003.01 826.63 1002.9 826.6 1002.8 L 826.55 1002.43 C 826.8 999.25 839.79 993.54 858.23 993.54 Z M 897.82 1032.95 C 897.44 1030.91 895.09 1028.75 890.31 1026.02 L 893.35 1003.45 L 893.35 1003.44 L 893.35 1003.44 L 893.44 1002.75 C 893.45 1002.67 893.46 1002.59 893.46 1002.52 C 893.46 995.14 874.89 990 858.23 990 C 841.57 990 823.01 995.14 823.01 1002.52 C 823.01 1002.6 823.01 1002.67 823.02 1002.75 L 823.11 1003.44 L 823.11 1003.45 L 830.6 1059.06 C 830.83 1065.57 847.62 1068 858.23 1068 C 864.1 1068 869.98 1067.39 874.8 1066.27 C 876.74 1065.83 878.48 1065.3 879.97 1064.71 C 883.81 1063.2 885.79 1061.3 885.86 1059.06 L 888.95 1036.17 C 890.63 1036.53 892.16 1036.75 893.44 1036.75 C 895.15 1036.75 896.44 1036.39 897.13 1035.56 C 897.75 1034.82 897.99 1033.89 897.82 1032.95 Z" fill="#7aa116" style="fill: light-dark(rgb(122, 161, 22), rgb(97, 130, 11));" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 1075px; margin-left: 861px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">SPA</div></div></div></foreignObject><text x="861" y="1087" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">SPA</text></switch></g></g></g><g data-cell-id="8VBJgbjWb-JPUTvmKhwd-39"><g><path d="M 456.5 720.23 Q 525.69 720.23 525.69 874.62 Q 525.69 1029 588.63 1029" fill="none" stroke="#000000" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 593.88 1029 L 586.88 1032.5 L 588.63 1029 L 586.88 1025.5 Z" fill="#000000" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="x-eJfxxCqEvTNtFwHw6N-1"><g><path d="M 1890 1180 L 1968 1180 L 1968 1258 L 1890 1258 Z" fill="#e7157b" style="fill: light-dark(rgb(231, 21, 123), rgb(255, 129, 217));" stroke="none" pointer-events="all"/><path d="M 1902.34 1216.73 C 1903.59 1216.73 1904.61 1217.75 1904.61 1219 C 1904.61 1220.25 1903.59 1221.27 1902.34 1221.27 C 1901.09 1221.27 1900.07 1220.25 1900.07 1219 C 1900.07 1217.75 1901.09 1216.73 1902.34 1216.73 Z M 1929.57 1246.23 C 1916.31 1246.23 1904.94 1236.55 1902.56 1223.52 C 1904.57 1223.42 1906.23 1222.02 1906.71 1220.13 L 1913.68 1220.13 L 1913.68 1217.87 L 1906.71 1217.87 C 1906.23 1215.98 1904.57 1214.58 1902.56 1214.48 C 1904.93 1201.65 1916.53 1191.77 1929.57 1191.77 C 1934.46 1191.77 1939.59 1193.61 1944.8 1197.24 L 1946.1 1195.38 C 1940.5 1191.48 1934.93 1189.5 1929.57 1189.5 C 1915.12 1189.5 1902.28 1200.69 1900.14 1215.05 C 1898.75 1215.83 1897.8 1217.3 1897.8 1219 C 1897.8 1220.7 1898.75 1222.17 1900.14 1222.95 C 1902.3 1237.54 1914.88 1248.5 1929.57 1248.5 C 1935.7 1248.5 1941.98 1246.41 1947.25 1242.61 L 1945.92 1240.77 C 1941.04 1244.29 1935.23 1246.23 1929.57 1246.23 Z M 1917.2 1214.96 C 1919.31 1215.48 1921.94 1215.6 1923.89 1215.6 C 1925.76 1215.6 1928.23 1215.49 1930.28 1215.04 L 1925.15 1225.3 C 1925.07 1225.46 1925.03 1225.63 1925.03 1225.81 L 1925.03 1230.86 C 1924.12 1231.44 1922.57 1232.4 1921.63 1232.6 L 1921.63 1225.81 C 1921.63 1225.66 1921.6 1225.51 1921.54 1225.37 Z M 1923.89 1211.06 C 1928.46 1211.06 1930.9 1211.74 1931.66 1212.19 C 1930.9 1212.64 1928.46 1213.33 1923.89 1213.33 C 1919.33 1213.33 1916.89 1212.64 1916.13 1212.19 C 1916.89 1211.74 1919.33 1211.06 1923.89 1211.06 Z M 1920.49 1234.88 L 1921.63 1234.88 C 1921.67 1234.88 1921.72 1234.88 1921.77 1234.87 C 1923.16 1234.69 1924.93 1233.62 1926.47 1232.63 L 1926.77 1232.44 C 1927.1 1232.23 1927.3 1231.87 1927.3 1231.48 L 1927.3 1226.08 L 1933.61 1213.46 C 1933.92 1213.1 1934.11 1212.69 1934.11 1212.19 C 1934.11 1209.23 1927.71 1208.79 1923.89 1208.79 C 1920.08 1208.79 1913.68 1209.23 1913.68 1212.19 C 1913.68 1212.61 1913.82 1212.97 1914.05 1213.29 L 1919.36 1226.03 L 1919.36 1233.75 C 1919.36 1234.38 1919.86 1234.88 1920.49 1234.88 Z M 1953.39 1233.75 C 1954.64 1233.75 1955.66 1234.77 1955.66 1236.02 C 1955.66 1237.27 1954.64 1238.29 1953.39 1238.29 C 1952.14 1238.29 1951.12 1237.27 1951.12 1236.02 C 1951.12 1234.77 1952.14 1233.75 1953.39 1233.75 Z M 1953.39 1199.71 C 1954.64 1199.71 1955.66 1200.73 1955.66 1201.98 C 1955.66 1203.23 1954.64 1204.25 1953.39 1204.25 C 1952.14 1204.25 1951.12 1203.23 1951.12 1201.98 C 1951.12 1200.73 1952.14 1199.71 1953.39 1199.71 Z M 1955.66 1216.73 C 1956.91 1216.73 1957.93 1217.75 1957.93 1219 C 1957.93 1220.25 1956.91 1221.27 1955.66 1221.27 C 1954.41 1221.27 1953.39 1220.25 1953.39 1219 C 1953.39 1217.75 1954.41 1216.73 1955.66 1216.73 Z M 1944.32 1220.13 L 1951.29 1220.13 C 1951.79 1222.09 1953.55 1223.54 1955.66 1223.54 C 1958.16 1223.54 1960.2 1221.5 1960.2 1219 C 1960.2 1216.5 1958.16 1214.46 1955.66 1214.46 C 1953.55 1214.46 1951.79 1215.91 1951.29 1217.87 L 1944.32 1217.87 L 1944.32 1203.12 L 1949.02 1203.12 C 1949.52 1205.07 1951.28 1206.52 1953.39 1206.52 C 1955.9 1206.52 1957.93 1204.48 1957.93 1201.98 C 1957.93 1199.48 1955.9 1197.44 1953.39 1197.44 C 1951.28 1197.44 1949.52 1198.9 1949.02 1200.85 L 1943.18 1200.85 C 1942.55 1200.85 1942.05 1201.36 1942.05 1201.98 L 1942.05 1217.87 L 1934.11 1217.87 L 1934.11 1220.13 L 1942.05 1220.13 L 1942.05 1236.02 C 1942.05 1236.64 1942.55 1237.15 1943.18 1237.15 L 1949.02 1237.15 C 1949.52 1239.1 1951.28 1240.56 1953.39 1240.56 C 1955.9 1240.56 1957.93 1238.52 1957.93 1236.02 C 1957.93 1233.52 1955.9 1231.48 1953.39 1231.48 C 1951.28 1231.48 1949.52 1232.93 1949.02 1234.88 L 1944.32 1234.88 Z" fill="#ffffff" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 1265px; margin-left: 1929px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">SNS</div></div></div></foreignObject><text x="1929" y="1277" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">SNS</text></switch></g></g></g><g data-cell-id="x-eJfxxCqEvTNtFwHw6N-2"><g><path d="M 1990 1180 L 2068 1180 L 2068 1258 L 1990 1258 Z" fill="#e7157b" style="fill: light-dark(rgb(231, 21, 123), rgb(255, 129, 217));" stroke="none" pointer-events="all"/><path d="M 2016.66 1223.03 L 2019.95 1219.79 C 2020.15 1219.59 2020.27 1219.31 2020.27 1219.02 C 2020.27 1218.73 2020.15 1218.46 2019.95 1218.25 L 2016.67 1214.97 L 2015.12 1216.5 L 2016.52 1217.91 L 2011.52 1217.91 L 2011.52 1220.09 L 2016.54 1220.09 L 2015.12 1221.49 Z M 2041.71 1223.13 L 2046.09 1219.87 C 2046.36 1219.66 2046.53 1219.34 2046.53 1219 C 2046.53 1218.66 2046.36 1218.33 2046.09 1218.13 L 2041.71 1214.87 L 2040.4 1216.61 L 2042.15 1217.91 L 2037.77 1217.91 L 2037.77 1220.09 L 2042.15 1220.09 L 2040.4 1221.39 Z M 2023.76 1219 C 2023.76 1221.31 2023.36 1223.48 2022.62 1225.38 C 2024.58 1224.62 2026.8 1224.24 2029.02 1224.24 C 2031.24 1224.24 2033.46 1224.62 2035.43 1225.38 C 2034.68 1223.48 2034.28 1221.31 2034.28 1219 C 2034.28 1216.69 2034.68 1214.52 2035.43 1212.62 C 2031.49 1214.13 2026.55 1214.13 2022.62 1212.62 C 2023.36 1214.52 2023.76 1216.69 2023.76 1219 Z M 2018.4 1229.55 C 2018.19 1229.34 2018.08 1229.06 2018.08 1228.78 C 2018.08 1228.51 2018.19 1228.23 2018.4 1228.01 C 2020.39 1226.04 2021.57 1222.67 2021.57 1219 C 2021.57 1215.33 2020.39 1211.96 2018.4 1209.99 C 2018.19 1209.77 2018.08 1209.49 2018.08 1209.22 C 2018.08 1208.94 2018.19 1208.66 2018.4 1208.45 C 2018.83 1208.02 2019.52 1208.02 2019.95 1208.45 C 2024.19 1212.66 2033.85 1212.66 2038.09 1208.45 C 2038.52 1208.02 2039.21 1208.02 2039.64 1208.45 C 2039.85 1208.66 2039.96 1208.94 2039.96 1209.22 C 2039.96 1209.49 2039.85 1209.77 2039.64 1209.99 C 2037.65 1211.96 2036.47 1215.33 2036.47 1219 C 2036.47 1222.67 2037.65 1226.04 2039.64 1228.01 C 2039.85 1228.23 2039.96 1228.51 2039.96 1228.78 C 2039.96 1229.06 2039.85 1229.34 2039.64 1229.55 C 2039.43 1229.76 2039.15 1229.87 2038.87 1229.87 C 2038.59 1229.87 2038.31 1229.76 2038.09 1229.55 C 2033.85 1225.34 2024.19 1225.34 2019.95 1229.55 C 2019.52 1229.98 2018.83 1229.98 2018.4 1229.55 Z M 2057.43 1219.01 C 2057.43 1217.99 2057.03 1217.04 2056.31 1216.32 C 2055.57 1215.58 2054.59 1215.21 2053.61 1215.21 C 2052.63 1215.21 2051.66 1215.58 2050.91 1216.32 C 2049.42 1217.8 2049.42 1220.21 2050.91 1221.69 C 2052.4 1223.17 2054.82 1223.17 2056.31 1221.69 C 2057.03 1220.97 2057.43 1220.02 2057.43 1219.01 Z M 2057.86 1223.22 C 2056.69 1224.39 2055.15 1224.97 2053.61 1224.97 C 2052.07 1224.97 2050.54 1224.39 2049.36 1223.22 C 2047.02 1220.9 2047.02 1217.11 2049.36 1214.79 C 2051.71 1212.46 2055.52 1212.46 2057.86 1214.79 C 2060.2 1217.11 2060.2 1220.9 2057.86 1223.22 Z M 2008.2 1219.03 C 2008.2 1218.02 2007.81 1217.06 2007.08 1216.35 C 2006.36 1215.63 2005.41 1215.24 2004.39 1215.24 C 2003.37 1215.24 2002.41 1215.63 2001.69 1216.35 C 2000.97 1217.06 2000.57 1218.02 2000.57 1219.03 C 2000.57 1220.04 2000.97 1220.99 2001.69 1221.71 C 2003.13 1223.14 2005.64 1223.14 2007.08 1221.71 C 2007.81 1220.99 2008.2 1220.04 2008.2 1219.03 Z M 2008.63 1223.25 C 2007.46 1224.41 2005.92 1224.99 2004.39 1224.99 C 2002.85 1224.99 2001.31 1224.41 2000.14 1223.25 C 1997.8 1220.92 1997.8 1217.14 2000.14 1214.81 C 2002.48 1212.48 2006.29 1212.48 2008.63 1214.81 C 2010.97 1217.14 2010.97 1220.92 2008.63 1223.25 Z M 2045.29 1235.24 C 2040.92 1239.58 2035.11 1241.97 2028.93 1241.97 C 2022.75 1241.97 2016.95 1239.58 2012.58 1235.24 C 2009.58 1232.27 2007.93 1228.71 2007.08 1226.25 L 2005.01 1226.97 C 2005.94 1229.64 2007.74 1233.51 2011.03 1236.78 C 2015.81 1241.53 2022.17 1244.15 2028.93 1244.15 C 2035.7 1244.15 2042.05 1241.53 2046.83 1236.78 C 2049.6 1234.04 2051.8 1230.55 2053.03 1226.96 L 2050.96 1226.26 C 2049.83 1229.54 2047.82 1232.73 2045.29 1235.24 Z M 2007.08 1211.75 L 2005.01 1211.03 C 2006.34 1207.25 2008.48 1203.76 2011.04 1201.21 C 2015.82 1196.47 2022.17 1193.85 2028.93 1193.85 C 2035.69 1193.85 2042.05 1196.47 2046.83 1201.21 C 2049.52 1203.89 2051.79 1207.47 2053.03 1211.03 L 2050.96 1211.75 C 2049.82 1208.49 2047.75 1205.21 2045.28 1202.75 C 2040.92 1198.41 2035.11 1196.03 2028.93 1196.03 C 2022.76 1196.03 2016.95 1198.41 2012.59 1202.75 C 2010.25 1205.07 2008.3 1208.27 2007.08 1211.75 Z" fill="#ffffff" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 1265px; margin-left: 2029px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">SQS</div></div></div></foreignObject><text x="2029" y="1277" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">SQS</text></switch></g></g></g><g data-cell-id="x-eJfxxCqEvTNtFwHw6N-8"><g><path d="M 1866.21 670.17 Q 2029.08 670.23 2029.08 1173.63" fill="none" stroke="#000000" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2029.08 1178.88 L 2025.58 1171.88 L 2029.08 1173.63 L 2032.58 1171.88 Z" fill="#000000" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="x-eJfxxCqEvTNtFwHw6N-9"><g><path d="M 1866.66 671.08 Q 1929.08 671.15 1929.08 1173.63" fill="none" stroke="#000000" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1929.08 1178.88 L 1925.58 1171.88 L 1929.08 1173.63 L 1932.58 1171.88 Z" fill="#000000" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="x-eJfxxCqEvTNtFwHw6N-5"><g><path d="M 1838.86 642.51 C 1838.35 642.5 1837.82 642.54 1837.3 642.64 C 1833.79 643.3 1831.18 646.29 1831.03 649.86 C 1821.58 653.36 1815.25 662.25 1815.07 672.31 C 1815.07 673.06 1815.67 673.69 1816.42 673.72 C 1817.17 673.72 1817.8 673.12 1817.83 672.37 L 1817.83 672.31 C 1818.01 663.63 1823.35 655.87 1831.42 652.61 C 1832.32 655.28 1834.6 657.25 1837.36 657.76 L 1837.36 664.62 C 1833.46 665.37 1830.64 668.78 1830.7 672.73 C 1830.7 673.66 1830.85 674.59 1831.18 675.49 L 1824.73 679.23 C 1822.09 675.88 1817.26 675.31 1813.93 677.94 C 1810.6 680.58 1810 685.4 1812.64 688.72 C 1815.07 691.83 1819.48 692.58 1822.84 690.46 C 1830.46 697.67 1841.83 699.2 1851.1 694.29 C 1851.76 693.93 1852.03 693.12 1851.67 692.46 C 1851.31 691.77 1850.47 691.53 1849.81 691.89 C 1846.66 693.54 1843.18 694.41 1839.64 694.38 C 1834.15 694.38 1828.9 692.28 1824.91 688.54 C 1826.44 686.56 1826.92 683.96 1826.17 681.59 L 1832.62 677.85 C 1835.29 681.33 1840.24 681.98 1843.72 679.32 C 1844.29 678.9 1844.77 678.39 1845.19 677.85 L 1851.16 681.33 C 1850.86 682.16 1850.71 683.03 1850.71 683.93 C 1850.68 688.18 1854.13 691.63 1858.36 691.66 C 1862.62 691.69 1866.07 688.24 1866.1 684.02 C 1866.04 681.8 1865.11 679.71 1863.46 678.24 C 1863.88 676.41 1864.09 674.56 1864.09 672.7 C 1864.09 663.96 1859.38 655.87 1851.79 651.5 C 1851.13 651.11 1850.29 651.35 1849.9 652.01 C 1849.51 652.67 1849.75 653.51 1850.41 653.9 C 1858.45 658.48 1862.71 667.67 1860.94 676.77 C 1857.91 675.67 1854.52 676.56 1852.45 679.02 L 1846.42 675.49 C 1847.92 671.14 1845.64 666.44 1841.29 664.92 C 1840.93 664.8 1840.54 664.68 1840.15 664.62 L 1840.15 657.76 C 1844.32 656.98 1847.08 652.97 1846.3 648.78 C 1845.62 645.14 1842.45 642.57 1838.86 642.51 Z M 1838.8 645.21 C 1841.56 645.21 1843.81 647.46 1843.81 650.21 C 1843.81 652.97 1841.56 655.21 1838.8 655.21 C 1836.04 655.19 1833.79 652.97 1833.79 650.21 C 1833.79 647.46 1836.04 645.21 1838.8 645.21 Z M 1838.74 647.46 C 1837.21 647.46 1835.98 648.69 1835.98 650.21 C 1835.98 651.74 1837.21 652.97 1838.74 652.97 C 1840.27 652.94 1841.5 651.74 1841.5 650.21 C 1841.5 648.69 1840.27 647.46 1838.74 647.46 Z M 1841.71 667.31 C 1843.3 667.28 1844.62 668.54 1844.65 670.13 C 1844.68 671.71 1843.42 673.03 1841.83 673.06 C 1840.24 673.09 1838.92 671.83 1838.89 670.25 L 1838.89 670.16 C 1838.89 668.6 1840.15 667.34 1841.71 667.31 Z M 1858.27 678.87 C 1861.03 678.87 1863.28 681.12 1863.28 683.87 C 1863.28 686.65 1861.06 688.9 1858.27 688.87 C 1855.51 688.87 1853.26 686.62 1853.26 683.87 C 1853.26 681.12 1855.51 678.87 1858.27 678.87 Z M 1818.73 678.96 C 1821.49 678.96 1823.74 681.2 1823.74 683.96 C 1823.74 686.71 1821.49 688.96 1818.73 688.96 C 1815.97 688.96 1813.72 686.71 1813.72 683.96 C 1813.72 681.2 1815.97 678.96 1818.73 678.96 Z M 1818.67 681.21 C 1817.14 681.21 1815.91 682.43 1815.91 683.96 C 1815.91 685.49 1817.14 686.71 1818.67 686.71 C 1820.2 686.71 1821.43 685.49 1821.43 683.96 C 1821.43 682.46 1820.2 681.23 1818.67 681.21 Z M 1858.27 681.21 C 1856.74 681.21 1855.51 682.43 1855.51 683.96 C 1855.51 685.49 1856.74 686.71 1858.27 686.71 C 1859.8 686.71 1861.03 685.49 1861.03 683.96 C 1861.03 682.46 1859.8 681.23 1858.27 681.21 Z" fill="#ff6a00" style="fill: light-dark(rgb(255, 106, 0), rgb(233, 105, 14));" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 671px; margin-left: 1838px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: nowrap; "><div><br /></div><div><br /></div><div><br /></div><div><br /></div><div><br /></div><div><br /></div><div>Private Link<br />(SNS &amp; SQS)</div></div></div></div></foreignObject><text x="1838" y="674" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Private L...</text></switch></g></g></g><g data-cell-id="x-eJfxxCqEvTNtFwHw6N-6"><g><path d="M 1538 569 Q 1840 569 1840.01 633.64" fill="none" stroke="#000000" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1840.01 638.89 L 1836.51 631.89 L 1840.01 633.64 L 1843.51 631.89 Z" fill="#000000" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="x-eJfxxCqEvTNtFwHw6N-7"><g><path d="M 1538 718.5 Q 1674 718.54 1674 694.69 Q 1674 670.85 1803.63 670.85" fill="none" stroke="#000000" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1808.88 670.85 L 1801.88 674.35 L 1803.63 670.85 L 1801.88 667.35 Z" fill="#000000" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="x-eJfxxCqEvTNtFwHw6N-13"><g><path d="M 1163.5 720.23 Q 1311.69 720.23 1311.69 769.62 Q 1311.69 819 1453.63 819" fill="none" stroke="#000000" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1458.88 819 L 1451.88 822.5 L 1453.63 819 L 1451.88 815.5 Z" fill="#000000" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="x-eJfxxCqEvTNtFwHw6N-14"><g><path d="M 1163.5 720.23 Q 1311.69 720.23 1311.69 584.62 Q 1311.69 449 1453.63 449" fill="none" stroke="#000000" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1458.88 449 L 1451.88 452.5 L 1453.63 449 L 1451.88 445.5 Z" fill="#000000" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="x-eJfxxCqEvTNtFwHw6N-15"><g><path d="M 1163.5 720.23 Q 1311.69 720.23 1311.69 519.62 Q 1311.69 319 1453.63 319" fill="none" stroke="#000000" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1458.88 319 L 1451.88 322.5 L 1453.63 319 L 1451.88 315.5 Z" fill="#000000" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="x-eJfxxCqEvTNtFwHw6N-19"><g><path d="M 1707.3 1215 L 1798.62 1215 L 1798.62 1219 L 1883.63 1219" fill="none" stroke="#000000" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1888.88 1219 L 1881.88 1222.5 L 1883.63 1219 L 1881.88 1215.5 Z" fill="#000000" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="x-eJfxxCqEvTNtFwHw6N-16"><g><rect x="1640" y="1180" width="67.31" height="70" fill="none" stroke="none" pointer-events="all"/><path d="M 1699.6 1218.39 L 1699.95 1215.74 C 1702.79 1217.47 1703.66 1218.46 1703.92 1218.89 C 1703.16 1219.11 1701.65 1218.91 1699.6 1218.39 Z M 1671.47 1208.78 C 1671.47 1208.69 1671.54 1208.62 1671.62 1208.62 C 1671.65 1208.62 1671.66 1208.64 1671.68 1208.65 L 1671.57 1208.88 C 1671.52 1208.87 1671.47 1208.84 1671.47 1208.78 Z M 1693.26 1241.64 C 1693.25 1241.72 1693.24 1241.79 1693.24 1241.86 C 1693.24 1242.1 1692.64 1243.04 1689.96 1244.09 C 1688.77 1244.57 1687.36 1244.99 1685.77 1245.35 C 1681.68 1246.3 1676.65 1246.82 1671.62 1246.82 C 1658.17 1246.82 1650 1243.37 1650 1241.86 C 1650 1241.79 1649.99 1241.72 1649.99 1241.64 L 1643.89 1196.34 C 1644.28 1196.57 1644.7 1196.79 1645.14 1197 C 1645.27 1197.06 1645.4 1197.12 1645.53 1197.18 C 1645.91 1197.36 1646.31 1197.53 1646.72 1197.69 C 1646.9 1197.76 1647.08 1197.83 1647.26 1197.9 C 1647.7 1198.06 1648.15 1198.22 1648.62 1198.38 C 1648.76 1198.42 1648.89 1198.47 1649.03 1198.51 C 1649.64 1198.7 1650.26 1198.88 1650.9 1199.05 C 1651.08 1199.1 1651.27 1199.15 1651.45 1199.19 C 1651.95 1199.32 1652.45 1199.44 1652.97 1199.55 C 1653.19 1199.6 1653.42 1199.65 1653.64 1199.7 C 1654.22 1199.82 1654.81 1199.94 1655.42 1200.05 C 1655.55 1200.07 1655.67 1200.1 1655.8 1200.12 C 1656.53 1200.25 1657.28 1200.37 1658.03 1200.48 C 1658.25 1200.51 1658.46 1200.53 1658.68 1200.56 C 1659.25 1200.64 1659.83 1200.72 1660.41 1200.78 C 1660.65 1200.81 1660.89 1200.84 1661.13 1200.87 C 1661.84 1200.94 1662.55 1201.01 1663.27 1201.07 C 1663.35 1201.08 1663.44 1201.09 1663.53 1201.09 C 1664.33 1201.16 1665.13 1201.21 1665.94 1201.26 C 1666.17 1201.27 1666.4 1201.28 1666.63 1201.29 C 1667.24 1201.32 1667.85 1201.35 1668.46 1201.37 C 1668.7 1201.37 1668.94 1201.38 1669.18 1201.39 C 1669.99 1201.41 1670.81 1201.42 1671.62 1201.42 C 1672.43 1201.42 1673.24 1201.41 1674.06 1201.39 C 1674.3 1201.38 1674.54 1201.37 1674.78 1201.37 C 1675.39 1201.35 1676 1201.32 1676.6 1201.29 C 1676.84 1201.28 1677.07 1201.27 1677.3 1201.26 C 1678.11 1201.21 1678.91 1201.16 1679.71 1201.09 C 1679.8 1201.09 1679.89 1201.08 1679.98 1201.07 C 1680.69 1201.01 1681.4 1200.94 1682.1 1200.87 C 1682.35 1200.84 1682.59 1200.81 1682.83 1200.78 C 1683.41 1200.72 1683.99 1200.64 1684.56 1200.56 C 1684.78 1200.54 1685 1200.51 1685.21 1200.48 C 1685.96 1200.37 1686.71 1200.25 1687.44 1200.12 C 1687.57 1200.1 1687.7 1200.07 1687.83 1200.05 C 1688.43 1199.94 1689.02 1199.82 1689.6 1199.7 C 1689.83 1199.65 1690.05 1199.6 1690.27 1199.55 C 1690.79 1199.44 1691.29 1199.32 1691.79 1199.19 C 1691.97 1199.14 1692.16 1199.1 1692.34 1199.05 C 1692.98 1198.88 1693.61 1198.7 1694.21 1198.51 C 1694.35 1198.47 1694.48 1198.42 1694.61 1198.38 C 1695.08 1198.22 1695.54 1198.06 1695.98 1197.9 C 1696.16 1197.83 1696.34 1197.76 1696.52 1197.69 C 1696.93 1197.53 1697.33 1197.35 1697.72 1197.18 C 1697.84 1197.12 1697.98 1197.06 1698.1 1197 C 1698.54 1196.79 1698.96 1196.57 1699.36 1196.34 L 1696.51 1217.48 C 1690.73 1215.63 1682.62 1212.26 1674.93 1208.61 C 1674.85 1206.85 1673.4 1205.45 1671.62 1205.45 C 1669.79 1205.45 1668.29 1206.94 1668.29 1208.78 C 1668.29 1210.61 1669.79 1212.1 1671.62 1212.1 C 1672.34 1212.1 1673.01 1211.87 1673.55 1211.48 C 1680.44 1214.78 1689.22 1218.68 1696.08 1220.66 Z M 1671.62 1183.18 C 1688.17 1183.18 1699.84 1188.31 1700.05 1191.16 L 1700.01 1191.49 C 1699.98 1191.58 1699.92 1191.68 1699.87 1191.79 C 1699.83 1191.87 1699.79 1191.95 1699.74 1192.03 C 1699.67 1192.13 1699.56 1192.23 1699.47 1192.33 C 1699.39 1192.42 1699.32 1192.51 1699.23 1192.59 C 1699.12 1192.69 1698.98 1192.79 1698.85 1192.89 C 1698.73 1192.98 1698.63 1193.07 1698.49 1193.17 C 1698.34 1193.26 1698.17 1193.36 1698.01 1193.46 C 1697.85 1193.55 1697.71 1193.65 1697.53 1193.74 C 1697.36 1193.84 1697.15 1193.93 1696.96 1194.02 C 1696.76 1194.12 1696.58 1194.22 1696.36 1194.31 C 1696.16 1194.4 1695.92 1194.5 1695.7 1194.59 C 1695.47 1194.68 1695.24 1194.78 1694.99 1194.87 C 1694.75 1194.96 1694.49 1195.05 1694.24 1195.13 C 1693.97 1195.23 1693.71 1195.32 1693.42 1195.42 C 1693.15 1195.5 1692.85 1195.58 1692.57 1195.66 C 1692.27 1195.75 1691.98 1195.84 1691.66 1195.93 C 1691.35 1196.01 1691.03 1196.09 1690.71 1196.17 C 1690.38 1196.25 1690.06 1196.33 1689.72 1196.41 C 1689.37 1196.49 1689 1196.56 1688.65 1196.64 C 1688.3 1196.71 1687.96 1196.78 1687.6 1196.85 C 1687.22 1196.93 1686.81 1196.99 1686.41 1197.06 C 1686.05 1197.12 1685.69 1197.19 1685.31 1197.25 C 1684.9 1197.31 1684.46 1197.37 1684.03 1197.43 C 1683.64 1197.48 1683.27 1197.54 1682.87 1197.59 C 1682.4 1197.64 1681.91 1197.69 1681.43 1197.74 C 1681.04 1197.78 1680.67 1197.83 1680.27 1197.86 C 1679.73 1197.91 1679.18 1197.95 1678.63 1197.99 C 1678.26 1198.01 1677.9 1198.05 1677.52 1198.07 C 1676.92 1198.1 1676.29 1198.13 1675.66 1198.15 C 1675.32 1198.17 1674.99 1198.19 1674.63 1198.2 C 1673.65 1198.23 1672.65 1198.24 1671.62 1198.24 C 1670.59 1198.24 1669.59 1198.23 1668.6 1198.2 C 1668.25 1198.19 1667.91 1198.17 1667.56 1198.15 C 1666.94 1198.13 1666.32 1198.1 1665.72 1198.07 C 1665.32 1198.04 1664.94 1198.01 1664.55 1197.98 C 1664.02 1197.94 1663.48 1197.91 1662.97 1197.86 C 1662.57 1197.83 1662.2 1197.78 1661.81 1197.74 C 1661.33 1197.69 1660.84 1197.64 1660.37 1197.59 C 1659.97 1197.54 1659.6 1197.48 1659.21 1197.43 C 1658.78 1197.37 1658.34 1197.31 1657.93 1197.25 C 1657.53 1197.19 1657.16 1197.12 1656.78 1197.05 C 1656.4 1196.98 1656.01 1196.92 1655.64 1196.85 C 1655.27 1196.78 1654.93 1196.71 1654.58 1196.63 C 1654.22 1196.56 1653.86 1196.49 1653.52 1196.41 C 1653.18 1196.33 1652.86 1196.25 1652.53 1196.17 C 1652.22 1196.09 1651.89 1196.01 1651.58 1195.93 C 1651.26 1195.84 1650.97 1195.75 1650.66 1195.66 C 1650.38 1195.58 1650.09 1195.5 1649.82 1195.42 C 1649.53 1195.32 1649.27 1195.23 1648.99 1195.13 C 1648.74 1195.04 1648.49 1194.96 1648.25 1194.87 C 1648 1194.78 1647.77 1194.68 1647.54 1194.58 C 1647.32 1194.49 1647.08 1194.4 1646.88 1194.31 C 1646.67 1194.22 1646.48 1194.12 1646.28 1194.02 C 1646.09 1193.93 1645.88 1193.84 1645.71 1193.74 C 1645.53 1193.65 1645.39 1193.55 1645.23 1193.46 C 1645.07 1193.36 1644.9 1193.26 1644.75 1193.17 C 1644.61 1193.07 1644.51 1192.98 1644.39 1192.89 C 1644.26 1192.79 1644.12 1192.69 1644.02 1192.59 C 1643.92 1192.5 1643.85 1192.41 1643.77 1192.33 C 1643.68 1192.23 1643.58 1192.13 1643.51 1192.03 C 1643.45 1191.95 1643.42 1191.86 1643.37 1191.78 C 1643.32 1191.68 1643.26 1191.58 1643.23 1191.49 L 1643.19 1191.15 C 1643.41 1188.3 1655.07 1183.18 1671.62 1183.18 Z M 1707.14 1218.54 C 1706.81 1216.71 1704.7 1214.78 1700.41 1212.33 L 1703.14 1192.07 L 1703.14 1192.06 L 1703.14 1192.06 L 1703.22 1191.44 C 1703.23 1191.37 1703.23 1191.3 1703.23 1191.23 C 1703.23 1184.61 1686.57 1180 1671.62 1180 C 1656.67 1180 1640.01 1184.61 1640.01 1191.23 C 1640.01 1191.3 1640.01 1191.37 1640.02 1191.44 L 1640.1 1192.06 L 1640.1 1192.07 L 1646.82 1241.98 C 1647.03 1247.82 1662.1 1250 1671.62 1250 C 1676.89 1250 1682.17 1249.45 1686.49 1248.45 C 1688.23 1248.05 1689.79 1247.58 1691.13 1247.05 C 1694.57 1245.69 1696.35 1243.98 1696.42 1241.98 L 1699.18 1221.44 C 1700.7 1221.76 1702.07 1221.95 1703.21 1221.95 C 1704.75 1221.95 1705.9 1221.63 1706.53 1220.88 C 1707.08 1220.22 1707.3 1219.39 1707.14 1218.54 Z" fill="#7aa116" style="fill: light-dark(rgb(122, 161, 22), rgb(97, 130, 11));" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 1257px; margin-left: 1674px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; "><div>Parts Images</div></div></div></div></foreignObject><text x="1674" y="1269" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Parts Images</text></switch></g></g></g><g data-cell-id="x-eJfxxCqEvTNtFwHw6N-22"><g><path d="M 1855.34 532 C 1837.11 532 1821 517.09 1821 497.8 L 1821 493.88 C 1821 478.27 1834.07 460 1855.6 460 C 1874.54 460 1890 475.45 1890 494.21 L 1889.98 497.3 C 1889.98 516.95 1874.3 532 1855.34 532 Z" fill="#f58534" style="fill: light-dark(rgb(245, 133, 52), rgb(189, 92, 23));" stroke="none" pointer-events="all"/><path d="M 1890 494.21 L 1889.98 497.3 C 1889.98 516.97 1874.3 532 1855.34 532 C 1837.11 532 1821 517.08 1821 497.8 L 1821 493.88 C 1821 511.94 1834.2 529.06 1855.84 529.06 C 1875.26 529.06 1890 513.06 1890 494.21 Z" fill-opacity="0.3" fill="#000000" style="fill: light-dark(rgb(0, 0, 0), rgb(237, 237, 237));" stroke="none" pointer-events="all"/><rect x="1821" y="460" width="0" height="0" fill="none" stroke="#000000" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" pointer-events="all"/><path d="M 1829.43 513.21 L 1829.43 502.98 L 1833.62 502.98 L 1833.62 494.14 L 1840.08 494.14 L 1840.08 486.2 L 1854.59 486.2 L 1854.59 479.2 L 1845.43 479.2 L 1845.43 466.64 L 1865.56 466.64 L 1865.56 479.2 L 1856.41 479.2 L 1856.41 486.2 L 1870.55 486.2 L 1870.55 494.14 L 1876.96 494.14 L 1876.96 502.98 L 1881.14 502.98 L 1881.14 513.21 L 1870.94 513.21 L 1870.94 502.98 L 1875.11 502.98 L 1875.11 495.97 L 1864.06 495.97 L 1864.06 502.98 L 1868.23 502.98 L 1868.23 513.21 L 1858.03 513.21 L 1858.03 502.98 L 1862.22 502.98 L 1862.22 494.14 L 1868.7 494.14 L 1868.7 488.05 L 1841.92 488.05 L 1841.92 494.14 L 1848.36 494.14 L 1848.36 502.98 L 1852.55 502.98 L 1852.55 513.21 L 1842.33 513.21 L 1842.33 502.98 L 1846.51 502.98 L 1846.51 495.97 L 1835.45 495.97 L 1835.45 502.98 L 1839.64 502.98 L 1839.64 513.21 Z" fill="#ffffff" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 539px; margin-left: 1856px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: nowrap; ">Internal Application <br />Load Balancer (ALB)</div></div></div></foreignObject><text x="1856" y="551" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Internal Ap...</text></switch></g></g></g><g data-cell-id="x-eJfxxCqEvTNtFwHw6N-24"><g><path d="M 1538 338.5 Q 1538 490.08 1814.63 490.08" fill="none" stroke="#000000" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1819.88 490.08 L 1812.88 493.58 L 1814.63 490.08 L 1812.88 486.58 Z" fill="#000000" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="x-eJfxxCqEvTNtFwHw6N-25"><g><path d="M 1538 549.5 Q 1679.54 549.46 1679.54 522.77 Q 1679.54 496.08 1814.63 496.08" fill="none" stroke="#000000" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1819.88 496.08 L 1812.88 499.58 L 1814.63 496.08 L 1812.88 492.58 Z" fill="#000000" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="x-eJfxxCqEvTNtFwHw6N-26"><g><path d="M 964.39 756.25 C 944.84 756.25 930.02 740.15 930.02 722.35 L 930 718.21 C 930 700.68 944.68 684.25 964.45 684.25 C 984.69 684.25 999 700.74 999 718.46 L 999 722.42 C 999 739.47 984.69 756.25 964.39 756.25 Z" fill="#f58534" style="fill: light-dark(rgb(245, 133, 52), rgb(189, 92, 23));" stroke="none" pointer-events="all"/><path d="M 930 718.21 C 930 740.7 948.26 753.3 963.8 753.3 C 985.68 753.3 999 736.08 999 718.46 L 999 722.42 C 999 739.41 984.67 756.25 964.39 756.25 C 944.84 756.25 930.02 740.12 930.02 722.35 Z" fill-opacity="0.3" fill="#000000" style="fill: light-dark(rgb(0, 0, 0), rgb(237, 237, 237));" stroke="none" pointer-events="all"/><rect x="930" y="684.25" width="0" height="0" fill="none" stroke="#000000" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" pointer-events="all"/><path d="M 979.39 728.19 C 983.66 728.1 986.68 725.16 986.97 722.33 C 987.39 719.26 984.49 715.03 980.24 714.58 L 978.24 714.44 L 978.12 712.29 C 978 711.01 977.18 709.26 975.27 708.66 C 972.78 707.95 971.73 709.07 969.49 710.6 L 968.31 708.14 C 967.04 705.42 963.85 702.19 958.83 702.07 C 952.68 702.1 948.38 707.22 948.38 712.54 L 948.4 714.47 L 946.66 714.96 C 944.75 715.59 941.95 718.36 942.03 721.95 C 942.09 725.68 946.19 728.19 949.64 728.19 Z M 949.78 731.83 C 944.19 731.83 938.88 728 938.52 722.74 C 937.94 717.17 942.32 712.91 944.86 711.85 C 945.19 706.31 948.37 702.51 951.61 700.56 C 955.95 697.97 960.94 698.03 964.98 700 C 967.03 700.94 969.27 702.75 970.87 705.48 C 972.84 704.54 975.82 704.43 978.36 706.19 C 980.15 707.48 981.18 709.26 981.52 711.17 C 986.55 712.08 991.45 717.35 990.36 723.54 C 989.71 727.77 984.72 731.83 979.34 731.83 Z" fill="#ffffff" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 763px; margin-left: 965px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: nowrap; ">Internet<br /><div>Gateway</div></div></div></div></foreignObject><text x="965" y="775" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Internet...</text></switch></g></g></g></g><g data-cell-id="x-eJfxxCqEvTNtFwHw6N-21"><g><path d="M 1588.25 1386 Q 1588.31 1216.23 1634.18 1216.26" fill="none" stroke="#000000" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1639.43 1216.26 L 1632.43 1219.76 L 1634.18 1216.26 L 1632.43 1212.76 Z" fill="#000000" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="x-eJfxxCqEvTNtFwHw6N-20"><g><path d="M 1588.24 1479 L 1580.61 1475.18 L 1580.61 1469.29 L 1573.88 1471.79 L 1568.06 1468.86 L 1568.06 1464.64 L 1562.78 1466.15 L 1558.07 1463.77 L 1558.07 1460.67 L 1553.81 1461.63 L 1550 1459.7 L 1550 1405.31 L 1553.81 1403.38 L 1558.07 1404.35 L 1558.07 1401.25 L 1562.78 1398.87 L 1568.06 1400.36 L 1568.06 1396.16 L 1573.88 1393.22 L 1580.61 1395.71 L 1580.61 1389.84 L 1588.24 1386 L 1626.5 1405.3 L 1626.5 1459.7 Z" fill="#f58534" style="fill: light-dark(rgb(245, 133, 52), rgb(189, 92, 23));" stroke="none" pointer-events="all"/><path d="M 1553.81 1461.63 L 1550 1459.7 L 1550 1405.31 L 1553.81 1403.38 Z M 1562.78 1466.15 L 1558.07 1463.77 L 1558.07 1401.25 L 1562.78 1398.87 Z M 1573.88 1471.79 L 1568.06 1468.86 L 1568.06 1396.16 L 1573.88 1393.22 Z M 1580.61 1475.18 L 1580.61 1389.84 L 1588.24 1386 L 1588.24 1479 Z" fill-opacity="0.3" fill="#000000" style="fill: light-dark(rgb(0, 0, 0), rgb(237, 237, 237));" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 1486px; margin-left: 1588px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: nowrap; ">Visual SKUs<br /><div>(DAM)</div></div></div></div></foreignObject><text x="1588" y="1498" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Visual SKUs...</text></switch></g></g></g><g data-cell-id="x-eJfxxCqEvTNtFwHw6N-29"><g><path d="M 80 637.46 Q 155.08 637.46 155.08 678.85 Q 155.08 720.23 223.63 720.23" fill="none" stroke="#000000" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 228.88 720.23 L 221.88 723.73 L 223.63 720.23 L 221.88 716.73 Z" fill="#000000" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="x-eJfxxCqEvTNtFwHw6N-28"><g><rect x="0" y="597.5" width="80" height="80" fill="#ffffff" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke="#000000" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 638px; margin-left: 1px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Parts King</div></div></div></foreignObject><text x="40" y="641" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Parts King</text></switch></g></g></g><g data-cell-id="x-eJfxxCqEvTNtFwHw6N-31"><g><path d="M 80 760.08 Q 155.08 760.08 155.08 740.15 Q 155.08 720.23 223.63 720.23" fill="none" stroke="#000000" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 228.88 720.23 L 221.88 723.73 L 223.63 720.23 L 221.88 716.73 Z" fill="#000000" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="x-eJfxxCqEvTNtFwHw6N-30"><g><rect x="0" y="720" width="80" height="80" fill="#ffffff" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke="#000000" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 760px; margin-left: 1px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Parts West</div></div></div></foreignObject><text x="40" y="764" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Parts West</text></switch></g></g></g><g data-cell-id="x-eJfxxCqEvTNtFwHw6N-32"><g><path d="M 595 1055.97 Q 150 1056.08 150 922.46 Q 150 788.85 88.21 788.8" fill="none" stroke="#000000" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 82.96 788.8 L 89.96 785.31 L 88.21 788.8 L 89.96 792.31 Z" fill="#000000" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="x-eJfxxCqEvTNtFwHw6N-33"><g><path d="M 595 1029 Q 170 1029 170 843.23 Q 170 657.46 86.37 657.5" fill="none" stroke="#000000" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 81.12 657.5 L 88.12 654 L 86.37 657.5 L 88.12 661 Z" fill="#000000" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/></g></g></g></g></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.drawio.com/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Text is not SVG - cannot display</text></a></switch></svg>
