---
title: June 3rd Cybersecurity Meeting
summary: Agenda for the June 3rd Cybersecurity Meeting
---

## Attendees:
- Kyle
- <PERSON>
- Seing


## Agenda:

- Architecture Updates
  - Visual SKUs Integration
    - We propose defining a narrowly scoped IAM role in our account that the Visual SKUs AWS account can assume to write image data to our S3 bucket. This approach provides:
    - No long-lived credentials stored in an external vendor’s system
    - Instant revocation of access
    - No need to rotate keys or credentials
    - No publicly exposed attack surface
    - Fine-grained, conditional least-privilege controls
    - Built-in TLS, encryption at rest, and bucket-level protections
    - End-to-end logging via CloudTrail and S3 access logs
  - Contractor Access (Euvic)
    - We propose two AWS IAM groups—Developer and Architect—with these controls:
    - Access limited to the us-east-2 region
    - No IAM permissions unless an MFA device is configured
    - Service permissions scoped only to what each role requires
  - API Gateway Decision
    - Do we use API Gateway or not? The main benefit is offloading authentication. If we anticipate securing an integration layer for multiple applications on the same network, consider adding application-level security instead.
    - FYI: API Gateway does not support HMAC.
  - Milestones & Upcoming Sprints
    - Deploy Magento, Pimcore, and the Integration Layer to the ALS AWS account

## Questions:
 - Who will create Euvic AWS Accounts? Help Desk
 - What accounts/keys do I need to implement CodeQL? None
 - What accounts/keys do I need to implement Rapid7 InsightAppSec? Talk to Seing when we start deploying


## TODO:
1. Determine Magento and API Gateway compatability (Kyle)
2. Create a security folder in Axon repository (Kyle)
3. Draft network diagram for next weeks agenda (Kyle)
4. Enserue CodeQL is running in the Axon repository (Kyle)
6. Send a help desk request for Euvic AWS accounts - CC Adam Ritzel (Kyle)
7. Next weeks agenda: Are the IaC scanning tools available? (Kyle)