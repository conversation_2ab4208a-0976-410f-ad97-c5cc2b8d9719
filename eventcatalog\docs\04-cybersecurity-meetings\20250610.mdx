---
title: June 10th Cybersecurity Meeting
summary: Agenda for the June 10th Cybersecurity Meeting
---

## Attendees:
- <PERSON>
- <PERSON>
- <PERSON>
- <PERSON>ing
- Job

## Agenda:
- Closed issues since last meeting
    - Euvic contractor access
    - CodeQL turned on for all Evolution repositories and security issues addressed.
- Architecture Updates
    - Review Network Diagram
        - Lucid vs Visio vs Draw.io
    - Audience for Evoloution
        - Parts King
        - Parts West
    - Magento Ingress/Egress
        - We believe we can get the same functionality with an ALB - Send details in Teams chat (Kyle)
    - SAP Ingress/Egress
        - Moving to next week
    - SAST for PHP applications (Magento and Pimcore)
        - CodeQL doesn’t support PHP applications:
            - Investigate and implement a solution for scanning PHP code, focusing on the extensions written by <PERSON><PERSON>. (<PERSON>)
        - Milestones & Upcoming Sprints

## Meeting Notes
- **E-commerce and Alliance Distribution:** <PERSON><PERSON> and <PERSON> discussed the current state of e-commerce and alliance distribution, noting that the e-commerce platform is primarily used for B2B customers with invoice-based transactions. They also mentioned that the e-commerce project is expected to start in 2026.
- **Customer Single Sign-On Project:** Adam explained the customer single sign-on (SSO) project, which is being kicked off under the e-commerce project. He emphasized the importance of validating customer email addresses for e-commerce transactions and mentioned working with <PERSON> on the project requirements.
- **Fraud Prevention and Payment Systems:** <PERSON><PERSON> and <PERSON> discussed the need for fraud prevention measures and payment systems for e-commerce. <PERSON> mentioned that B2C transactions will be credit card-based, while B2B transactions will have more stringent user identity requirements, including multi-factor authentication (MFA).
- **Customer Profiles and Security Requirements:** Adam highlighted the different security requirements for various customer profiles, such as laundromat owners and general users. He explained that laundromat owners will need MFA and strong passwords, while general users will have simpler registration processes.
- **PHP and Code Scanning:** Seing and Adam discussed the use of PHP in their codebase and the need for code scanning. Adam clarified that their codebase includes both custom-written code and open-source platforms like Magento and Pimcore, which are written in PHP. Kyle suggested scanning only the extensions written by Uvic and not the entire Magento platform.
- **E-commerce Transaction Volume:** Seing and Adam discussed the current transaction volume of their e-commerce platform, which is around 70,000 transactions per year, generating $20 million in revenue. They also mentioned that the transaction volume is expected to increase by about 10% annually.
- **Future Security Tools and Budgeting:** Adam suggested using the current year to determine the security tools and software needed for 2026. He emphasized the importance of planning and budgeting for tools like SonarQube, Veracode, or Black Duck before September.
- **API Gateway and Application Load Balancer:** Kyle mentioned that they will hold off on the API gateway for now and use the application load balancer to achieve the desired functionality. He promised to make this the first agenda item for the next week and provide a write-up.

