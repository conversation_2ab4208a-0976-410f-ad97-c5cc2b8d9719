---
title: June 17th Cybersecurity Meeting
summary: Agenda for the June 17th Cybersecurity Meeting
---
## Attendees:
- <PERSON>
- <PERSON>
- Kyle
- Seing

## Agenda:
- Closed issues since last meeting   
- Architecture Updates
    - Magento Ingress/Egress
    - SAP Ingress/Egress
    - Devsecops Maturity Model introduction for Axon
        - https://github.com/ALSSoftware/axon/blob/main/DSOMM.md
    - SAST for PHP applications (Magento and Pimcore)
        - Evaluating github.com/github/codeql-php-pack and PHPStan. Both will output in SARIF format. CodeQL is the desired option.
    - Milestones & Upcoming Sprints
        - Spacelift
        - Entra Id P2 Evaluation
        - DNS

## Meeting Notes

- **DSAM Assessment:** <PERSON> and <PERSON> discussed the DSAM assessment, focusing on the maturity levels of their DevSecOps practices. They identified areas where they are meeting standards and areas needing improvement, such as code signing and artifact signing.
  - Initial Assessment: <PERSON> explained that the DSAM assessment was conducted to understand their DevSecOps maturity. An AI initially reviewed their repository, but it lacked access to their IAC repo, so <PERSON> manually verified the results. They have a defined build process but lack code and artifact signing, which are necessary for higher maturity levels.
  - Maturity Levels: <PERSON> outlined the maturity levels: Level 2 for basic security practices, Level 3 for high adoption, and Levels 4 and 5 for advanced practices. They aim to achieve higher maturity by improving their build and deployment processes.
  - Target Levels: Seing inquired about the maturity overview, and <PERSON> clarified that the target levels are their proposed goals. <PERSON> emphasized the importance of achieving a high level of maturity, especially since they are working on a Greenfield project.
  - Challenges: They discussed the challenges in achieving higher maturity levels, such as the need for feature toggles and blue-green deployments, which may not inherently increase security. They also noted the absence of a patch policy, which they plan to address before their go-live date.
- **Build and Deployment Maturity:** Kyle and Adam emphasized the importance of achieving a high level of maturity in their build and deployment processes by October 1st. They acknowledged the aggressive timeline but believed it was attainable.
  - Timeline: Kyle and Adam set an aggressive timeline to achieve high maturity in their build and deployment processes by October 1st. They believe this goal is attainable despite the challenges.
  - Current Status: They reviewed their current status, noting that they have a defined build process but lack code and artifact signing. They aim to improve these areas to reach higher maturity levels.
  - Action Plan: Kyle proposed focusing on the most valuable practices and applying them to all repositories. They plan to address any gaps quickly and ensure a visual representation of their maturity levels to track progress.
- **Patch Policy:** Kyle and Seing discussed the need for a defined patch policy before their go-live date. They agreed on the steps required to change their current status from non-compliant to compliant.
  - Current Status: Kyle noted that they currently lack a defined patch policy, which is necessary for compliance. They plan to establish this policy before their go-live date.
  - Steps to Compliance: Seing asked about the steps to change their status from non-compliant to compliant. Kyle explained that they need to create a policy, upload it to GitHub, and ensure it is implemented and followed.
  - Accountability: Kyle emphasized the importance of holding themselves accountable for their maturity levels and ensuring that their practices align with their defined policies.
- **Blue-Green Deployments:** Adam explained the concept of blue-green deployments to Seing, clarifying that it involves having a more complex production environment to introduce new versions without affecting the entire system.
  - Concept Explanation: Adam explained that blue-green deployments involve having a more complex production environment where traffic is directed to a subset of containers running the new version, allowing for testing without affecting the entire system.
  - Comparison: Adam compared blue-green deployments to canary deployments, where new versions are gradually introduced and tested before being fully deployed. This method is used by mature organizations like Facebook.
  - Relevance: They concluded that blue-green deployments are not necessary for their current setup, as they do not have the business justification for such a complex deployment strategy.
- **Nightly Build of Images:** Adam and Kyle discussed the relevance of nightly builds of images, concluding that it may not be necessary for their current setup as they are already creating Docker images on every push.
  - Current Practice: Adam explained that they are already creating Docker images on every push, which is more efficient for their current setup than nightly builds.
  - Contextual Relevance: They discussed that nightly builds are more relevant for larger teams with continuous contributions, whereas their current practice suits their smaller, more agile team.
- **Security Champions and Training:** Seing and Kyle discussed the importance of security training and consulting. They considered the possibility of using AI for initial security consulting and agreed on the need for a budget for future training.
  - Training Importance: Seing emphasized the importance of security training for the team. They discussed the need for a budget to facilitate regular security training sessions.
  - AI Consulting: Kyle suggested using AI for initial security consulting to identify potential issues and provide guidance, which could be a cost-effective first step.
- **Landing Zone Project:** Job updated the team on the progress of the landing zone project, emphasizing its importance for managing multiple AWS environments efficiently. They discussed the need for a standardized approach across different teams.
  - Project Progress: Job provided an update on the landing zone project, highlighting its importance for managing multiple AWS environments efficiently. They aim to have the landing zone operational within the next few weeks.
  - Standardization: They discussed the need for a standardized approach across different teams to ensure consistency and efficiency in managing AWS environments.
  - Marketing Sites: Job mentioned the need to migrate marketing sites to the new landing zone, which will be tackled as a separate phase to avoid impacting the current project.
- **Customer SSO and P2 License:** Kyle and Job discussed the requirements for customer SSO and the need for a P2 license for evaluation. They agreed to gather the necessary information and proceed with obtaining the licenses.
  - Requirements: Kyle and Job discussed the requirements for implementing customer SSO, including the need for a P2 license for evaluation purposes.
  - License Acquisition: Job agreed to gather the necessary information and proceed with obtaining the required licenses to ensure the solution meets their needs.
- **DNS Strategy:** Kyle presented a plan for DNS strategy, considering the use of Cloudflare versus AWS Route 53. They discussed the pros and cons of each option and the need for standardization across the company.
  - Plan Presentation: Kyle presented a plan for their DNS strategy, considering the use of Cloudflare versus AWS Route 53. He highlighted the pros and cons of each option.
  - Pros and Cons: Kyle noted that Cloudflare offers better uptime and zero trust security, while AWS Route 53 provides a more integrated solution within their existing AWS infrastructure.
  - Standardization: They discussed the need for standardization across the company to ensure consistency and efficiency in managing DNS configurations.
- **Next Steps and Prioritization:** Kyle and Seing agreed on the next steps, including prioritizing tasks and creating a list of things to start on first. They discussed the importance of documenting and addressing any disagreements or issues.
  - Task Prioritization: Kyle and Seing agreed on the next steps, including prioritizing tasks and creating a list of things to start on first to ensure progress and focus on critical areas.
  - Documentation: They emphasized the importance of documenting any disagreements or issues to ensure transparency and accountability in their processes.


## Follow-up tasks
- **DSOM Assessment:** Review and update the DSOM assessment to include the IAC repository and ensure all security practices are accurately represented. (Kyle)
- **Patch Policy:** Define and upload a patch policy to GitHub to change the red X to a green in the DSOM assessment. (Kyle)
- **Trend Model Analysis:** Dump the trend model analysis into the project folder for review and validation. (Seing)
P2 License Evaluation: Provide the requirements and number of licenses needed for the P2 license evaluation for customer SSO. (Kyle)
- **DNS Strategy:** Open a dialogue with Alex to discuss the DNS strategy and the use of Cloudflare versus Route 53. (Kyle)
- **Prioritized List:** Give Seing a prioritized list of the things the team will start on first. (Kyle)
- **GitHub Issue:** Create a GitHub issue in the repo to document disagreements and updates needed for the DSOM assessment. (Adam, Seing)
