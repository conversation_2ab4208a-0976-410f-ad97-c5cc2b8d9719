---
title: Discounts
description: Discount system requirements and implementation guidelines for the e-commerce platform
summary: Comprehensive discount system requirements and implementation guidelines for the e-commerce platform, covering discount types, customer groups, usage limits, and technical implementation notes
owners:
    - euvic
---

# Discounts

Comprehensive discount system requirements and implementation guidelines for the e-commerce platform.

## Change History

| **Version** | **Date** | **Author** | **Description of Change** |
| --- | --- | --- | --- |
| 1 | 24.06.2025 | Katarzyna Krzyt | Initial version |

## Related Tasks

1. [Related task link](https://fwc-commerce.atlassian.net/wiki/spaces/ALS/pages/4575264779)

## Process Description

### Must Have for Launch

#### Discount Type Options
- **Percentage**: Discount applied as a percentage of the total
- **Flat**: Fixed amount discount

#### Discount From Options
- **Subtotal**: Discount applied to the order subtotal
- **Shipping**: Discount applied to shipping costs

#### Include or Exclude Products
The system must support including or excluding:
- Specific products
- Specific brands
- Specific categories
- Heavy or bulky items

#### Customer Groups - Implementation Considerations

**Current State:**
- One segmented customer group: "Alliance Premier"
- Alliance Premier receives loyalty discount on popular eCommerce items
- Each product has an "Alliance Premier" price loaded/updated from PIM
- Logged-in Alliance Premier members receive the lower of normal price or Alliance Premier price

**Future State:**
- Link membership dynamically with customer purchase history volume
- Apply loyalty discount based on purchase volume thresholds

**Implementation Options:**

1. **Fixed Price Field Approach:**
   - Create new price field dedicated to "Alliance Premier" customer group
   - Provides fixed price per product

2. **Percentage Discount Approach:**
   - Product attribute: "eligible for loyalty discount"
   - Set up percentage discount values for each tier
   - Different tiers can apply across product groups based on attributes
   - Multiple price fields may be necessary for fixed discount per product

#### Additional Must-Have Features
- **Start date and expiration date**
- **Usage limits:**
  - Total redemptions
  - Per user redemptions
- **Force Login**: Apply rules only for logged-in users (available out-of-the-box)
- **Reporting**: Available in Magento rather than Salesforce
- **Report sales based on promotions/codes**: Coupons Report in Magento lists total orders per day/month/year (note: does not provide order numbers)

### Not Required for Launch / Phase 2

#### Email Integration Features
- **Integrate with email service provider**: For dedicated coupon codes
- **Create one-time use discount codes for each email address**: Not available out-of-the-box
- **Allow for guest checkout**: Possible with generic coupons (usable by different users)
- **New subscriber discounts**: Only one generic coupon possible; can automatically discount a purchase once per customer

#### Advanced Automation Features
- **Automations**
- **First-time order discounts**
- **Large order discounts**: Available out-of-the-box
  - Rule can apply if total value equals or exceeds X
  - Rule can apply if total number of products equals or exceeds Y
- **Loyalty discount extension**
- **Win-back campaign**

#### Cart Recovery Features
- **Cart recovery (abandoned cart)**
- **Create unique cart recovery discount codes**

## Technical Implementation Notes

### Magento Capabilities
- Force Login functionality is available out-of-the-box
- Large order discounts supported with configurable thresholds
- Coupons Report provides order statistics but not individual order numbers
- Generic coupons can be configured for multiple users

### Limitations
- One-time use discount codes per email address require custom development
- Unique cart recovery discount codes need custom implementation

---

*Source: Confluence Page ID 4679106567*  
*Created: June 24, 2025*  
*Author: Katarzyna Krzyt*  
*Space: ALS* 