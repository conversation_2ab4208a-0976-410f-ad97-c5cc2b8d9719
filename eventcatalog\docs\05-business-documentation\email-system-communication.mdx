---
id: email-system-communication
title: Email System Communication
version: 1.0.0
summary: Email communication flow and responsibilities across different systems
owners:
    - euvic
---

# Email System Communication

## Overview

This document outlines the email communication flow and system responsibilities for various customer interactions and business processes.

## Communication Matrix

| **Scope** | **Action** | **Email from [system]** | **Email to** |
|-----------|------------|-------------------------|--------------|
| "contact" forms from website | Customer sends form from website | PWA → Magento -&gt; integration layer -&gt; Genesys Cloud | customer service |
| transactional emails | order placed, order status change etc | Magento | customer |
| customer registation/password reset | after customer action in SSO service | Customer service SSO | customer |
| Subbscribe/unsubscribe to newsletter | subscribe from website<br/>unsubscribe from newsletter email | Salesforce Marketing Cloud | subscriber |
| Returns | information regarding return | Magento | customer |
| Request product information | customer sends form from product detail page | PWA → Magento -&gt; integration layer -&gt; Genesys Cloud | customer service |
| Marketing | promotions, marketing information | Salesforce Marketing Cloud | customer |
| Newsletter | sends newsletter to subscribers | Salesforce Marketing Cloud | subscriber |

## System Responsibilities

### PWA (Progressive Web App)
- Collects customer form submissions
- Routes contact forms and product information requests

### Magento
- Handles transactional emails (order confirmations, status updates)
- Processes return-related communications
- Serves as integration point for customer service requests

### Integration Layer
- Routes customer service requests from Magento to Genesys Cloud
- Ensures proper data flow between systems

### Genesys Cloud
- Receives customer service requests
- Manages customer service communications

### Customer Service SSO
- Handles customer registration confirmations
- Manages password reset communications

### Salesforce Marketing Cloud
- Manages newsletter subscriptions and unsubscriptions
- Handles marketing communications and promotions
- Distributes newsletters to subscribers

## Communication Flow Types

### Customer Service Requests
1. Customer submits contact form on website
2. PWA captures form data
3. Request flows: PWA → Magento → Integration Layer → Genesys Cloud
4. Customer service team receives and responds to request

### Transactional Communications
1. Customer completes action (order, status change)
2. Magento generates appropriate email
3. Email sent directly to customer

### Marketing Communications
1. Salesforce Marketing Cloud manages subscriber lists
2. Marketing team creates campaigns
3. Emails distributed to targeted customer segments

### Newsletter Management
1. Customer subscribes via website
2. Subscription processed through Salesforce Marketing Cloud
3. Newsletter content distributed to subscribers
4. Unsubscribe requests processed automatically 