---
id: pimcore-product-structure
title: PIMCORE Product Structure
version: 2.0.0
summary: Product structure and relationships in PIMCORE system
owners:
    - euvic
---

# PIMCORE Product Structure

## Change History

| **Version** | **Date** | **Author** | **Description of Change** |
|-------------|----------|------------|---------------------------|
| 1 | 18.06.2025 | Katarzyna Krzyt | Initial version |
| 2 | 26.05.2025 | Katarzyna Krzyt | Diagram updated |

## Related Tasks

1. [ALS-178](https://fwc-commerce.atlassian.net/browse/ALS-178)

## Related Documents

1. [Product Management Documentation](https://fwc-commerce.atlassian.net/wiki/spaces/ALS/pages/4544462869)

## Class Diagram

![PIMCORE Product Structure](./images/PIMCORE_product_structure.drawio.png)

## Process Description

1. Each Product object in PIMCORE needs to have an association with the plant object in order to retrieve the attribute data from the Plant
2. Each Plant object in PIMCORE needs to have an association with the Sales Organization object in order to retrieve the attribute data from the Sales Organization
3. SKU can exist in more than one Plant object

## Key Entities &amp; Attributes

### 1. Sales Organisation

- `Sales Organisation ID`: Identifier
- `name`: Name of the sales entity
- `plant: List&lt;Plant&gt;`: One-to-many relationship with **Plant**

### 2. Plant

- `name`: Plant name
- **Relationships**:
  - Belongs to one `Sales Organisation`
  - Holds multiple `Product` instances
  - Has associated `Stock` levels

### 3. Product

- `name`, `SKU`, `description`, `status`: Core attributes
- `visibility`: List of visibility rules in Magento 2
- `salesOrganisation ID`: FK to Sales Organisation
- `productClass`: Product type in SAP
- `typeId`: FK to `&lt;&lt;typeId&gt;&gt;` (Simple, Configurable, Bundle, Virtual)
- `attributeSetId`: Set of grouped attributes
- `image`: Reference to image asset
- `taxClassId`: FK to taxation class
- `category`: List of categories associated with the product
- **Relationships**:
  - Linked to `Plant`, `Sales Organisation`, `Price`, `Stock`

### 4. Price

- `SKU`, `price`, `specialPrice`, `currency`
- `validFrom` / `validTo`: Pricing time windows
- `tierPrice: List&lt;tierPrice&gt;`: Optional tiered pricing (volume-based)

### 5. tierPrice

- `SKU`, `validFrom` / `validTo`
- `quantity`, `price`: Pricing dependent on purchase quantity

### 6. Stock

- `SKU`, `quantity`, `plant`: Stock level per plant

### 7. &lt;&lt;typeId&gt;&gt; (Product Types)

- Contains: `simple`, `configurable`, `bundle`, `virtual`
- Used to classify products for logic/rendering (in Magento 2)

## Questions

1. Should product update sent from PIMCORE include the product master data related to Plant and Sales Organization objects?
2. What are the attributes from Plant that we should manage in PIM? Which ones are mandatory? They should be already in the template files that are used.
3. What are the attributes from Sales Organization that we should manage in PIM? Which ones are mandatory? They should be already in the template files that are used.

## Use Cases

### UC01 – Product Association with Plant

#### User Story

As a system administrator, I want to ensure that each Product object in PIMCORE has proper association with Plant objects to retrieve necessary attribute data.

#### Acceptance Criteria

- Each Product must be linked to at least one Plant object
- Product can retrieve Plant-specific attributes through the association
- SKU can exist across multiple Plant objects
- Association maintains data integrity and referential constraints 