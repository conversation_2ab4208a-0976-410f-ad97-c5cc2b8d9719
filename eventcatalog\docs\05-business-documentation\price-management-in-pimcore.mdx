---
title: Price Management in PIMCORE
summary: "Comprehensive price management documentation for PIMCORE including user roles, supported price fields, validation processes, and synchronization with SAP and Magento systems"
owners:
    - euvic
---

# Change History

| **Version** | **Date** | **Author** | **Description of Change** |
| --- | --- | --- | --- |
| 1 | 13.06.2025 | Katarzyna <PERSON> | Initial version |
|  |   |   |   |

# Related Tasks

1. [ALS-119](https://fwc-commerce.atlassian.net/browse/ALS-119)
2. [ALS-209](https://fwc-commerce.atlassian.net/browse/ALS-209)
3. [ALS-246](https://fwc-commerce.atlassian.net/browse/ALS-246)

# Related Documents

1. 

# Process Description

## 1. User Roles and Permissions

* Only users with the **designated admin role** can:
    * Edit product prices.
    * Change product workflow statuses.

* Non-admin users must not see or access any UI elements related to price management.

## 2. Supported Price Fields

Admin user can update the following price-related fields in PIMCORE:

* **List Price / Web List** (Magento Regular Price)
* **Web Sale Price** (Magento Special Price)
* **Web GM Min.** – a minimum margin (percentage of List Price)
* **Valid From** – effective start date (for SAP)
* **Valid To** – effective end date (for SAP)
* **Tier Price** – unit price based on quantity
* **Tier Quantity** – quantity threshold for tier pricing
* Products can have **multiple tier prices** (tier pricing matrix).

## 3. Methods for Price Updates

Admin user can update prices using:

* Manual edit on the product detail page.
* Mass action on a selected group of products.
* Uploading an Excel file with a predefined format.

## 4. Excel Import Validation

* System must **validate the Excel file** before saving changes:
    * Show an error if any rows contain invalid data.
    * Display row number and error message per each invalid entry.
    * Allow the user to perform a **pre-validation** step before final upload.

* Invalid conditions include:
    * Incorrect data types (e.g., text in numeric fields).
    * Missing required fields (e.g., List Price).
    * Invalid tier pricing logic (e.g., negative quantity).
    * **Valid To \< Valid From** must result in a validation error.

## 5. Logging and Audit

* All price updates must be logged with:
    * Admin username.
    * Timestamp.
    * Fields updated (with old and new values).
    * Optional: Comment (if added).

* Logs must be accessible per product version.

## 6. Workflow and Comments

* Admin user must be able to:
    * Submit a comment when sending updates for approval.
    * Manually flag a price change as **non-standard** (requires approval).

* System must automatically flag price changes as **non-standard** when:
    * Price change exceeds a predefined threshold (e.g., \>10% change vs previous price).

## 7. Discount Class and Net Price Calculation

* Each product must have a `Discount Class`, defined by manufacturer.
* Discount Class contains `NetPriceFactorFromList` (e.g., 0.42).
    * Net Price = List Price × NetPriceFactorFromList

* Each manufacturer must have:
    * At least one Discount Class defined.
    * Discount Class "X" as the default fallback.

## 8. Categorization for Pricing Groups (Optional / Nice-to-Have)

* System should allow assigning products to pricing rule groups, such as:
    * High-volume sellers for special discounting rules.

## 9. Bulk Price Modification

* Admin must be able to:
    * Filter products by attribute (e.g., custom Yes/No flag).
    * Apply a % increase/decrease to prices in bulk (e.g., +2.5%).
    * Preview price changes before saving.

## 10. Limits and Performance Controls

* **Mass update** via UI or file upload should:
    * Be limited to **maximum 5,000 products per operation**.
    * If exceeded, system should prompt: "Limit exceeded, reduce selection or split file."

* For updates \>1,000 products:
    * Changes must be **queued asynchronously** to avoid performance bottlenecks.

## 11. Synchronization with SAP and Magento

* All price updates are pushed via the **Integration Layer API**.
* For each system (SAP and Magento), a **custom field** (e.g., `SAP Sync Status`, `Magento Sync Status`) must reflect:
    * "Pending Sync", "Success", or "Failed".

* Sync logs must be available per product for traceability.

## 12. Versioning and Rollback

* Admin must be able to **restore previous product versions**, including price changes.
* **Action point**: System should allow rollback of a mass import/update if all affected products can be linked to a single import event (TBD – see below).

---

## Action Points & Recommendations

| ID | Action Point | Description |
| --- | --- | --- |
| AP-01 | Define file format | Specify structure, required columns, and validation rules for Excel price upload |
| AP-02 | Determine rollback scope | Define how mass price updates can be grouped and reverted in bulk |
| AP-03 | UI feedback for sync | Display SAP/Magento sync status directly in UI (e.g., colored icon or tooltip) |
| AP-04 | Threshold configuration | Allow system admin to set/change non-standard threshold (e.g., \>10%) |
| AP-05 | Confirm queue mechanism | Ensure async job queue is implemented for operations \>1k products |
| AP-06 | Define product pricing groups | If optional categorization is in scope, define rule types and UI for grouping |

# Questions

1. 
2. 

# Use Cases

## **UC01 –** 

### **User Story**

### **Acceptance Criteria**

* a 