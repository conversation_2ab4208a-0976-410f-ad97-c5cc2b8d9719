---
id: product-management
title: Product Management
version: 2.0.0
summary: Product management lifecycle, attributes, and processes in PIMCORE and Magento
owners:
    - euvic
---

# Product Management

## Change History

| **Version** | **Date** | **Author** | **Description** |
|-------------|----------|------------|-----------------|
| 1 | 06.06.2025 | <PERSON><PERSON> | Initial Version |
| 2 | 10.06.2025 | <PERSON><PERSON> | Product update and user stories added |

## Introduction

The documentation describes key business assumptions and requirements for product management. The purpose of the process is to centralize, automate, and standardize processes related to managing product data, its attributes, prices, descriptions, and links to other business systems.

## Related Tasks

- Main task: [ALS-178](https://fwc-commerce.atlassian.net/browse/ALS-178)

## Documents

- Service: [INT-EVE-MA005 ProductService](https://fwc-commerce.atlassian.net/wiki/spaces/ALS/pages/4609867819/INT-EVE-MA005+ProductService)
- Files: [Google Drive Folder](https://drive.google.com/drive/u/0/folders/1TAJj-zg7Otmxo5m6UGo27svsWHMmmp7S)
- Attributes: [Attributes Spreadsheet](https://docs.google.com/spreadsheets/d/1QFlJYyoAzMmHHvKDfyfcgnE7wusQylfZ/edit?gid=602770274#gid=602770274)

## Process

### Description

The product management process covers the full lifecycle of product data in an organization – from acquisition, through updating, verification, publication, to archiving and product withdrawal.

Main steps of the process:

- **Obtaining product data** - product data is entered into the PIM system manually (individual editing) or in bulk (import from files, integration with external systems). PIM is the source of truth for product data.
- **Data verification and enrichment** - the entered data is verified for completeness and correctness, and then supplemented with additional information - descriptions, attributes, photos, categories, connections to models and price classifications.
- **Update and maintenance** - the system allows both editing of individual records and mass updates (e.g. price changes, introducing promotions, marking products as withdrawn or adding substitutes).
- **Data publication and distribution** - verified and approved product data is made available in related systems and transferred to other systems via integrations (API, exchange files).
- **Monitoring and managing the product lifecycle** - the system supports availability control, product status management, handling of withdrawn items and automatic generation of content for SEO.

### Attributes

#### Introduction

Product attributes are the basis for managing product data in the PIM system. They define both basic information (e.g. SKU, name, description) and advanced parameters related to logistics, prices, availability or product classification. Some attributes come directly from product pages, others are generated or maintained in the PIM system. Attributes are used in various business processes, integrations and publications to sales systems.

#### Main Product Attributes

Below is a list of attributes, their sources, field types, and notes on use in filters and search engines. The description also includes business context, if available.

| **Attribute name** | **Attribute source** | **PIM attribute name** | **Field type** | **Options (for dropdown/multiselect)** | **Filterable** | **Searchable** | **Example value** | **Comment** |
|--------------------|---------------------|------------------------|----------------|----------------------------------------|----------------|----------------|-------------------|-------------|
| SKU | product page | DEALERID | - | - | yes | - | WP-W10289661 | "SKU" (unique product ID) |
| item # | product page | - | - | - | yes | - | - | |
| image | product page | - | - | - | yes | - | - | |
| description | product page | ItemDescription | - | - | yes | - | "Buy this Maytag part..." | |
| category name | product page | CategoryTree | - | - | yes | - | "Residential Inglis Laundry Parts" | |
| List price | Price | - | $ | - | - | - | $4.46 | |
| Sale (price) | Halls | - | $ | - | - | - | $3.35 | |
| Save (amount) | Calculated | - | $ | - | - | - | -$1.11 | calculated by website |
| xx OEM Description | product page | - | ? | - | - | - | "Replacement GLASS-DOOR HORIZON" | |
| model | product page | - | - | - | yes | - | - | Models are a list of models for each product |
| Laundry Machine Type | product page | MachineType | - | - | yes | - | "Residential Washer" | |
| Replaces the following Part # | product page | xThisPartReplacedBy | - | - | yes | - | W10388315, ... | |
| Genuine xx Part # | product page | - | ? | - | - | - | WPW10388315 | |
| Name | product page | Name | - | - | yes | - | "Inglis #WPW10388315 Washer GLASS-DOOR HORIZON" | |
| Brand | product page | Brand | - | - | yes | yes | Maytag | |
| Machine usage | product page | Machine usage | - | - | yes | yes | - | |
| Machine type | product page | Machine type | - | - | yes | yes | - | |
| Part type | product page | Part type | - | - | yes | yes | - | |
| Model number | product page | Model number | - | - | yes | yes | - | |
| Product type | product page | Product type | - | - | yes | yes | - | |
| Condition | product page | Condition | - | - | yes | - | - | |

*[Table continues with additional PIM attributes...]*

#### Additional Notes

- Some attributes have special uses or can be calculated automatically by the system (e.g. AVAILABILITY, Save, NLA flags).
- Some attributes may only be available in specific contexts (e.g. price list, product view, variant configuration).
- Attributes such as Brand, Machine usage, Product type, etc. are also used for filtering and searching in the system.
- Attributes such as SKU, ManufacturerPartNumber, DEALERID, etc. must be unique within the system.

#### Product Attribute Mapping Table

| **ALS** | **Magento API / payload** | **PIMcore (PIM) structure** | **Comment** |
|---------|---------------------------|----------------------------|-------------|
| SKU / DEALERID | sku | sku | Required, unique identifier |
| Name | name | name | Required, Trade Name |
| ItemDescription / description | description | description | Required, full description |
| short_description | short_description | short_description | Optional short description |
| List price / Price / NetPrice | price | price | Required, Base Price/MSRP |
| Sale (price) | special_price | special_price | Optional, promotional price |
| special_from_date | special_from_date | special_from_date | Promotion start date, optional |
| special_to_date | special_to_date | special_to_date | Promotion end date, optional |
| status | status | status | Required, status (enabled/disabled) |
| visibility | visibility | visibility | Required, visibility (shop, catalog, search engine) |
| image | image | image | Required in the shop, main photo |
| gallery | gallery | gallery | Additional photo gallery |
| CategoryTree / category name | categories | categories | Required in store, product categories |
| WEIGHT | weight | weight | Required for logistics/shop |
| tax_class_id / TAXABLE | tax_class_id | tax_class_id | Required for tax calculation |
| AVAILABILITY / is_in_stock | / stock API | is_in_stock | Availability (Boolean, warehouse) |
| stock_qty | / stock API | stock_qty | Number of pieces in stock |
| attribute_set / attribute_set_id | attribute_set_id | attribute_set | Mandatory in Magento, attribute set type |

*[Mapping table continues...]*

#### Mapping Comments

- Basic attributes (SKU, name, description, price, status, visibility, weight, categories, tax_class_id, attribute_set_id) are always present in every structure and are required for correct product creation.
- Specialized and business attributes (e.g. Brand, ManufacturerPartNumber, Condition, Model, etc.) are supported by Magento and PIMcore as custom attributes – they can be freely extended depending on the implementation.
- Attributes related to promotions, shipping, status flags (e.g. FREE GROUND, HANDLING, NLA) – are stored as custom attributes, but can be a configuration element or inherited from attribute templates.
- SEO attributes (meta_title, meta_description, meta_keywords, url_key) – are supported natively in Magento/PIMcore, but are not mandatory.
- Attributes related to the warehouse (is_in_stock, stock_qty) – are crucial for sales management, they are distributed to separate API endpoints.

### Product Creation

#### Preparation and Entry of Product Data

**Minimum required set of attributes** - for a product to be created and displayed in related systems, it is necessary to fill in the mandatory fields in PIM:

- `sku` – unique product identifier
- `name` – product name (commercial, visible to the customer)
- `price` – base price (MSRP, list price)
- `status` – product status (enabled/disabled)
- `visibility` – visibility in the catalog/shop
- `type_id` – product type (e.g. "simple")
- `weight` – product weight
- `attribute_set_id` – a set of attributes (predefined for a given product type)

**For a complete in-store presentation (recommended for business):**

- `description` – full description of the product
- `categories` – belonging to at least one category
- `image` – main product photo
- `tax_class_id` – tax class
- `is_in_stock` – availability (warehouse)

#### Generating API Payload

Based on data from PIM, a request is prepared that is compatible with the Magento API ([INT-EVE-MA005 ProductService](https://fwc-commerce.atlassian.net/wiki/spaces/ALS/pages/4609867819/INT-EVE-MA005+ProductService)):

- /rest/V1/products request is sent to the Magento endpoint.
- Magento creates a product based on the passed attributes, checking required fields and data validity.

#### Stock Data and Promotional Prices Supplemented

Information about stock status and availability (is_in_stock, stock_qty) and special/tiered prices are sent via dedicated endpoints:

- `/rest/V1/inventory/source-items` for stock
- `/rest/V1/products/special-prices` for special prices

#### Enrichment and Support of Additional Attributes

- Once a product is created, additional data can be entered (e.g. SEO descriptions, custom attributes, shipping flags, substitution information, promotion and variant support – color, size, etc.)
- Some custom attributes require prior definition in the Magento attribute_set and appropriate configuration in PIM.

#### Monitoring and Updating

- After publishing a product, all changes (e.g. change of price, status, availability) are synchronized on an ongoing basis, according to events of the ProductUpdated, ProductStockUpdated, ProductPriceUpdated type.
- PIM/Magento integration systems and processes ensure full data consistency and product lifecycle support.

### Product Update

#### Initiate a Product Data Change

A product update can occur in several situations:

- manual editing in PIM (e.g. the operator changes the description, product status),
- mass import from a file (e.g. attributes from a supplier),
- automatic synchronization with an external system (e.g. SAP).

#### Introduction of Changes to PIM

The operator or integration process edits the appropriate product field (or set of fields) in PIMcore, e.g.:

- Text (e.g. CleanDescription, ManufacturersDescription),
- Numeric (e.g. WEIGHT, SHIPPINGAMOUNT),
- Boolean (e.g. FREE GROUND, OWN BOX, SpecialOrder),
- Picklists (e.g. DefaultBrand, DefaultMachineType, DiscountClass).
- Custom attributes: change values according to business requirements.

#### Synchronization and Propagation of Changes to Magento

**Generating payload for API**

- Based on the updated data, PIM generates JSON payload according to Magento requirements (PUT /rest/V1/products/\{sku\}): [Product Updated](https://fwc-commerce.atlassian.net/wiki/spaces/ALS/pages/4609867819/INT-EVE-MA005+ProductService#Product-Updated)
- You can update only selected attributes, it is not necessary to provide the entire product object.

**Magento API call**

- The request is sent to Magento through an intermediate integration layer.
- The Magento system identifies the product by SKU and updates only the indicated attributes.
- An HTTP 200 response indicates success and Magento returns the full current status of the product.
- In the event of an error (e.g. missing requirement field, incorrect format, invalid value) – the API returns the appropriate error code and description.

**Stock and promotional prices update**

Changes in stock levels (is_in_stock, stock_qty) or special prices (special_price, tier_price) are sent via dedicated endpoints:

- /rest/V1/inventory/source-items for inventory,
- /rest/V1/products/special-prices for promotions,
- /rest/V1/products/tier-prices for tier prices.

**Change verification and monitoring**

- After each update, PIM and Magento synchronize the change status.
- Changes are immediately visible in the store catalog, search engine and administration panel.
- In case of mass integrations, processing statuses and possible errors are logged to reports or diagnostic panels.

## User Stories

### Creating a New Product

#### User Story

As a business, I want to be able to create a new product in the PIM system so that it can be published correctly in the Magento store and be available to end customers.

#### Business Assumptions

The new product must be correctly created in PIM with all required data and synchronized with Magento using defined API integrations.

#### Acceptance Criteria

- PIM enables the creation of a new product by:
  - individual form editing,
  - import from file (e.g. Excel),
  - external API.

- The new product must contain a set of mandatory attributes:
  - sku, name, price, status, visibility, type_id, weight, attribute_set_id.

- Optional data, recommended for business purposes (for full presentation):
  - description, categories, image, tax_class_id, is_in_stock.

- After saving the data, PIM generates the correct API payload and sends it to Magento:
  - POST /rest/V1/products with complete product data

- Magento responds with status 200 and returns the full status of the newly created product.

### Enriching Product Data with Extended Attributes

#### User Story

As a business, I want to be able to enhance existing products with extended attributes to present more complete information to the customer and support sales, logistics and SEO processes.

#### Business Assumptions

- Extended attributes can include descriptions, logistics, statuses, as well as features used in search, filtering, and promotions.
- Enrichment can be done manually in PIM or automatically from a file or external integration.
- Attributes must be correctly mapped to the Magento structure and supported by the API.

#### Acceptance Criteria

- Extended attributes are added to products via:
  - Editing a single product in PIM,
  - Bulk import from file (e.g. Excel),
  - Synchronization with an external system (e.g. SAP, manufacturer's API).

- The PIM system allows you to update the following types of attributes:
  - Text (e.g. CleanDescription, ManufacturersDescription),
  - Numeric (e.g. WEIGHT, SHIPPINGAMOUNT),
  - Boolean (e.g. FREE GROUND, OWN BOX, SpecialOrder),
  - Picklists (e.g. DefaultBrand, DefaultMachineType, DiscountClass).

- Attributes are saved in the appropriate set (attribute_set) and exported as custom_attributes or dedicated fields in the Magento API.
- The PIM system generates the correct payload and sends it to Magento via PUT /rest/V1/products/\{sku\}.
- Magento saves attributes and allows them to be used in further processes (e.g. filtering, SEO, logistics).
- Computed attributes (e.g. Save, NLA_AllSourcesCalc) are updated automatically based on related data or system scripts.

### Updating an Existing Product

#### User Story

As a business, I want to be able to update product data to keep it accurate, current, and aligned with supplier data and customer expectations.

#### Business Assumptions

- The update may concern various attributes, e.g. basic (e.g. price, description), logistic (e.g. dimensions, weight), statuses (e.g. withdrawal), associations (e.g. replacement).
- Changes can be made manually, in bulk, or through automated integrations.

#### Acceptance Criteria

- Data update can be initiated by:
  - editing a single product in PIM,
  - file import (e.g. new price list, file with substitutes),
  - automatic synchronization with external systems (e.g. SAP).

- As part of the update, it is possible to change, for example:
  - price, NetPrice, StandardCost, DiscountClass, MAP_Price,
  - description, short_description, meta_description,
  - weight, dimensions, shipping attributes,
  - status, is_in_stock, stock_qty,
  - ReplacementPartExists, ReplacedByPN, NLAFlag.

- PIM generates JSON payload with only updated fields and sends it to Magento via endpoint: PUT /rest/V1/products/\{sku\}
- Magento identifies the product by SKU, updates the data, and returns status 200 on success.
- Separate endpoints are used for:
  - inventory: /rest/V1/inventory/source-items,
  - promotional prices: /rest/V1/products/special-prices,
  - tiered prices: /rest/V1/products/tier-prices.

- Changes are automatically synchronized between PIM and Magento with each update.

### Mass Product Handling

#### User Story

As a business, I want to be able to mass process product data to add new products and update existing ones, to effectively manage a large number of product items and ensure their consistency in the PIM and Magento system.

#### Business Assumptions

- The system enables mass action: adding or updating products in bulk.
- The process also includes validation, change logging, and synchronization to Magento via API.

#### Acceptance Criteria

- New products are visible in the online store after being added correctly if:
  - have required fields (name, price, status, visibility, image, category),
  - have the enabled status set and appropriate visibility (catalog, search, both).

- Existing products are updated – for example, new prices, descriptions, attributes or statuses are visible.
- Products marked as unavailable (NLAFlag or status = disabled) are not displayed in the product list or search results.
- The system enables mass import of product data in the "create or update" mode.
- The system recognizes products by SKU:
  - if SKU does not exist – creates a new product,
  - if SKU exists – updates the data.

- Supported data sources for bulk import/update:
  - Excel/CSV files with column mapping to system fields,

- Required fields for new products:
  - sku, name, price, status, visibility, type_id, weight, attribute_set_id.

- Supported update attributes, e.g.:
  - prices (price, special_price, MAP_Price, NetPrice, StandardCost),
  - logistic attributes (weight, dimensions, shipping),
  - descriptions (description, CleanDescription, EXTENDEDDESCRIPTION),
  - statuses (NLAFlag, ReplacementPartExists, PartPriceIsZero),
  - replacements (ReplacedByPN), categories, images, SEO.

- The system generates appropriate requests to Magento:
  - POST /rest/V1/products for new products,
  - PUT /rest/V1/products/\{sku\} for update,
  - POST /rest/V1/products/special-prices for specials and /rest/V1/products/tier-prices for tier prices
  - POST /rest/V1/inventory/source-items for inventory.

- The system validates the data:
  - formats (numbers, texts, booleans),
  - completeness of required fields,
  - correctness of values (e.g. visibility within the allowed range). 