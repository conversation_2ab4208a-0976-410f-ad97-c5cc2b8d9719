---
id: replacement-parts-management
title: Replacement Parts Management
version: 1.0.0
summary: Management of replacement parts relationships and display logic
owners:
    - euvic
---

# Replacement Parts Management

## Change History

| **Version** | **Date** | **Author** | **Description** |
|-------------|----------|------------|-----------------|
| 1 | 20.06.2025 | Katarzy<PERSON> | Initial Version |

## Introduction

This document outlines the process and requirements for managing replacement parts relationships within the product catalog system.

## Related Tasks

- Main task: [ALS-264](https://fwc-commerce.atlassian.net/browse/ALS-264)

## Documents

- Service: [INT-EVE-MA005 ProductService](https://fwc-commerce.atlassian.net/wiki/spaces/ALS/pages/4609867819/INT-EVE-MA005+ProductService)
- Files: [Google Drive Folder](https://drive.google.com/drive/u/0/folders/1TAJj-zg7Otmxo5m6UGo27svsWHMmmp7S)
- Attributes: [Attributes Spreadsheet](https://docs.google.com/spreadsheets/d/1QFlJYyoAzMmHHvKDfyfcgnE7wusQylfZ/edit?gid=602770274#gid=602770274)

## Process

### Description

The replacement parts management system handles the display and maintenance of product replacement relationships on Product Detail Pages (PDP).

### 1. Display of Replaced Part Information on PDP

- If a product has replaced another product, the PDP of the current product displays the part number(s) of the replaced product(s).
- The replaced part number(s) are displayed with a clickable link leading to the PDP(s) of the replaced product(s) in the same browser tab.
- The label "Replaces" (or equivalent) precedes the replaced part number(s) for clear context.

### 2. Display of Replacement Part Information on PDP

- If a product has been replaced by another product, the PDP of the current product displays the part number(s) of the replacement product(s).
- The replacement part number(s) are displayed with a clickable link leading to the PDP(s) of the replacement product(s) in the same browser tab.
- The label "Replaced by" (or equivalent) precedes the replacement part number(s) for clear context.

### 3. Multiple Replacement Scenarios

- A single product can have **multiple replacements**, and all associated replacement part numbers must be listed and linked appropriately.
- A single product can **replace multiple other products**, and all replaced part numbers must be listed and linked appropriately.

### 4. Manual Maintenance via PIMCORE

- An admin can manually assign a replacement part number (i.e., define that a product replaces another SKU) using the PIMCORE admin panel.
- An admin can manually assign a "replaced by" part number (i.e., define that a product has been replaced by another SKU or SKUs) using the PIMCORE admin panel.

### 5. Bulk Import Support

- It must be possible to bulk import replacement relationships via a structured CSV file in PIMCORE.
- The import must support both "replaces" and "replaced by" mappings.
- If invalid SKUs are provided in the CSV, the import process must flag errors with clear error messaging, without importing incorrect data.

## Business Rules

### Replacement Relationships

- **One-to-Many**: A single product can replace multiple other products
- **Many-to-One**: Multiple products can be replaced by a single product
- **Many-to-Many**: Complex replacement scenarios are supported
- **Bidirectional Display**: Both "replaces" and "replaced by" relationships are shown on respective PDPs

### Data Integrity

- All replacement relationships must reference valid, existing SKUs
- Invalid or non-existent SKUs in bulk imports are rejected with error reporting
- Replacement links only appear for active, available products

### User Experience

- Replacement part numbers are clickable links
- Links open in the same browser tab
- Clear labeling distinguishes "Replaces" from "Replaced by" relationships
- Multiple replacement parts are listed clearly

## Technical Implementation

### PIMCORE Configuration

- Admin interface for manual replacement assignment
- CSV bulk import functionality with validation
- Error handling and reporting for invalid data
- Relationship storage and management

### Frontend Display

- Dynamic rendering of replacement information on PDP
- Link generation to replacement product pages
- Conditional display based on relationship existence
- Responsive design for multiple replacement listings

## Questions

1. If the product A is replaced by B, and B is replaced by C, should we update automatically the product A associations with the C product?
2. Is there a limit of the replacement number for one product, e.g. 50?
3. Should we display a replacement/replaced by product to the customer if the product is disabled? The link would then navigate to a 404 (not found) page.

## Use Cases

### UC01 – Display Replacement Information

#### User Story

As a customer, I want to see replacement part information on product detail pages so that I can find alternative or updated parts when needed.

#### Acceptance Criteria

- Replacement relationships are clearly displayed on PDP
- "Replaces" and "Replaced by" labels are distinct and clear
- Part numbers are clickable links to respective product pages
- Multiple replacements are listed appropriately
- Links only appear for valid, active products

### UC02 – Manage Replacement Relationships

#### User Story

As an admin, I want to manage replacement part relationships through PIMCORE so that customers can easily find alternative parts.

#### Acceptance Criteria

- Manual assignment interface available in PIMCORE admin
- Bulk CSV import functionality with validation
- Error reporting for invalid SKUs during import
- Ability to assign both "replaces" and "replaced by" relationships
- Changes reflect immediately on frontend PDPs 