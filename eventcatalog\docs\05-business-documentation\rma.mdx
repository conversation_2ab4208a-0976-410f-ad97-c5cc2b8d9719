---
id: rma
title: RMA (Return Merchandise Authorization)
version: 1.0.0
summary: Return Merchandise Authorization process and workflow
owners:
    - euvic
---

# RMA (Return Merchandise Authorization)

## Return Process

The return process follows a structured workflow to ensure proper handling of customer returns and maintain service quality.

### 1. Access Order History

The customer logs into their account and navigates to their order history or completed orders page.

### 2. Select Order

The customer selects the specific completed order that contains the item they want to return.

### 3. Choose Item(s) to return

From within the order details, the customer selects the item(s) and quantity they wish to return.

### 4. Initiate return

After selecting the item(s), the customer clicks on a "Return" or "Initiate Return" button.

### 5. Answer qualifying questions

The system presents a series of qualifying questions (e.g., reason for return, condition of item, whether it's been used/opened). These questions will follow a structured flow to determine eligibility.

### 6. Review return summary

The customer reviews a summary of the return request, including item(s), reason, and any applicable return conditions.

### 7. Submit return request

The customer confirms and submits the return request.

### 8. Receive return instructions → by email

Once submitted, the customer receives return instructions, including return shipping label (if applicable), return address, and timeline.

### 9. Ship the item back → different shipping addresses (i.e. Marcone)

The customer packages the item securely and sends it back using the provided method.

### 10. Return Confirmation &amp; Refund Processing

Once the returned item is received and inspected, the customer is notified, and the refund or exchange is processed according to the return policy.

## Key Features

- **Self-service portal**: Customers can initiate returns through their account
- **Qualifying questions**: Automated eligibility assessment
- **Multiple shipping destinations**: Returns may be routed to different facilities based on the item
- **Email notifications**: Automated communication throughout the process
- **Return tracking**: Customers can monitor the status of their return

## Business Benefits

- **Streamlined process**: Reduces manual intervention in return processing
- **Improved customer experience**: Clear, step-by-step guidance
- **Operational efficiency**: Automated routing and notifications
- **Quality control**: Structured questions ensure proper return handling

## Integration Points

- **Customer Account System**: Order history and authentication
- **Email System**: Automated notifications and instructions
- **Shipping Partners**: Label generation and tracking
- **Inventory Management**: Return processing and restocking
- **Payment Processing**: Refund handling 