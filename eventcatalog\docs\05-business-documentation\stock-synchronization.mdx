---
id: stock-synchronization
title: Stock Synchronisation
version: 1.0.0
summary: Multi-source inventory model and stock synchronization processes
owners:
    - euvic
---

# Stock Synchronisation

## Change History

| **Version** | **Date** | **Author** | **Description of Change** |
|-------------|----------|------------|---------------------------|
| 1 | 05.06.2025 | Katarzyna <PERSON>rzyt | Initial version |

## Related Tasks

1. [ALS-120](https://fwc-commerce.atlassian.net/browse/ALS-120)

## Related Documents

1. [INT-EVE-MA005 ProductService - Product Stock Updated](https://fwc-commerce.atlassian.net/wiki/spaces/ALS/pages/4609867819/INT-EVE-MA005+ProductService#INT-EVE-MA005ProductService-Product-Stock-Updated)

## Process Description

The e-commerce websites Parts Alliance Laundry and PartsKing operate on a **multi-source inventory model**, allowing products to be stocked and fulfilled from various suppliers and fulfillment channels. This structure supports flexible sourcing, dropshipping, and marketplace integration.

## Stock Sources

Products on the website may be available from one or more of the following sources:

- **Alliance Stock** – The primary inventory location, physically managed by Alliance.
- **Marcone Stock** – Sourced via Marcone, either through direct dropshipping or by first being shipped to the Alliance warehouse.
- **Supplier Stock** – Additional suppliers provide stock either via feed integrations or as static stock (i.e., non-managed inventory).
- **Dropshipping** – Fulfillment is handled directly by third-party suppliers, without stock held in-house.
- **Marketplace Channels** – Stock and availability are synchronized with third-party platforms such as:
  - Amazon
  - eBay
  - Partstown
  - Zoro

Some suppliers or marketplaces may not provide real-time inventory feeds. In such cases, the system handles these products with **non-managed stock**, where the product is always available or unavailable based on the product's active status in the catalog.

## Recommended Minimum Stock Sources

To maintain adequate inventory visibility and fulfillment flexibility, the following sources are recommended at minimum:

- Alliance
- Marcone
- Supplier 1
- Supplier 2
- Supplier 3
- Amazon
- eBay
- Partstown
- Zoro

## Integration and Stock Synchronization

An **integration layer** serves as a central communication and processing hub between systems (SAP, Supplier APIs, PIMCORE, Magento 2). Stock data is synchronized through the standardized event:

- `ProductStockUpdated` **Event**  
  Triggered whenever a product is created or updated in SAP or a supplier system.  
  The event includes the following attributes:
  - `SKU`
  - `source_code`
  - `quantity`
  - `status`

This event updates product inventory in both **PIMCORE** and **Magento 2**, ensuring consistent stock information across all touchpoints.

Event specification: [INT-EVE-MA005 ProductService – Product Stock Updated](https://fwc-commerce.atlassian.net/wiki/spaces/ALS/pages/4609867819/INT-EVE-MA005+ProductService#INT-EVE-MA005ProductService-Product-Stock-Updated)

## Order Fulfillment and Split Orders

When a customer places an order containing items from multiple stock sources, the order is automatically split in SAP based on the product's supplier or warehouse assignment. This ensures that each portion of the order is routed to the correct fulfillment source.

The split order functionality is driven by product attributes such as:

- Supplier
- Warehouse
- Source priority rules (to be defined)

This allows for streamlined multi-source fulfillment and supports both warehouse-fulfilled and dropshipped orders.

### Integration Details

- Integration layer includes the product stock update - [INT-EVE-MA005 ProductService – Product Stock Updated](https://fwc-commerce.atlassian.net/wiki/spaces/ALS/pages/4609867819/INT-EVE-MA005+ProductService#INT-EVE-MA005ProductService-Product-Stock-Updated)
  - Event can be sent from either SAP or supplier directly
  - Event provides information on the SKU, source_code, quantity, status
  - Event is triggered when a new product has been added or updated

- When the customer is purchasing the product from various sources, the order is sent using split order in SAP

## Questions

1. Should the stock data be kept in PIMCORE? If yes, is it enough to keep the track of the stock source, qty and stock status? The in-depth location should be still kept in WMS/SAP
2. As there are two websites, how should the sources be assigned? Are the products shipped from the same locations and share the quantity?
3. How to define low stock? Is it always the same qty, regardless of the stock source? Or the marketplaces, dropshipping, Alliance warehouse would have different rules for low stock?
4. In case of e.g. Marcone, should the dropshipped products and ones sent to Alliance warehouses be separated to different stock sources?
5. Is the end customer informed on the different stock locations?
6. Should the end customer see different stock sources on the website?
7. If the product is available in various sources, which source has the priority? Is the admin able to set up the rules based on customer address, delivery time or cost?

## Use Cases

### UC01 – Synchronise Product Stock from SAP in PIMCORE

#### User Story

As a system administrator, I want to update the product stock imported from SAP in PIMCORE, so that inventory allocation and fulfillment work properly.

#### Acceptance Criteria

- The integration layer event ProductStockUpdated is triggered whenever the stock is updated in SAP and per schedule.
- The event provides stock data for all recently updated products from SAP.
- The event provides information on the stock source, quantity and status for the imported product.
- The stock source provided by the event is mapped with the PIMCORE stock source attribute.
- The quantity provided by the event is updated in PIMCORE.
- The status provided by the event is updated in PIMCORE.

### UC02 – Synchronise Product Stock from Supplier in PIMCORE

#### User Story

As a system administrator, I want to update the product stock imported from Supplier in PIMCORE, so that inventory allocation and fulfillment work properly.

#### Acceptance Criteria

- The integration layer event ProductStockUpdated is triggered whenever the stock is updated by Supplier and per schedule.
- The event provides stock data for all recently updated products from Supplier.
- The event provides information on the stock source, quantity and status for the imported product.
- The stock source provided by the event is mapped with the PIMCORE stock source attribute.
- The quantity provided by the event is updated in PIMCORE.
- The status provided by the event is updated in PIMCORE.

### UC03 – Synchronise Product Stock from SAP in Magento 2

#### User Story

As a system administrator, I want to update the product stock imported from SAP in Magento 2, so that inventory allocation and fulfillment work properly.

#### Acceptance Criteria

- The integration layer event ProductStockUpdated is triggered whenever the stock is updated by SAP and per schedule.
- The event provides stock data for all recently updated products from SAP.
- The event provides information on the stock source, quantity and status for the imported product.
- The stock source provided by the event is mapped with the Magento 2 stock source.
- The quantity provided by the event is updated in Magento 2.
- The status provided by the event is updated in Magento 2.

### UC04 – Synchronise Product Stock from Supplier in Magento 2

#### User Story

As a system administrator, I want to update the product stock imported from Supplier in Magento 2, so that inventory allocation and fulfillment work properly.

#### Acceptance Criteria

- The integration layer event ProductStockUpdated is triggered whenever the stock is updated by Supplier and per schedule.
- The event provides stock data for all recently updated products from Supplier.
- The event provides information on the stock source, quantity and status for the imported product.
- The stock source provided by the event is mapped with the Magento 2 stock source.
- The quantity provided by the event is updated in Magento 2.
- The status provided by the event is updated in Magento 2. 