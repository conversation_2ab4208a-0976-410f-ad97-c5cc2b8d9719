---
id: int-ca-ma001-newsletter-integration-layer
title: INT-CA-MA001 Newsletter Integration Layer
sidebar_label: INT-CA-MA001 Newsletter Integration Layer
slug: /docs/06-magento-backend-documentation/customer-account/interfaces/int-ca-ma001-newsletter-integration-layer
summary: 'INT-CA-MA001 Newsletter Integration Layer interface specification for handling newsletter subscription and communication operations between Magento customer account system and external newsletter services'
owners:
    - euvic
---

# INT-CA-MA001 Newsletter Integration Layer

## Change History

| **Date** | **Author** | **Description of Change** |
| --- | --- | --- |
| 6/24/2025 | @<PERSON><PERSON> | Initial version |
|   |   |   |

## Related Tasks

1. [ALS-197](https://fwc-commerce.atlassian.net/browse/ALS-197)
2. [ALS-221](https://fwc-commerce.atlassian.net/browse/ALS-221)
3. [ALS-222](https://fwc-commerce.atlassian.net/browse/ALS-222)
4. [ALS-223](https://fwc-commerce.atlassian.net/browse/ALS-223)
5. [ALS-224](https://fwc-commerce.atlassian.net/browse/ALS-224)

## Objective

This interface facilitates the transfer of newsletter event data from Magento to the Integration Layer. The system synchronizes the following events:

* Customer subscribed
* Customer unsubscribed
* Guest subscribed
* Guest unsubscribed

## Scope

Newsletter events are sent asynchronously. When an event occurs, it's initially stored in a **queue table**. A **periodic cron job** then processes these waiting event requests, sending them to the Integration Layer.

## Configuration

| **Path** | _Stores → Configuration → Als → Integration Layer Newsletter_ |
| --- | --- |
| **Responsibility** | Team FWC |

| **Field** | **Description** |
| --- | --- |
| **Enable export** | enable/disable subscribers synchronization |
| **Enable Exceptions Log** | enable/disable exceptions logging |
| **Enable Communication Log** | enable/disable communication logging between systems |

## Method of Initiation and Data Exchange

Newsletter-related events are primarily triggered within Magento's `newsletter_subscriber_save_after` event observer. Changes in subscriber data determine the type of event triggered and the action to be prepared.

Additionally, subscriber synchronization can be initiated manually from the `Customer Edit` page in the Admin panel via the "**Export subscriber to ALS**" button.

The synchronization process itself is invoked **in the background as a cron job** at a specified time. This job prepares and dispatches waiting events to the Integration Layer. If an error occurs during transmission, a **retry counter** for the event is incremented, and the event will be reattempted in the subsequent run. Upon successful transmission, the event is removed from the queue table and moved to a table specifically for exported events.

### Event Source

Both **customer** and **guest** subscribed events include an additional **source** parameter, indicating where the event originated. Possible sources are:

* **admin**: Triggered from the admin panel.
* **import**: Resulting from a data import.
* **customer_account**: Initiated through a customer's account.
* **footer**: From a subscription form in the website's footer.
* **graphql**: Via a GraphQL API request - not mapped to footer or customer account.
* **api**: Through a direct REST/SOAP API call.
* **frontend**: A fallback type used when the specific source on the frontend cannot be precisely determined.

### Synchronization

| **Key** | **Value** | **Description** |
| --- | --- | --- |
| Type | Cron job | Synchronization task executed periodically by Cron job |
| Schedule | every minute | Cron job frequency for sending subscription changes |
| Repeat times | 5 | Number of retries in case of an error during event synchronization |

### Database Tables

**Name**: `als_integrationlayer_newsletter_exported_entries`

**Description**: Table with events successfully exported to integration layer

**Name**: `als_integrationlayer_newsletter_export_request`

**Description**: Table with events waiting for synchronization with integration layer

## Exception logging

Logs are located in `var/log/als_integrationlayer_newsletter` directory:

* error-\[DATE\].log - file with error logs
* communication-\[DATE\].log - file with communication logs

---

**Confluence Page Reference:** [INT-CA-MA001 Newsletter Integration Layer](https://fwc-commerce.atlassian.net/wiki/spaces/ALS/pages/4678189120)  
**Page ID:** 4678189120  
**Parent:** [CA] Interfaces  
**Depth:** 3

---

*This page is synchronized from Confluence. For the most up-to-date information, please refer to the original Confluence page.* 