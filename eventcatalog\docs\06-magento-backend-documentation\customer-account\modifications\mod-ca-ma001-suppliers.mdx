---
id: mod-ca-ma001-suppliers
title: MOD-CA-MA001 Suppliers
sidebar_label: MOD-CA-MA001 Suppliers
slug: /docs/06-magento-backend-documentation/customer-account/modifications/mod-ca-ma001-suppliers
summary: 'MOD-CA-MA001 Suppliers modification specification for customer account system, defining supplier management functionality and data structures within Magento customer account module'
owners:
    - euvic
---

# MOD-CA-MA001 Suppliers

## Change History

| **Date** | **Author** | **Description of Change** |
| --- | --- | --- |
| 4/30/2025 | @<PERSON><PERSON><PERSON> | Initial version |
|   |   |   |

## Purpose

Suppliers module introduces a new business entity to the e-commerce system, which is Supplier. This entity defines the product supplier, which is used to split orders and limit data visibility for admin users who log in with a supplier role. In the context of the vocabulary, term `Supplier` has 2 meanings:

1. Product supplier, which is a physical organization that provides physical product;
2. Role of the admin user who can log into the Magento backend to manage orders assigned to him (in other words, orders that consist of at least single product with a particular supplier).

The `Fwc_Suppliers` and `Fwc_SuppliersProduct` modules are designed to manage suppliers and their integration with Magento's catalog. These modules provide functionality for creating, managing, and associating suppliers with products in the catalog. The `Fwc_SuppliersProduct` module extends the functionality of `Fwc_Suppliers` by adding product attribute propagation and catalog integration.

## Related Tasks

* [ALS-101](https://fwc-commerce.atlassian.net/browse/ALS-101)

## Related Processes, Interfaces, and Modifications

* [PRO-PU-MA001 Order split](https://fwc-commerce.atlassian.net/wiki/spaces/ALS/pages/4580376583)

## Usage Scenarios

1. **Managing Suppliers**: admin users can create, edit, and deactivate suppliers via the admin panel.
2. **Mass Deactivation**: admin users can deactivate multiple suppliers at once using the mass action feature.
3. **Catalog Integration**: when a new supplier is created, it is automatically added as an option to the `supplier` product attribute (if enabled).
4. **Customizing Supplier Attributes**: developers can extend the `supplier` attribute or modify its behavior using the provided setup and observer classes.

## Configuration

| **Path** | `Stores → Configuration → Customers → Suppliers` |
| --- | --- |
| **Responsibility** | FWC Team |

| **Field** | **Description** | **Responsibility** |
| --- | --- | --- |
| **Propagate Catalog With New Supplier** | Enables or disables automatic propagation of new suppliers to the catalog. | FWC |

## Modules

### Fwc_Suppliers

#### Purpose

The `Fwc_Suppliers` module provides the core functionality for managing suppliers in the Magento 2 admin panel. It includes features for creating, editing, deactivating, and listing suppliers.

#### Key Features

1. **Supplier Management**:
   * Create, edit, and delete suppliers.
   * Activate or deactivate suppliers.

2. **Admin Grid**:
   * Displays a list of suppliers with filtering and sorting capabilities.
   * Uses a custom grid collection (`Fwc\\Suppliers\\Model\\ResourceModel\\Supplier\\Grid\\Collection`).

3. **Mass Actions**:
   * Mass deactivation of suppliers via the admin grid.

4. **ACL (Access Control List)**:
   * Restricts access to supplier management based on user roles.

### Fwc_SuppliersProduct

#### Purpose

The `Fwc_SuppliersProduct` module extends the functionality of `Fwc_Suppliers` by integrating suppliers with Magento's catalog. It adds a custom product attribute (`supplier`) and provides functionality for propagating supplier data to the catalog.

#### Key Features

1. **Supplier Product Attribute**:
   * Adds a `supplier` attribute to products using a data patch (`AddSupplierProductAttribute`).

2. **Catalog Integration**:
   * Automatically propagates new suppliers as options for the `supplier` attribute in the catalog.

3. **Observer**:
   * Listens for supplier creation events and adds the supplier as an option to the `supplier` attribute.

4. **System Configuration**:
   * Adds a configuration option to enable or disable automatic propagation of suppliers to the catalog.

---

**Confluence Page Reference:** [MOD-CA-MA001 Suppliers](https://fwc-commerce.atlassian.net/wiki/spaces/ALS/pages/4599644182)  
**Page ID:** 4599644182  
**Parent:** [MOD] Modifications  
**Depth:** 3

---

*This page is synchronized from Confluence. For the most up-to-date information, please refer to the original Confluence page.* 