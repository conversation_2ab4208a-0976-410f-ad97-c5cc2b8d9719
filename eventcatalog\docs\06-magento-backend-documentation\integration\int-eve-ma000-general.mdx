---
id: int-eve-ma000-general
title: INT-EVE-MA000 General
sidebar_label: INT-EVE-MA000 General
slug: /docs/06-magento-backend-documentation/integration/int-eve-ma000-general
version: '0.0.1'
summary: 'General entity type definitions and common patterns for Magento backend integration'
owners:
    - euvic
badge:
  label: 'Backend Documentation'
  color: 'blue'
confluencePageId: '4628054017'
---

# Introduction

# Entity type definitions

Since each event description article must declare type definitions on the same page, it could happen that some articles would provide a duplicated type description (e.g. the same `CustomerAddress` entity is referenced in multiple pages).

To address this problem, `Excerpt` macro should be used to declare entity definition, and `Insert excerpt` should be used to reference an entity definition.

Entity type declaration:

Entity type reference: 

---
id: int-eve-ma000-general
title: INT-EVE-MA000 General
sidebar_label: INT-EVE-MA000 General
slug: /docs/06-magento-backend-documentation/integration/int-eve-ma000-general
summary: 'INT-EVE-MA000 General integration interface specification providing common configuration, authentication, and shared functionality for all Magento integration layer services'
owners:
    - euvic
---

# INT-EVE-MA000 General

## Change History

| **Date** | **Author** | **Description of Change** |
| --- | --- | --- |
| 5/8/2025 | @Vladyslav Deyneko | Initial version |
|  |  |  |

## Objective

This document describes the general configuration and shared functionality for all Magento integration layer services.

## Configuration

### Authentication

All integration services use the same authentication mechanism:

- **Method**: Bearer Token
- **Header**: `Authorization: Bearer \{token\}`
- **Token Type**: JWT (JSON Web Token)

### Base URL

All integration services are accessible through the same base URL:

```
https://api.magento.example.com/rest/default/V1/
```

### Common Headers

All requests should include the following headers:

| **Header** | **Value** | **Required** |
| --- | --- | --- |
| Content-Type | application/json | true |
| Authorization | Bearer \{token\} | true |
| Accept | application/json | true |

### Error Handling

All integration services follow the same error response format:

```json
{
  "message": "Error description",
  "errors": [
    {
      "message": "Detailed error message",
      "parameters": [
        {
          "resources": "resource_name",
          "fieldName": "field_name",
          "fieldValue": "field_value"
        }
      ]
    }
  ],
  "code": 400,
  "parameters": [
    {
      "resources": "resource_name",
      "fieldName": "field_name",
      "fieldValue": "field_value"
    }
  ],
  "trace": "Error stack trace"
}
```

### Common HTTP Status Codes

| **Code** | **Description** |
| --- | --- |
| 200 | Success |
| 400 | Bad Request |
| 401 | Unauthorized |
| 403 | Forbidden |
| 404 | Not Found |
| 500 | Internal Server Error |

### Rate Limiting

All integration services are subject to rate limiting:

- **Limit**: 100 requests per minute per client
- **Reset**: Every minute
- **Headers**: 
  - `X-RateLimit-Limit`: Maximum requests allowed
  - `X-RateLimit-Remaining`: Remaining requests in current window
  - `X-RateLimit-Reset`: Time when rate limit resets

### Logging

All integration services log the following information:

- Request timestamp
- Client IP address
- Request method and endpoint
- Response status code
- Response time
- Error details (if any)

### Monitoring

Integration services provide health check endpoints:

- **Endpoint**: `/health`
- **Method**: GET
- **Response**: JSON object with service status

```json
{
  "status": "healthy",
  "timestamp": "2025-05-08T10:30:00Z",
  "version": "1.0.0",
  "dependencies": {
    "database": "healthy",
    "cache": "healthy",
    "external_api": "healthy"
  }
}
``` 