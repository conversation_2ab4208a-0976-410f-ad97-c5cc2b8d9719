---
id: int-eve-ma001-orderservice
title: INT-EVE-MA001 OrderService
sidebar_label: INT-EVE-MA001 OrderService
slug: /docs/06-magento-backend-documentation/integration/int-eve-ma001-orderservice
summary: 'INT-EVE-MA001 OrderService integration interface specification for handling order-related operations including order creation, updates, and status management within Magento order processing system'
owners:
    - euvic
---

# INT-EVE-MA001 OrderService

## Change History

| **Date** | **Author** | **Description of Change** |
| --- | --- | --- |
| 5/8/2025 | @<PERSON><PERSON><PERSON> | Initial version |
| 5/9/2025 | @<PERSON><PERSON><PERSON> | Updated type definitions. |
|  |  |  |

## Events

### Order Created

Request allows to create a new order.

**Endpoint:** `POST /rest/default/V1/orders`

**Body schema:** `application/json`

**Body:**

```json
{
  "entity": {
    "base_currency_code": "USD",
    "base_discount_amount": 0,
    "base_grand_total": 165,
    "base_discount_tax_compensation_amount": 0,
    "base_shipping_amount": 5,
    "base_shipping_discount_amount": 0,
    "base_shipping_discount_tax_compensation_amnt": 0,
    "base_shipping_incl_tax": 5,
    "base_shipping_tax_amount": 0,
    "base_subtotal": 160,
    "base_subtotal_incl_tax": 160,
    "base_tax_amount": 0,
    "base_total_due": 165,
    "base_to_global_rate": 1,
    "base_to_order_rate": 1,
    "billing_address_id": 1,
    "created_at": "2012-10-13 14:15:22",
    "customer_email": "<EMAIL>",
    "customer_firstname": "John",
    "customer_group_id": 1,
    "customer_id": 1,
    "customer_is_guest": 0,
    "customer_lastname": "Doe",
    "customer_note_notify": 1,
    "discount_amount": 0,
    "email_sent": 1,
    "entity_id": 1,
    "global_currency_code": "USD",
    "grand_total": 165,
    "discount_tax_compensation_amount": 0,
    "increment_id": "*********",
    "is_virtual": 0,
    "order_currency_code": "USD",
    "protect_code": "61a56c",
    "quote_id": 4,
    "remote_ip": "127.0.0.1",
    "shipping_amount": 5,
    "shipping_description": "Flat Rate - Fixed",
    "shipping_discount_amount": 0,
    "shipping_discount_tax_compensation_amount": 0,
    "shipping_incl_tax": 5,
    "shipping_tax_amount": 0,
    "state": "new",
    "status": "pending",
    "store_currency_code": "USD",
    "store_id": 1,
    "store_name": "Main Website\\nMain Website Store\\nDefault Store View",
    "store_to_base_rate": 0,
    "store_to_order_rate": 0,
    "subtotal": 160,
    "subtotal_incl_tax": 160,
    "tax_amount": 0,
    "total_due": 165,
    "total_item_count": 5,
    "total_qty_ordered": 7,
    "updated_at": "2012-10-13 14:15:22",
    "weight": 7,
    "items": [
      {
        "amount_refunded": 0,
        "applied_rule_ids": "1,5",
        "base_amount_refunded": 0,
        "base_discount_amount": 0,
        "base_discount_invoiced": 0,
        "base_discount_tax_compensation_amount": 0,
        "base_original_price": 22,
        "base_price": 22,
        "base_price_incl_tax": 22,
        "base_row_invoiced": 0,
        "base_row_total": 22,
        "base_row_total_incl_tax": 22,
        "base_tax_amount": 0,
        "base_tax_invoiced": 0,
        "created_at": "2012-10-13 14:15:22",
        "discount_amount": 0,
        "discount_invoiced": 0,
        "discount_percent": 0,
        "free_shipping": 0,
        "discount_tax_compensation_amount": 0,
        "is_qty_decimal": 0,
        "is_virtual": 0,
        "item_id": 1,
        "name": "Simple Product",
        "no_discount": 0,
        "order_id": 1,
        "original_price": 22,
        "price": 22,
        "price_incl_tax": 22,
        "product_id": 1,
        "product_type": "simple",
        "qty_backordered": 0,
        "qty_canceled": 0,
        "qty_invoiced": 0,
        "qty_ordered": 1,
        "qty_refunded": 0,
        "qty_shipped": 0,
        "quote_item_id": 6,
        "row_invoiced": 0,
        "row_total": 22,
        "row_total_incl_tax": 22,
        "row_weight": 1,
        "sku": "simple",
        "store_id": 1,
        "tax_amount": 0,
        "tax_invoiced": 0,
        "tax_percent": 0,
        "updated_at": "2012-10-13 14:15:22",
        "weight": 1
      }
    ],
    "billing_address": {
      "address_type": "billing",
      "city": "Culver City",
      "company": "Goooogle",
      "country_id": "US",
      "customer_address_id": 1,
      "customer_id": 1,
      "email": "<EMAIL>",
      "entity_id": 1,
      "fax": "************",
      "firstname": "John",
      "lastname": "Doe",
      "parent_id": 1,
      "postcode": "90230",
      "prefix": "Mr.",
      "region": "California",
      "region_code": "CA",
      "region_id": 12,
      "street": [
        "11111 E Las Olas Blvd"
      ],
      "suffix": "Jr.",
      "telephone": "************"
    },
    "payment": {
      "account_status": "string",
      "additional_information": [
        "string"
      ],
      "amount_ordered": 0,
      "amount_paid": 0,
      "amount_refunded": 0,
      "base_amount_ordered": 0,
      "base_amount_paid": 0,
      "base_amount_refunded": 0,
      "base_shipping_amount": 0,
      "base_shipping_captured": 0,
      "base_shipping_refunded": 0,
      "cc_exp_year": "string",
      "cc_last4": "string",
      "cc_ss_start_month": "string",
      "cc_ss_start_year": "string",
      "entity_id": 0,
      "method": "string",
      "parent_id": 0,
      "shipping_amount": 0,
      "shipping_captured": 0,
      "shipping_refunded": 0
    },
    "status_histories": [
      {
        "comment": "string",
        "created_at": "string",
        "entity_id": 0,
        "entity_name": "string",
        "is_customer_notified": 0,
        "is_visible_on_front": 0,
        "parent_id": 0,
        "status": "string"
      }
    ],
    "extension_attributes": {
      "shipping_assignments": [
        {
          "shipping": {
            "address": {
              "address_type": "shipping",
              "city": "Culver City",
              "company": "Goooogle",
              "country_id": "US",
              "customer_address_id": 1,
              "customer_id": 1,
              "email": "<EMAIL>",
              "entity_id": 2,
              "fax": "************",
              "firstname": "John",
              "lastname": "Doe",
              "parent_id": 1,
              "postcode": "90230",
              "prefix": "Mr.",
              "region": "California",
              "region_code": "CA",
              "region_id": 12,
              "street": [
                "11111 E Las Olas Blvd"
              ],
              "suffix": "Jr.",
              "telephone": "************"
            },
            "method": "flatrate_flatrate"
          },
          "items": [
            {
              "amount_refunded": 0,
              "applied_rule_ids": "1,5",
              "base_amount_refunded": 0,
              "base_discount_amount": 0,
              "base_discount_invoiced": 0,
              "base_discount_tax_compensation_amount": 0,
              "base_original_price": 22,
              "base_price": 22,
              "base_price_incl_tax": 22,
              "base_row_invoiced": 0,
              "base_row_total": 22,
              "base_row_total_incl_tax": 22,
              "base_tax_amount": 0,
              "base_tax_invoiced": 0,
              "created_at": "2012-10-13 14:15:22",
              "discount_amount": 0,
              "discount_invoiced": 0,
              "discount_percent": 0,
              "free_shipping": 0,
              "discount_tax_compensation_amount": 0,
              "is_qty_decimal": 0,
              "is_virtual": 0,
              "item_id": 1,
              "name": "Simple Product",
              "no_discount": 0,
              "order_id": 1,
              "original_price": 22,
              "price": 22,
              "price_incl_tax": 22,
              "product_id": 1,
              "product_type": "simple",
              "qty_backordered": 0,
              "qty_canceled": 0,
              "qty_invoiced": 0,
              "qty_ordered": 1,
              "qty_refunded": 0,
              "qty_shipped": 0,
              "quote_item_id": 6,
              "row_invoiced": 0,
              "row_total": 22,
              "row_total_incl_tax": 22,
              "row_weight": 1,
              "sku": "simple",
              "store_id": 1,
              "tax_amount": 0,
              "tax_invoiced": 0,
              "tax_percent": 0,
              "updated_at": "2012-10-13 14:15:22",
              "weight": 1
            }
          ]
        }
      ]
    }
  }
}
```

**Response:**

* HTTP 200 - Order entity that was created.
* HTTP 400 - Bad Request  
* HTTP 401 - Unauthorized
* HTTP 500 - Internal Server Error

### Order Updated

Request allows to update an existing order.

**Endpoint:** `PUT /rest/default/V1/orders/\{id\}`

**Body schema:** `application/json`

**Body:**

```json
{
  "entity": {
    "entity_id": 1,
    "status": "processing",
    "state": "processing"
  }
}
```

**Response:**

* HTTP 200 - Order entity that was updated.
* HTTP 400 - Bad Request
* HTTP 401 - Unauthorized  
* HTTP 404 - Not Found
* HTTP 500 - Internal Server Error

### Order Canceled

Request allows to cancel an existing order.

**Endpoint:** `POST /rest/default/V1/orders/\{id\}/cancel`

**Response:**

* HTTP 200 - Boolean result indicating success.
* HTTP 400 - Bad Request
* HTTP 401 - Unauthorized
* HTTP 404 - Not Found  
* HTTP 500 - Internal Server Error

### Order Comment Added

Request allows to add a comment to an existing order.

**Endpoint:** `POST /rest/default/V1/orders/\{id\}/comments`

**Body schema:** `application/json`

**Body:**

```json
{
  "statusHistory": {
    "comment": "Order comment",
    "is_customer_notified": 1,
    "is_visible_on_front": 1,
    "status": "processing"
  }
}
```

**Response:**

* HTTP 200 - Boolean result indicating success.
* HTTP 400 - Bad Request
* HTTP 401 - Unauthorized
* HTTP 404 - Not Found
* HTTP 500 - Internal Server Error

## Overview

This document describes the INT-EVE-MA001 Order Service integration interface specification.

## Purpose

The Order Service handles order-related operations and data exchange between Magento and Evolution systems.

## Interface Specification

### Service Details
- **Interface ID**: INT-EVE-MA001
- **Service Name**: Order Service
- **Protocol**: REST/GraphQL
- **Authentication**: JWT Token

## API Endpoints

*This section will be populated with detailed API endpoint documentation.*

## Data Models

*This section will be populated with data model specifications.*

## Error Handling

*This section will be populated with error handling procedures.*

## Integration Points

*This section will be populated with integration point details.*

## Configuration

*This section will be populated with configuration details.*

---

*This page corresponds to Confluence page ID: 4604755991* 