---
id: int-eve-ma002-shipmentservice
title: INT-EVE-MA002 ShipmentService
sidebar_label: INT-EVE-MA002 ShipmentService
slug: /docs/06-magento-backend-documentation/integration/int-eve-ma002-shipmentservice
summary: 'INT-EVE-MA002 ShipmentService integration interface specification for handling shipment operations including shipment creation, tracking, and comment management within Magento order processing system'
owners:
    - euvic
---

# INT-EVE-MA002 ShipmentService

## Change History

| **Date** | **Author** | **Description of Change** |
| --- | --- | --- |
| 5/8/2025 | @<PERSON><PERSON><PERSON> | Initial version |
| 5/9/2025 | @<PERSON><PERSON><PERSON> | Updated type definitions. |
|  |  |  |

## Events

### Shipment Created

Request allows to create a new shipment for existing order.

**Endpoint:** `POST /rest/default/V1/order/\{orderId\}/ship`

**Body schema:** `application/json`

**Body:**

```json
{
  "items": [
    {
      "order_item_id": 123,
      "qty": 2
    },
    {
      "order_item_id": 456,
      "qty": 1
    }
  ],
  "notify": true,
  "comment": {
    "comment": "This comment will be visible on fronted for customer, since the according flag is set to 1",
    "is_visible_on_front": 1
  },
  "tracks": [
    {
      "track_number": "1Y-9876543210",
      "title": "United Parcel Service",
      "carrier_code": "ups"
    }
  ]
}
```

**Types:**

| **Attribute** | **Type** | **Is Required** | **Description** |
| --- | --- | --- | --- |
| items | `ShippingItem[]` | true | Defines items from the order, which are included in the shipment. |
| notify | Boolean | true | Defines if customer should be notified of the shipment creation. |
| comment | `ShipmentCommentCreationInterface` | true | Comment that will be seen by the client in the store (Order details view). |
| tracks | `ShipmentTrackCreationInterface[]` | true | Defines if shipment have a tracking information that can be used to track the shipment (e.g. external carrier can offer such functionality). |

**Response:**

* HTTP 200 - Contains ID of the created shipment.

    * ```plaintext
      12
      ```
    
* HTTP 400 - Bad Request

    * ```json
      {
        "message": "string",
        "errors": [
          {
            "message": "string",
            "parameters": [
              {
                "resources": "string",
                "fieldName": "string",
                "fieldValue": "string"
              }
            ]
          }
        ],
        "code": 0,
        "parameters": [
          {
            "resources": "string",
            "fieldName": "string",
            "fieldValue": "string"
          }
        ],
        "trace": "string"
      }
      ```
    
* HTTP 401 - Unauthorized
* HTTP 500 - Internal Server Error

Response body structure for HTTP codes 400, 401, 500 is the same.

### Shipment Comment Added

Request allows to add a comment to existing shipment.

**Endpoint:** `POST /rest/default/V1/shipment/\{id\}/comments`

**Body schema:** `application/json`

**Body:**

```json
{
  "entity": {
    "is_customer_notified": 1,
    "parent_id": 12,
    "comment": "Shipment has been prepared in the parcel service.",
    "is_visible_on_front": 1
  }
}
```

**Types:**

| **Attribute** | **Type** | **Is Required** | **Description** |
| --- | --- | --- | --- |
| entity | `ShipmentComment` | true | Shipment comment object. |

**Response:**

* HTTP 200 - Created comment (type: `ShipmentComment`).

    * ```json
      {
          "is_customer_notified": 1,
          "parent_id": 12,
          "comment": "Shipment has been prepared in the parcel service.",
          "is_visible_on_front": 1
      }
      ```
    
* HTTP 400 - Bad Request

    * ```json
      {
        "message": "string",
        "errors": [
          {
            "message": "string",
            "parameters": [
              {
                "resources": "string",
                "fieldName": "string",
                "fieldValue": "string"
              }
            ]
          }
        ],
        "code": 0,
        "parameters": [
          {
            "resources": "string",
            "fieldName": "string",
            "fieldValue": "string"
          }
        ],
        "trace": "string"
      }
      ```
    
* HTTP 401 - Unauthorized
* HTTP 500 - Internal Server Error

Response body structure for HTTP codes 400, 401, 500 is the same.

## Types

### ShipmentComment

Code: `als.ecommerce.event.types.shipment-comment`

| **Attribute** | **Type** | **Is Required** | **Description** |
| --- | --- | --- | --- |
| parent_id | Int | true | ID of the shipment. |
| comment | String | true | Comment that will be seen by the client in the store (Order details view). |
| is_customer_notified | Boolean | true | Defines if customer should be notified of new shipment comment. |
| is_visible_on_front | Boolean | true | Defines if shipment comment should be visible to the customer. |

### ShippingItem

Code: `als.ecommerce.event.types.shipping-item`

Order item that is included in the specified shipment.

| **Attribute** | **Type** | **Is Required** | **Description** |
| --- | --- | --- | --- |
| order_item_id | Int | true | Reference to the order item that should be included in the shipment. |
| qty | Int | true | Order item quantity in the shipment. |

### ShipmentCommentCreationInterface

Code: `als.ecommerce.event.types.shipment-comment-creation-interface`

| **Attribute** | **Type** | **Is Required** | **Description** |
| --- | --- | --- | --- |
| comment | String | true | Comment related to the shipment. |
| is_visible_on_front | Boolean | false | Defines if shipment comment should be visible to the customer. |

### ShipmentTrackCreationInterface

Code: `als.ecommerce.event.types.shipment-track-creation-interface`

| **Attribute** | **Type** | **Is Required** | **Description** |
| --- | --- | --- | --- |
| track_number | String | true | Shipment tracking number. |
| title | String | true | Tracking title |
| carrier_code | String | true | Carrier code that should be used for delivering a shipment. |

---

*This page corresponds to Confluence page ID: 4607934466* 