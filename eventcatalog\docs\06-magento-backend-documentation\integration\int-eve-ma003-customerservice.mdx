---
id: int-eve-ma003-customerservice
title: INT-EVE-MA003 Customer Service
slug: /docs/06-magento-backend-documentation/integration/int-eve-ma003-customerservice
summary: Customer Service defines a set of events that are handled by Customer related system API and provides an ability to handle events of new customer registration and management of existing entities in the store.
owners:
  - euvic
---

# Change History

| **Date** | **Author** | **Description of Change** |
| --- | --- | --- |
| 5/9/2025 | @<PERSON><PERSON><PERSON> | Initial version |
|   |   |   |

# Introduction

Customer Service defines a set of events that are handled by Customer related system API and provides an ability to handle events of new customer registration and management of existing entities in the store.

# Related Tasks

1. https://fwc-commerce.atlassian.net/browse/ALS-140

# Events

## Customer Created

Event triggered when a new customer has been registered in the system.

**Event code:** `CustomerCreated`.

**Event type:** `custom`.

**Event producer:** E-commerce.

**Body schema:** `application/json`

**Body:**

```json
{
    "entity_id": 22,
    "group_id": 1,
    "default_shipping": "5",
    "email": "<EMAIL>",
    "firstname": "<PERSON><PERSON><PERSON>",
    "lastname": "Deyneko",
    "dob": "01-01-1995",
    "gender": 1,
    "store_id": 1,
    "website_id": 1,
    "addresses": [
        {
            "id": 4,
            "customer_id": 22,
            "region": {
                "region_code": "FL",
                "region": "Florida",
                "region_id": 18
            },
            "region_id": 18,
            "country_id": "US",
            "street": [
                "Miami street"
            ],
            "telephone": "987654321",
            "postcode": "09876",
            "city": "Miami",
            "firstname": "Vlad",
            "lastname": "Deyneko"
        },
        {
            "id": 5,
            "customer_id": 22,
            "region": {
                "region_code": "FL",
                "region": "Florida",
                "region_id": 18
            },
            "region_id": 18,
            "country_id": "US",
            "street": [
                "Miami street"
            ],
            "telephone": "987654321",
            "postcode": "09876",
            "city": "Miami",
            "firstname": "Vlad",
            "lastname": "Deyneko",
            "default_shipping": true
        }
    ]
}
```

## Customer Updated

Event triggered when existing customer has been updated. Outbound entity provides all of the customer attribute values, both modified and unmodified ones. In general, document structure is the same as in the case of `CustomerCreated` event.

**Event code:** `CustomerUpdated`.

**Event type:** `custom`.

**Event producer:** E-commerce.

**Body schema:** `application/json`

**Body:**

```json
{
    "entity_id": 22,
    "group_id": 1,
    "default_shipping": "5",
    "email": "<EMAIL>",
    "firstname": "Vladyslav - updated data",
    "lastname": "Deyneko - updated",
    "dob": "01-01-1995",
    "gender": 1,
    "store_id": 1,
    "website_id": 1,
    "addresses": [
        {
            "id": 4,
            "customer_id": 22,
            "region": {
                "region_code": "FL",
                "region": "Florida",
                "region_id": 18
            },
            "region_id": 18,
            "country_id": "US",
            "street": [
                "Miami street"
            ],
            "telephone": "987654321",
            "postcode": "09876",
            "city": "Miami",
            "firstname": "Vlad",
            "lastname": "Deyneko"
        },
        {
            "id": 5,
            "customer_id": 22,
            "region": {
                "region_code": "FL",
                "region": "Florida",
                "region_id": 18
            },
            "region_id": 18,
            "country_id": "US",
            "street": [
                "Miami street"
            ],
            "telephone": "987654321",
            "postcode": "09876",
            "city": "Miami",
            "firstname": "Vlad",
            "lastname": "Deyneko",
            "default_shipping": true
        }
    ]
}
```

**Types:**

| **Attribute** | **Type** | **Is Required** | **Description** |
| --- | --- | --- | --- |
| customer | Customer | true | Customer object with updated properties. |

## Customer Deleted

Event triggered when customer has been deleted from the system.

**Event code:** `CustomerDeleted`.

**Event type:** `custom`.

**Event producer:** E-commerce.

**Body schema:** `application/json`

**Body:**

```json
{
    "entity_id": 22
}
```

# Types

## Customer

| **Attribute** | **Type** | **Is Required** | **Description** |
| --- | --- | --- | --- |
| entity_id | Int | true | Internal ID of the customer. |
| email | String | true | Customer email. |
| firstname | String | true | Customer first name. |
| lastname | String | true | Customer last name. |
| store_id | Int | - | Assigned store ID. |
| website_id | Int | - | Assigned website ID. |
| created_at | String | - | Customer creation date. |
| updated_at | String | - | Customer last update date. |
| dob | String | - | Customer date of birth. |
| gender | Int | - | Customer gender: 1 - Male 2 - Female 3 - Not Specified |
| created_in | String | - | Customer assigned store code when entity was created. |
| group_id | Int | - | Customer group ID. |
| default_shipping | Int | - | ID of the default shipping address. |
| addresses | `CustomerAddress[]` | - | List of assigned customer addresses. |

## CustomerAddress

## Region

---

*This page corresponds to Confluence page ID: 4609048623* 