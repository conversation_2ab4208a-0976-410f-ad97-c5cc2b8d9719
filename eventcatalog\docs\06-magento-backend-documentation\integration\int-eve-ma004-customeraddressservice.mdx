---
id: int-eve-ma004-customeraddressservice
title: INT-EVE-MA004 Customer Address Service
slug: /docs/06-magento-backend-documentation/integration/int-eve-ma004-customeraddressservice
summary: Customer Address Service defines a set of events that are handled by Customer related system API and propagates events of new customer address creation and management of existing entities in the store.
owners:
  - euvic
---

# Change History

| **Date** | **Author** | **Description of Change** |
| --- | --- | --- |
| 5/9/2025 | @<PERSON><PERSON><PERSON> | Initial version |
|   |   |   |

# Introduction

Customer Address Service defines a set of events that are handled by Customer related system API and propagates events of new customer address creation and management of existing entities in the store.

# Related Tasks

1. https://fwc-commerce.atlassian.net/browse/ALS-141

# Events

## Address Created

Event triggered when a new address has been added in the system by the customer or system administrator.

**Event code:** `CustomerAddressCreated`.

**Event type:** `custom`.

**Event producer:** E-commerce.

**Body schema:** `application/json`

**Body:**

```json
{
    "id": 4,
    "customer_id": 22,
    "region": {
        "region_code": "FL",
        "region": "Florida",
        "region_id": 18
    },
    "region_id": 18,
    "country_id": "US",
    "street": [
        "Miami street"
    ],
    "telephone": "987654321",
    "postcode": "09876",
    "city": "Miami",
    "firstname": "Vlad",
    "lastname": "Deyneko"
}
```

## Address Updated

Event triggered when an existing address has been modified in the system by the customer or system administrator.

**Event code:** `CustomerAddressCreated`.

**Event type:** `custom`.

**Event producer:** E-commerce.

**Body schema:** `application/json`

**Body:**

```json
{
    "id": 4,
    "customer_id": 22,
    "region": {
        "region_code": "FL",
        "region": "Florida",
        "region_id": 18
    },
    "region_id": 18,
    "country_id": "US",
    "street": [
        "Ocean Drive"
    ],
    "telephone": "987654321",
    "postcode": "09876",
    "city": "Vice City",
    "firstname": "Tommy",
    "lastname": "Verceti"
}
```

## Address Deleted

Event triggered when an existing address has been removed from the system by the customer or system administrator.

**Event code:** `CustomerAddressCreated`.

**Event type:** `custom`.

**Event producer:** E-commerce.

**Body schema:** `application/json`

**Body:**

```json
{
    "entity_id": 22
}
```

# Types

## CustomerAddress

Code: `als.ecommerce.event.types.customer-address`

| **Attribute** | **Type** | **Is Required** | **Description** |
| --- | --- | --- | --- |
| id | Int | true | Internal ID of the customer. |
| customer_id | Int | true | ID of the Customer. |
| region | `Region` | true | Region or State. |
| region_id | Int | true | ID of the region. |
| country_id | String | - | Assigned country code defined by the `ISO 3166-1` standard. |
| street | String\[\] | - | Street. |
| telephone | String | - | Phone number. |
| postcode | String | - | Postcode. |
| city | String | - | City. |
| firstname | String | - | First name assigned to address. |
| lastname | String | - | Last name assigned to address. |
| default_shipping | Boolean | - | If this address is default shipping address. |
| default_billing | Boolean | - | If this address is default billing address. |

## Region

Code: `als.ecommerce.event.types.region`

| **Attribute** | **Type** | **Is Required** | **Description** |
| --- | --- | --- | --- |
| region_code | String | true | Assigned region code defined by the `ISO 3166-2` standard. |
| region | String | true | Region name. |
| region_id | Int | true | Internal region ID. |

---

*This page corresponds to Confluence page ID: 4609933355* 