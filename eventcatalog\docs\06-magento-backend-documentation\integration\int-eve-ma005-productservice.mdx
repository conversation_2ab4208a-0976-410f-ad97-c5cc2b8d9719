---
id: int-eve-ma005-productservice
title: INT-EVE-MA005 Product Service
slug: /docs/06-magento-backend-documentation/integration/int-eve-ma005-productservice
summary: Product Service defines a set of events that are handled by Product related system API and propagates events of product catalog items management in the store.
owners:
  - euvic
---

# Change History

| **Date** | **Author** | **Description of Change** |
| --- | --- | --- |
| 5/9/2025 | @<PERSON><PERSON><PERSON> | Initial version |
| 5/12/2025 | @<PERSON><PERSON><PERSON> | Added `ProductUpdated`, `ProductStockUpdated` and `ProductPriceUpdated` event descriptions. |
| 5/13/2025 | @<PERSON><PERSON><PERSON> | Extracted `ProductPriceUpdated` single event to event group. |

# Introduction

Product Service defines a set of events that are handled by Product related system API and propagates events of product catalog items management in the store.

# Related Tasks

1. https://fwc-commerce.atlassian.net/browse/ALS-143

# Events

## Product Created

Event triggered when a new product has been added to the system by a system administrator or external system.

**Endpoint:** `POST /rest/V1/products`

**Event code:** `ProductCreated`

**Event type:** `custom`

**Event producer:** SAP, Supplier, PIM

**Body schema:** `application/json`

**Body:**

```json
{
    "product": {
        "sku": "LG-EBF49827801",
        "name": "LG #EBF49827801 SWITCH ASSEMBLY,LOCKER",
        "price": 60.48,
        "status": "1",
        "visibility": "1",
        "supplier": "LG",
        "type_id": "simple",
        "weight": "150"
    }
}
```

**Types:**

| **Attribute** | **Type** | **Is Required** | **Description** |
| --- | --- | --- | --- |
| product | `Product` | true | Product entity. |

**Response:**

* HTTP 200 - Contains created entity.

    * ```json
      {
          "product": {
              "sku": "LG-EBF49827801",
              "name": "LG #EBF49827801 SWITCH ASSEMBLY,LOCKER",
              "price": 60.48,
              "status": "1",
              "visibility": "1",
              "supplier": "LG",
              "type_id": "simple",
              "created_at": "13:00 15-05-2025",
              "updated_at": "13:00 15-05-2025",
              "weight": "150"
          }
      }
      ```
    
* HTTP 400 - Bad Request

    * ```json
      {
        "message": "string",
        "errors": [
          {
            "message": "string",
            "parameters": [
              {
                "resources": "string",
                "fieldName": "string",
                "fieldValue": "string"
              }
            ]
          }
        ],
        "code": 0,
        "parameters": [
          {
            "resources": "string",
            "fieldName": "string",
            "fieldValue": "string"
          }
        ],
        "trace": "string"
      }
      ```
    
* HTTP 401 - Unauthorized
* HTTP 500 - Internal Server Error

Response body structure for HTTP codes 400, 401, 500 is the same.

## Product Updated

Event triggered when a product has been updated in the system.

**Endpoint:** `PUT /rest/V1/products/:sku`

**Event code:** `ProductUpdated`

**Event type:** `custom`

**Event producer:** SAP, DAM, Supplier, PIM

**Body schema:** `application/json`

**Body:**

```json
{
    "product": {
        "name": "LG #EBF49827801 SWITCH ASSEMBLY,LOCKER - updated data",
        "status": "1",
        "visibility": "1",
        "supplier": "LG"
    }
}
```

**Types:**

| **Attribute** | **Area** | **Type** | **Is Required** | **Description** |
| --- | --- | --- | --- | --- |
| sku | Request param | String | true | Product SKU, which is to be modified. |
| product | Request body | `Product[]` | true | Updated product entity. |

**Response:**

* HTTP 200 - Contains updated entity with all available attributes included (not only modified ones).

    * ```json
      {
          "product": {
              "sku": "LG-EBF49827801",
              "name": "LG #EBF49827801 SWITCH ASSEMBLY,LOCKER - updated data",
              "price": 59.95,
              "status": "1",
              "visibility": "1",
              "supplier": "LG",
              "type_id": "simple",
              "created_at": "13:00 15-05-2025",
              "updated_at": "13:15 15-05-2025",
              "weight": "150"
          }
      }
      ```
    
* HTTP 400 - Bad Request

    * ```json
      {
        "message": "string",
        "errors": [
          {
            "message": "string",
            "parameters": [
              {
                "resources": "string",
                "fieldName": "string",
                "fieldValue": "string"
              }
            ]
          }
        ],
        "code": 0,
        "parameters": [
          {
            "resources": "string",
            "fieldName": "string",
            "fieldValue": "string"
          }
        ],
        "trace": "string"
      }
      ```
    
* HTTP 401 - Unauthorized
* HTTP 500 - Internal Server Error

Response body structure for HTTP codes 400, 401, 500 is the same.

## Product Stock Updated

Event triggered when product stock has been updated.

**Endpoint:** `POST /rest/V1/inventory/source-items`

**Event code:** `ProductStockUpdated`

**Event type:** `custom`

**Event producer:** SAP, Supplier

**Body schema:** `application/json`

**Body:**

```json
{
    "sourceItems": [
        {
            "sku": "product-sku-a",
            "source_code": "primary-stock",
            "quantity": 5,
            "status": 0
        },
        {
            "sku": "product-stock-b",
            "source_code": "secondary-stock",
            "quantity": 999,
            "status": 1
        }
    ]
}
```

**Types:**

| **Attribute** | **Type** | **Is Required** | **Description** |
| --- | --- | --- | --- |
| sourceItems | `SourceItem[]` | true | List of updated stock items. |

**Response:**

* HTTP 200 - Contains updated entity with all available attributes included (not only modified ones).

    * ```json
      {
          "items": [
              {
                  "sku": "product-sku-a",
                  "source_code": "primary-stock",
                  "quantity": 5,
                  "status": 0,
                  "extension_attributes": {}
              },
              {
                  "sku": "product-stock-b",
                  "source_code": "secondary-stock",
                  "quantity": 999,
                  "status": 1,
                  "extension_attributes": {}
              },
          ],
          "total_count": 2
      }
      ```
    
* HTTP 400 - Bad Request

    * ```json
      {
        "message": "string",
        "errors": [
          {
            "message": "string",
            "parameters": [
              {
                "resources": "string",
                "fieldName": "string",
                "fieldValue": "string"
              }
            ]
          }
        ],
        "code": 0,
        "parameters": [
          {
            "resources": "string",
            "fieldName": "string",
            "fieldValue": "string"
          }
        ],
        "trace": "string"
      }
      ```
    
* HTTP 401 - Unauthorized
* HTTP 500 - Internal Server Error

Response body structure for HTTP codes 400, 401, 500 is the same.

## Product Price Updated

Events group for price updates with three types:
- **Product Fixed Price** (`ProductFixedPriceUpdated`)
- **Product Special Price** (`ProductSpecialPriceUpdated`) 
- **Product Tier Price** (`ProductTierPriceUpdated`)

# Types

## Product

| **Attribute** | **Type** | **Is Required** | **Description** |
| --- | --- | --- | --- |
| sku | String | true | Product unique SKU. |
| name | String | true | Product commercial name. |
| price | Decimal | true | Product price. |
| status | Int | true | Product status (0-Disabled, 1-Enabled). |
| visibility | Int | true | Product visibility mode. |
| supplier | String | - | Product supplier. |
| type_id | Int | - | Product type. |
| created_at | String | - | Creation timestamp (ISO-8601). |
| updated_at | String | - | Last modification timestamp (ISO-8601). |
| weight | Float | - | Product weight. |

### Product Fixed Price

**Endpoint:** `POST /rest/V1/products/base-prices`

**Event code:** `ProductFixedPriceUpdated`

**Event type:** `custom`

**Event producer:** SAP, Supplier

**Body schema:** `application/json`

**Body:**

```json
{
    "prices": [
        {
            "price": 45.99,
            "store_id": 2,
            "sku": "test-sku-a"
        },
        {
            "price": 59.99,
            "store_id": 1,
            "sku": "test-sku-b"
        }
    ]
}
```

**Types:**

| **Attribute** | **Type** | **Is Required** | **Description** |
| --- | --- | --- | --- |
| prices | `ProductPriceFixed[]` | true | List of fixed prices. |

**Response:**

* HTTP 200 - Contains a list of invalid prices or price handler errors. Empty list means successful import of all provided price entries.

    * ```json
      [
          {
              "message": "string",
              "parameters": [
                  "string"
              ],
              "extension_attributes": {}
          }
      ]
      ```
    
* HTTP 400 - Bad Request

    * ```json
      {
        "message": "string",
        "errors": [
          {
            "message": "string",
            "parameters": [
              {
                "resources": "string",
                "fieldName": "string",
                "fieldValue": "string"
              }
            ]
          }
        ],
        "code": 0,
        "parameters": [
          {
            "resources": "string",
            "fieldName": "string",
            "fieldValue": "string"
          }
        ],
        "trace": "string"
      }
      ```
    
* HTTP 401 - Unauthorized
* HTTP 500 - Internal Server Error

Response body structure for HTTP codes 400, 401, 500 is the same.

### Product Special Price

**Endpoint:** `POST /rest/V1/products/special-prices`

**Event code:** `ProductSpecialPriceUpdated`

**Event type:** `custom`

**Event producer:** SAP, Supplier

**Body schema:** `application/json`

**Body:**

```json
{
    "prices": [
        {
            "price": 14.99,
            "store_id": 1,
            "price_from": "01:00 12-05-2025",
            "price_to": "21:00 12-05-2025",
            "sku": "test-sku-c"
        }
    ]
}
```

**Types:**

| **Attribute** | **Type** | **Is Required** | **Description** |
| --- | --- | --- | --- |
| prices | `ProductPriceSpecial[]` | true | List of special prices. |

**Response:**

* HTTP 200 - Contains a list of invalid prices or price handler errors. Empty list means successful import of all provided price entries.

    * ```json
      [
          {
              "message": "string",
              "parameters": [
                  "string"
              ],
              "extension_attributes": {}
          }
      ]
      ```
    
* HTTP 400 - Bad Request

    * ```json
      {
        "message": "string",
        "errors": [
          {
            "message": "string",
            "parameters": [
              {
                "resources": "string",
                "fieldName": "string",
                "fieldValue": "string"
              }
            ]
          }
        ],
        "code": 0,
        "parameters": [
          {
            "resources": "string",
            "fieldName": "string",
            "fieldValue": "string"
          }
        ],
        "trace": "string"
      }
      ```
    
* HTTP 401 - Unauthorized
* HTTP 500 - Internal Server Error

Response body structure for HTTP codes 400, 401, 500 is the same.

### Product Tier Price

**Endpoint:** `POST /rest/V1/products/tier-prices`

**Event code:** `ProductTierPriceUpdated`

**Event type:** `custom`

**Event producer:** SAP, Supplier

**Body schema:** `application/json`

**Body:**

```json
{
    "prices": [
        {
            "price": 19.99,
            "price_type": "fixed",
            "website_id": 1,
            "customer_group_id": 0,
            "qty": 10,
            "sku": "test-sku-c"
        },
        {
            "price": 5,
            "price_type": "discount",
            "website_id": 1,
            "customer_group_id": 0,
            "qty": 5,
            "sku": "test-sku-d"
        }
    ]
}
```

**Types:**

| **Attribute** | **Type** | **Is Required** | **Description** |
| --- | --- | --- | --- |
| prices | `ProductPriceTier[]` | true | List of tier prices. |

**Response:**

* HTTP 200 - Contains a list of invalid prices or price handler errors. Empty list means successful import of all provided price entries.

    * ```json
      [
          {
              "message": "string",
              "parameters": [
                  "string"
              ],
              "extension_attributes": {}
          }
      ]
      ```
    
* HTTP 400 - Bad Request

    * ```json
      {
        "message": "string",
        "errors": [
          {
            "message": "string",
            "parameters": [
              {
                "resources": "string",
                "fieldName": "string",
                "fieldValue": "string"
              }
            ]
          }
        ],
        "code": 0,
        "parameters": [
          {
            "resources": "string",
            "fieldName": "string",
            "fieldValue": "string"
          }
        ],
        "trace": "string"
      }
      ```
    
* HTTP 401 - Unauthorized
* HTTP 500 - Internal Server Error

Response body structure for HTTP codes 400, 401, 500 is the same.

---

*This page corresponds to Confluence page ID: 4609867819* 