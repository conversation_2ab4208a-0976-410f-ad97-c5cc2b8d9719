---
id: int-eve-ma006-cartservice
title: INT-EVE-MA006 Cart Service
slug: /docs/06-magento-backend-documentation/integration/int-eve-ma006-cartservice
summary: Cart Service defines a set of events that are handled by Cart related system API and propagates events of shopping cart management in the store.
owners:
  - euvic
---

# Change History

| **Date** | **Author** | **Description of Change** |
| --- | --- | --- |
| 5/9/2025 | @<PERSON><PERSON><PERSON> | Initial version |
|   |   |   |

# Introduction

Cart Service defines a set of events that are handled by Cart related system API and propagates events of shopping cart management in the store.

# Related Tasks

1. https://fwc-commerce.atlassian.net/browse/ALS-144

# Events

## Cart Created

Event triggered when a new cart has been created in the system by a guest customer or registered user.

**Event code:** `CartCreated`
**Event type:** `custom`
**Event producer:** E-commerce
**Body schema:** `application/json`

```json
{
    "id": 22,
    "created_at": "2025-05-09 13:00:00",
    "updated_at": "2025-05-09 13:00:00",
    "is_active": true,
    "is_virtual": false,
    "items": [],
    "items_count": 0,
    "items_qty": 0,
    "customer": {
        "id": 1,
        "group_id": 1,
        "email": "<EMAIL>",
        "firstname": "John",
        "lastname": "Doe"
    },
    "billing_address": {
        "id": 4,
        "customer_id": 1,
        "region": {
            "region_code": "FL",
            "region": "Florida",
            "region_id": 18
        },
        "region_id": 18,
        "country_id": "US",
        "street": ["Miami street"],
        "telephone": "*********",
        "postcode": "09876",
        "city": "Miami",
        "firstname": "John",
        "lastname": "Doe"
    },
    "reserved_order_id": "*********",
    "orig_order_id": 0,
    "currency": {
        "global_currency_code": "USD",
        "base_currency_code": "USD",
        "store_currency_code": "USD",
        "quote_currency_code": "USD",
        "store_to_base_rate": 1,
        "store_to_quote_rate": 1,
        "base_to_global_rate": 1,
        "base_to_quote_rate": 1
    },
    "customer_is_guest": false,
    "customer_note_notify": true,
    "customer_tax_class_id": 3,
    "store_id": 1,
    "extension_attributes": {
        "shipping_assignments": [
            {
                "shipping": {
                    "address": {
                        "id": 5,
                        "customer_id": 1,
                        "region": {
                            "region_code": "FL",
                            "region": "Florida",
                            "region_id": 18
                        },
                        "region_id": 18,
                        "country_id": "US",
                        "street": ["Miami street"],
                        "telephone": "*********",
                        "postcode": "09876",
                        "city": "Miami",
                        "firstname": "John",
                        "lastname": "Doe",
                        "default_shipping": true
                    },
                    "method": "flatrate_flatrate"
                },
                "items": []
            }
        ]
    }
}
```

## Cart Updated

Event triggered when an existing cart has been modified in the system.

**Event code:** `CartUpdated`
**Event type:** `custom`
**Event producer:** E-commerce
**Body schema:** `application/json`

Similar structure to CartCreated with updated fields.

## Cart Item Added

Event triggered when a new item has been added to the cart.

**Event code:** `CartItemAdded`
**Event type:** `custom`
**Event producer:** E-commerce
**Body schema:** `application/json`

```json
{
    "item_id": 15,
    "sku": "test-product-sku",
    "qty": 2,
    "name": "Test Product Name",
    "price": 45.99,
    "product_type": "simple",
    "quote_id": "22",
    "product_option": {
        "extension_attributes": {
            "configurable_item_options": [
                {
                    "option_id": "93",
                    "option_value": 50
                }
            ]
        }
    }
}
```

## Cart Item Updated

Event triggered when an existing cart item has been modified.

**Event code:** `CartItemUpdated`
**Event type:** `custom`
**Event producer:** E-commerce

## Cart Item Removed

Event triggered when an item has been removed from the cart.

**Event code:** `CartItemRemoved`
**Event type:** `custom`
**Event producer:** E-commerce

## Cart Converted to Order

Event triggered when a cart has been successfully converted to an order.

**Event code:** `CartConvertedToOrder`
**Event type:** `custom`
**Event producer:** E-commerce

# Types

## Cart

| **Attribute** | **Type** | **Is Required** | **Description** |
| --- | --- | --- | --- |
| id | Int | true | Internal cart ID. |
| created_at | String | true | Cart creation timestamp. |
| updated_at | String | true | Last modification timestamp. |
| is_active | Boolean | true | Cart active status. |
| is_virtual | Boolean | - | Virtual cart indicator. |
| items | `CartItem[]` | - | List of cart items. |
| items_count | Int | - | Number of unique items. |
| items_qty | Float | - | Total quantity of items. |
| customer | `Customer` | - | Associated customer. |
| billing_address | `CustomerAddress` | - | Billing address. |
| reserved_order_id | String | - | Reserved order ID. |
| currency | `Currency` | - | Currency information. |
| customer_is_guest | Boolean | - | Guest customer indicator. |
| store_id | Int | - | Store ID. |

## CartItem

| **Attribute** | **Type** | **Is Required** | **Description** |
| --- | --- | --- | --- |
| item_id | Int | true | Cart item ID. |
| sku | String | true | Product SKU. |
| qty | Float | true | Item quantity. |
| name | String | true | Product name. |
| price | Decimal | true | Item price. |
| product_type | String | - | Product type. |
| quote_id | String | - | Associated cart ID. |

---

*This page corresponds to Confluence page ID: 4609736706* 