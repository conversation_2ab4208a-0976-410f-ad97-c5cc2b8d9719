---
id: int-eve-ma007-returnservice
title: INT-EVE-MA007 Return Service
sidebar_label: INT-EVE-MA007 Return Service
slug: /docs/06-magento-backend-documentation/integration/int-eve-ma007-returnservice
summary: 'INT-EVE-MA007 Return Service integration interface specification for handling return and refund operations and data exchange between Magento and Evolution systems'
owners:
    - euvic
---

# Change History

| **Date** | **Author** | **Description of Change** |
| --- | --- | --- |
| 5/9/2025 | @<PERSON><PERSON> | Initial version |
|   |   |  |

# Related Tasks

1. https://fwc-commerce.atlassian.net/browse/ALS-138

# Events

## Return Created

| **Event** | **Producer** | **Consumer** |
| --- | --- | --- |
| Return Created | E-Commerce | SAP, Supplier |

Event triggered when new return has been created. Produces CreditMemo type.

**Body schema:** `application/json`

### Body

```json
{
  "adjustment": 0,
  "adjustment_negative": 0,
  "adjustment_positive": 0,
  "base_adjustment": 0,
  "base_adjustment_negative": 0,
  "base_adjustment_positive": 0,
  "base_currency_code": "string",
  "base_discount_amount": 0,
  "base_grand_total": 0,
  "base_discount_tax_compensation_amount": 0,
  "base_shipping_amount": 0,
  "base_shipping_discount_tax_compensation_amnt": 0,
  "base_shipping_incl_tax": 0,
  "base_shipping_tax_amount": 0,
  "base_subtotal": 0,
  "base_subtotal_incl_tax": 0,
  "base_tax_amount": 0,
  "base_to_global_rate": 0,
  "base_to_order_rate": 0,
  "billing_address_id": 0,
  "created_at": "string",
  "creditmemo_status": 0,
  "discount_amount": 0,
  "discount_description": "string",
  "email_sent": 0,
  "entity_id": 0,
  "global_currency_code": "string",
  "grand_total": 0,
  "discount_tax_compensation_amount": 0,
  "increment_id": "string",
  "invoice_id": 0,
  "order_currency_code": "string",
  "order_id": 0,
  "shipping_address_id": 0,
  "shipping_amount": 0,
  "shipping_discount_tax_compensation_amount": 0,
  "shipping_incl_tax": 0,
  "shipping_tax_amount": 0,
  "state": 0,
  "store_currency_code": "string",
  "store_id": 0,
  "store_to_base_rate": 0,
  "store_to_order_rate": 0,
  "subtotal": 0,
  "subtotal_incl_tax": 0,
  "tax_amount": 0,
  "transaction_id": "string",
  "updated_at": "string",
  "items": [
    {
      "additional_data": "string",
      "base_cost": 0,
      "base_discount_amount": 0,
      "base_discount_tax_compensation_amount": 0,
      "base_price": 0,
      "base_price_incl_tax": 0,
      "base_row_total": 0,
      "base_row_total_incl_tax": 0,
      "base_tax_amount": 0,
      "base_weee_tax_applied_amount": 0,
      "base_weee_tax_applied_row_amnt": 0,
      "base_weee_tax_disposition": 0,
      "base_weee_tax_row_disposition": 0,
      "description": "string",
      "discount_amount": 0,
      "entity_id": 0,
      "discount_tax_compensation_amount": 0,
      "name": "string",
      "order_item_id": 0,
      "parent_id": 0,
      "price": 0,
      "price_incl_tax": 0,
      "product_id": 0,
      "qty": 0,
      "row_total": 0,
      "row_total_incl_tax": 0,
      "sku": "string",
      "tax_amount": 0,
      "weee_tax_applied": "string",
      "weee_tax_applied_amount": 0,
      "weee_tax_applied_row_amount": 0,
      "weee_tax_disposition": 0,
      "weee_tax_row_disposition": 0,
      "extension_attributes": {}
    }
  ],
  "comments": [
    {
      "comment": "string",
      "created_at": "string",
      "entity_id": 0,
      "is_customer_notified": 0,
      "is_visible_on_front": 0,
      "parent_id": 0,
      "extension_attributes": {}
    }
  ]
}
```

# Types

## CreditMemo

| **Attribute** | **Type** | **Is Required** | **Description** |
| --- | --- | --- | --- |
| entity_id | Int | true | Credit memo ID. |
| order_id | Int | true | Credit memo order ID. |
| items | CreditMemoItem\[\] | true | Array of credit memo items. |
| adjustment | Float |  | Credit memo adjustment. |
| adjustment_negative | Float |  | Credit memo negative adjustment. |
| adjustment_positive | Float |  | Credit memo positive adjustment. |
| base_adjustment | Float |  | Credit memo base adjustment. |
| base_adjustment_negative | Float |  | Credit memo negative base adjustment. |
| base_adjustment_positive | Float |  | Credit memo positive base adjustment. |
| base_currency_code | String |  | Credit memo base currency code. |
| base_discount_amount | Float |  | Credit memo base discount amount. |
| base_discount_tax_compensation_amount | Float |  | Credit memo base discount tax compensation amount. |
| base_grand_total | Float |  | Credit memo base grand total. |
| base_shipping_amount | Float |  | Credit memo base shipping amount. |
| base_shipping_discount_tax_compensation_amnt | Float |  | Credit memo base shipping discount tax compensation amount. |
| base_shipping_incl_tax | Float |  | Credit memo base shipping including tax. |
| base_shipping_tax_amount | Float |  | Credit memo base shipping tax amount. |
| base_subtotal | Float |  | Credit memo base subtotal. |
| base_subtotal_incl_tax | Float |  | Credit memo base subtotal including tax. |
| base_tax_amount | Float |  | Credit memo base tax amount. |
| base_to_global_rate | Float |  | Credit memo base-to-global rate. |
| base_to_order_rate | Float |  | Credit memo base-to-order rate. |
| billing_address_id | Int |  | Credit memo billing address ID. |
| comments | CreditMemoComment\[\] |  | Array of any credit memo comments. Otherwise, null. |
| created_at | String |  | Credit memo created-at timestamp. |
| creditm_emo_status | Int |  | Credit memo status. |
| discount_amount | Float |  | Credit memo discount amount. |
| discount_description | String |  | Credit memo discount description. |
| discount_tax_compensation_amount | Float |  | Credit memo discount tax compensation amount. |
| email_sent | Int |  | Credit memo email sent flag value. |
| global_currency_code | String |  | Credit memo global currency code. |
| grand_total | Float |  | Credit memo grand total. |
| increment_id | String |  | Credit memo increment ID. |
| invoice_id | Int |  | Credit memo invoice ID. |
| order_currency_code | String |  | Credit memo order currency code. |
| shipping_address_id | Int |  | Credit memo shipping address ID. |
| shipping_amount | Float |  | Credit memo shipping amount. |
| shipping_discount_tax_compensation_amount | Float |  | Credit memo shipping discount tax compensation amount. |
| shipping_incl_tax | Float |  | Credit memo shipping including tax. |
| shipping_tax_amount | Float |  | Credit memo shipping tax amount. |
| state | Int |  | Credit memo state. |
| store_currency_code | String |  | Credit memo store currency code. |
| store_id | Int |  | Credit memo store ID. |
| store_to_base_rate | Float |  | Credit memo store-to-base rate. |
| store_to_order_rate | Float |  | Credit memo store-to-order rate. |
| subtotal | Float |  | Credit memo subtotal. |
| subtotal_incl_tax | Float |  | Credit memo subtotal including tax. |
| tax_amount | Float |  | Credit memo tax amount. |
| transaction_id | String |  | Credit memo transaction ID. |
| updated_at | String |  | Credit memo updated-at timestamp. |

## CreditMemoItem

| **Attribute** | **Type** | **Is Required** | **Description** |
| --- | --- | --- | --- |
| entity_id | Int | true | Credit memo item ID. |
| order_item_id | Int | true | Order item ID. |
| parent_id | Int | true | Parent ID (CreditMemo ID). |
| additional_data | String |  | Additional data. |
| base_cost | Float | true | The base cost for a credit memo item. |
| base_discount_amount | Float |  | The base discount amount for a credit memo item. |
| base_discount_tax_compensation_amount | Float |  | The base discount tax compensation amount for a credit memo item. |
| base_price | Float | true | The base price for a credit memo item. |
| base_price_incl_tax | Float |  | Base price including tax. |
| base_row_total | Float |  | Base row total. |
| base_row_total_incl_tax | Float |  | Base row total including tax. |
| base_tax_amount | Float |  | Base tax amount. |
| base_weee_tax_applied_amount | Float |  | Base WEEE tax applied amount. |
| base_weee_tax_applied_row_amnt | Float |  | Base WEEE tax applied row amount. |
| base_weee_tax_disposition | Float |  | Base WEEE tax disposition. |
| base_weee_tax_row_disposition | Float |  | Base WEEE tax row disposition. |
| description | String |  | Description. |
| discount_amount | Float |  | Discount amount. |
| discount_tax_compensation_amount | Float |  | Discount tax compensation amount. |
| name | String |  | Name. |
| price | Float |  | Price. |
| price_incl_tax | Float |  | Price including tax. |
| product_id | Int |  | Product ID. |
| qty | Float | true | Quantity. |
| row_total | Float |  | Row total. |
| row_total_incl_tax | Float |  | Row total including tax. |
| sku | String |  | SKU. |
| tax_amount | Float |  | Tax amount. |
| weee_tax_applied | String |  | WEEE tax applied. |
| weee_tax_applied_amount | Float |  | WEEE tax applied amount. |
| weee_tax_applied_row_amount | Float |  | WEEE tax applied row amount. |
| weee_tax_disposition | Float |  | WEEE tax disposition. |
| weee_tax_row_disposition | Float |  | WEEE tax row disposition. |

## CreditMemoComment

| **Attribute** | **Type** | **Is Required** | **Description** |
| --- | --- | --- | --- |
| entity_id | Int | true | Credit memo comment ID. |
| parent_id | Int | true | Parent ID (CreditMemo ID). |
| comment | String | true | Comment. |
| created_at | String |  | Created-at timestamp. |
| is_customer_notified | Int | true | Is-customer-notified flag value. |
| is_visible_on_front | Int | true | Is-visible-on-storefront flag value. |

---

*This page corresponds to Confluence page ID: 4611506177* 