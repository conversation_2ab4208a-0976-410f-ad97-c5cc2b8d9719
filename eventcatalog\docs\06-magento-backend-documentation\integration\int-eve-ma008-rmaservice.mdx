---
id: int-eve-ma008-rmaservice
title: INT-EVE-MA008 RMA Service
sidebar_label: INT-EVE-MA008 RMA Service
slug: /docs/06-magento-backend-documentation/integration/int-eve-ma008-rmaservice
summary: 'INT-EVE-MA008 RMA Service integration interface specification for handling Return Merchandise Authorization operations and data exchange between Magento and Evolution systems'
owners:
    - euvic
---

# Change History

| **Date** | **Author** | **Description of Change** |
| --- | --- | --- |
| 5/20/2025 | @<PERSON><PERSON><PERSON> | Initial version |
|  |  |  |

# Introduction

RMA Service defines a set of events that are handled by RMA related system API and propagates events of return request management in the store.

RMA flow mainly operates on 2 entities:

1. **Return request/ticket/RMA** - a request initiated by a customer in E-commerce platform or one of the marketplaces (integrated through ChannelAdvisor platform).
2. **Return document** - entity generated after the RMA is approved in E-commerce platform. This document is sent to the customer (e.g. by email).
3. **Return order** - entity generated after the RMA is approved in E-commerce platform. This document is propagated once and main target consumer is SAP system.

# Related Tasks

1. https://fwc-commerce.atlassian.net/browse/ALS-142

# Events

## Rma Created

Request allows to create a new RMA for existing order (if an order has eligible items to be returned).

**Endpoint:** `POST /rest/default/V1/return-request`

**Event code:** `RmaRequestInitiated`

**Event type:** `custom`.

**Event producer:** E-commerce, Supplier.

**Body schema:** `application/json`

**Body:**

```json
{
    "rmaRequest": {
        "status_id": "new|pending_approval|approved|rejected|fraud",
        "date_requested": "13:17 19-05-2025",
        "initiator_type": "buyer|seller",
        "source_type": "ecommerce|marketplace",
        "marketplace_id": "amazon|ebay|etc",
        "order": {
            "increment_id": "#12345678",
            "type": "regular"
        },
        "guest": {
            "email": "<EMAIL>",
            "firstname": "Vlad",
            "lastname": "Deyneko"
        },
        "items": [
            {
                "order_item_id": 1,
                "qty_requested": 2,
                "sku": "test-sku-a",
                "condition_code": "",
                "reason_code": "",
                "resolution_code": ""
            }
        ],
        "initial_message": "Seller returning due to incorrect sized shoe in box."
    }
}
```

**Types:**

| **Attribute** | **Type** | **Is Required** | **Description** |
| --- | --- | --- | --- |
| rmaRequest | `RmaRequest` | true | RMA request entity. |

**Response:**

* HTTP 200 - Created RMA entity.

    * ```json
      {
          "increment_id": "string",
          "status_id": "new",
          "date_requested": "13:17 19-05-2025",
          "initiator_type": "buyer|seller",
          "source_type": "ecommerce|marketplace",
          "marketplace_id": "amazon|ebay|etc",
          "order": {
              "increment_id": "#12345678",
              "type": "regular"
          },
          "guest": {
              "email": "<EMAIL>",
              "firstname": "Vlad",
              "lastname": "Deyneko"
          },
          "items": [
              {
                  "order_item_id": 1,
                  "qty_requested": 2,
                  "sku": "test-sku-a",
                  "condition_code": "",
                  "reason_code": "",
                  "resolution_code": ""
              }
          ],
          "initial_message": "Seller returning due to incorrect sized shoe in box."
      }
      ```
    
* HTTP 400 - Bad Request

    * ```json
      {
        "message": "string",
        "errors": [
          {
            "message": "string",
            "parameters": [
              {
                "resources": "string",
                "fieldName": "string",
                "fieldValue": "string"
              }
            ]
          }
        ],
        "code": 0,
        "parameters": [
          {
            "resources": "string",
            "fieldName": "string",
            "fieldValue": "string"
          }
        ],
        "trace": "string"
      }
      ```
    
* HTTP 401 - Unauthorized
* HTTP 500 - Internal Server Error

Response body structure for HTTP codes 400, 401, 500 is the same.

## Rma Updated

Request allows to update an existing RMA request (if a request is in status `pending`). Approved RMA requests are not available for adjustments.

**Endpoint:** `PUT /rest/default/V1/return-request/:id`

**Event code:** `RmaRequestUpdated`

**Event type:** `custom`.

**Event producer:** E-commerce, Supplier.

**Body schema:** `application/json`

**Body:**

```json
{
    "rmaRequest": {
        "status_id": "pending_approval",
        "date_requested": "13:17 19-05-2025",
        "initiator_type": "seller",
        "source_type": "marketplace",
        "marketplace_id": "amazon",
        "order": {
            "increment_id": "#12345678",
            "type": "regular"
        },
        "guest": {
            "email": "<EMAIL>",
            "firstname": "Vlad",
            "lastname": "Deyneko"
        },
        "items": [
            {
                "order_item_id": 1,
                "qty_requested": 2,
                "sku": "test-sku-a",
                "condition_code": "",
                "reason_code": "",
                "resolution_code": ""
            }
        ],
        "initial_message": "Seller returning due to incorrect sized shoe in box."
    }
}
```

**Types:**

| **Attribute** | **Area** | **Type** | **Is Nullable** | **Is Required** | **Description** |
| --- | --- | --- | --- | --- | --- |
| id | Request param | Int | false | true | ID of the RMA request that should be updated. |
| rmaRequest | Request body | `RmaRequest` | false | true | RMA request entity. |

**Response:**

* HTTP 200 - Created RMA entity.

    * ```json
      {
          "increment_id": "string",
          "status_id": "new",
          "date_requested": "13:17 19-05-2025",
          "initiator_type": "buyer|seller",
          "source_type": "ecommerce|marketplace",
          "marketplace_id": "amazon|ebay|etc",
          "order": {
              "increment_id": "#12345678",
              "type": "regular"
          },
          "guest": {
              "email": "<EMAIL>",
              "firstname": "Vlad",
              "lastname": "Deyneko"
          },
          "items": [
              {
                  "order_item_id": 1,
                  "qty_requested": 2,
                  "sku": "test-sku-a",
                  "condition_code": "",
                  "reason_code": "",
                  "resolution_code": ""
              }
          ],
          "initial_message": "Seller returning due to incorrect sized shoe in box."
      }
      ```
    
* HTTP 400 - Bad Request

    * ```json
      {
        "message": "string",
        "errors": [
          {
            "message": "string",
            "parameters": [
              {
                "resources": "string",
                "fieldName": "string",
                "fieldValue": "string"
              }
            ]
          }
        ],
        "code": 0,
        "parameters": [
          {
            "resources": "string",
            "fieldName": "string",
            "fieldValue": "string"
          }
        ],
        "trace": "string"
      }
      ```
    
* HTTP 401 - Unauthorized
* HTTP 500 - Internal Server Error

Response body structure for HTTP codes 400, 401, 500 is the same.

## Rma Approved

Event is propagated when existing RMA request has been approved.

**Event code:** `RmaRequestApproved`

**Event type:** `custom`.

**Event producer:** E-commerce.

**Body schema:** `application/json`

**Body:**

```json
{
    "increment_id": "string",
    "status_id": "approved",
    "date_requested": "13:17 19-05-2025",
    "initiator_type": "buyer|seller",
    "source_type": "ecommerce|marketplace",
    "marketplace_id": "amazon|ebay|etc",
    "order": {
        "increment_id": "#12345678",
        "type": "regular"
    },
    "guest": {
        "email": "<EMAIL>",
        "firstname": "Vlad",
        "lastname": "Deyneko"
    },
    "items": [
        {
            "order_item_id": 1,
            "qty_requested": 2,
            "sku": "test-sku-a",
            "condition_code": "",
            "reason_code": "",
            "resolution_code": ""
        }
    ],
    "initial_message": "Seller returning due to incorrect sized shoe in box."
}
```

# Types

## RmaRequest

Code: `als.ecommerce.event.types.rma.request`

| **Attribute** | **Type** | **Is Nullable** | **Is Required** | **Description** |
| --- | --- | --- | --- | --- |
| status_id | Int | false | true | Reference to the order item that should be included in the shipment. |
| date_requested | String | true | - | RMA request time and date defined by the `ISO-8601` standard. |
| initiator_type | String | false | true | Defines who initiated the RMA request. Values: buyer seller |
| source_type | Boolean | false | true | Defines source system type where RMA was initiated. RMA requests initiated by the external systems with `source_type` set to `ecommerce` will lead to validation failure. Values: ecommerce marketplace |
| marketplace_id | String | true | false | Defines the ID of the marketplace, if `source_type` is set to `marketplace`. |
| order | `RmaOrder` | false | true | Order related to an RMA request. |
| guest | `RmaCustomer` | true | - | Defines customer data if RMA request has been created by guest. |
| items | `RmaOrderItem[]` | false | true | Ordered items that are subject for a return/refund. |
| initial_message | String | true | - | Initial seller or buyer message added when the RMA has been requested. |

## RmaOrder

Code: `als.ecommerce.event.types.rma.order`

| **Attribute** | **Type** | **Is Nullable** | **Is Required** | **Description** |
| --- | --- | --- | --- | --- |
| entity_id | Int | false | false | Order ID in the E-commerce system. |
| increment_id | String | false | true | Order increment ID. |
| type | String | false | true | Order type: Defines the ID of the marketplace, if `source_type` is set to `marketplace`. |

## RmaCustomer

Code: `als.ecommerce.event.types.rma.customer`

| **Attribute** | **Type** | **Is Nullable** | **Is Required** | **Description** |
| --- | --- | --- | --- | --- |
| email | Int | false | true | Customer email. |
| firstname | Int | false | true | Customer first name. |
| lastname | Int | false | true | Customer last name. |

## RmaOrderItem

Code: `als.ecommerce.event.types.rma.order-item`

| **Attribute** | **Type** | **Is Nullable** | **Is Required** | **Description** |
| --- | --- | --- | --- | --- |
| item_id | Int | false | true | RMA item ID. |
| order_item_id | Int | false | true | Related Order Item ID that should be returned/refunded. |
| qty_requested | Int | false | true | Requested product quantity in the initial RMA request. |
| qty_approved | Int | false | true | Approved product quantity for return/refund. |
| sku | String | false | true | Related product SKU. |
| condition_code | String | false | true | Product condition. |
| reason_code | String | false | true | Product return/refund reason. |
| resolution_code | String | false | true | Requested product resolution. |

---

*This page corresponds to Confluence page ID: 4620353537* 