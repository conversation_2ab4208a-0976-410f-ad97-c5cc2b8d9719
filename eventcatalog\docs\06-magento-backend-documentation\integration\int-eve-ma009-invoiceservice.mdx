---
id: 'int-eve-ma009-invoiceservice'
title: 'INT-EVE-MA009 InvoiceService'
slug: /docs/06-magento-backend-documentation/integration/int-eve-ma009-invoiceservice
version: '0.0.1'
summary: 'Invoice Service integration documentation for Magento backend'
owners:
    - euvic
badge:
  label: 'Backend Documentation'
  color: 'blue'
confluencePageId: '4638474243'
---

# Change History

| **Date** | **Author** | **Description of Change** |
| --- | --- | --- |
| 5/27/2025 | @<PERSON><PERSON><PERSON> | Initial version |
|   |  |  |
|   |   |   |

# Related Tasks

1. https://fwc-commerce.atlassian.net/browse/ALS-137

# Events

## Invoice Created

Request allows to create invoice for specific order.

**Endpoint:** `POST /rest/default/V1/order/\{orderId\}/invoice`

**Event code:** `OrderInvoiced`

**Event type:** `custom`.

**Event producer:** SAP.

**Body schema:** `application/json`

**Body:**

```json
{
    "capture": true,
    "items": [
        {
            "order_item_id": 0,
            "qty": 0
        }
    ],
    "notify": true,
    "appendComment": true,
    "comment": {
        "comment": "string",
        "is_visible_on_front": 0
    }
}
```

**Types:**

| **Attribute** | **Type** | **Is Required** | **Description** |
| --- | --- | --- | --- |
| capture | Boolean | true | Defines if . |
| items | `OrderInvoiceItem[]` | false | List of invoiced items. If invoiced items list is not the same as ordered items - partial invoice will be created. |
| notify | Boolean | true | Defined if customer should be notified about invoice generation. |
| appendComment | Boolean | false | Defines if comment should be added to the invoice initialization operation. |
| comment | `OrderInvoiceComment` | false | Invoice comment, which customer may see in his dashboard (when logged in). This comment is also visible in the order details page in admin panel. |

**Response:**

* HTTP 200 - Contains ID of the created invoice.

    * ```
      141
      ```
    
* HTTP 400 - Bad Request

    * ```json
      {
        "message": "string",
        "errors": [
          {
            "message": "string",
            "parameters": [
              {
                "resources": "string",
                "fieldName": "string",
                "fieldValue": "string"
              }
            ]
          }
        ],
        "code": 0,
        "parameters": [
          {
            "resources": "string",
            "fieldName": "string",
            "fieldValue": "string"
          }
        ],
        "trace": "string"
      }
      ```
    
* HTTP 401 - Unauthorized
* HTTP 500 - Internal Server Error

Response body structure for HTTP codes 400, 401, 500 is the same.

# Types

## OrderInvoiceItem

Code: `als.ecommerce.event.types.order.invoice-item`

| **Attribute** | **Type** | **Is Required** | **Description** |
| --- | --- | --- | --- |
| order_item_id | Int | true | ID of an order item to be included in the invoice. |
| qty | Int | true | Quantity to invoice. |

## OrderInvoiceComment

Code: `als.ecommerce.event.types.order.invoice-comment`

| **Attribute** | **Type** | **Is Required** | **Description** |
| --- | --- | --- | --- |
| comment | String | true | Content of the comment. |
| is_visible_on_front | Boolean | true | Defines if the comment should be visible in user dashboard on frontend. |

# INT-EVE-MA009 InvoiceService

This document describes the Invoice Service integration and functionality within the Magento backend system.

## Overview

The Invoice Service handles invoice generation, management, and distribution for orders processed through the Magento platform.

## Key Components

### Invoice Generation
- Automatic invoice creation
- Manual invoice processing
- Partial invoicing support
- Invoice validation

### Invoice Management
- Invoice status tracking
- Payment reconciliation
- Credit memo processing
- Invoice modifications

### Document Distribution
- PDF generation
- Email delivery
- Print formatting
- Archive management

## Integration Points

### Events Published
- Invoice created events
- Payment status updates
- Invoice delivery notifications
- Credit memo events

### Events Consumed
- Order completion events
- Payment confirmations
- Customer data updates
- Tax calculation results

## API Endpoints

### Invoice Operations
- Invoice creation services
- Status update APIs
- Document retrieval endpoints
- Payment processing interfaces

### Document Management
- PDF generation services
- Template management APIs
- Delivery confirmation endpoints
- Archive access interfaces

## Data Models

### Invoice Entity
- Invoice identification
- Order reference
- Customer information
- Line items and totals
- Tax calculations
- Payment status

### Invoice Line Item
- Product identification
- Quantity and pricing
- Tax information
- Discount applications
- Total calculations 