---
id: 'is-payment-service'
title: 'INT-EVE-MA009 PaymentService'
version: '0.0.1'
summary: 'Payment Service defines a set of events that are handled by Payment related system API and propagates events of payment processing operations in the store.'
owners:
    - euvic
badge:
  label: 'Integration Service'
  color: 'green'
confluencePageId: '**********'
---

# INT-EVE-MA009 PaymentService

## Change History

| **Date** | **Author** | **Description of Change** |
| --- | --- | --- |
| 5/16/2025 | <PERSON><PERSON><PERSON> | Initial version |

## Introduction

Payment Service defines a set of events that are handled by Payment related system API and propagates events of payment processing operations in the store.

## Related Tasks

1. [ALS-146](https://fwc-commerce.atlassian.net/browse/ALS-146)

## Events

### Payment Authorized

Event triggered when a payment has been authorized by the payment gateway.

**Endpoint:** `POST /rest/V1/payments/authorize`

**Event code:** `PaymentAuthorized`

**Event type:** `custom`

**Event producer:** Payment Gateway, Order Management System

**Body schema:** `application/json`

**Body:**

```json
{
    "payment": {
        "transaction_id": "TXN-2025-001234",
        "order_id": "ORD-2025-5678",
        "amount": 199.99,
        "currency": "USD",
        "payment_method": "credit_card",
        "gateway": "stripe",
        "status": "authorized",
        "authorization_code": "AUTH123456",
        "authorized_at": "2025-05-16T14:30:00Z"
    }
}
```

**Types:**

| **Attribute** | **Type** | **Is Required** | **Description** |
| --- | --- | --- | --- |
| payment | `Payment` | true | Payment transaction entity. |

**Response:**

* HTTP 200 - Payment authorized successfully
* HTTP 400 - Bad Request (invalid payment data)
* HTTP 401 - Unauthorized
* HTTP 402 - Payment Required (insufficient funds)
* HTTP 500 - Internal Server Error

### Payment Captured

Event triggered when an authorized payment has been captured.

**Endpoint:** `POST /rest/V1/payments/capture`

**Event code:** `PaymentCaptured`

**Event type:** `custom`

**Event producer:** Order Fulfillment System, Payment Gateway

**Body schema:** `application/json`

**Body:**

```json
{
    "payment": {
        "transaction_id": "TXN-2025-001234",
        "order_id": "ORD-2025-5678",
        "captured_amount": 199.99,
        "status": "captured",
        "capture_id": "CAP-789012",
        "captured_at": "2025-05-16T15:45:00Z"
    }
}
```

### Payment Refunded

Event triggered when a payment has been refunded to the customer.

**Endpoint:** `POST /rest/V1/payments/refund`

**Event code:** `PaymentRefunded`

**Event type:** `custom`

**Event producer:** Customer Service, Return Management System

**Body schema:** `application/json`

**Body:**

```json
{
    "refund": {
        "refund_id": "REF-2025-3456",
        "original_transaction_id": "TXN-2025-001234",
        "order_id": "ORD-2025-5678",
        "refund_amount": 99.99,
        "refund_reason": "customer_return",
        "status": "refunded",
        "refunded_at": "2025-05-16T16:20:00Z"
    }
}
```

### Payment Failed

Event triggered when a payment processing attempt has failed.

**Event code:** `PaymentFailed`

**Event type:** `custom`

**Event producer:** Payment Gateway, Fraud Detection System

**Body schema:** `application/json`

**Body:**

```json
{
    "payment": {
        "transaction_id": "TXN-2025-001235",
        "order_id": "ORD-2025-5679",
        "amount": 299.99,
        "currency": "USD",
        "payment_method": "credit_card",
        "status": "failed",
        "failure_reason": "insufficient_funds",
        "error_code": "E001",
        "failed_at": "2025-05-16T17:10:00Z"
    }
}
```

### Payment Method Added

Event triggered when a new payment method has been added to a customer account.

**Endpoint:** `POST /rest/V1/customers/{customerId}/payment-methods`

**Event code:** `PaymentMethodAdded`

**Event type:** `custom`

**Event producer:** Customer Account Management, Payment Gateway

**Body schema:** `application/json`

**Body:**

```json
{
    "payment_method": {
        "payment_method_id": "PM-2025-7890",
        "customer_id": 12345,
        "type": "credit_card",
        "last_four": "1234",
        "brand": "visa",
        "expiry_month": 12,
        "expiry_year": 2028,
        "is_default": false,
        "added_at": "2025-05-16T18:00:00Z"
    }
}
```

## Types

### Payment

Code: `als.ecommerce.event.types.payment.transaction`

| **Attribute** | **Type** | **Is Required** | **Description** |
| --- | --- | --- | --- |
| transaction_id | String | true | Unique transaction identifier. |
| order_id | String | true | Associated order identifier. |
| amount | Decimal | true | Payment amount. |
| currency | String | true | Payment currency code (ISO 4217). |
| payment_method | String | true | Payment method type: credit_card, debit_card, paypal, bank_transfer |
| gateway | String | true | Payment gateway provider. |
| status | String | true | Payment status: pending, authorized, captured, failed, refunded |
| authorization_code | String | false | Payment authorization code. |
| capture_id | String | false | Payment capture identifier. |
| authorized_at | String | false | Authorization timestamp in ISO-8601 format. |
| captured_at | String | false | Capture timestamp in ISO-8601 format. |
| failed_at | String | false | Failure timestamp in ISO-8601 format. |
| failure_reason | String | false | Reason for payment failure. |
| error_code | String | false | Error code from payment gateway. |

### Refund

Code: `als.ecommerce.event.types.payment.refund`

| **Attribute** | **Type** | **Is Required** | **Description** |
| --- | --- | --- | --- |
| refund_id | String | true | Unique refund identifier. |
| original_transaction_id | String | true | Original payment transaction ID. |
| order_id | String | true | Associated order identifier. |
| refund_amount | Decimal | true | Refund amount. |
| refund_reason | String | true | Reason for refund: customer_return, order_cancellation, fraud_protection |
| status | String | true | Refund status: pending, processed, failed |
| refunded_at | String | false | Refund timestamp in ISO-8601 format. |
| processing_fee | Decimal | false | Processing fee deducted from refund. |

### PaymentMethod

Code: `als.ecommerce.event.types.payment.method`

| **Attribute** | **Type** | **Is Required** | **Description** |
| --- | --- | --- | --- |
| payment_method_id | String | true | Unique payment method identifier. |
| customer_id | Int | true | Associated customer ID. |
| type | String | true | Payment method type: credit_card, debit_card, bank_account, digital_wallet |
| last_four | String | false | Last four digits of card/account number. |
| brand | String | false | Card brand: visa, mastercard, amex, discover |
| expiry_month | Int | false | Card expiry month. |
| expiry_year | Int | false | Card expiry year. |
| is_default | Boolean | true | Whether this is the default payment method. |
| added_at | String | true | Addition timestamp in ISO-8601 format. |
| billing_address | Object | false | Associated billing address. |

---

*This page corresponds to Confluence page ID: *********** 