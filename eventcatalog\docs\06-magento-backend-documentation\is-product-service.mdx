---
id: is-product-service
title: 'INT-EVE-MA005 ProductService'
sidebar_label: 'Product Service'
summary: 'Product Service defines a set of events that are handled by Product related system API and propagates events of product catalog items management in the store.'
owners:
    - euvic
confluencePageId: '4685922347'
---

# INT-EVE-MA005 ProductService

## Change History

| **Date** | **Author** | **Description of Change** |
| --- | --- | --- |
| 5/9/2025 | <PERSON><PERSON><PERSON> | Initial version |
| 5/12/2025 | <PERSON><PERSON><PERSON> | Added `ProductUpdated`, `ProductStockUpdated` and `ProductPriceUpdated` event descriptions |
| 5/13/2025 | <PERSON><PERSON><PERSON> | Extracted `ProductPriceUpdated` single event to event group |

## Introduction

Product Service defines a set of events that are handled by Product related system API and propagates events of product catalog items management in the store.

## Related Tasks

1. [ALS-143](https://fwc-commerce.atlassian.net/browse/ALS-143)

## Events

### Product Created

Event triggered when a new product has been added to the system by a system administrator or external system.

**Endpoint:** `POST /rest/V1/products`

**Event code:** `ProductCreated`

**Event type:** `custom`

**Event producer:** SAP, Supplier, PIM

**Body schema:** `application/json`

**Body:**

```json
{
    "product": {
        "sku": "LG-EBF49827801",
        "name": "LG #EBF49827801 SWITCH ASSEMBLY,LOCKER",
        "price": 60.48,
        "status": "1",
        "visibility": "1",
        "supplier": "LG",
        "type_id": "simple",
        "weight": "150"
    }
}
```

**Types:**

| **Attribute** | **Type** | **Is Required** | **Description** |
| --- | --- | --- | --- |
| product | `Product` | ✅ | Product entity. |

**Response:**

* HTTP 200 - Contains created entity.

```json
{
    "product": {
        "sku": "LG-EBF49827801",
        "name": "LG #EBF49827801 SWITCH ASSEMBLY,LOCKER",
        "price": 60.48,
        "status": "1",
        "visibility": "1",
        "supplier": "LG",
        "type_id": "simple",
        "created_at": "13:00 15-05-2025",
        "updated_at": "13:00 15-05-2025",
        "weight": "150"
    }
}
```

* HTTP 400 - Bad Request
* HTTP 401 - Unauthorized
* HTTP 500 - Internal Server Error

### Product Updated

Event triggered when a product has been updated in the system by a system administrator or external system.

**Endpoint:** `PUT /rest/V1/products/:sku`

**Event code:** `ProductUpdated`

**Event type:** `custom`

**Event producer:** SAP, DAM, Supplier, PIM

**Body schema:** `application/json`

**Body:**

```json
{
    "product": {
        "name": "LG #EBF49827801 SWITCH ASSEMBLY,LOCKER - updated data",
        "status": "1",
        "visibility": "1",
        "supplier": "LG"
    }
}
```

**Types:**

| **Attribute** | **Area** | **Type** | **Is Required** | **Description** |
| --- | --- | --- | --- | --- |
| sku | Request param | String | ✅ | Product SKU, which is to be modified. |
| product | Request body | `Product[]` | ✅ | Updated product entity. |

**Response:**

* HTTP 200 - Contains updated entity with all available attributes included (not only modified ones).

```json
{
    "product": {
        "sku": "LG-EBF49827801",
        "name": "LG #EBF49827801 SWITCH ASSEMBLY,LOCKER - updated data",
        "price": 59.95,
        "status": "1",
        "visibility": "1",
        "supplier": "LG",
        "type_id": "simple",
        "created_at": "13:00 15-05-2025",
        "updated_at": "13:15 15-05-2025",
        "weight": "150"
    }
}
```

### Product Stock Updated

Event triggered when product stock has been updated in the system by a system administrator or external system.

**Endpoint:** `POST /rest/V1/inventory/source-items`

**Event code:** `ProductStockUpdated`

**Event type:** `custom`

**Event producer:** SAP, Supplier

**Body schema:** `application/json`

**Body:**

```json
{
    "sourceItems": [
        {
            "sku": "product-sku-a",
            "source_code": "primary-stock",
            "quantity": 5,
            "status": 0
        },
        {
            "sku": "product-stock-b",
            "source_code": "secondary-stock",
            "quantity": 999,
            "status": 1
        }
    ]
}
```

**Types:**

| **Attribute** | **Type** | **Is Required** | **Description** |
| --- | --- | --- | --- |
| sourceItems | `SourceItem[]` | ✅ | List of updated stock items. |

### Product Price Updated

Events group triggered when a price for a product has been updated in the system by a system administrator or external system. There are a few price types - each of them defines different application rules and all of them are propagated using separate events:

| **Type** | **Code** | **Description** |
| --- | --- | --- |
| Base Price (fixed) | `fixed` | Base product price applied for all customers (guests and logged in). |
| Special Price | `special` | A discounted price that can be scheduled for specific dates. |
| Tier Price | `tier` | Defines a volume based pricing (e.g. "Buy 10+, get each for $9.99). Tier prices are eligible to be set per client group. |

#### Product Fixed Price

**Endpoint:** `POST /rest/V1/products/base-prices`

**Event code:** `ProductFixedPriceUpdated`

**Event type:** `custom`

**Event producer:** SAP, Supplier

**Body schema:** `application/json`

**Body:**

```json
{
    "prices": [
        {
            "price": 45.99,
            "store_id": 2,
            "sku": "test-sku-a"
        },
        {
            "price": 59.99,
            "store_id": 1,
            "sku": "test-sku-b"
        }
    ]
}
```

#### Product Special Price

**Endpoint:** `POST /rest/V1/products/special-prices`

**Event code:** `ProductSpecialPriceUpdated`

**Event type:** `custom`

**Event producer:** SAP, Supplier

**Body:**

```json
{
    "prices": [
        {
            "price": 14.99,
            "store_id": 1,
            "price_from": "01:00 12-05-2025",
            "price_to": "21:00 12-05-2025",
            "sku": "test-sku-c"
        }
    ]
}
```

---

*This page corresponds to Confluence page ID: 4685922347* 