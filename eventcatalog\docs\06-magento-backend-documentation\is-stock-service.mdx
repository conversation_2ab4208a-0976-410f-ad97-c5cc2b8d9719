---
id: stock-service
title: 'INT-EVE-MA007 StockService'
version: 1.0.0
summary: 'Stock Service defines a set of events that are handled by Stock related system API and propagates events of stock management operations in the store.'
owners:
    - euvic
badge:
  label: 'Integration Service'
  color: 'green'
confluencePageId: '**********'
---

# INT-EVE-MA007 StockService

## Change History

| **Date** | **Author** | **Description of Change** |
| --- | --- | --- |
| 5/14/2025 | <PERSON><PERSON><PERSON> | Initial version |

## Introduction

Stock Service defines a set of events that are handled by Stock related system API and propagates events of stock management operations in the store.

## Related Tasks

1. [ALS-144](https://fwc-commerce.atlassian.net/browse/ALS-144)

## Events

### Stock Updated

Event triggered when stock levels have been updated in the system by inventory management or external systems.

**Endpoint:** `POST /rest/V1/inventory/source-items`

**Event code:** `StockUpdated`

**Event type:** `custom`

**Event producer:** SAP, WMS, Inventory Management System

**Body schema:** `application/json`

**Body:**

```json
{
    "sourceItems": [
        {
            "sku": "product-sku-a",
            "source_code": "primary-warehouse",
            "quantity": 150,
            "status": 1
        },
        {
            "sku": "product-sku-b",
            "source_code": "secondary-warehouse",
            "quantity": 0,
            "status": 0
        }
    ]
}
```

**Types:**

| **Attribute** | **Type** | **Is Required** | **Description** |
| --- | --- | --- | --- |
| sourceItems | `SourceItem[]` | true | List of stock items to update. |

**Response:**

* HTTP 200 - Stock updated successfully
* HTTP 400 - Bad Request
* HTTP 401 - Unauthorized
* HTTP 500 - Internal Server Error

### Stock Reserved

Event triggered when stock has been reserved for pending orders.

**Endpoint:** `POST /rest/V1/inventory/reservations`

**Event code:** `StockReserved`

**Event type:** `custom`

**Event producer:** Order Management, Shopping Cart

**Body schema:** `application/json`

**Body:**

```json
{
    "reservations": [
        {
            "sku": "product-sku-a",
            "quantity": 2,
            "metadata": {
                "order_id": "12345",
                "customer_id": "67890"
            }
        }
    ]
}
```

### Stock Released

Event triggered when reserved stock has been released back to available inventory.

**Endpoint:** `DELETE /rest/V1/inventory/reservations`

**Event code:** `StockReleased`

**Event type:** `custom`

**Event producer:** Order Management, Payment System

**Body schema:** `application/json`

**Body:**

```json
{
    "reservations": [
        {
            "sku": "product-sku-a",
            "quantity": 2,
            "metadata": {
                "order_id": "12345",
                "reason": "order_cancelled"
            }
        }
    ]
}
```

### Low Stock Alert

Event triggered when product stock falls below predefined threshold levels.

**Event code:** `LowStockAlert`

**Event type:** `custom`

**Event producer:** Inventory Management System

**Body schema:** `application/json`

**Body:**

```json
{
    "alerts": [
        {
            "sku": "product-sku-a",
            "current_quantity": 5,
            "threshold": 10,
            "source_code": "primary-warehouse"
        }
    ]
}
```

## Types

### SourceItem

Code: `als.ecommerce.event.types.stock.source-item`

| **Attribute** | **Type** | **Is Required** | **Description** |
| --- | --- | --- | --- |
| sku | String | true | Product SKU that is related to stock data. |
| source_code | String | true | Stock source identifier (warehouse, store, etc.). |
| quantity | Int | true | Available product quantity. |
| status | Int | true | Defines if product is available in the specified stock. Values: 0 - Not Available, 1 - Available |

### Reservation

Code: `als.ecommerce.event.types.stock.reservation`

| **Attribute** | **Type** | **Is Required** | **Description** |
| --- | --- | --- | --- |
| sku | String | true | Product SKU for which stock is reserved. |
| quantity | Int | true | Quantity of stock reserved. |
| metadata | Object | false | Additional information about the reservation (order ID, customer ID, etc.). |

### StockAlert

Code: `als.ecommerce.event.types.stock.alert`

| **Attribute** | **Type** | **Is Required** | **Description** |
| --- | --- | --- | --- |
| sku | String | true | Product SKU with low stock. |
| current_quantity | Int | true | Current available quantity. |
| threshold | Int | true | Minimum stock threshold. |
| source_code | String | true | Stock source identifier. |

## Integration Points

### Internal Systems

* **Product Catalog**: Product availability status
* **Order Management**: Stock reservation and release
* **Analytics**: Stock level tracking and reporting

### External Systems

* **WMS**: Warehouse Management System for stock updates
* **ERP**: Enterprise Resource Planning for inventory synchronization
* **Suppliers**: External supplier stock feeds

## API Endpoints

The Stock Service module exposes several REST API endpoints:

```
GET /rest/V1/stockSources
POST /rest/V1/stockSources
PUT /rest/V1/stockSources/{sourceCode}
DELETE /rest/V1/stockSources/{sourceCode}
GET /rest/V1/stockItems/{productSku}
PUT /rest/V1/stockItems/{productSku}
GET /rest/V1/stockStatuses/{productSku}
```

## Business Rules

### Stock Management

* Real-time stock updates across all sources
* Stock reservation for cart and checkout
* Automatic stock release for cancelled orders
* Low stock notifications

### Multi-source Inventory

* Support for multiple warehouses and stores
* Prioritization of stock sources
* Aggregate stock view across all sources

### Backorders

* Backorder management for out-of-stock items
* Pre-order support
* Customer notifications for backordered items

## Monitoring and Analytics

### Stock Metrics

* Stock turnover rate
* Sell-through rate
* Days of supply
* Stock-out rate

### Accuracy Metrics

* Inventory accuracy
* Cycle count accuracy
* Shrinkage rate

### Performance Metrics

* Stock update latency
* API response times
* Order fulfillment time

## Security and Privacy

### Data Protection

* Secure transmission of stock data
* Anonymization of sensitive business data
* Access controls for stock information

### Access Control

* Role-based access for inventory management
* API key authentication for external systems
* IP whitelisting for trusted partners

## Scalability and Performance

### Asynchronous Processing

* Message queues for high-volume stock updates
* Load balancing for API endpoints
* Scalable architecture for real-time inventory tracking

### Caching Strategies

* Caching of stock levels for popular products
* CDN for static assets
* Database optimization for stock queries

### Performance Testing

* Load testing of stock update APIs
* Stress testing of reservation system
* Monitoring of database performance

## Error Handling and Resilience

### Retry Mechanisms

* Automatic retries for failed stock updates
* Exponential backoff for API calls
* Dead-letter queues for failed messages

### Failure Detection

* Health checks for stock service APIs
* Monitoring of stock discrepancies
* Alerting for system failures

### Data Recovery

* Backup and restore procedures for inventory data
* Data reconciliation processes
* Disaster recovery plan

---

*This page corresponds to Confluence page ID: *********** 