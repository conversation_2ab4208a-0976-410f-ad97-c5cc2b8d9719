---
id: mod-op-ma001-sap-order-id
title: MOD-OP-MA001 SAP Order ID
sidebar_label: MOD-OP-MA001 SAP Order ID
slug: /docs/06-magento-backend-documentation/order-process/modifications/mod-op-ma001-sap-order-id
summary: 'MOD-OP-MA001 SAP Order ID modification specification for order process system, defining SAP order identifier integration and mapping functionality within Magento order processing module'
owners:
    - euvic
---

# MOD-OP-MA001 SAP Order ID

## Change History

| **Date** | **Author** | **Description of Change** |
| --- | --- | --- |
| 5/30/2025 | @<PERSON><PERSON> | Initial version |
| 6/11/2025 | @<PERSON><PERSON> | Changed to integration order id |
| 6/13/2025 | @<PERSON><PERSON> | Removed webapi endpoints |

## Related Tasks

* [ALS-175](https://fwc-commerce.atlassian.net/browse/ALS-175)

## Related Pages

* [INT-EVE-MA001 Order Service - Update SAP Order ID](https://fwc-commerce.atlassian.net/wiki/spaces/ALS/pages/**********/INT-EVE-MA001+Order+Service#Update-SAP-Order-ID)

## Purpose

`Als_SapIntegration` module introduces the `integration_order_id` attribute to the Order entity. The value assigned to an order is visible in the admin panel, within the Order & Account Information section.

## Key Features

1. **Order**:
   * Added new extension attribute `integration_order_id`

2. **Admin Order View**:
   * Displays Integration Order ID on the order view page in Order & Account Information section

3. **REST API**:
   * Added `integration_order_id` as an extension attribute to the Order entity which is returned using `/V1/orders/\{id\}` endpoint

## Dependencies

* Fwc ExtensionAttributes - module which provides logic for custom extension attributes

---

**Confluence Page Reference:** [MOD-OP-MA001 SAP Order ID](https://fwc-commerce.atlassian.net/wiki/spaces/ALS/pages/**********)  
**Page ID:** **********  
**Parent:** [OP] Modifications  
**Depth:** 3

---

*This page is synchronized from Confluence. For the most up-to-date information, please refer to the original Confluence page.* 