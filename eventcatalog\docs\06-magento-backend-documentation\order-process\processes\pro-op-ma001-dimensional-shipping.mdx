---
id: pro-op-ma001-dimensional-shipping
title: PRO-OP-MA001 Dimensional Shipping
sidebar_label: PRO-OP-MA001 Dimensional Shipping
slug: /docs/06-magento-backend-documentation/order-process/processes/pro-op-ma001-dimensional-shipping
summary: 'PRO-OP-MA001 Dimensional Shipping process specification for order processing system, defining dimensional weight calculation and shipping cost determination procedures within Magento order processing workflows'
owners:
    - euvic
---

# PRO-OP-MA001 Dimensional Shipping

## Dimensional Shipping Guide

### Change History

| **Date** | **Author** | **Description of Change** |
| --- | --- | --- |
| 5/29/2025 | @Jace<PERSON>i | Initial version |
|   |   |   |

### Related Tasks

1. [ALS-188](https://fwc-commerce.atlassian.net/browse/ALS-188)
2. [ALS-171](https://fwc-commerce.atlassian.net/browse/ALS-171)

## Configuration

The extension's settings are accessible in the Magento admin panel via:

`Configuration > Aitoc Extensions > Dimensional Shipping`

It's critical that the **Length Unit** selected within the extension's configuration precisely matches the **Weight Unit** defined in Magento's general settings:

`General > General > Locale Options > Weight Unit`

Ensure the following pairings are used:

* Imperial Units → Length Unit: `in` , Weight Unit: `lbs`
* Metric Units → Length Unit: `mm`, Weight Unit: `kgs`

## Shipping Boxes Definition

Shipping boxes used in delivery process can be defined on the page located in:

`Sales > Dimensional Shipping > Shipping Boxes (Parcels)`

This page displays a list of existing boxes and allows you to add new ones with custom dimensions. Ensure all dimensions are provided in the configured unit (`in` or `mm`). Each box requires inner and outer dimensions, as well as weight. All attributes are mandatory, with the exception of **Empty Weight**. Boxes with dimensions or maximum weight set to zero won't be included in calculations.

## Mandatory Product Attributes

The Product Edit page has been updated with a new **Dimensional Shipping Options** tab. Accurate dimensions and weight are required for each product to utilize this feature. Products missing any of these values will default to the core UPS shipping method.

Extensions allows setting special boxes per product and applying special rules like packing each item to separate box.

## Frontend

On the frontend, this extension affects prices displayed by delivery methods in checkout process. Delivery prices are calculated based on the boxes which were assigned to cart items.

## Order View Page

Successfully assigned boxes will be displayed next to the order items in Order view page. Items are split by boxes. Action column allows changing boxes assigned to the items directly from admin panel.

## Video Walkthrough (PL)

*Note: Video content is available in the original Confluence page.*

---

**Confluence Page Reference:** [PRO-OP-MA001 Dimensional Shipping](https://fwc-commerce.atlassian.net/wiki/spaces/ALS/pages/4641783811)  
**Page ID:** 4641783811  
**Parent:** [OP] Processes  
**Depth:** 3

---

*This page is synchronized from Confluence. For the most up-to-date information, please refer to the original Confluence page.* 