---
id: pro-pu-ma001-order-split
title: 'PRO-PU-MA001 Order split'
sidebar_label: 'PRO-PU-MA001 Order split'
slug: /docs/06-magento-backend-documentation/purchase/processes/pro-pu-ma001-order-split
version: '0.0.1'
summary: 'Order split process documentation for purchase operations'
owners:
    - euvic
badge:
  label: 'Backend Documentation'
  color: 'blue'
confluencePageId: '4580376583'
---

# PRO-PU-MA001 Order split

This document describes the order split process within the Purchase module processes.

## Overview

The Order split process handles the division of large orders into smaller, manageable sub-orders based on various business rules and constraints.

## Purpose

Order splitting is essential for:
- Managing inventory allocation across multiple warehouses
- Optimizing shipping costs and delivery times
- Handling partial stock availability
- Meeting supplier capacity constraints
- Complying with regulatory requirements

## Process Flow

### 1. Order Analysis
- Evaluate order size and complexity
- Check product availability across locations
- Assess shipping requirements
- Review customer preferences

### 2. Split Criteria Evaluation
- Inventory availability by location
- Shipping cost optimization
- Delivery time requirements
- Supplier capacity limits
- Product compatibility rules

### 3. Split Execution
- Create sub-orders with appropriate items
- Maintain order relationships and tracking
- Update inventory allocations
- Generate shipping arrangements
- Notify relevant systems and stakeholders

### 4. Order Tracking
- Maintain parent-child order relationships
- Provide unified tracking for customers
- Handle partial deliveries and returns
- Manage payment allocations

## Business Rules

### Split Triggers
- Order value exceeds threshold limits
- Products from different warehouses
- Mixed product types requiring different handling
- Supplier capacity constraints
- Shipping restrictions

### Split Logic
- Prioritize customer delivery preferences
- Optimize shipping costs
- Maintain product compatibility
- Respect inventory allocation rules
- Consider regulatory requirements

## Integration Points

### Internal Systems
- Inventory Management System
- Order Management System
- Warehouse Management System
- Shipping Management System

### External Systems
- Supplier systems for capacity checks
- Shipping carrier systems
- Customer notification systems
- ERP system for financial tracking

## Configuration

### Configurable Parameters
- Maximum order value thresholds
- Product compatibility matrices
- Warehouse priority rules
- Shipping cost optimization parameters
- Customer preference handling rules

---

*This page corresponds to Confluence page ID: 4580376583* 