---
name: Brands Block
title: Brands Block
summary: "Brands Block PWA Frontend Documentation"
owners:
  - euvic
tags:
  - pwa
  - frontend
  - brands
  - block
  - magento
confluencePageId: "4632018949"
---

# Brands Block

## Change History

| **Date** | **Author** | **Description of Change** |
|----------|------------|---------------------------|
| 5/22/2025 | <PERSON><PERSON><PERSON><PERSON> | Initial version |
| 6/13/2025 | Rafał Słowik | Replaced Base HTML and CSS code of the element content |

## Purpose

This document provides step-by-step instructions for editing an HTML element placed on a CMS page or block using **Magento Page Builder**. This applies to frontend users who need to update textual or visual content directly on the website.

## Related Tasks

- [ALS-165: Brands block](https://fwc-commerce.atlassian.net/browse/ALS-165)

## Usage Scenario

**Use case**: A marketing team member wants to update the promotional message inside a homepage banner that uses a custom HTML block in Page Builder.

## Element Preview

![Brands Block Preview](./images/brands-block-preview.png)

## Editing HTML Element in Page Builder

### Prerequisites

- Admin access to Magento backend
- Knowledge of basic HTML (optional, depending on the element)

### Steps to Edit HTML Content

1. **Log in** to the Magento Admin Panel.
2. Go to one of the following:
   - **Content → Pages**, or
   - **Content → Blocks**
3. Find the page or block you want to edit (e.g., **Homepage** or **Footer Block**).
4. Click **Edit**.
5. In the content section, locate the **Page Builder** canvas.
6. Hover over the element you want to change (e.g., **HTML Code block**).
7. Click the **(gear icon) Edit** button.
8. An **HTML editor** modal will appear.
9. Modify the content as needed.
10. Click **Save** to close the HTML editor.
11. Click **Save Page** or **Save Block**

## Additional Notes

The HTML code of the Brands Block is described by the comment "Brands Block HTML":

![Brands Block HTML Comment](./images/brands-block-html-comment.png)

## Base HTML and CSS Code

The complete HTML and CSS implementation for the Brands Block:

```html
<!-- Brands block HTML -->

<style>
    .homepage-brands-block {
        display: grid;
        row-gap: 45px;
        padding: 40px 0;
    }

    .homepage-brands-block__header {
        display: grid;
        gap: 20px;
        max-width: 1115px;
        width: 100%;
        margin: 0 auto;
    }

    .homepage-brands-block__header-text {
        display: grid;
        row-gap: 15px;
    }

    .homepage-brands-block__header-title {
        color: #002854;

        font-size: 2.5rem;
        font-weight: 800;
    }

    .homepage-brands-block__header-title>span {
        color: #9AC7E2;
    }

    .homepage-brands-block__header-subtitle {
        color: #002854;

        font-size: 1.8rem;
        font-weight: 600;
    }

    .homepage-brands-block__header-button {
        align-self: center;
    }

    .homepage-brands-block__header-button-link {
        display: grid;
        place-items: center;
        grid-auto-flow: column;
        column-gap: 7px;
        height: 54px;
        padding: 0 16px;
        border-radius: 3px;
        justify-content: start;
        width: max-content;

        background-color: #002854;
        color: #FFFFFF;

        text-decoration: none;
    }

    .homepage-brands-block__brands {
        display: grid;
        gap: 10px;
        justify-content: center;
    }

    .homepage-brands-block__brand {
        display: grid;
        grid-template-columns: 83px 1fr;
        column-gap: 22px;
        padding: 24px;
        border-radius: 3px;

        background-color: #FFFFFF;
    }

    .homepage-brands-block__brand>img {
        display: block;
        grid-row: 1 / 3;
        place-self: center;
    }

    .homepage-brands-block__brand-name {
        display: block;
        align-self: end;
        margin-bottom: 8px;

        color: #002854;

        font-size: 1.6rem;
        line-height: 1.7rem;
        font-weight: 800;
    }

    .homepage-brands-block__brand-view {
        display: grid;
        grid-auto-flow: column;
        column-gap: 8px;
        justify-content: start;
        align-items: center;
        align-self: start;
        width: max-content;

        color: #002854;
        font-size: 1.4rem;
        line-height: 1.7rem;
    }

    @media (min-width: 650px) {
        .homepage-brands-block__brands {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    @media (min-width: 1024px) {
        .homepage-brands-block {
            padding: 95px 0 50px;
        }

        .homepage-brands-block__header {
            grid-auto-flow: column;
            justify-content: space-between;
        }

        .homepage-brands-block__header-title {
            font-size: 4rem;
        }

        .homepage-brands-block__header-button {
            justify-self: end;
        }

        .homepage-brands-block__brands {
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        }

        .homepage-brands-block__brand:nth-last-child(-2n+2) {
            grid-column: 2;
        }
    }
</style>

<div class="homepage-brands-block">
    <div class="homepage-brands-block__header">
        <div class="homepage-brands-block__header-text">
            <span class="homepage-brands-block__header-title">
                We work with 15<span>+</span> brands
            </span>
            <span class="homepage-brands-block__header-subtitle">
                Laundry Alliance is your trusted partner, offering high-quality
                details.
            </span>
        </div>
        <div class="homepage-brands-block__header-button">
            <a
                class="homepage-brands-block__header-button-link text-microSubheading"
                href="https://parts.alliancelaundry.com/p-27179-brands.html"
            >
                Explore all brands
                <svg
                    fill="none"
                    height="22"
                    viewBox="0 0 23 22"
                    width="23"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <path
                        d="M11.129 2.2513C6.25158 2.2513 2.28323 6.21965 2.28323 11.0971C2.28323 15.9745 6.25158 19.9428 11.129 19.9428C16.0064 19.9428 19.9748 15.9745 19.9748 11.0971C19.9748 6.21965 16.0064 2.2513 11.129 2.2513ZM11.129 3.61219C15.2709 3.61219 18.6139 6.95521 18.6139 11.0971C18.6139 15.2389 15.2709 18.5819 11.129 18.5819C6.98714 18.5819 3.64412 15.2389 3.64412 11.0971C3.64412 6.95521 6.98714 3.61219 11.129 3.61219ZM9.91781 6.52449L8.93797 7.50433L12.5334 11.0971L8.93933 14.6898L9.91917 15.6697L14.0018 11.587L14.4686 11.0971L14.0012 10.6072L9.91781 6.52449Z"
                        fill="white"
                    />
                </svg>
            </a>
        </div>
    </div>
    <div class="homepage-brands-block__brands">
        <div class="homepage-brands-block__brand">
            <img
                alt="adc"
                src="/media/wysiwyg/brand-adc.png"
            />
            <span class="homepage-brands-block__brand-name">ADC</span>
            <a
                class="homepage-brands-block__brand-view text-microSubheading"
                href="#"
            >
                View all products
                <svg
                    fill="none"
                    height="25"
                    viewBox="0 0 24 25"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <path
                        d="M10.75 8.70996L14.25 12.21L10.75 15.71"
                        stroke="#002854"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="1.5"
                    />
                    <path
                        d="M21 12.21C21 13.3919 20.7672 14.5622 20.3149 15.6541C19.8626 16.746 19.1997 17.7382 18.364 18.5739C17.5282 19.4096 16.5361 20.0726 15.4442 20.5249C14.3522 20.9772 13.1819 21.21 12 21.21C10.8181 21.21 9.64778 20.9772 8.55585 20.5249C7.46392 20.0726 6.47177 19.4096 5.63604 18.5739C4.80031 17.7382 4.13738 16.746 3.68508 15.6541C3.23279 14.5622 3 13.3919 3 12.21C3 9.82301 3.94821 7.53383 5.63604 5.846C7.32387 4.15817 9.61305 3.20996 12 3.20996C14.3869 3.20996 16.6761 4.15817 18.364 5.846C20.0518 7.53383 21 9.82301 21 12.21Z"
                        stroke="#002854"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="1.5"
                    />
                </svg>
            </a>
        </div>
        <!-- Additional brand blocks continue with Alliance Laundry Systems, Amana, Cissel, Crossley, Electrolux, Inglis, LG, Magic Chef, Maytag, R & B, Rowe, Samsung, Sol-O-Matic, Speed Queen, Standard Change-Makers, UniMac, Whirlpool -->
    </div>
</div>