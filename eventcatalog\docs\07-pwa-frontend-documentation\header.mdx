---
name: Header
title: Header
summary: "Header PWA Frontend Documentation"
owners:
  - euvic
tags:
  - pwa
  - frontend
  - header
  - navigation
  - magento
confluencePageId: "4651122689"
---

# Header

## Change History

| **Date** | **Author** | **Description of Change** |
|----------|------------|---------------------------|
| 6/23/2025 | <PERSON><PERSON><PERSON><PERSON> | Initial version |

## Purpose

This document provides step-by-step instructions for editing an HTML element placed on a CMS page or block using **Magento Page Builder**. This applies to frontend users who need to update textual or visual content directly on the website.

## Related Tasks

- [ALS-182: Header](https://fwc-commerce.atlassian.net/browse/ALS-182)

## Usage Scenario

**Use case**: A marketing team member wants to update the promotional message inside a homepage banner that uses a custom HTML block in Page Builder.

## Element Preview

![Header Preview](./images/header-preview.png)

## Editing HTML Element in Page Builder

### Prerequisites

- Admin access to Magento backend
- Knowledge of basic HTML (optional, depending on the element)

### Steps to Edit HTML Content

1. **Log in** to the Magento Admin Panel.
2. Go to one of the following:
   - **Content → Pages**, or
   - **Content → Blocks**
3. Find the page or block you want to edit (e.g., **Homepage** or **Footer Block**).
4. Click **Edit**.
5. In the content section, locate the **Page Builder** canvas.
6. Hover over the element you want to change (e.g., **HTML Code block**).
7. Click the **(gear icon) Edit** button.
8. An **HTML editor** modal will appear.
9. Modify the content as needed.
10. Click **Save** to close the HTML editor.
11. Click **Save Page** or **Save Block**

## Additional Notes

The HTML code of the Header is described by the comment "Header block":

![Header HTML Comment](./images/header-html-comment.png)

## Base HTML and CSS Code

The complete HTML and CSS implementation for the Header:

```html
<!-- Header block -->

<style>
.header-block {
    background-color: #002854;
    color: white;
    padding: 10px 0;
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
}

.header-logo {
    display: flex;
    align-items: center;
}

.header-logo img {
    height: 40px;
    width: auto;
}

.header-navigation {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 30px;
}

.header-navigation li {
    position: relative;
}

.header-navigation a {
    color: white;
    text-decoration: none;
    font-weight: 500;
    font-size: 16px;
    transition: color 0.3s ease;
}

.header-navigation a:hover {
    color: #9AC7E2;
}

.header-search {
    display: flex;
    align-items: center;
    gap: 15px;
}

.header-search-input {
    padding: 8px 12px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    width: 200px;
}

.header-cart {
    position: relative;
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    font-size: 18px;
}

.header-cart-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: #ff4444;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 15px;
    }
    
    .header-navigation {
        gap: 20px;
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .header-search-input {
        width: 150px;
    }
}
</style>

<div class="header-block">
    <div class="header-content">
        <div class="header-logo">
            <img src="/media/wysiwyg/alliance-logo.png" alt="Alliance Laundry" />
        </div>
        
        <nav>
            <ul class="header-navigation">
                <li><a href="/parts">Parts</a></li>
                <li><a href="/equipment">Equipment</a></li>
                <li><a href="/service">Service</a></li>
                <li><a href="/support">Support</a></li>
                <li><a href="/about">About</a></li>
            </ul>
        </nav>
        
        <div class="header-search">
            <input 
                type="text" 
                class="header-search-input" 
                placeholder="Search products..."
            />
            <button class="header-cart">
                🛒
                <span class="header-cart-count">0</span>
            </button>
        </div>
    </div>
</div>
```

## Component Features

### Navigation Menu
- **Parts**: Link to parts catalog
- **Equipment**: Link to equipment section
- **Service**: Link to service information
- **Support**: Link to support resources
- **About**: Link to company information

### Search Functionality
- Search input field for product searches
- Responsive design for mobile devices

### Shopping Cart
- Cart icon with item counter
- Interactive cart functionality

### Responsive Design
- Mobile-first approach
- Collapsible navigation on smaller screens
- Optimized layout for different screen sizes

## Overview

This document provides comprehensive documentation for the Header component in the PWA frontend, including implementation details, customization options, and integration with Magento backend.

## Component Structure

The Header is a critical navigation component that appears at the top of every page, providing users with access to key site functionality and navigation options.

### Key Features

- **Logo/Brand Display**: Company branding and homepage link
- **Main Navigation Menu**: Primary site navigation
- **Search Functionality**: Product and content search
- **User Account Access**: Login, registration, account management
- **Shopping Cart**: Cart icon with item count and quick access
- **Mobile Responsiveness**: Hamburger menu for mobile devices
- **Multi-language Support**: Language switching capabilities

## Implementation Details

### Header Layout Structure

The header follows a structured layout with:

1. **Top Bar** (Optional)
   - Store information (phone, hours)
   - Language/currency switchers
   - Quick links

2. **Main Header**
   - Logo/brand area
   - Primary navigation menu
   - Search bar
   - User account links
   - Shopping cart icon

3. **Mobile Header**
   - Hamburger menu toggle
   - Logo
   - Search icon
   - Cart icon

### Technical Implementation

```javascript
// Header Component Structure
const Header = {
  topBar: {
    storeInfo: {
      phone: '******-567-8900',
      hours: 'Mon-Fri 9AM-6PM'
    },
    switchers: {
      language: ['EN', 'ES', 'FR'],
      currency: ['USD', 'EUR', 'GBP']
    },
    quickLinks: ['About', 'Contact', 'Help']
  },
  mainHeader: {
    logo: {
      src: '/logo.png',
      alt: 'Company Logo',
      link: '/'
    },
    navigation: {
      items: [
        { label: 'Home', url: '/' },
        { label: 'Categories', url: '/categories', hasSubmenu: true },
        { label: 'Sale', url: '/sale' },
        { label: 'About', url: '/about' }
      ]
    },
    search: {
      placeholder: 'Search products...',
      suggestions: true,
      autocomplete: true
    },
    userMenu: {
      authenticated: false,
      links: ['Login', 'Register', 'My Account', 'Wishlist']
    },
    cart: {
      itemCount: 0,
      showMiniCart: true
    }
  }
};
```

## Navigation Menu Configuration

### Main Menu Structure

The header navigation supports multi-level menu structures:

```javascript
// Navigation Menu Configuration
const navigationMenu = {
  categories: {
    label: 'Categories',
    submenu: [
      {
        label: 'Electronics',
        url: '/electronics',
        submenu: [
          { label: 'Smartphones', url: '/electronics/smartphones' },
          { label: 'Laptops', url: '/electronics/laptops' },
          { label: 'Tablets', url: '/electronics/tablets' }
        ]
      },
      {
        label: 'Clothing',
        url: '/clothing',
        submenu: [
          { label: 'Men\'s Clothing', url: '/clothing/mens' },
          { label: 'Women\'s Clothing', url: '/clothing/womens' },
          { label: 'Kids\' Clothing', url: '/clothing/kids' }
        ]
      }
    ]
  }
};
```

### Mega Menu Implementation

For categories with many subcategories, implement mega menus:

```css
/* Mega Menu Styling */
.mega-menu {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  background: white;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  z-index: 1000;
  display: none;
}

.mega-menu.active {
  display: block;
}

.mega-menu-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  padding: 30px;
  max-width: 1200px;
  margin: 0 auto;
}
```

## Search Functionality

### Search Bar Implementation

The header search includes advanced features:

```javascript
// Search Component
class HeaderSearch {
  constructor() {
    this.searchInput = document.querySelector('.header-search-input');
    this.searchResults = document.querySelector('.search-suggestions');
    this.debounceTimeout = null;
    this.initializeSearch();
  }

  initializeSearch() {
    this.searchInput.addEventListener('input', this.handleSearchInput.bind(this));
    this.searchInput.addEventListener('focus', this.showSuggestions.bind(this));
    document.addEventListener('click', this.hideSuggestions.bind(this));
  }

  handleSearchInput(event) {
    const query = event.target.value.trim();
    
    if (this.debounceTimeout) {
      clearTimeout(this.debounceTimeout);
    }

    this.debounceTimeout = setTimeout(() => {
      if (query.length >= 2) {
        this.fetchSuggestions(query);
      } else {
        this.hideSuggestions();
      }
    }, 300);
  }

  async fetchSuggestions(query) {
    try {
      const response = await fetch(`/api/search/suggestions?q=${encodeURIComponent(query)}`);
      const suggestions = await response.json();
      this.displaySuggestions(suggestions);
    } catch (error) {
      console.error('Search suggestions error:', error);
    }
  }

  displaySuggestions(suggestions) {
    this.searchResults.innerHTML = '';
    
    suggestions.forEach(suggestion => {
      const suggestionElement = document.createElement('div');
      suggestionElement.className = 'search-suggestion';
      suggestionElement.innerHTML = `
        <img src="${suggestion.image}" alt="${suggestion.name}" class="suggestion-image">
        <div class="suggestion-content">
          <div class="suggestion-name">${suggestion.name}</div>
          <div class="suggestion-price">${suggestion.price}</div>
        </div>
      `;
      
      suggestionElement.addEventListener('click', () => {
        window.location.href = suggestion.url;
      });
      
      this.searchResults.appendChild(suggestionElement);
    });
    
    this.showSuggestions();
  }
}
```

## User Account Integration

### Authentication States

The header displays different content based on user authentication:

```javascript
// User Account Menu
const userAccountMenu = {
  guest: {
    items: [
      { label: 'Sign In', url: '/customer/account/login' },
      { label: 'Create Account', url: '/customer/account/create' }
    ]
  },
  authenticated: {
    items: [
      { label: 'My Account', url: '/customer/account' },
      { label: 'My Orders', url: '/customer/account/orders' },
      { label: 'Wishlist', url: '/wishlist' },
      { label: 'Address Book', url: '/customer/address' },
      { label: 'Sign Out', url: '/customer/account/logout' }
    ]
  }
};
```

## Shopping Cart Integration

### Mini Cart Implementation

The header cart icon displays a mini cart on hover/click:

```javascript
// Mini Cart Component
class MiniCart {
  constructor() {
    this.cartIcon = document.querySelector('.cart-icon');
    this.miniCart = document.querySelector('.mini-cart');
    this.cartCount = document.querySelector('.cart-count');
    this.initializeMiniCart();
  }

  initializeMiniCart() {
    this.cartIcon.addEventListener('mouseenter', this.showMiniCart.bind(this));
    this.miniCart.addEventListener('mouseleave', this.hideMiniCart.bind(this));
    this.updateCartCount();
  }

  showMiniCart() {
    this.miniCart.classList.add('active');
    this.loadCartItems();
  }

  hideMiniCart() {
    this.miniCart.classList.remove('active');
  }

  async loadCartItems() {
    try {
      const response = await fetch('/api/cart/items');
      const cartData = await response.json();
      this.renderCartItems(cartData.items);
      this.updateCartTotal(cartData.total);
    } catch (error) {
      console.error('Cart loading error:', error);
    }
  }

  updateCartCount() {
    const count = this.getCartItemCount();
    this.cartCount.textContent = count;
    this.cartCount.style.display = count > 0 ? 'block' : 'none';
  }
}
```

## Mobile Responsiveness

### Mobile Header Implementation

```css
/* Mobile Header Styles */
@media (max-width: 768px) {
  .header-main {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
  }

  .main-navigation {
    display: none;
  }

  .mobile-menu-toggle {
    display: block;
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
  }

  .mobile-navigation {
    position: fixed;
    top: 0;
    left: -100%;
    width: 80%;
    height: 100vh;
    background: white;
    z-index: 9999;
    transition: left 0.3s ease;
    overflow-y: auto;
  }

  .mobile-navigation.active {
    left: 0;
  }

  .mobile-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: rgba(0,0,0,0.5);
    z-index: 9998;
    display: none;
  }

  .mobile-overlay.active {
    display: block;
  }
}
```

## Magento Integration

### GraphQL Queries

The header uses GraphQL queries to fetch navigation data:

```graphql
query getNavigationMenu {
  categoryList {
    id
    name
    url_key
    url_path
    children {
      id
      name
      url_key
      url_path
      children {
        id
        name
        url_key
        url_path
      }
    }
  }
}

query getCustomerInfo {
  customer {
    firstname
    lastname
    email
    is_subscribed
  }
}

query getCartSummary {
  cart {
    id
    total_quantity
    prices {
      grand_total {
        value
        currency
      }
    }
    items {
      id
      quantity
      product {
        name
        sku
        thumbnail {
          url
        }
      }
    }
  }
}
```

### Backend Configuration

Header behavior is configured in Magento admin:

1. **Content > Configuration > Design Configuration**
2. **Stores > Configuration > General > Web**
3. **Catalog > Navigation**
4. **Customer > Customer Configuration**

## Customization Guide

### Styling Customization

Header styling can be customized through:

```scss
// Header Variables
$header-height: 80px;
$header-bg-color: #ffffff;
$header-text-color: #333333;
$header-border-color: #e0e0e0;
$header-logo-max-height: 50px;

// Header Styles
.header {
  background: $header-bg-color;
  border-bottom: 1px solid $header-border-color;
  position: sticky;
  top: 0;
  z-index: 100;
  
  &-main {
    height: $header-height;
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }
  
  &-logo {
    img {
      max-height: $header-logo-max-height;
      width: auto;
    }
  }
}
```

### Logo Customization

To customize the header logo:

1. Replace logo image in `/static/images/logo.png`
2. Update logo dimensions in CSS
3. Ensure responsive scaling
4. Add appropriate alt text

## Performance Optimization

### Loading Strategies

- **Critical CSS**: Inline header styles for faster rendering
- **Lazy Loading**: Load non-critical header components after initial render
- **Caching**: Cache navigation menu data
- **Compression**: Optimize header images

### SEO Considerations

- **Structured Data**: Implement organization schema
- **Navigation Breadcrumbs**: Proper breadcrumb implementation
- **Internal Linking**: Strategic internal link placement
- **Mobile-First**: Mobile-optimized navigation structure

## Change History

| Date | Version | Author | Changes |
|------|---------|---------|---------|
| 2024-01-10 | 1.0 | Development Team | Initial header implementation |
| 2024-01-25 | 1.1 | Frontend Team | Added mobile responsiveness |
| 2024-02-10 | 1.2 | UX Team | Improved search functionality |
| 2024-02-25 | 1.3 | Development Team | Added mega menu support |

## Related Documentation

- [Footer Documentation](./footer.mdx)
- [Navigation Documentation](./navigation.mdx)
- [Search Documentation](./search.mdx)
- [Mobile Menu Documentation](./mobile-menu.mdx)

## Support

For technical support or questions regarding the Header implementation, please contact the development team or refer to the project documentation. 