---
id: homepage-values-block
title: '[ALS-163] Homepage Values Block'
version: 1.0.0
summary: Documentation for editing an HTML element placed on a CMS page or block using Magento Page Builder
confluencePageId: '4627136514'
owners:
    - euvic
---

# [ALS-163] Homepage Values Block

## Change History

| **Date** | **Author** | **Description of Change** |
| --- | --- | --- |
| 5/20/2025 | <PERSON><PERSON><PERSON><PERSON> | Initial version |
|   |  |  |

## **Purpose**

This document provides step-by-step instructions for editing an HTML element placed on a CMS page or block using **Magento Page Builder**. This applies to frontend users who need to update textual or visual content directly on the website.

## **Related Tasks**

Related to Jira task: ALS-163

## **Usage Scenario**

**Use case**:  
A marketing team member wants to update the promotional message inside a homepage banner that uses a custom HTML block in Page Builder.

## **Element preview**

![Homepage Values Block Preview](./images/homepage-values-block-preview.png)

## **Editing HTML Element in Page Builder**

### **Prerequisites**

* Admin access to Magento backend
* Knowledge of basic HTML (optional, depending on the element)

### **Steps to Edit HTML Content**

1. **Log in** to the Magento Admin Panel.
2. Go to one of the following:

    * **Content → Pages**, or
    * **Content → Blocks**
    
3. Find the page or block you want to edit (e.g., **Homepage** or **Footer Block**).
4. Click **Edit**.
5. In the content section, locate the **Page Builder** canvas.
6. Hover over the element you want to change (e.g., **HTML Code block**).
7. Click the **(gear icon) Edit** button.
8. An **HTML editor** modal will appear.
9. Modify the content as needed. Example:
10. Click **Save** to close the HTML editor.
11. Click **Save Page** or **Save Block**

## **Additional Notes**

The HTML code of the Values Block is described by the comment "Values Block HTML":

![Values Block HTML Comment](./images/values-block-html-comment.png)

## **Base HTML and CSS code of the element**

```html
<!-- Values block HTML -->


<style>
.values-block {
    display: grid;
    row-gap: 40px;
}

.values-block-wrapper {
    display: grid;
    grid-auto-flow: column;
    column-gap: 24px;
    align-items: start;
    justify-content: start;
}

.values-block-icon {
    display: grid;
    place-items: center;
}

.values-block-icon-border {
    display: grid;
    place-items: center;
    border-radius: 50%;
    background-color: #CFDBE0;
    width: 50px;
    height: 50px;
}

.values-block-content {
    display: grid;
    row-gap: 12px;
}

.values-block-title {
    font-size: 2.4rem;
    font-weight: var(--fontWeight-bold);
    color: rgb(var(--color-brand-base));
    line-height: 1.4;
}

.values-block-text {
    font-size: 1.6rem;
    color: #6C757D;
}

.values-block-link {
    display: grid;
    grid-auto-flow: column;
    align-items: center;
    justify-content: start;
    column-gap: 8px;
    margin-top: 12px;
    color: rgb(var(--color-brand-base));
    font-weight: var(--fontWeight-bold);
    text-decoration: none;
}

@media (min-width: 1024px) {
    .values-block {
        grid-template-columns: repeat(3, 1fr);
        justify-content: space-between;
        row-gap: 0;
    }
}
</style>

<div class="values-block">
    <div class="values-block-wrapper">
        <div class="values-block-icon">
            <div class="values-block-icon-border">
                <svg
                    fill="none"
                    height="24"
                    viewBox="0 0 24 24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <path
                        d="M10.5651 2.075C11.0132 1.861 11.5035 1.74994 12.0001 1.74994C12.4966 1.74994 12.9869 1.861 13.4351 2.075C13.8291 2.264 14.1901 2.572 14.6951 3.003L14.7741 3.069C15.2541 3.479 15.7131 3.673 16.3541 3.724L16.4561 3.732C17.1181 3.785 17.5911 3.822 18.0031 3.968C18.4713 4.13343 18.8966 4.40149 19.2479 4.75256C19.5991 5.10364 19.8674 5.52883 20.0331 5.997C20.1781 6.409 20.2151 6.882 20.2681 7.544L20.2761 7.646C20.3271 8.287 20.5221 8.746 20.9311 9.226L20.9971 9.304C21.4281 9.81 21.7371 10.171 21.9251 10.565C22.1391 11.0131 22.2501 11.5034 22.2501 12C22.2501 12.4966 22.1391 12.9869 21.9251 13.435C21.7361 13.829 21.4281 14.19 20.9971 14.695L20.9311 14.774C20.5131 15.264 20.3261 15.725 20.2761 16.354L20.2681 16.456C20.2151 17.118 20.1781 17.591 20.0321 18.003C19.8666 18.4712 19.5986 18.8966 19.2475 19.2478C18.8964 19.5991 18.4712 19.8673 18.0031 20.033C17.5911 20.178 17.1181 20.215 16.4561 20.268L16.3541 20.276C15.7131 20.327 15.2541 20.522 14.7741 20.931L14.6951 20.997C14.1901 21.428 13.8291 21.737 13.4351 21.925C12.9869 22.139 12.4966 22.2501 12.0001 22.2501C11.5035 22.2501 11.0132 22.139 10.5651 21.925C10.1711 21.736 9.81006 21.428 9.30506 20.997L9.22606 20.931C8.79006 20.538 8.23224 20.3067 7.64606 20.276L7.54406 20.268C6.88206 20.215 6.40906 20.178 5.99706 20.032C5.52881 19.8666 5.10349 19.5985 4.75224 19.2474C4.40099 18.8964 4.13272 18.4712 3.96706 18.003C3.82206 17.591 3.78506 17.118 3.73206 16.456L3.72406 16.354C3.69332 15.7678 3.46208 15.21 3.06906 14.774L3.00306 14.695C2.57206 14.19 2.26306 13.829 2.07506 13.435C1.86106 12.9869 1.75 12.4966 1.75 12C1.75 11.5034 1.86106 11.0131 2.07506 10.565C2.26406 10.171 2.57206 9.81 3.00306 9.305L3.06906 9.226C3.46208 8.79 3.69332 8.23218 3.72406 7.646L3.73206 7.544C3.78506 6.882 3.82206 6.409 3.96806 5.997C4.13349 5.52875 4.40155 5.10342 4.75263 4.75218C5.1037 4.40093 5.52889 4.13266 5.99706 3.967C6.40906 3.822 6.88206 3.785 7.54406 3.732L7.64606 3.724C8.23224 3.69326 8.79006 3.46202 9.22606 3.069L9.30406 3.003C9.81006 2.572 10.1711 2.263 10.5651 2.075ZM10.7501 9C10.7501 8.80108 10.671 8.61032 10.5304 8.46967C10.3897 8.32902 10.199 8.25 10.0001 8.25C9.80115 8.25 9.61038 8.32902 9.46973 8.46967C9.32908 8.61032 9.25006 8.80108 9.25006 9V13.95C9.25006 14.1489 9.32908 14.3397 9.46973 14.4803C9.61038 14.621 9.80115 14.7 10.0001 14.7H14.9501C15.149 14.7 15.3397 14.621 15.4804 14.4803C15.621 14.3397 15.7001 14.1489 15.7001 13.95C15.7001 13.7511 15.621 13.5603 15.4804 13.4197C15.3397 13.279 15.149 13.2 14.9501 13.2H10.7501V9Z"
                        fill="#002854"
                    />
                </svg>
            </div>
        </div>
        <div class="values-block-content">
            <span class="values-block-title">Fast Shipping</span>
            <span class="values-block-text">Most orders ship the same day</span>
            <a class="values-block-link" href="#">
                View delivery details
                <svg
                    fill="none"
                    height="24"
                    viewBox="0 0 24 24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <path
                        d="M10.75 8.5L14.25 12L10.75 15.5"
                        stroke="#002854"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="1.5"
                    />
                    <path
                        d="M21 12C21 13.1819 20.7672 14.3522 20.3149 15.4442C19.8626 16.5361 19.1997 17.5282 18.364 18.364C17.5282 19.1997 16.5361 19.8626 15.4442 20.3149C14.3522 20.7672 13.1819 21 12 21C10.8181 21 9.64778 20.7672 8.55585 20.3149C7.46392 19.8626 6.47177 19.1997 5.63604 18.364C4.80031 17.5282 4.13738 16.5361 3.68508 15.4442C3.23279 14.3522 3 13.1819 3 12C3 9.61305 3.94821 7.32387 5.63604 5.63604C7.32387 3.94821 9.61305 3 12 3C14.3869 3 16.6761 3.94821 18.364 5.63604C20.0518 7.32387 21 9.61305 21 12Z"
                        stroke="#002854"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="1.5"
                    />
                </svg>
            </a>
        </div>
    </div>
    <div class="values-block-wrapper">
        <div class="values-block-icon">
            <div class="values-block-icon-border">
                <svg
                    fill="none"
                    height="24"
                    viewBox="0 0 24 24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <path
                        d="M10.68 2.10498L4.07001 5.90498L4.06801 5.90698C3.66704 6.14011 3.33429 6.4744 3.103 6.87643C2.87171 7.27847 2.74999 7.73416 2.75001 8.19798V15.801C2.74881 16.2652 2.87001 16.7215 3.10141 17.1239C3.33281 17.5263 3.66621 17.8605 4.06801 18.093L4.07101 18.095L10.679 21.894H10.681C11.0819 22.1265 11.5371 22.2489 12.0005 22.2489C12.4639 22.2489 12.9191 22.1265 13.32 21.894H13.321L19.929 18.094H19.932C20.3334 17.8606 20.6665 17.5258 20.8978 17.1232C21.1291 16.7206 21.2506 16.2643 21.25 15.8V8.19998C21.2502 7.73599 21.1286 7.28009 20.8973 6.87787C20.666 6.47564 20.3331 6.1412 19.932 5.90798L13.322 2.10798L13.32 2.10598C12.919 1.87329 12.4636 1.75073 12 1.75073C11.5364 1.75073 11.081 1.87229 10.68 2.10498ZM8.75001 12C8.75001 11.138 9.09242 10.3114 9.70191 9.70188C10.3114 9.09239 11.1381 8.74998 12 8.74998C12.862 8.74998 13.6886 9.09239 14.2981 9.70188C14.9076 10.3114 15.25 11.138 15.25 12C15.25 12.8619 14.9076 13.6886 14.2981 14.2981C13.6886 14.9076 12.862 15.25 12 15.25C11.1381 15.25 10.3114 14.9076 9.70191 14.2981C9.09242 13.6886 8.75001 12.8619 8.75001 12Z"
                        fill="#002854"
                    />
                </svg>
            </div>
        </div>
        <div class="values-block-content">
            <span class="values-block-title">Largest Selection</span>
            <span class="values-block-text">
                Over 150,000 laundry parts online
            </span>
            <a class="values-block-link" href="#">
                Explore all parts
                <svg
                    fill="none"
                    height="24"
                    viewBox="0 0 24 24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <path
                        d="M10.75 8.5L14.25 12L10.75 15.5"
                        stroke="#002854"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="1.5"
                    />
                    <path
                        d="M21 12C21 13.1819 20.7672 14.3522 20.3149 15.4442C19.8626 16.5361 19.1997 17.5282 18.364 18.364C17.5282 19.1997 16.5361 19.8626 15.4442 20.3149C14.3522 20.7672 13.1819 21 12 21C10.8181 21 9.64778 20.7672 8.55585 20.3149C7.46392 19.8626 6.47177 19.1997 5.63604 18.364C4.80031 17.5282 4.13738 16.5361 3.68508 15.4442C3.23279 14.3522 3 13.1819 3 12C3 9.61305 3.94821 7.32387 5.63604 5.63604C7.32387 3.94821 9.61305 3 12 3C14.3869 3 16.6761 3.94821 18.364 5.63604C20.0518 7.32387 21 9.61305 21 12Z"
                        stroke="#002854"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="1.5"
                    />
                </svg>
            </a>
        </div>
    </div>
    <div class="values-block-wrapper">
        <div class="values-block-icon">
            <div class="values-block-icon-border">
                <svg
                    fill="none"
                    height="24"
                    viewBox="0 0 24 24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <path
                        d="M10.186 13.814L11.093 11.093L13.814 10.186L12.907 12.907L10.186 13.814Z"
                        fill="#002854"
                    />
                    <path
                        d="M2.25 12C2.25 6.615 6.615 2.25 12 2.25C17.385 2.25 21.75 6.615 21.75 12C21.75 17.385 17.385 21.75 12 21.75C6.615 21.75 2.25 17.385 2.25 12ZM15.712 9.237C15.7563 9.10471 15.7628 8.96269 15.7308 8.82691C15.6988 8.69112 15.6295 8.56696 15.5308 8.46837C15.4321 8.36978 15.3079 8.30066 15.1721 8.2688C15.0363 8.23694 14.8942 8.24359 14.762 8.288L10.262 9.788C10.1517 9.82506 10.0516 9.88727 9.96948 9.96972C9.88739 10.0522 9.8256 10.1526 9.789 10.263L8.289 14.763C8.2452 14.895 8.23898 15.0366 8.27104 15.172C8.3031 15.3073 8.37216 15.4311 8.47052 15.5295C8.56888 15.6278 8.69266 15.6969 8.82801 15.729C8.96337 15.761 9.10497 15.7548 9.237 15.711L13.737 14.211C13.8474 14.1742 13.9476 14.1122 14.0299 14.0299C14.1122 13.9476 14.1742 13.8474 14.211 13.737L15.712 9.237Z"
                        fill="#002854"
                    />
                </svg>
            </div>
        </div>
        <div class="values-block-content">
            <span class="values-block-title">Manuals and Diagrams</span>
            <span class="values-block-text">Over 100,000 manuals online</span>
            <a class="values-block-link" href="#">
                Find needed materials
                <svg
                    fill="none"
                    height="24"
                    viewBox="0 0 24 24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <path
                        d="M10.75 8.5L14.25 12L10.75 15.5"
                        stroke="#002854"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="1.5"
                    />
                    <path
                        d="M21 12C21 13.1819 20.7672 14.3522 20.3149 15.4442C19.8626 16.5361 19.1997 17.5282 18.364 18.364C17.5282 19.1997 16.5361 19.8626 15.4442 20.3149C14.3522 20.7672 13.1819 21 12 21C10.8181 21 9.64778 20.7672 8.55585 20.3149C7.46392 19.8626 6.47177 19.1997 5.63604 18.364C4.80031 17.5282 4.13738 16.5361 3.68508 15.4442C3.23279 14.3522 3 13.1819 3 12C3 9.61305 3.94821 7.32387 5.63604 5.63604C7.32387 3.94821 9.61305 3 12 3C14.3869 3 16.6761 3.94821 18.364 5.63604C20.0518 7.32387 21 9.61305 21 12Z"
                        stroke="#002854"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="1.5"
                    />
                </svg>
            </a>
        </div>
    </div>
</div>