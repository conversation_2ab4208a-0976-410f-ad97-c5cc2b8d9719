---
id: pwa-frontend-documentation
title: PWA Frontend Documentation
version: 1.0.0
summary: Complete documentation for PWA frontend components and pages
owners:
    - euvic
---

# PWA Frontend Documentation

This documentation covers all aspects of the Progressive Web Application (PWA) frontend, including data sources, page components, and layout elements.

## Documentation Sections

### Data Sources
- **[Pages Data Source](./pages-data-source)** - Information about data sources used across different pages

### Page Components
- **[Homepage Values Block](./homepage-values-block)** - Homepage promotional values section
- **[Category Left Right Block](./category-left-right-block)** - Three-section promotional layout with Special Offers, Repair Help, and Manuals
- **[Brands Block](./brands-block)** - Brand logos display component

### Layout Components
- **[Preheader](./preheader)** - Store contact information displayed before the main header
- **[Header](./header)** - Main navigation with logo, search functionality, and category menu
- **[Footer](./footer)** - Site-wide footer with links and information

### Pages
- **[Category Page](./category-page)** - Product category listing and navigation

## Overview

The PWA frontend is built using modern web technologies and follows best practices for:
- **Performance**: Fast loading times and optimized assets
- **Accessibility**: WCAG compliant components and navigation
- **Responsiveness**: Mobile-first design that works across all devices
- **SEO**: Search engine optimized structure and content

## Getting Started

Each component documentation includes:
- Purpose and usage scenarios
- Configuration instructions
- Technical implementation details
- Styling and customization options
- Maintenance guidelines

## Related Jira Tasks

The components documented here relate to various Jira tasks in the ALS project:
- ALS-163: Homepage values block
- ALS-164: Category left right block
- ALS-165: Brands block
- ALS-181: Preheader
- ALS-182: Header
- ALS-60: Footer
- ALS-59: Category page

## Support

For technical support or questions about these components, please refer to the individual component documentation or contact the development team. 