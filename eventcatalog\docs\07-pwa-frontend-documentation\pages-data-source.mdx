---
id: pages-data-source
name: Pages Data Source
title: Pages Data Source
summary: "Pages Data Source PWA Frontend Documentation"
owners:
  - euvic
tags:
  - pwa
  - frontend
  - pages
  - data
  - source
  - magento
confluencePageId: "4666687489"
---

# Pages Data Source

## PDP

| **Scope** | **System** | **Comment** |
| --- | --- | --- |
| product info | PIM |  |
| stock/availability | Magento |  |
| lead time | PIM |  |
| prices | Magento |  |
| related products | Algolia manual link: PIM | check PIM options to link products |
| Images | PIM |  |
| TAB Category | PIM | tbc |
| TAB Q&A | TurnTo |  |
| TAB reviews | TurnTo |  |
| Breadcrumbs | Magento |  |
| Manuals and diagrams | PIM | tbc |

## Category

| **Scope** | **System** | **Comment** |
| --- | --- | --- |
| URL route | Magento |  |
| Breadcrumbs | Magento |  |
| Sort Fields options | Magento |  |
| Category base data (name, url) | Magento |  |
| Top description | Magento |  |
| Bottom description (SEO) | Magento |  |
| Filters aggregation | Magento | tbc |
| Filters schema ( required to create valid search query ) | Magento | tbc |
| Product base data (name, url, sku) → Stock, prices | PDP Magento | tbc |
| Page size configuration | Magento |  |

## Search Result

| **Scope** | **System** | **Comment** |
| --- | --- | --- |
|  |  |  |
|  |  |  |
|  |  |  |

## Search

| **Scope** | **System** | **Comment** |
| --- | --- | --- |
|  |  |  |
|  |  |  |
|  |  |  |

## Homepage

| **Scope** | **System** | **Comment** |
| --- | --- | --- |
|  |  |  |
|  |  |  |
|  |  |  |

## Layout

| **Scope** | **System** | **Comment** |
| --- | --- | --- |
|  |  |  |
|  |  |  |
|  |  |  |

## Cart

| **Scope** | **System** | **Comment** |
| --- | --- | --- |
|  |  |  |
|  |  |  |
|  |  |  |

## Checkout

| **Scope** | **System** | **Comment** |
| --- | --- | --- |
|  |  |  |
|  |  |  |
|  |  |  |

## My Account

| **Scope** | **System** | **Comment** |
| --- | --- | --- |
|  |  |  | 