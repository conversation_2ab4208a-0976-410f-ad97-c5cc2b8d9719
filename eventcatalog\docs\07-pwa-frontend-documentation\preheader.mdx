---
name: Preheader
title: Preheader
summary: "Preheader PWA Frontend Documentation"
owners:
  - euvic
tags:
  - pwa
  - frontend
  - preheader
  - header
  - magento
confluencePageId: "4650107035"
---

# [ALS-181] Preheader

## Change History

| **Date** | **Author** | **Description of Change** |
| --- | --- | --- |
| 6/5/2025 | Rafał Słowik | Initial version |
|   |   |   |

## **Purpose**

This document provides details about element placed on a page before header. This applies to frontend users who need to update textual or visual content on the website.

## **Related Tasks**

Related to Jira task: ALS-181

## **Usage Scenario**

**Use case**:  
A marketing team member wants to update shop phone number

## **Element preview**

![Preheader Preview](./images/preheader-preview.png)

## **Editing store phone number in Magento backend**

### **Prerequisites**

* Admin access to Magento backend

### **Steps to Edit HTML Content**

1. **Log in** to the Magento Admin Panel.
2. Go to one of the following:

    * **Stores → Configuration → General → General**
    
3. Find and expand accordion **Store Information** 
4. In the content section, locate the **Store Phone Number** input.
5. Fill input with valid store phone
6. Click **Save Config** button located on the top of page.

## **Additional Notes**

New phone number can release on website with delay 