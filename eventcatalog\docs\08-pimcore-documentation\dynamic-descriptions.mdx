---
id: dynamic-descriptions
title: Dynamic Descriptions
version: 1.0.0
summary: SEO template functionality and content automation in PIMCORE
owners:
    - euvic
---

# [PIM-SEO] Dynamic Descriptions

## Change History

| **Date** | **Author** | **Description of Change** |
|----------|------------|---------------------------|
| 6/20/2025 | Leszek Kruk | Initial version |

## Purpose

Dynamic descriptions functionality is designed to allow creation of unique templates that provide content for products. This feature enables automated content generation using token-based templating system.

## Related Task

- [ALS-86: Dynamic Descriptions Implementation](https://fwc-commerce.atlassian.net/browse/ALS-86)

## Feature Description

### 1. SeoTemplate Object Structure

In Pimcore, there is a created object called `SeoTemplate` with the following field definition:

![SeoTemplate Field Definition](./images/image-20250620-083225.png)

### 2. Field Correspondence

Fields in `SeoTemplate` correspond to fields in `Products` objects. This feature allows addition of new fields in the future, providing flexibility for content expansion.

### 3. Website Settings Configuration

In system `Website Settings` section under the key `seo_template_objects`, we define which classes are allowed to use templates (comma separated):

![Website Settings Configuration](./images/image-20250620-084309.png)

### 4. Creating SEO Templates

In Data Object section, we can create multiple `SeoTemplates`:

![SEO Templates List](./images/image-20250620-084852.png)

### 5. Template Content Configuration

Example `SeoTemplate` Content can be configured differently per available language:

![SEO Template Content Example](./images/image-20250620-085036.png)

### 6. Token System

Words in curly brackets like `{sku}` are considered variables from Product (called tokens). As a token, we can use every field available in Product Class.

#### Token Limitations

**Important restrictions when using tokens:**

1. **Avoid recursive references**: Don't use token `{description}` in field called description - it will cause system malfunction
2. **Avoid Date fields**: Date fields should not be used as tokens
3. **Avoid Relation fields**: Relation fields should not be used as tokens  
4. **Avoid Gallery fields**: Gallery fields should not be used as tokens

### 7. Product Template Selection

During Product Configuration, there is a Template select field that aggregates all available templates from Data objects section:

![Product Template Selection](./images/image-20250620-091733.png)

### 8. Service Configuration

Aggregation of data is done by assigning `@template.options-provider` service in `seo_template` select field:

![Service Configuration](./images/image-20250620-091838.png)

### 9. Template Processing Mechanism

Selecting any template from `seo_template` field and saving products triggers a special mechanism which:

1. **During the save process**: Sets values from the template into corresponding fields in the product
2. **Token replacement**: Simultaneously replaces tokens with specific values from the object
3. **Real-time processing**: Occurs automatically during product save operation

## Error Handling

Because the process takes place during object save, we do not interrupt this process and do not show popup messages to maintain user experience.

### Error Logging

If there are any indications that an error has occurred and data has not been filled correctly, all information about the error is recorded in the **Application Logger**.

![Application Logger](./images/image-20250620-092317.png)

### Error Example

Example error log entry:

![Error Log Example](./images/image-20250620-093300.png)

This shows detailed error information including:
- Timestamp of the error
- Specific error message
- Context information for debugging
- Stack trace when applicable

## Technical Implementation

### Template Processing Workflow

1. **Template Selection**: User selects a template from the dropdown in product configuration
2. **Save Trigger**: Product save operation initiates template processing
3. **Token Parsing**: System identifies all tokens (fields in curly brackets) in template
4. **Value Replacement**: Tokens are replaced with actual product field values
5. **Field Population**: Processed content is saved to corresponding product fields
6. **Error Logging**: Any processing errors are logged to Application Logger

### Performance Considerations

- Template processing occurs during save operation
- No real-time preview during template selection
- Bulk operations should be monitored for performance impact
- Error handling is non-blocking to maintain system responsiveness

### Best Practices

1. **Template Design**: 
   - Use clear, descriptive template names
   - Test templates with sample products before production use
   - Avoid complex nested token structures

2. **Token Usage**:
   - Use only simple field types as tokens
   - Verify token field names match product class definitions
   - Document custom tokens for team reference

3. **Error Management**:
   - Regularly monitor Application Logger for template errors
   - Test template changes in development environment first
   - Maintain backup templates for critical content

## Multi-Language Support

The SEO template system supports multi-language content generation:

- Templates can be configured per language
- Token replacement works across all configured languages
- Language-specific content rules can be applied
- Fallback mechanisms for missing language content

## Integration Points

### Content Management System
- Templates integrate with PIMCORE's content management workflows
- Support for approval processes and content versioning
- Integration with publishing workflows

### E-commerce Platform Integration
- Generated content synchronizes with Magento
- SEO metadata propagation to frontend systems
- Product description updates reflected in catalog

### API Access
- Template configuration available via PIMCORE API
- Programmatic template processing for bulk operations
- Integration with external content management tools

## Troubleshooting

### Common Issues

1. **Token Not Replaced**: 
   - Verify token name matches product field exactly
   - Check field type compatibility
   - Review Application Logger for specific errors

2. **Template Not Applied**:
   - Confirm template is saved properly
   - Verify Website Settings configuration
   - Check user permissions for template access

3. **Performance Issues**:
   - Monitor bulk template processing operations
   - Consider template complexity and token count
   - Review server resources during processing

### Support Resources

For technical support with Dynamic Descriptions:
- Check Application Logger for detailed error information
- Review template configuration in Data Objects section
- Verify Website Settings for proper class configuration
- Contact development team for complex template requirements 