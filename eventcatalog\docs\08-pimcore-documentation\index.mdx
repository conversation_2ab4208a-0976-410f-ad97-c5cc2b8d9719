---
title: PIMCORE Documentation
id: pimcore-documentation
sidebar_position: 8
summary: Complete PIMCORE Product Information Management system documentation for Alliance Laundry Systems
owners:
    - euvic
---

# PIMCORE Documentation

## Change History

| Date | Author | Description of Change |
|------|--------|----------------------|
| 2025-04-17 | System | Initial version (migrated from Confluence) |
| 2025-06-27 | System Migration | Migrated to EventCatalog |
| 2025-06-26 | Katarzyna Krzyt | Updated product structure diagram |
| 2025-06-24 | Leszek Kruk | Added GraphQL configuration |
| 2025-06-20 | Leszek <PERSON> | Added Dynamic Descriptions functionality |
| 2025-06-13 | Katarzyna Krzyt | Added Price Management processes |

## Overview

This section contains the complete PIMCORE Product Information Management system documentation for Alliance Laundry Systems. PIMCORE serves as the central hub for product data management, integration, and content delivery across multiple channels.

## Documentation Structure

The PIMCORE documentation is organized into the following core components:

### Core Components

- **[PIMCORE Attributes](pimcore-attributes)** - Product attribute management and configuration
- **[PIMCORE - Magento Integration](pimcore-magento)** - Integration between PIMCORE and Magento e-commerce platform
- **[Dynamic Descriptions](dynamic-descriptions)** - SEO template functionality and content automation
- **[Pimcore GraphQL](pimcore-graphql)** - GraphQL API configuration and usage documentation
- **[Price Management](#price-management)** - Comprehensive price management processes and workflows

## System Purpose

PIMCORE provides comprehensive product information management capabilities including:

- **Product Master Data Management**: Centralized product catalog with hierarchical structures
- **Digital Asset Management**: Image and media file storage with automated processing
- **Content Management**: Product descriptions and marketing content with multi-language support
- **E-commerce Integration**: Real-time synchronization with Magento and other platforms
- **API-First Architecture**: RESTful APIs and GraphQL endpoints for flexible integration
- **Advanced Price Management**: Multi-tier pricing with workflow approvals and audit trails

## Product Structure

The PIMCORE system implements a sophisticated product structure that supports the complex requirements of Alliance Laundry Systems. The diagram below illustrates the complete product hierarchy and relationships within the system:

![PIMCORE Product Structure](./images/PIMCORE_product_structure.drawio.png)

### Product Hierarchy Components

The product structure includes several key components:

- **Product Categories**: Hierarchical organization of products by type and functionality
- **Product Variants**: Different configurations and options for each product model
- **Attributes and Properties**: Comprehensive product specifications and characteristics
- **Relationships**: Cross-references between related products, accessories, and replacement parts
- **Digital Assets**: Associated images, documentation, and multimedia content

### Key Entities & Attributes

#### 1. **Sales Organisation**
- `Sales Organisation ID`: Identifier
- `name`: Name of the sales entity
- `plant: List&lt;Plant&gt;`: One-to-many relationship with **Plant**

#### 2. **Plant**
- `name`: Plant name
- **Relationships**:
  - Belongs to one `Sales Organisation`
  - Holds multiple `Product` instances
  - Has associated `Stock` levels

#### 3. **Product**
- `name`, `SKU`, `description`, `status`: Core attributes
- `visibility`: List of visibility rules in Magento 2
- `salesOrganisation ID`: FK to Sales Organisation
- `productClass`: Product type in SAP
- `typeId`: FK to `&lt;&lt;typeId&gt;&gt;` (Simple, Configurable, Bundle, Virtual)
- `attributeSetId`: Set of grouped attributes
- `image`: Reference to image asset
- `taxClassId`: FK to taxation class
- `category`: List of categories associated with the product
- **Relationships**: Linked to `Plant`, `Sales Organisation`, `Price`, `Stock`

#### 4. **Price**
- `SKU`, `price`, `specialPrice`, `currency`
- `validFrom` / `validTo`: Pricing time windows
- `tierPrice: List&lt;tierPrice&gt;`: Optional tiered pricing (volume-based)

#### 5. **tierPrice**
- `SKU`, `validFrom` / `validTo`
- `quantity`, `price`: Pricing dependent on purchase quantity

#### 6. **Stock**
- `SKU`, `quantity`, `plant`: Stock level per plant

#### 7. **&lt;&lt;typeId&gt;&gt; (Product Types)**
- Contains: `simple`, `configurable`, `bundle`, `virtual`
- Used to classify products for logic/rendering (in Magento 2)

### Data Flow Integration

The product structure supports seamless data flow between systems:

![PIMCORE Data Flow](./images/image-20250626-085657.png)

This integration ensures that product information is consistently maintained across all platforms while supporting the specific business requirements of Alliance Laundry Systems.

### Process Description

1. Each Product object in PIMCORE needs to have an association with the plant object in order to retrieve the attribute data from the Plant
2. Each Plant object in PIMCORE needs to have an association with the Sales Organization object in order to retrieve the attribute data from the Sales Organization
3. SKU can exist in more than one Plant object

## Price Management

PIMCORE includes comprehensive price management functionality with role-based access control, workflow approvals, and integration with SAP and Magento systems.

### Key Features

#### 1. User Roles and Permissions
- Only users with the **designated admin role** can:
  - Edit product prices
  - Change product workflow statuses
- Non-admin users cannot see or access price management UI elements

#### 2. Supported Price Fields
Admin users can update the following price-related fields:
- **List Price / Web List** (Magento Regular Price)
- **Web Sale Price** (Magento Special Price)
- **Web GM Min.** – minimum margin (percentage of List Price)
- **Valid From** – effective start date (for SAP)
- **Valid To** – effective end date (for SAP)
- **Tier Price** – unit price based on quantity
- **Tier Quantity** – quantity threshold for tier pricing
- Products can have **multiple tier prices** (tier pricing matrix)

#### 3. Price Update Methods
Admin users can update prices using:
- Manual edit on the product detail page
- Mass action on selected groups of products
- Excel file upload with predefined format

#### 4. Excel Import Validation
- System validates Excel files before saving changes
- Shows errors with row numbers and specific error messages
- Pre-validation step available before final upload
- Invalid conditions include:
  - Incorrect data types
  - Missing required fields
  - Invalid tier pricing logic
  - **Valid To &lt; Valid From** validation errors

#### 5. Logging and Audit
All price updates are logged with:
- Admin username
- Timestamp
- Fields updated (old and new values)
- Optional comments
- Logs accessible per product version

#### 6. Workflow and Approval Process
- Admin users can submit comments when sending updates for approval
- Manual flagging of price changes as **non-standard** (requires approval)
- Automatic flagging when price changes exceed predefined thresholds (e.g., &gt;10%)

#### 7. Discount Class and Net Price Calculation
- Each product has a `Discount Class` defined by manufacturer
- Discount Class contains `NetPriceFactorFromList` (e.g., 0.42)
- Net Price = List Price × NetPriceFactorFromList
- Each manufacturer must have at least one Discount Class defined
- Discount Class "X" serves as default fallback

#### 8. Bulk Price Modification
Admin users can:
- Filter products by attributes (e.g., custom Yes/No flags)
- Apply percentage increases/decreases to prices in bulk
- Preview price changes before saving

#### 9. System Integration
- All price updates pushed via **Integration Layer API**
- Custom fields track sync status for SAP and Magento:
  - "Pending Sync", "Success", or "Failed"
- Sync logs available per product for traceability

#### 10. Versioning and Rollback
- Admin users can restore previous product versions including price changes
- Mass import/update rollback capability for linked import events

## Key Features

### Integration Capabilities
- SAP system synchronization for product master data
- Magento e-commerce platform integration
- PWA frontend connectivity via GraphQL
- Real-time data synchronization and event-driven updates

### Content Management
- Template-based content generation with Dynamic Descriptions
- SEO optimization tools with token-based content automation
- Multi-language content support
- Workflow and approval processes

### Technical Architecture
- API-first design with REST and GraphQL endpoints
- Event-driven architecture for real-time updates
- Scalable enterprise-grade platform
- High availability and performance optimization

## Getting Started

For detailed information on specific PIMCORE components, navigate to the relevant documentation sections above. Each section provides comprehensive technical specifications, configuration details, and integration guidelines.

## Support and Resources

For additional support or technical questions regarding PIMCORE implementation, please refer to the individual component documentation or contact the development team. 