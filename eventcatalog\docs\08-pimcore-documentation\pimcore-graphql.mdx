---
id: pimcore-graphql
title: Pimcore GraphQL
version: 1.0.0
summary: GraphQL API configuration and usage documentation for PIMCORE
owners:
    - euvic
---

# [Pim-GQL] Pimcore GraphQL

## Change History

| **Date** | **Author** | **Description of Change** |
|----------|------------|---------------------------|
| 6/24/2025 | Leszek Kruk | Initial version |

## Purpose

Pimcore GraphQL configuration was created to allow communication between frontend PWA application and Pimcore data objects. This provides a modern, efficient API interface for accessing product data and related information.

## Related Task

- [ALS-283: GraphQL Implementation](https://fwc-commerce.atlassian.net/browse/ALS-283)

## GraphQL Endpoint Configuration

### Test Environment Endpoint

```
https://pim.alliance.fwc.pl/pimcore-graphql-webservices/graphql
```

**Important Notes:**
- The last part of URL `graphql` is fully customized and can be named differently
- This endpoint provides access to all PIMCORE data objects
- Designed specifically for PWA frontend integration

### Authentication

#### API Key Authorization

```
591196e51f979f8cc250878f8277e372
```

**Access Permissions:**
- Access is allowed to all objects available in the system
- Full read access to product data, categories, and related entities
- Suitable for frontend application integration

#### Additional Security

- Authorization by .htaccess login and password
- VPN access requirements for additional security layer
- Environment-specific access controls

## GraphQL Playground and Documentation

### Explorer Interface

All documentation about queries, available fields, objects, and usage examples is available in the playground explorer:

```
https://pim.alliance.fwc.pl/pimcore-datahub-webservices/explorer/graphql?apikey=591196e51f979f8cc250878f8277e372
```

### Features Available in Playground

1. **Interactive Query Builder**: Build and test GraphQL queries in real-time
2. **Schema Explorer**: Browse all available types, fields, and relationships
3. **Documentation Tab**: Comprehensive API documentation in `DOCS` tab
4. **Query Examples**: Sample queries for common use cases
5. **Real-time Validation**: Query syntax validation and error reporting

## API Capabilities

### Data Access

The GraphQL API provides access to:

- **Product Objects**: Complete product information including attributes, pricing, and relationships
- **Category Structures**: Hierarchical category data with nested relationships  
- **Digital Assets**: Images, documents, and media files associated with products
- **Inventory Data**: Stock levels and availability information
- **Pricing Information**: List prices, special prices, and tier pricing structures
- **Localization Data**: Multi-language content and region-specific information

### Query Features

- **Flexible Field Selection**: Request only the data fields needed
- **Nested Relationships**: Query related objects in single requests
- **Filtering and Sorting**: Advanced filtering capabilities for precise data retrieval
- **Pagination Support**: Efficient handling of large datasets
- **Real-time Data**: Direct access to current PIMCORE data

## Integration Architecture

### PWA Frontend Integration

```mermaid
graph LR
    A[PWA Frontend] --> B[GraphQL API]
    B --> C[PIMCORE Core]
    C --> D[Product Data]
    C --> E[Digital Assets]
    C --> F[Category Data]
```

### Data Flow

1. **PWA Request**: Frontend application sends GraphQL queries
2. **API Processing**: GraphQL endpoint processes and validates queries
3. **Data Retrieval**: PIMCORE core retrieves requested data
4. **Response Formation**: Data formatted according to GraphQL schema
5. **Frontend Rendering**: PWA receives structured data for display

## Common Query Examples

### Basic Product Query

```graphql
query GetProduct($id: ID!) {
  getProduct(id: $id) {
    id
    sku
    name
    description
    price
    image {
      url
      alt
    }
    categories {
      name
      path
    }
  }
}
```

### Product Listing with Filtering

```graphql
query GetProducts($filter: ProductFilter, $limit: Int, $offset: Int) {
  getProducts(filter: $filter, limit: $limit, offset: $offset) {
    items {
      id
      sku
      name
      price
      image {
        url
      }
    }
    totalCount
  }
}
```

### Category Tree Query

```graphql
query GetCategoryTree {
  getCategories {
    id
    name
    children {
      id
      name
      children {
        id
        name
      }
    }
  }
}
```

## Performance Optimization

### Query Optimization

- **Field Selection**: Only request needed fields to minimize data transfer
- **Depth Limiting**: Avoid deeply nested queries that impact performance
- **Caching Strategy**: Implement appropriate caching for frequently accessed data
- **Batch Requests**: Use query batching for multiple related requests

### Best Practices

1. **Efficient Queries**: Design queries to minimize server load
2. **Error Handling**: Implement robust error handling for API failures
3. **Rate Limiting**: Respect API rate limits and implement backoff strategies
4. **Data Validation**: Validate received data on frontend before processing

## Security Considerations

### API Security

- **API Key Management**: Secure storage and rotation of API keys
- **Access Control**: Implement proper access controls in frontend applications
- **HTTPS Only**: All API communication must use HTTPS
- **Input Validation**: Validate all query parameters and variables

### Environment Configuration

- **Development**: Use development API keys and endpoints for testing
- **Production**: Implement production-specific security measures
- **Monitoring**: Log API usage and monitor for suspicious activity

## Troubleshooting

### Common Issues

1. **Authentication Failures**:
   - Verify API key is correct and active
   - Check VPN connection if required
   - Confirm .htaccess credentials if applicable

2. **Query Errors**:
   - Use GraphQL playground to validate query syntax
   - Check field names against schema documentation
   - Verify variable types match schema requirements

3. **Performance Issues**:
   - Review query complexity and depth
   - Implement appropriate caching strategies
   - Consider query optimization techniques

### Debug Resources

- **GraphQL Playground**: Test queries and explore schema
- **API Logs**: Review server logs for detailed error information
- **Schema Documentation**: Reference complete API documentation
- **Development Tools**: Use browser developer tools for network debugging

## Migration and Updates

### Version Management

- Monitor API version updates and deprecations
- Test applications against new API versions before deployment
- Implement version-specific error handling

### Schema Evolution

- GraphQL schema may evolve with new fields and types
- Backward compatibility maintained for existing queries
- New features announced through proper channels

## Support and Resources

### Technical Support

For GraphQL API support:
- Use GraphQL playground for query testing and validation
- Review comprehensive documentation in explorer DOCS tab
- Contact development team for complex integration requirements
- Monitor API status and updates through designated channels

### Additional Resources

- **GraphQL Specification**: Official GraphQL documentation
- **PIMCORE Documentation**: Core PIMCORE system documentation  
- **PWA Integration Guides**: Frontend integration best practices
- **Performance Guidelines**: API optimization recommendations 