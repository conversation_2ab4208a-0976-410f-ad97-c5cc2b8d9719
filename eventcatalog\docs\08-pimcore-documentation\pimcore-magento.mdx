---
title: PIMCORE - Magento Integration
id: pimcore-magento
sidebar_position: 3
summary: Integration between PIMCORE Product Information Management and Magento e-commerce platform
owners:
    - euvic
---

# [PIM-MAG] PIMCORE - Magento

## Change History

| Date | Author | Description of Change |
|------|--------|----------------------|
| 2025-04-17 | System | Initial version (migrated from Confluence) |
| 2025-06-27 | System Migration | Migrated to EventCatalog |

## Purpose

This document describes the integration between PIMCORE (Product Information Management) and Magento e-commerce platform, detailing how product data flows from PIM to the online store.

## Overview

The PIMCORE-Magento integration ensures that product information, including attributes, pricing, categories, and inventory data, is synchronized from the central PIM system to the Magento e-commerce platform.

## Integration Architecture

The integration utilizes the Integration Layer API to facilitate data exchange between PIMCORE and Magento, ensuring consistent product information across systems.

## Key Data Flows

### Product Information Sync
- Product master data (SKU, name, description)
- Product attributes and specifications
- Category assignments
- Product images and media assets
- Product status and visibility settings

### Pricing Synchronization
- Regular pricing updates
- Special pricing and promotions
- Tier pricing for volume discounts
- Currency-specific pricing

### Inventory Management
- Stock levels per plant/location
- Availability status
- Backorder settings

## Sync Status Tracking

Each product maintains sync status fields:
- `Magento Sync Status`: "Pending Sync", "Success", or "Failed"
- Sync logs for traceability and troubleshooting

## Related Components

- [Price Management](price-management) - Pricing sync details
- [PIMCORE Product Structure](product-structure) - Data model
- [Integration Layer API](../integration-layer) - Technical implementation

## Technical Specifications

*Note: Detailed API specifications and sync mechanisms to be documented based on implementation.*

## Error Handling

- Failed sync detection and alerting
- Retry mechanisms for temporary failures
- Manual sync override capabilities
- Comprehensive logging for troubleshooting 