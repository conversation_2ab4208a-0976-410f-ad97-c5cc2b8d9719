---
title: Integrations
summary: Integration Flows for Evolution SSO
---

## Create Account

```mermaid
sequenceDiagram
    participant C as Customer
    participant SPA as SPA
    participant B2C as Entra ID
    participant SSO as Magento SSO
    participant MAG as Magento API

    C->>SPA: Click “Sign up”
    SPA->>B2C: Redirect → /authorize (Sign-up policy)
    B2C->>C: Sign-up UI (email, pwd, MFA, verify email)
    C-->>B2C: Provide details & verify email
    B2C-->>C: Redirect back with auth code
    C->>SPA: GET /auth-callback?code=…
    SPA->>B2C: Exchange code → tokens
    SPA->>SSO: POST ID token
    SSO->>MAG: Create customer / session
    MAG-->>SSO: 201 Created
    SSO-->>SPA: Session cookie / JWT
    SPA-->>C: Welcome, user logged in
```

## Login

```mermaid
sequenceDiagram
    participant C as Customer
    participant SPA as SPA
    participant B2C as Entra ID
    participant SSO as Magento SSO
    participant MAG as Magento API

    C->>SPA: Click “Log in”
    SPA->>B2C: Redirect → /authorize (Sign-in policy)
    B2C-->>C: Redirect back with auth code
    C->>SPA: /auth-callback
    SPA->>B2C: Redeem code → tokens
    SPA->>SSO: POST ID token
    SSO->>MAG: Create/restore session
    SSO-->>SPA: Session cookie / JWT
    SPA-->>C: Dashboard page
```

## Edit Profile

```mermaid
sequenceDiagram
    participant C as Customer
    participant SPA as SPA
    participant B2C as Entra ID

    C->>SPA: Click “Edit Profile”
    SPA->>B2C: Redirect → /editProfile (user flow)
    B2C->>C: Show form with Email, First Name, Last Name, Phone
    C-->>B2C: Submit changes (and verify via OTP if required)
    B2C->>B2C: Persist updated attributes in directory
    B2C-->>C: Return to SPA with new auth code
    C->>SPA: /auth-callback?code=…
    SPA->>B2C: Exchange code → ID token (fresh claims)
    B2C-->>SPA: ID token including updated email, given_name, family_name, phone_number
    SPA-->>C: Display “Profile updated” (values from ID token)
```

## Logout

```mermaid
sequenceDiagram
    participant C as Customer
    participant SPA as SPA
    participant B2C as Entra ID
    participant MAG as Magento API

    C->>SPA: Click “Logout”
    SPA->>B2C: GET /logout?post_logout_redirect_uri=…
    B2C-->>C: 302 Back to SPA (B2C session cleared)
    SPA->>MAG: DELETE /customer/session
    MAG-->>SPA: 204 No Content
    SPA-->>C: Return to home (logged-out state)
```


## Forgot Password

```mermaid
sequenceDiagram
    participant C as Customer
    participant SPA as SPA
    participant B2C as Azure AD B2C

    C->>SPA: Click “Forgot password”
    SPA->>B2C: Redirect → /password-reset policy
    B2C->>C: Enter email
    C-->>B2C: Submit
    B2C-->>C: Sends email with OTP / link
    C->>B2C: Open link, enter OTP + new password
    B2C-->>C: Password reset success → optional auto-login
    C->>SPA: Redirect with auth code (if auto-login)
    Note over SPA,C: User can now sign in with new password
```


## Guest Checkout & OTP login (Needs proof-of-concept)
```mermaid
sequenceDiagram
    participant C as Customer
    participant SPA as SPA
    participant B2C as Entra
    participant MAG as Magento API

    Note over C, SPA: Guest enters checkout details (email, first/last name, phone)
    C->>SPA: Submit checkout form with user attributes
    SPA->>B2C: GET /authorize
    B2C->>B2C: Check if user exists
    alt User does not exist
        B2C->>B2C: Create account with provided attributes
    end
    B2C->>EMAIL: Send <NAME_EMAIL>
    B2C-->>C: Render OTP entry page
    C-->>B2C: POST /verify
    B2C->>B2C: Validate OTP & complete sign-up
    B2C-->>C: 302 Redirect to SPA callback with auth code
    C->>SPA: /auth-callback?code=ABC123
    SPA->>B2C: POST /token (exchange code)
    B2C-->>SPA: ID token (contains sub, email, given_name, family_name, phone_number)
    Note over SPA: SPA now has a JWT for the guest user and can continue checkout

```