---
title: Integrations
summary: Integration Flows for Evolution SSO
---

## Create Account

```mermaid
sequenceDiagram
    participant C as Customer
    participant SPA as SPA
    participant B2C as Entra ID
    participant SSO as Magento SSO
    participant MAG as Magento API

    C->>SPA: Click “Sign up”
    SPA->>B2C: Redirect → /authorize (Sign-up policy)
    B2C->>C: Sign-up UI (email, pwd, MFA, verify email)
    C-->>B2C: Provide details & verify email
    B2C-->>C: Redirect back with auth code
    C->>SPA: GET /auth-callback?code=…
    SPA->>B2C: Exchange code → tokens
    SPA->>SSO: POST ID token
    SSO->>MAG: Create customer / session
    MAG-->>SSO: 201 Created
    SSO-->>SPA: Session cookie / JWT
    SPA-->>C: Welcome, user logged in
```

## Login

```mermaid
sequenceDiagram
    participant C as Customer
    participant SPA as SPA
    participant B2C as Entra ID
    participant SSO as Magento SSO
    participant MAG as Magento API

    C->>SPA: Click “Log in”
    SPA->>B2C: Redirect → /authorize (Sign-in policy)
    B2C-->>C: Redirect back with auth code
    C->>SPA: /auth-callback
    SPA->>B2C: Redeem code → tokens
    SPA->>SSO: POST ID token
    SSO->>MAG: Create/restore session
    SSO-->>SPA: Session cookie / JWT
    SPA-->>C: Dashboard page
```

## Edit Profile

```mermaid
sequenceDiagram
    participant C as Customer
    participant SPA as SPA
    participant B2C as Entra ID

    C->>SPA: Click “Edit Profile”
    SPA->>B2C: Redirect → /editProfile (user flow)
    B2C->>C: Show form with Email, First Name, Last Name, Phone
    C-->>B2C: Submit changes (and verify via OTP if required)
    B2C->>B2C: Persist updated attributes in directory
    B2C-->>C: Return to SPA with new auth code
    C->>SPA: /auth-callback?code=…
    SPA->>B2C: Exchange code → ID token (fresh claims)
    B2C-->>SPA: ID token including updated email, given_name, family_name, phone_number
    SPA-->>C: Display “Profile updated” (values from ID token)
```

## Logout

```mermaid
sequenceDiagram
    participant C as Customer
    participant SPA as SPA
    participant B2C as Entra ID
    participant MAG as Magento API

    C->>SPA: Click “Logout”
    SPA->>B2C: GET /logout?post_logout_redirect_uri=…
    B2C-->>C: 302 Back to SPA (B2C session cleared)
    SPA->>MAG: DELETE /customer/session
    MAG-->>SPA: 204 No Content
    SPA-->>C: Return to home (logged-out state)
```


## Forgot Password

```mermaid
sequenceDiagram
    participant C as Customer
    participant SPA as SPA
    participant B2C as Azure AD B2C

    C->>SPA: Click “Forgot password”
    SPA->>B2C: Redirect → /password-reset policy
    B2C->>C: Enter email
    C-->>B2C: Submit
    B2C-->>C: Sends email with OTP / link
    C->>B2C: Open link, enter OTP + new password
    B2C-->>C: Password reset success → optional auto-login
    C->>SPA: Redirect with auth code (if auto-login)
    Note over SPA,C: User can now sign in with new password
```


## Guest Checkout & OTP login (Needs proof-of-concept)
```mermaid
sequenceDiagram
    participant C as Customer
    participant SPA as SPA
    participant B2C as Entra
    participant MAG as Magento API

    Note over C, SPA: Guest enters checkout details (email, first/last name, phone)
    C->>SPA: Submit checkout form with user attributes
    SPA->>B2C: GET /authorize
    B2C->>B2C: Check if user exists
    alt User does not exist
        B2C->>B2C: Create account with provided attributes
    end
    B2C->>EMAIL: Send <NAME_EMAIL>
    B2C-->>C: Render OTP entry page
    C-->>B2C: POST /verify
    B2C->>B2C: Validate OTP & complete sign-up
    B2C-->>C: 302 Redirect to SPA callback with auth code
    C->>SPA: /auth-callback?code=ABC123
    SPA->>B2C: POST /token (exchange code)
    B2C-->>SPA: ID token (contains sub, email, given_name, family_name, phone_number)
    Note over SPA: SPA now has a JWT for the guest user and can continue checkout

```

## Security Event Handling (Axon Integration)

```mermaid
sequenceDiagram
    participant B2C as Entra ID
    participant AX as Axon Adapter API
    participant SEC as Axon Domain Security Service
    participant MAG as Magento
    participant C as Customer

    Note over B2C: Detects suspicious activity
    B2C->>AX: Webhook: RiskDetected event<br/>(userId, riskLevel, reason)
    AX->>SEC: Process risk event
    
    alt High Risk: Impossible travel
        SEC->>MAG: RevokeSession command
        MAG-->>SEC: Session terminated
        SEC->>B2C: LockAccount command
        B2C-->>SEC: Account locked
        Note over C: User forced to re-authenticate
    else Medium Risk: New device
        SEC->>B2C: RequireMFA command
        B2C-->>SEC: MFA enforced for user
        Note over C: Next login requires MFA
    else Failed login spike
        SEC->>SEC: Analyze pattern
        SEC->>AX: Publish SecurityAlert event
        Note over SEC: Notify security team
    end
```

## Admin SSO (Okta)

```mermaid
sequenceDiagram
    participant A as Admin
    participant MAG as Magento Admin
    participant OKTA as Okta IdP
    participant MAGSSO as Magento SAML Handler

    A->>MAG: Access /admin
    MAG->>MAG: Check session
    MAG-->>A: Redirect to Okta
    A->>OKTA: SAML AuthnRequest
    OKTA->>A: Login page
    A-->>OKTA: Corporate credentials + MFA
    OKTA-->>A: SAML Response (signed)
    A->>MAGSSO: POST SAML assertion
    MAGSSO->>MAGSSO: Validate SAML signature
    MAGSSO->>MAG: Create admin session
    MAG-->>A: Admin dashboard
```

## Magic Link Authentication

```mermaid
sequenceDiagram
    participant C as Customer
    participant SPA as SPA
    participant B2C as Entra ID
    participant EMAIL as Email Service
    participant SSO as Magento SSO

    C->>SPA: Enter email for magic link
    SPA->>B2C: POST /passwordless/start<br/>(email, redirect_uri)
    B2C->>B2C: Generate OTP token
    B2C->>EMAIL: Send magic link<br/>(15 min expiry)
    EMAIL-->>C: Email with link
    
    Note over C: Customer clicks link in email
    C->>B2C: GET /passwordless/verify?token=xyz
    B2C->>B2C: Validate token (single-use)
    B2C-->>C: Redirect with auth code
    C->>SPA: /auth-callback?code=abc
    SPA->>B2C: Exchange code for tokens
    B2C-->>SPA: ID & Access tokens
    SPA->>SSO: POST ID token
    SSO-->>SPA: Magento session
    Note over C: Logged in without password
```