---
title: Network
summary: Customer SSO Network Documentation
---

```mermaid
flowchart TD
    %% User tier
    subgraph User
        Browser["Customer&nbsp;Browser"]
    end

    %% Edge / delivery
    subgraph Edge
        CDN["CloudFront"]
    end

    %% Front-end application tier
    subgraph Frontend["React SPA (Brand A & B)"]
        ReactSPA[("SPA<br/>(static assets<br/>+ OIDC client)")]
    end

    %% Identity provider
    subgraph IdP["Microsoft Entra ID<br/>(Azure AD B2C tenant)"]
        B2C["Azure AD B2C<br/>Sign-in / Sign-up<br/>MFA & Tokens"]
    end

    %% Magento back-end tier
    subgraph Magento["Magento (Headless)"]
        SSO["SSO Endpoint<br/>(Token validation)"]
        API["Magento GraphQL / REST<br/>Customer & Order APIs"]
    end

    Browser -->|HTTPS| CDN
    CDN --> ReactSPA
    ReactSPA -- "OIDC / Auth code flow" --> B2C
    B2C -- "ID & Access Tokens" --> Browser
    Browser -- "Tokens (silent)" --> ReactSPA
    ReactSPA -- "POST ID Token" --> SSO
    SSO -- "Create/restore Magento session" --> API
    API -- "JSON / GraphQL" --> ReactSPA
```