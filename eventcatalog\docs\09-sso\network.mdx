---
title: Network
summary: Customer SSO Network Documentation
---

```mermaid
flowchart TD
    %% User tier
    subgraph User
        Browser["Customer&nbsp;Browser"]
    end

    %% Edge / delivery
    subgraph Edge
        CDN["CloudFront"]
    end

    %% Front-end application tier
    subgraph Frontend["React SPA (Brand A & B)"]
        ReactSPA[("SPA<br/>(static assets<br/>+ OIDC client)")]
    end

    %% Identity provider
    subgraph IdP["Microsoft Entra External ID<br/>(formerly Azure AD B2C)"]
        B2C["Microsoft Entra External ID<br/>Sign-in / Sign-up<br/>MFA & Tokens"]
    end

    %% Magento back-end tier
    subgraph Magento["Magento (Headless)"]
        SSO["SSO Endpoint<br/>(Token validation)"]
        API["Magento GraphQL / REST<br/>Customer & Order APIs"]
    end

    %% Event-driven security orchestration
    subgraph Axon["Axon Services"]
        EventBus["Event Bus<br/>(RabbitMQ)"]
        Security["Security Service<br/>(Risk Analysis)"]
    end

    Browser -->|HTTPS| CDN
    CDN --> ReactSPA
    ReactSPA -- "OIDC / Auth code flow" --> B2C
    B2C -- "ID & Access Tokens" --> Browser
    Browser -- "Tokens (silent)" --> ReactSPA
    ReactSPA -- "POST ID Token" --> SSO
    SSO -- "Create/restore Magento session" --> API
    API -- "JSON / GraphQL" --> ReactSPA
    
    %% Security event flows
    B2C -.->|"Webhook: Risk events<br/>(suspicious login, impossible travel)"| EventBus
    EventBus -->|"Process security events"| Security
    Security -->|"Session revocation command"| SSO
    Security -->|"Account lock command"| B2C
```