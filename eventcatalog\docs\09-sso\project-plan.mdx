---
title: Project Plan
summary: Project Plan for Evolution SSO
---

## Executive Summary

This project will implement separate Single Sign-On (SSO) solutions for customers (Microsoft Entra External ID) and administrators (Okta) across e-commerce platforms by October 1, 2025.

**Approach:** Email-only user migration with progressive profile completion on first login, avoiding complex data transformation.

**Ongoing Costs:** ~$8,290 annually (Azure P2 licensing, storage/bandwidth, SMS/email)

**Timeline:** 3-month implementation (July-September 2025), ready for October 1st launch

## Open Questions and Decisions
- Is hCAPTCHA or reCAPTCHA needed for create account/login? **(<PERSON><PERSON>)**
  - Adam: hCAPTCHA for account creation; TBD for login
- Can Magento handle sessions from the same user on different SPAs? **(<PERSON>/<PERSON>)**
- Can Magento support different IdPs for customer and admin without a large increase to scope? **(<PERSON>/<PERSON>otr)**
  - If not, would it be faster to federate Magento Admin to Okta through Entra?

## Requirements

### Customer SSO
- Customer SSO will use Microsoft Entra External ID with a fully branded experience for five e-commerce sites (two launching initially, three in 2026).
- Support session durations greater than 24 hours.
- Enforce security features: email/phone verification on signup, multi-factor authentication (MFA) for risky logins, risk-based policies (impossible travel, etc.), and robust threat mitigation (DDoS, brute-force protection).
- Provide a migration path for existing user accounts.
- Enable self-service password reset (SSPR) via email and SMS verification.
- Support email registration and verification flow during checkout.
- Enable rollback to legacy system if critical issues arise.

### Admin SSO
- Admin SSO for Magento will use the corporate Okta IdP.
- Support session durations less than 24 hours.

## Architecture and Design

### Key Architecture Decisions

**Customer Identity (Microsoft Entra External ID):**
- Single tenant serving all brands/sites
- OIDC-based authentication flow
- Custom branding per site via dynamic templates
- Session duration: 24+ hours with refresh tokens

**Admin Identity (Okta):**
- SAML 2.0 integration with Magento
- Session duration: Less than 24 hours
- Leverage existing Okta configuration

**Customer Authentication Methods:**
- Email + password (standard registration)
- Magic link (passwordless)
- Guest checkout with account creation

**Customer Account Recovery (SSPR):**
- Self-service password reset via email verification
- Optional SMS verification for enhanced security
- Custom branded password reset pages
- 24/7 availability reduces support ticket load
- Configurable password complexity requirements

**Customer SSO Technical Implementation:**
- React SPAs use MSAL.js for OIDC flow
- Magento validates tokens and creates local sessions

**Magento-Entra Session Flow:**
1. User authenticates with Entra External ID via React SPA
2. React receives ID token (JWT) from Entra
3. React calls Magento custom endpoint with Entra token
4. Magento validates token using Entra's public keys
5. Magento creates local customer session (GraphQL/REST)
6. Subsequent API calls use Magento session, not Entra token

### Security Features

**Built-in Protection (P2 License):**
- Email verification on signup
- Risk-based conditional access
- MFA enforcement for risky logins (impossible travel, new devices)
- Automated threat detection (password spray, brute force)

**MFA Options:**
- TOTP authenticator apps
- SMS verification codes
- Email OTP (for magic links)

**Incident Response Capabilities:**
- Bulk password reset via Graph API
- Session revocation (token invalidation)
- Real-time monitoring with Azure AD reports

**Note:** Research needed on anomaly detection for failed login spikes.

### User Migration Strategy (Simplified Approach)

To meet the October 1st deadline and reduce complexity, we're adopting a simplified "progressive profile" migration approach that focuses on migrating only email addresses initially, with users completing their profiles on first login.

**Core Principle:** Instead of attempting complex password migration or full data transfer, we'll migrate only the minimum viable data (email addresses) and let users complete their profiles when they first access the new system.

**Email-Only Migration Process:**

Prior to launch, we will:
- Extract all email addresses from existing user databases for the two launching sites
- Perform a one-time bulk import to create accounts in Microsoft Entra External ID with:
  - Email as the primary identifier
  - Random password (forces password reset on first login)
  - Extension attributes: `profileIncomplete = true`, `migrationDate = [timestamp]`
- Complete the entire migration in a single operation
- Estimated time: 1-2 weeks for implementation and testing

**Benefits:**
- Dramatically reduced migration complexity
- No need for legacy password validation APIs
- No dependency on keeping old systems online
- Faster implementation timeline
- Lower risk of data migration errors
- Single migration event reduces operational complexity

**First Login Experience:**

When a migrated user attempts to sign in for the first time:

1. **Email Recognition**: The system recognizes their email exists but requires password reset
2. **Password Reset Flow**:
   - User enters their email on the sign-in page
   - System prompts: "Welcome back! For security, please reset your password"
   - Verification code sent to their email
   - User creates new password
3. **Profile Completion** (Post-Authentication):
   - After successful authentication, check `profileIncomplete` flag
   - Display profile completion form:
     ```
     Welcome to our new system! Please complete your profile:
     - First Name: [required]
     - Last Name: [required]
     - Phone Number: [optional]
     - Preferred Language: [dropdown]
     - Marketing Preferences: [checkboxes]
     ```
   - Update both Entra External ID and Magento with profile data
   - Set `profileIncomplete = false`

**Technical Implementation:**
- Microsoft Entra External ID hosts branded profile edit pages
- Custom user flow redirects to profile edit after first login if `profileIncomplete = true`
- Profile data automatically syncs to Magento via webhook/API
- All profile management handled within Entra (no React forms needed)

**Rollback Strategy:**

The simplified approach provides an easy rollback path:
- If issues arise, route traffic back to the legacy sites
- Users continue using legacy login
- No data has been permanently altered in source systems
- Can retry migration after addressing issues

**Communication Plan:**

- **Pre-Launch** (2 weeks before): Email all users about upcoming changes
- **Launch Day**: Email migrated users with password reset instructions
- **Post-Launch**: Follow-up reminders at 1 week, 2 weeks, and 1 month intervals

This simplified approach eliminates the need for:
- Complex REST APIs for legacy password validation
- Maintaining legacy systems in read-only mode
- Complicated data transformation and mapping
- Extended migration windows
- Special handling for different regions

The result is a cleaner, faster implementation that can confidently meet the October 1st deadline while providing a good user experience.

### Special Use-Cases Handling

#### 1. Magic Link Authentication (Passwordless Login)
Magic link authentication provides a seamless, password-free login experience:
- User enters their email address on the login page
- System sends a time-limited, single-use authentication link to their email
- Clicking the link automatically authenticates the user
- Ideal for reducing friction during checkout and for users who forget passwords frequently
- Implementation: Microsoft Entra External ID custom policy with email OTP flow

#### 2. Guest Checkout with Account Creation
This flow allows users to complete purchases without pre-existing accounts:
- User proceeds through checkout as guest
- At payment step, system prompts: "Create an account for faster checkout next time?"
- If user opts in:
  - System sends verification email during checkout
  - Creates account in Microsoft Entra External ID with `profileIncomplete = true`
  - Post-purchase, user receives magic link to set password and complete profile
- Benefits: Reduces cart abandonment while building user base

#### 3. Duplicate Email Handling
Strategy for managing duplicate emails across sites:
- During migration, dedup email addresses
- Implement email uniqueness constraint in Microsoft Entra External ID

### Performance and Scale Considerations

With 70,000 orders per year across two sites (approximately 200 orders/day), the system must handle:
- **Peak Load**: Assuming 3x average during peak hours = ~25 orders/hour
- **Authentication Events**: ~400-500 daily (new + returning customers)
- **Concurrent Users**: Estimated 50-100 during peak hours
- **Response Time SLA**: Authentication complete within 2 seconds

**Infrastructure Requirements:**
- Microsoft Entra External ID with P2 licensing: Supports millions of users (35,000 MAU estimated for this project)
- Static assets: Microsoft Entra External ID requires custom branding files (HTML/CSS/images) to be hosted in Azure Blob Storage - cannot use external CDNs like CloudFront

### Security and Rate Limiting Plan

**API Rate Limiting:**
- Authentication endpoints: 10 attempts per email per hour
- Magic link requests: 3 per email per hour
- Implement exponential backoff for repeated failures

**DDoS Protection:**
- AWS CloudFront and WAF handle DDoS protection for applications
- Azure's built-in DDoS Basic protects authentication endpoints
- Geo-blocking for suspicious traffic patterns

**Password Security:**
- Minimum 8 characters, require complexity
- Check against common password lists
- Force reset if account shows suspicious activity

## Project Plan
### Phase 1: Initiation & Design (Complete by July 11, 2025)
- **Requirements Deep-Dive:** Conduct workshops with stakeholders to clarify any ambiguities (e.g., exact user flows, any regulatory concerns, branding guidelines for each site's login, etc.). For instance, confirm with business if social login is in-scope for Oct 1 or strictly later (most likely later).
- **Architecture Design:** Finalize architecture as discussed: single Microsoft Entra External ID tenant vs multiple (we opt for single tenant with multiple user flows), data flow diagrams for login, session management, etc. Identify the components to build: e.g., Migration API service, custom Microsoft Entra External ID policies, Magento plugin config, etc.
- **Microsoft Entra External ID Tenant Creation:** Set up the DEV Microsoft Entra External ID tenant in Azure. Configure basic settings and initial user flows for proof of concept.
- **Okta Setup:** Coordinate with the team managing Okta to register Magento as an application. Decide on SAML vs OIDC – SAML is commonly used for web app SSO with Okta.
- **Extension Inventory:** For Magento, identify a suitable SSO extension that supports headless React scenario – possibly the "Headless Store SSO" extension or similar. Plan how the React front-end interacts with Magento: since Magento is headless, likely the React app uses Magento's REST/GraphQL API. We might need to override Magento's authentication mechanism so that when a user logs in via SSO, Magento trusts the token. One approach: use Magento's OAuth token exchange – i.e., the React app gets an ID Token from Azure, then calls a Magento API endpoint (custom) which validates the ID Token and creates a Magento customer session (JWT or session cookie) for API use. This needs design now.
- **Axon-Entra Integration Planning:** Design event-driven integrations between Axon services and Entra External ID:
  - Session revocation: When Entra detects suspicious activity, publish event to invalidate Magento sessions
  - Risk score synchronization: Subscribe to Entra risk events and update user status in downstream systems
  - Bulk operations: Design commands for mass password resets and account lockouts
  - Audit trail: Plan event flow for security-related actions across all systems
  - Real-time alerts: Define events for failed login spikes, impossible travel, etc.
- **Proof of Concept Requirements:** Identify and validate critical technical assumptions:
  - Token validation: Verify Magento can validate Entra JWT tokens and create sessions
  - Profile completion flow: Test Entra-hosted profile edit pages with custom policy redirecting users with `profileIncomplete` flag
  - Magic Link: Test passwordless authentication flow with email OTP, verify link expiration (15 min), and validate single-use enforcement
  - User creation during checkout: Prototype guest checkout flow that creates Entra account mid-transaction without disrupting purchase, test email verification during payment process.
  - Multi-brand support: Validate single tenant with dynamic branding approach
  - Risk detection: Confirm P2 features work with External ID (not just corporate accounts)
  - Event integration: Prototype Entra webhook to Axon event bus for security events

Deliverables of Phase 1: Architecture docs, project plan (this document), Microsoft Entra External ID tenant ready, Okta app draft, proof of concept results, and a backlog of user stories/tasks for implementation.

### Phase 2: Development & Configuration (July 14 - August 31, 2025)

**Microsoft Entra External ID Configuration:**
- Set up QA and PROD tenants in Azure
- Define user flows: sign-up/sign-in, SSPR (password reset), profile edit
- Configure branded profile management pages hosted by Entra
- Create custom branded pages for each site
- Configure MFA policies and conditional access (P2 features)
- Set up custom domain (login.alliancelaundry.com) on PROD tenant
- Test Azure Communication Services for SMS/email delivery

**React Front-End Integration:**
- Implement MSAL.js for OIDC authentication flow
- Configure client applications in Microsoft Entra External ID
- Build token management and refresh logic
- Implement single sign-out functionality

**Magento Integration:**
- Customer SSO: Create custom module that:
  - Accepts Entra ID tokens from React SPA
  - Validates tokens against Entra's public keys
  - Creates/updates Magento customer records
  - Issues Magento session tokens for API access
- Admin SSO: Install/configure SAML extension for Okta integration
- Implement just-in-time user creation and attribute mapping
- Test end-to-end authentication flows

**Migration Implementation:**
- Build email extraction scripts for legacy systems
- Develop bulk import tool using Microsoft Graph API
- Configure Entra-to-Axon-to-Magento webhook for profile updates
- Test with subset of users before production migration

**Email & Monitoring Setup:**
- Configure Azure Communication Services
- Set up SPF, DKIM, DMARC records
- Implement monitoring with Application Insights
- Create custom dashboards for key metrics

**Data Migration Planning:**
- Start extracting user data from legacy systems
- Clean the data (remove duplicates, obsolete accounts)
- Plan communication to users about the upcoming change (draft emails, helpdesk scripts)

**Deliverables:** Working SSO for both sites, migration tools ready, monitoring dashboards operational

### Phase 3: Testing & Migration (September 2025)

**Testing Activities:**
- User acceptance testing with internal users and beta customers
- Security audit and penetration testing
- Performance and load testing
- Email deliverability verification

**Production Migration:**
- Week 1-2: Dry-run with subset of users in dev environment
- Week 3: Execute full production migration (all user emails imported to Entra)
- Week 4: Verify all users migrated (especially brand new users), test password reset flows

**Training & Preparation:**
- Train customer support on new SSO process
- Prepare helpdesk scripts for common issues
- Document admin access via Okta
- Finalize user communication emails

**Deliverables:** All users migrated to Microsoft Entra External ID, support teams trained, SSO ready for October 1st SPA launch

## Budget Estimates

### Azure Licensing Costs (Annual)

**Microsoft Entra External ID:**
- Tier: P2 (required for Identity Protection, risk-based conditional access, privileged identity management)
- Pricing: $0.01375 per MAU (Monthly Active User)
- Estimated MAU: 35,000 (based on 70k orders/year, ~50% repeat customers)
- **Annual Cost: $5,775** ($481.25/month)

**Azure Infrastructure:**

*Storage & Bandwidth: ~$200/month = **$2,400/year***
- **Purpose**:
  - Azure Blob Storage for custom HTML/CSS branding files (required by Microsoft Entra External ID)
  - Bandwidth for authentication redirects and token exchanges
  - Logs and audit trail storage (important for security compliance)
- **Why needed**:
  - Microsoft Entra External ID requires branding assets to be hosted in Azure Blob Storage
  - Cannot use external CDNs like CloudFront for authentication page assets
  - Each auth flow involves multiple redirects (3-4 round trips)
  - With 500 daily auth events, this is ~60,000 requests/month
- **Breakdown**:
  - Storage: ~$50/month (branding files, logs)
  - Bandwidth: ~$150/month (authentication traffic)

**Total Infrastructure: ~$2,400/year**

### Communication Services Costs

**Email Costs (Azure Communication Services):**
- Volume: ~150,000 emails/year (migrations, resets, magic links)
- Rate: $0.00025 per email
- **Annual Email Cost: ~$40**

**SMS Costs (for MFA via Azure Communication Services):**
- Provider: Azure Communication Services
- Estimated volume: 10,000 SMS/year (10% users use SMS MFA)
- Rate: $0.0075 per SMS (US)
- **Annual SMS Cost: ~$75**

**Total Communication: ~$115/year**

### Summary

**Ongoing Annual Costs:**
- Azure Licensing (P2): $5,775
- Azure Infrastructure (Storage & Bandwidth): $2,400
- Communication Services: $115
- **Total Annual: ~$8,290**
