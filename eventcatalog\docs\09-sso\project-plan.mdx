---
title: Project Plan
summary: Project Plan for Evolution SSO
---

## Draft thoughts
- The migration plan is too complex to meet the deadline. We need to simplify this.
- Need to validate that Entra ID can support multiple custom domains for the same tenant
- Is hCAPTCHA or reCAPTCHA needed for create account/login?
- What data needs to be captured in Magento when a JWT token is seen for the first time. How will this data be updated when it changes?
- Would it be faster to federate Magento Admin to Okta through Entra?
- Add Account Recovery & Support: Ensure users have a self-service password reset (SSPR) capability. Microsoft Entra External ID can allow users to reset their password via verified email or phone (which ties into the verification methods above). This means less load on support and a better user experience if they forget credential
- Can Magento handle sessions from the same user on different SPAs?
 ---

The requirements for a unified Single Sign-On (SSO) solution across multiple e-commerce sites and platforms are technically feasible within the given timeframe (go-live by October 1, 2025). The key objectives are:

- **Customer SSO** using Microsoft Entra ID (Azure AD) with a fully branded experience for five e-commerce sites (two launching initially, three in 2026).  
- **Admin SSO** for Magento backend using Okta.  
- Long session durations (~8 hours).  
- Integrate a central Identity Provider (IdP) that can support diverse platforms (Magento/React, WooCommerce, Intershop, Web Shop Manager, Salesforce Community, etc.) via standard protocols (OAuth2, OpenID Connect, SAML).  
- Enforce security features: email/phone verification on signup, multi-factor authentication (MFA) for risky logins, risk-based policies (impossible travel, etc.), and robust threat mitigation (DDoS, brute-force protection).  
- Provide a migration path for existing user accounts (no plaintext passwords available) with minimal user friction.  
- Accommodate special cases: accounts without email (APAC users using phone/username) and an email verification flow during checkout (account creation completed later).  
- Design for future social login providers (Google, Facebook, etc.) in addition to enterprise IdPs.  
- Plan a phased rollout on Oct 1 with a subset of traffic (canary release) and the ability to roll back to the legacy system if needed.  

Given the complexity, we will leverage cloud identity services (CIAM) to satisfy these requirements and reduce the amount of custom code that needs to be implemented. In particular, Microsoft Entra ID External Identities is an ideal choice for the customer-facing SSO platform. Microsoft Entra External ID is explicitly designed for customer identity and supports millions of users, billions of authentications, and threat mitigation out-of-the-box. It allows users to authenticate with enterprise or social accounts or local credentials, enabling SSO across multiple applications. Crucially, it provides a white-label experience – we can fully customize the branding (HTML/CSS) of the login and signup UI to match each site's look and feel. Using this managed identity platform will accelerate development and ensure scalability and security compliance within the tight timeline.

On the admin side, Okta will serve as the IdP for Magento's back-end users. Okta is a proven enterprise SSO solution and can be integrated with Magento Open Source via industry-standard protocols. In practice, Magento can act as a SAML or OAuth Service Provider trusting Okta as the IdP. There are existing Magento extensions for Okta integration, which confirms the feasibility of this approach. This means your Magento administrators will seamlessly authenticate through Okta to access the Magento admin panel.

Overall, meeting an October 1 go-live is ambitious but achievable. The feasibility is high if we start immediately and use mostly off-the-shelf capabilities of Entra External ID and Okta, with custom development focused on integration glue and user migration. Below we break down the project plan, architecture, and timeline in detail, highlighting how each requirement will be addressed.

## Architecture and Design
### Identity Solution Architecture

At the core of the architecture is a central Identity Provider service for customers: Microsoft Entra ID. All customer authentication requests from any of the front-end platforms will be redirected to this IdP, which then issues security tokens (OIDC ID tokens or SAML assertions) back to the applications upon successful login. Key design points for this IdP:

- **Single Tenant vs. Multiple:** We will create a dedicated Microsoft Entra External ID tenant for the customer identities. A single tenant can handle all five brands/sites, allowing a unified user database and SSO across them. Microsoft Entra External ID is built to manage external customer identities separately from internal corporate directories. It will store customer profiles securely and issue tokens for your applications. Each site (application) will be registered as an "Application" in this tenant with appropriate callback URLs and permissions. This centralization means a customer can use one account to access all brands (if desired) and streamlines user management.
- **Standards-Based Integration:** Microsoft Entra External ID supports OpenID Connect, OAuth2, and SAML2 protocols for authentication. We will use OpenID Connect (OIDC) for integrating the React SPA front-ends to facilitate modern token-based auth flows. For platforms that prefer SAML, Entra can also act as a SAML 2.0 IdP. This ensures all your varied platforms (Magento/React, WooCommerce, Intershop, Salesforce, etc.) can be connected to the single IdP using the protocol each supports.
- **Customer Authentication Methods:** The IdP will be configured to allow multiple identity providers for login. Initially, this will include local accounts (username + password).
- **White-Label Branded Experience:** Each of the five e-commerce sites has its own branding, and the login/user account experience must be branded accordingly. Microsoft Entra External ID is a white-label solution, meaning we can fully customize the UI that users see during sign-up and sign-in. We will create custom HTML/CSS templates for the Entra pages (or use Entras's built-in page customization with company branding). Options include using user flows with embedded page templates or custom policies for more flexibility. For each brand, we can apply the specific logo, color scheme, and wording so that the user feels they never left the site's domain. (Microsoft Entra External ID even supports custom domains, so instead of seeing "contoso.b2clogin.com" in the URL, we can configure e.g. id.alliancels.com,for a seamless experience.) Because multiple brands are served, we might implement either separate user flows for each site or a dynamic page template that adjusts based on a query parameter or domain. Entra's flexibility with custom policies allows either approach. This satisfies the "fully branded (white-label) experience matching each site" requirement. As Microsoft's documentation states, "you get nearly full control of the HTML/CSS... to blend seamlessly with your web applications".
- **Session Management:** We will leverage token lifetimes and session cookies to achieve the ~8 hour session duration requirement. Microsoft Entra External ID issues JWT tokens (ID token, access token) with configurable lifetimes. Typically, we might use a short-lived access token and a longer-lived refresh token. The React front-end can silently use the refresh token to get new tokens and keep the user logged in without re-prompting for credentials for up to 8 hours (or whatever policy is set). Microsoft Entra External ID also sets its own session cookie so that if a user is still within the session and they come back to any application, SSO is achieved without login prompt. We will configure the token lifetimes/SSO session according to the security team's guidance (e.g., 8 hours idle timeout, or persistent sessions if allowed). Magento (and other apps) will treat the validated token as proof of authentication and create its own session state for the user. If Magento needs a session cookie, upon successful SSO we can generate a Magento session for the user so that the site functions as it would with a normal login. Essentially, once the initial SSO completes, the user continues with a logged-in session on the site (no re-auth unless the token expires or they logout). All these settings are adjustable per application or user flow in Entra, meeting the long-lived session requirement.
- **Admin SSO (Okta Integration):** For internal admin users (those logging into the Magento backend), the IdP will be Okta, as specified. The architecture here is simpler: Magento Open Source admin panel will be configured to delegate authentication to Okta using SAML 2.0 or OIDC. We will likely use a Magento extension for SAML SSO – for example, miniOrange or similar plugins exist that turn Magento into a SAML Service Provider. Using one of these, Magento will redirect admin login attempts to Okta. Okta will handle the multi-factor, etc., for admins, and upon successful auth, send a SAML Response back to Magento to log the user in. Okta supports SAML and OAuth/OIDC, and such integration is a well-trodden path (Okta themselves provide guides and there are marketplace extensions). This satisfies "Admin SSO via Okta". We will set up appropriate Okta applications: one for the Magento admin (and potentially separate ones for any other internal apps if needed). Okta's cloud infrastructure can easily handle your number of admin users (which is much smaller than customer count), so performance is not a concern. The main effort is configuration and testing.
- **Scalability and Performance:** Using Microsoft Entra External ID and Okta means we are delegating the heavy lifting of authentication to cloud services that are built to scale globally. Millions of users and high auth volumes are well within Microsoft Entra External ID's capabilities – it can handle "millions of users and billions of authentications per day". It also provides automatic threat detection and mitigation (blocking common attacks like DDoS, password spray, brute force) without us having to custom-build it. This gives confidence that performance will not be a bottleneck. We will choose an appropriate Azure region for the Entra tenant close to our user base (possibly a multi-region setup if available for failover). Okta similarly is a cloud SaaS with high availability. Our React front-ends and Magento servers will need to handle the post-auth load (session management, etc.), but that is similar to current state just centralized. We will do load testing with simulated logins to ensure the redirect flows and token verifications do not add noticeable latency for users. In summary, scaling to millions is feasible with this architecture, as proven by large implementations of Microsoft Entra External ID.

### Security and Authentication Features

Security is a major focus of the requirements. Below is how the architecture will satisfy them:

 - **Email and Phone Verification:** Microsoft Entra External ID includes built-in email verification during user sign-up and password reset flows. When a new user registers, B2C can automatically send a verification code to their email which the user must input to prove the email is valid (or click a link). This ensures no account is created with an unverified email, preventing spam accounts. We will enable this for all sites where email is required. For the site requiring "email verification during checkout flow", we will handle it slightly differently (discussed later in Migration & Guest Checkout section), but the principle of verifying an email address via a code or link is standard and supported.

For phone number verification - if we allow phone sign-ups (for APAC users with no email), Entra also supports sending an OTP code via SMS to verify phone ownership. In fact, you can configure user flows such that the primary identifier is a phone number, in which case the sign-up uses an SMS code as the first factor (and if desired, an email as a recovery method). We will leverage these capabilities on the site that needs no-email accounts.

- **Multi-Factor Authentication (MFA):** The solution will enforce MFA in scenarios required by policy. Microsoft Entra External ID supports MFA for consumer accounts (using either text message codes, authenticator app TOTP codes, or even email OTP). We can require MFA conditionally – for example, only if the sign-in is deemed "risky" or for certain high-value transactions. Because we have Entra ID P2 features, we might integrate Identity Protection risk-based policies if possible (note: Microsoft's "Identity Protection" risk scoring is a feature of Entra ID P2 for corporate users; for B2C, an equivalent might be achievable via custom policies or upcoming Entra External ID features). At minimum, we can implement rules like: if user login from a new country or impossible travel scenario, force an MFA step. B2C can't natively detect impossible travel without custom logic, but one approach is using IP address in a REST API call to an external risk service or enabling geolocation checks. Alternatively, since Entra ID P2 is mentioned, it could be the plan to use Conditional Access in some capacity. There is an Microsoft Entra External ID integration with Conditional Access to enforce MFA based on conditions (this is a feature that can be enabled on B2C tenants for certain user flows). We will explore enabling risk-based MFA: e.g., if login attempt is high risk, require additional verification. This satisfies the requirement "MFA must be enforced for risky login behavior (impossible travel, etc.)".

Aside from risk-based triggers, we can enforce MFA for at least some scenarios by policy. Okta for admin SSO will have MFA enabled according to ALS internal corporate policy.

Microsoft Entra External ID's MFA methods will cover your the required MFA options for Phase 1:

- **TOTP apps:** Users can register an authenticator app (like Microsoft Authenticator, Google Authenticator) with the Entra account.
- **SMS codes:** Out-of-the-box supported for MFA or even primary auth in phone-based sign-in.

Microsoft Entra External ID provides out of the box recognition for attacks:
- **Threat Detection & Mitigation:** By leveraging Microsoft Entra External ID, we inherently get enterprise-grade security monitoring. Microsoft states that Microsoft Entra External ID "automatically handles threats like denial-of-service, password spray, or brute force attacks". This means if someone tries to DDoS the login service or mass-guess passwords, Entra will throttle or block those attempts, protecting our user accounts. We will still implement monitoring and alerting on our side – e.g., using Azure AD reports for risky sign-ins, plus custom logs to detect unusual patterns (like a sudden spike in failed logins, which could indicate an attack or an integration issue). Microsoft Entra External ID can integrate with Azure AD Identity Protection to some extent, but since that's a newer area, we'll treat it as a secondary measure for future phases and rely on our own monitoring initially.

If an incident occurs (e.g., many accounts compromised or a vulnerability discovered), we have mitigation plans:
- **Bulk password reset:** We can force all users or a subset to reset passwords by using Azure Graph API or PowerShell to disable accounts or invalidate refresh tokens. If needed, B2C allows marking accounts (or all accounts) such that next login requires password change. This addresses the "Bulk force password reset" scenario – though we'd only do this in a serious breach situation.
- **Bulk session revocation:** Similar to above, we can revoke tokens. Also, because sessions are centralized, we can effectively log everyone out by changing the token cryptography keys (which forces all tokens to become invalid) – not something to do lightly, but a last-resort option.
- **DDoS response:** Should the IdP be targeted by DDoS, Azure's built-in DDoS protection will handle it. But if the sites are targeted (high traffic causing slowdowns), we should use a Web Application Firewall/CDN (e.g., Azure Front Door or Cloudflare) in front of the sites to absorb that. That's outside SSO scope but part of overall launch readiness.

Azure AD can send risky sign-in alerts. **Additional research is needed to determine what is available for detecting large anomilies of failed logins.**

### User Migration Strategy

Migrating existing users into the new SSO system is one of the most critical and challenging parts, especially since we cannot retrieve current passwords (only usernames, emails, etc.). We need to bring users over without forcing everyone to re-register from scratch, ideally. Our strategy will combine pre-migration of accounts with "just-in-time" (JIT) migration for credentials, aligning with Microsoft Entra External ID's recommended approaches.

**Account Pre-Migration:** Prior to go-live, we will export all users from the current identity stores of the two sites launching (and any other user databases we plan to unify initially). For each user, we will gather as much info as possible: username, email, phone, any profile data. We will create corresponding accounts in Microsoft Entra External ID via Microsoft Graph API in bulk. Since we don't have their actual passwords, we have two options:
- Set a random initial password for each migrated account and mark the account in an "needs password set" state.
- Or flag the account as external/placeholder to trigger a custom flow on first login.

Microsoft Entra External ID migration guidance suggests creating the accounts with a random password and a flag (extension attribute) indicating the password is not yet verified. We will use this "seamless migration" pattern: mark each pre-created user with an attribute like needsMigration = True.

**Just-In-Time Credential Migration:** When a user who was pre-created tries to log in for the first time on the new system, we can handle it in two possible ways:
- Preferred approach – Password Reset Flow: We notify users ahead of time that the site has a new login system and they need to reset their password on first login. We can pre-send an email to all users with a link to set a new password. Or when they try to sign in the first time, they can click "Forgot Password" which we'll customize to say "As this is your first time logging into our new system, please reset your password." This is a simple method: we imported their email, so the reset email will go to them and they choose a new password. This doesn't preserve their old password but ensures security (since old hashes might be weak or different algorithm).
- Alternate approach – Legacy Password Check: For a smoother experience, we could attempt to validate the user's old password on the fly. Microsoft Entra External ID's custom policy with REST API allows this: during sign-in, if needsMigration=True, B2C can call out to a REST API we host that checks the entered credentials against the legacy user store. If the legacy check returns success, B2C then accepts that password and sets it as the new password in B2C, and flips the flag so next time the user just logs in normally. If the legacy check returns fail, the user gets a generic "wrong password" error (and could then do a reset if needed). This "seamless migration" technique means users can log in with their old password one time and the system silently migrates them. We will implement this for at least the APAC site or others where email reset might be difficult (e.g., APAC users without emails must use their existing password or phone). It requires the old identity systems (or databases) to remain accessible via an API for a short period. For the two initial sites, if those identity providers will be decomissioned, we might instead freeze them in a read-only state for authentication calls during the migration phase.

In summary, the plan is:
- Bulk import all users into B2C (with random passwords). (Pre-migration phase).
- Mark them as requiring migration.
- Implement a custom sign-in policy in B2C: if needsMigration=True, call a Migration API (which we will develop) with the username & password the user entered. This API will validate against the legacy system's data (likely by hashing the password and comparing with stored hash, or calling an old authentication endpoint).
- If validation succeeds, the API responds accordingly and B2C will then set that password for the user's account and set needsMigration=False. The user is then logged in and now fully on the new system without even realizing a migration happened. If validation fails, the user either entered the wrong old password or wasn't in the old system (shouldn't happen if we pre-loaded everyone). After a few failures, we can just redirect them to a self-serve reset.
- For any users that never log in during the migration window (some people might not come back for months), their accounts remain in B2C with random password and needsMigration=True. We could keep the legacy auth API alive for a certain period (say 6-12 months) to catch those stragglers. Alternatively, as a backup, we can email those users later prompting a password reset.

This approach is aligned with Microsoft's best practices for migrating to Microsoft Entra External ID when you cannot get plaintext passwords. It allows gradual migration without forcing a big-bang credential reset for all users, thus providing a smoother user experience. We will have to develop the REST API and host it securely (likely as an Azure Function or a small service), and also import the legacy hashes or integrate with legacy IdPs accordingly. Security of this API is paramount (it will only be called by B2C via server-to-server, and we'll secure it with a secret or certificate). We also need to protect the API from brute-force (throttle by IP or user after X attempts) because as Microsoft notes, if someone knows we're forwarding passwords to an API, it could be targeted – but since B2C itself throttles failed attempts, and we can implement the API to similarly throttle, it should be fine.

For user attributes, we'll migrate only what's needed (emails, names, etc.).

### Special Use-Cases Handling

1. Email Verification / Account Creation during Checkout: This flow refers to allowing a user to check out as a guest but still create an account seamlessly.

**Needs definition**

## Draft Project Plan ##
### Phase 1: Initiation & Design (Complete by July 11 2025)
- **Requirements Deep-Dive:** Conduct workshops with stakeholders to clarify any ambiguities (e.g., exact user flows, any regulatory concerns, branding guidelines for each site's login, etc.). For instance, confirm with business if social login is in-scope for Oct 1 or strictly later (most likely later). Clarify how the partial traffic rollout will be measured and switched (by geo? random split? user segment?).
- **Architecture Design:** Finalize architecture as discussed: single B2C tenant vs multiple (we opt for single tenant with multiple user flows), data flow diagrams for login, session management, etc. Identify the components to build: e.g., Migration API service, custom B2C policies, Magento plugin config, etc.
- **Microsoft Entra ID (Microsoft Entra External ID) Tenant Creation:** Set up the Entra tenants in Azure (DEV, QA, and PROD). Configure basic settings: custom domain (if we want login.mybrand.com), initial user flows or custom policy framework (Identity Experience Framework upload if needed for custom policies).
- **Okta Setup:** Coordinate with the team managing Okta to register Magento as an application. Decide on SAML vs OIDC – SAML is commonly used for web app SSO with Okta. Begin configuration in a dev Okta environment if available.
- **Extension Inventory:** For Magento, identify a suitable SSO extension that supports headless React scenario – possibly the "Headless Store SSO" extension or similar. Plan how the React front-end interacts with Magento: since Magento is headless, likely the React app uses Magento's REST/GraphQL API. We might need to override Magento's authentication mechanism so that when a user logs in via SSO, Magento trusts the token. One approach: use Magento's OAuth token exchange – i.e., the React app gets an ID Token from Azure, then calls a Magento API endpoint (custom) which validates the ID Token and creates a Magento customer session (JWT or session cookie) for API use. This needs design now.
- **Data Migration Planning:** Start extracting user data from legacy systems (for the two initial sites). Clean the data (remove duplicates, obsolete accounts). Decide on attribute mapping to the new system (e.g., what becomes username? likely email for most, except APAC which will use phone or a generated username if no email). Plan communication to users about the upcoming change (draft emails, helpdesk scripts).

Deliverables of Phase 1: Architecture docs, project plan (this document), Microsoft Entra External ID tenant ready, Okta app draft, and a backlog of user stories/tasks for implementation.

### Phase 2: Development & Configuration (July through August 2025)

This is the core implementation period. We will break it into sub-streams:

#### Microsoft Entra External ID Configuration:

- **Define User Flows / Custom Policies:** We'll create the sign-in/sign-up flows for: 
  - Standard email/password sign-up with email verification and optional MFA
  - Profile edit and password reset flow. Also define a custom policy for the migration (this likely means writing an XML custom policy that extends sign-in to call our REST API as described).
- **UI Customization:** Using HTML/CSS to style the pages. We'll implement brand-specific pages for at least the two initial sites. Microsoft Entra External ID allows either one set of pages for all (with dynamic logo) or separate pages per policy. For simplicity, we might create two separate policies (or user flows) – one for Site A and one for Site B, each with its own custom HTML content (logo, colors). This duplication is manageable for initial launch (and we can reuse for the other sites when they come on board). Ensure the pages are responsive and accessible. Also include localization if needed (Microsoft Entra External ID supports 36+ languages, but we can decide if multi-language is in scope or not now).
- **MFA Policy Configuration:** Ensure MFA settings are turned on appropriately (Entra allows toggling MFA per flow or via conditional access). We will probably make MFA optional for normal users unless risk detected.
  - Test the SMS sending (will need an Azure resource – by default MS handles phone OTP via Azure's integration, charges per usage).
- **Custom Domain:** Complete domain verification and certificate for Entra domains, so users see a friendly URL.

#### React Front-End Integration:

- Implement the OIDC client in the React single-page application. Likely we'll use MSAL.js or a similar library to handle redirects to Microsoft Entra External ID. The flow: when user clicks "Login" on the site, the app triggers an OIDC authorization code flow to B2C. After authentication, B2C redirects back with an authorization code to a callback URL on the React app, which then exchanges it for tokens (ID token, etc.) using the B2C /.well-known/openid-configuration endpoints. We'll need to set up those client IDs in B2C (one per site application). Store the ID token or user info in the app's state (and perhaps a refresh token in memory or cookie if using one).
- **Session and API calls:** The React app after login needs to communicate with Magento (and possibly other APIs) as an authenticated user. If Magento's APIs can be secured via OAuth tokens, we might have Magento trust the Microsoft Entra External ID JWT directly. If not, another method: once React has the user's ID token (which is JWT with user's sub, email, etc.), we create a call to a Magento SSO endpoint (custom module) that validates that token (using Microsoft Entra External ID's public keys) and then creates a Magento customer session. This could return a Magento session token or set a cookie that the Magento API (or GraphQL) will accept on subsequent requests. We will implement this Magento module in Phase 2.
- Ensure logout in React triggers log out in B2C (so that single sign-out works properly). Also implement token renewal (if using refresh tokens).
- The React apps (for two sites) will be fairly similar in auth logic, just using different B2C configurations (since branding differs). We'll duplicate or parameterize as needed.

#### Magento Backend Integration (Customer side):
- **Just-In-Time User Creation:** Enable the option to "Create user if not exists" on SSO login.
- **Implement attribute/claim mapping:** e.g., B2C token will contain email, perhaps given name, surname, etc. Magento's SSO module should map those into the customer profile
- **Test end-to-end:** go to React site, redirect to B2C, login, get back to app, call Magento APIs – the API should confirm the user is authenticated (either by token or session).

#### Magento Admin: 
- Install/configure the Magento extension for Okta SSO. For example, miniOrange's plugin can turn on SAML SSO for the Magento Admin URL. We'll configure Okta with the Magento SAML SP metadata (Magento plugin will provide an XML metadata with ACS URL etc.)
- In Okta, set up attributes to send (like username, maybe group = admin role). Then test logging into Magento admin: it should redirect to Okta, authenticate, and back to Magento with admin access. This needs to be ready for internal users by launch.

#### Migration Implementation:
** NEEDS FURTHER PLANNING**

### Phase 3: Pilot & UAT (September 2025)
- **User Acceptance Testing:** Invite a small group of internal users and friendly customers to exercise the new system. This could include company staff from different departments, or a beta tester group. Have them test logging in, signing up, checking out, etc., on the new sites. Pay special attention to the user experience of the SSO (branding correct? any confusing steps?), and the performance (are logins snappy?).
- **Security Testing:** Possibly conduct a security audit or penetration test focusing on the authentication. Ensure things like: tokens can't be manipulated, no XSS on the custom pages, the logout truly logs the user out, etc. Also verify the migration API cannot be abused.
- **Data Migration Dry-Run:** Before the final migration, do a dress rehearsal with a subset. E.g., import 100 users from legacy into B2C in a dev environment, test that their credentials work as expected (either by reset or JIT). This will catch any mapping issues (like maybe some emails are duplicates across sites – how do we handle that? Possibly unify them or add a suffix for one site's duplicate).
- **Train Support/IT:** Make sure customer support is ready for the change. There will be an increase in "I can't log in" queries around the launch. Provide them with knowledge: if a user is from site A and hasn't logged in since migration, instruct them to use password reset. Also help them understand MFA (some users might need help setting up Authenticator app, etc.). Similarly, train internal admins on the Okta SSO change (they'll log in via Okta now, not with Magento local accounts).
- **Go/No-Go Checkpoints:** Throughout September, hold regular checkpoints with stakeholders to ensure we are on track. Oct 1 is a hard deadline; if critical pieces are behind, make contingency plans.
