---
title: Checkout Guest User Test Cases
id: checkout-guest-user
description: Test cases for guest user checkout functionality
summary: Test cases covering guest user checkout process including login options, address validation, payment methods, shipping selection, and order completion scenarios.
---

# Checkout guest user

Test cases for guest user checkout functionality

## TC-001 – Guest Login Option at Shipping Step

**Preconditions:**  
User is not logged in.

**Steps:**
1. Navigate to the checkout page.
2. Observe the message: "Already have an account?"
3. Click the **"Login"** button.

**Expected Results:**
- User is redirected to login form (modal or page).
- After successful login, user continues checkout as registered user.
- Login process is seamless and maintains cart contents.

---

## TC-002 – Fill In Guest Shipping Address

**Preconditions:**  
User is not logged in.

**Steps:**
1. Choose to continue as guest.
2. Fill in required address fields (e.g., name, street, city, ZIP, country).
3. Click "Continue to shipping method".

**Expected Results:**
- Address is accepted and validated.
- Checkout moves to Shipping Method step.
- Address information is preserved throughout checkout.

---

## TC-003 – Guest Shipping Address – Validation

**Preconditions:**  
User is on shipping address step.

**Steps:**
1. Leave one or more required fields empty or fill them incorrectly.
2. Try to proceed to shipping method.

**Expected Results:**
- Validation messages are shown.
- User cannot proceed until all fields are corrected.
- Error messages are clear and helpful.

---

## TC-004 – Display Shipping Policy Notice for Non-Continental US and Canada

**Preconditions:**  
User is on the shipping address step during checkout.

**Steps:**
1. Expand "For Non-Continental U.S Customers"
2. Expand "For Canada Shipments"

**Expected Results:**
- A visible section with information related to shipping policies is displayed.
- Policy information is accurate and up-to-date.
- Sections expand and collapse properly.

---

## TC-005 – UPS Shipping Option is Visible and Pre-selected

**Preconditions:**  
Guest user is on the Shipping Method step.

**Steps:**
1. Observe the available shipping options.

**Expected Results:**
- UPS is visible with delivery estimate.
- It is selected by default.
- Shipping cost is clearly displayed.

---

## TC-006 – Proceed to Additional Info from Shipping Method

**Preconditions:**  
UPS is selected.

**Steps:**
1. Click "Continue to Additional Info".

**Expected Results:**
- User is moved to the Additional Info step.
- Previous selections are preserved.
- Progress indicator shows current step.

---

## TC-007 – UPS Shipping Cost Appears in Order Summary

**Preconditions:**  
User is on the checkout page and UPS is selected.

**Steps:**
1. Observe the sidebar or summary section.

**Expected Results:**
- Shipping cost for **UPS** is listed in the summary.
- The value is correctly calculated and added to the total.
- If free shipping applies, cost should show as 0.00.

---

## TC-008 – Submit Without Filling Additional Info Fields

**Preconditions:**  
User is on the Additional Info step.

**Steps:**
1. Leave all fields empty.
2. Click **"Continue to Payment"**.

**Expected Results:**
- User is navigated to the Payment step.
- No errors or warnings are displayed.
- Empty values are still accepted.

---

## TC-009 – Fill Out All Optional Fields and Proceed

**Preconditions:**  
User is on the Additional Info step.

**Steps:**
1. Enter text in "Special Instructions".
2. Select an option from "Type of Business".
3. Enter a reference in "Purchase Order Number / Reference".
4. Click **"Continue to Payment"**.

**Expected Results:**
- Entered values are accepted and carried forward to order summary (if visible).
- No validation errors appear.
- User proceeds to the Payment step.

---

## TC-010 – Switch Between Payment Methods

**Preconditions:**  
User is on Payment step.

**Steps:**
1. Select "Bank Transfer Payment".
2. Switch to "Pay Online".
3. Switch back to "Bank Transfer Payment".

**Expected Results:**
- UI updates accordingly each time.
- For Pay Online: sub-options (Card, PayPal) become visible.
- Only one payment method is active at a time.

---

## TC-011 – Selecting Sub-option Under Pay Online

**Preconditions:**  
User selected "Pay Online".

**Steps:**
1. Choose one of the available options: "Card Payment" or "PayPal Payment".

**Expected Results:**
- Sub-option is selected and visually highlighted.
- If Card is selected, card form appears.
- If PayPal is selected, PayPal button appears or flow is initialized.

---

## TC-012 – Display Billing Address Checkbox

**Preconditions:**  
User is on Payment step.

**Steps:**
1. Observe the checkbox: **"Use shipping address as billing address"**.

**Expected Results:**
- Checkbox is visible and **checked by default**.
- When checked: billing address form is hidden.
- When unchecked: billing address UI appears.

---

## TC-013 – Terms and Conditions Checkbox Must Be Checked

**Preconditions:**  
User is on Payment step.

**Steps:**
1. Leave "I have read and agree to Terms and Conditions" **unchecked**.
2. Attempt to continue or place the order.

**Expected Results:**
- An error message is shown: "You must accept the terms and conditions".
- User cannot proceed without checking it.

---

## TC-014 – Proceed After Selecting Payment Method and Accepting Terms

**Preconditions:**  
User selected a valid payment method and accepted terms.

**Steps:**
1. Check "I have read and agree to Terms and Conditions".
2. Click "Submit Order".

**Expected Results:**
- User proceeds to order summary page.
- Selected billing address and payment method are saved for the order.

---

## TC-015 – Failed Payment Handling

**Preconditions:**
- User is on the Payment step.
- User has selected a valid payment method (e.g., Pay Online – Card or PayPal).
- Payment is rejected by the provider (e.g., card declined, connection error).

**Steps:**
1. Attempt to complete the payment.
2. Simulate or receive a payment failure from the provider.

**Expected Results:**
- User is redirected to a dedicated error screen.
- Page displays the copy: **"We could not process your payment"** and **"Something went wrong..."**
- A visible button labeled **"Return to Payment"** is available.
- Clicking the button takes the user back to the Payment step with previously selected data preserved. 