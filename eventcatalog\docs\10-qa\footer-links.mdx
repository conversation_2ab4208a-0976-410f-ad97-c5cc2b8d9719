---
title: Footer Links Test Cases
id: footer-links
description: Test cases for footer links functionality
summary: Test cases covering footer links functionality including link visibility, navigation, external links, accessibility, and link accuracy validation scenarios.
---

# Footer links

Test cases for footer links functionality

## TC-001 – Footer Visibility and Layout

**Preconditions:**  
User is on any page of the website.

**Steps:**
1. Scroll to the bottom of the page.
2. Review footer layout and content.

**Expected Results:**
- Footer is visible and properly positioned.
- All footer links are clearly displayed.
- Footer layout is consistent across pages.
- Footer content is well-organized and readable.

---

## TC-002 – Navigation via Footer Link – Internal Page

**Preconditions:**  
Footer is visible.

**Steps:**
1. Click on an internal footer link (e.g., "Contact Us" or "Returns").

**Expected Results:**
- User is navigated to the correct internal page.
- Page title and content match the selected topic.
- URL is updated accordingly.
- Navigation maintains site consistency.

---

## TC-003 – Open External Link in New Tab

**Preconditions:**  
Footer includes social media links (e.g., Facebook, LinkedIn).

**Steps:**
1. Click on a social media icon/link.

**Expected Results:**
- <PERSON> opens in a **new browser tab**.
- Target page loads correctly (e.g., official company Facebook page).
- Original site remains open in the original tab.
- External links are properly identified.

---

## TC-004 – Footer Link Accessibility

**Preconditions:**  
User is navigating footer using keyboard or screen reader.

**Steps:**
1. Use Tab key to navigate through footer links.
2. Test footer links with accessibility tools.

**Expected Results:**
- All footer links are keyboard accessible.
- Links have proper focus indicators.
- Screen readers can properly identify and read links.
- Footer meets accessibility standards.

---

## TC-005 – Footer Link Accuracy

**Preconditions:**  
Footer contains various types of links.

**Steps:**
1. Test all footer links systematically.
2. Verify link destinations and functionality.

**Expected Results:**
- All footer links work correctly.
- Links lead to appropriate destinations.
- No broken or dead links exist.
- Link text accurately describes destination. 