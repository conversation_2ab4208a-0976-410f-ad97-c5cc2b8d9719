---
title: Login Test Cases
id: login
description: Test cases for user login functionality
summary: Test cases covering user authentication including valid/invalid credentials, password recovery, session management, and security validation scenarios.
---

# Login

Test cases for user login functionality

## TC-001 – Login with Valid Credentials

**Preconditions:**  
User has a valid registered account.

**Steps:**
1. Navigate to the login page.
2. Enter valid email and password.
3. Click "Login" or "Sign In".

**Expected Results:**
- User is successfully logged in.
- User is redirected to dashboard or intended page.
- User session is established.

---

## TC-002 – Login with Invalid Email

**Preconditions:**  
User is on the login page.

**Steps:**
1. Enter an email that doesn't exist in the system.
2. Enter any password.
3. Click "Login".

**Expected Results:**
- Error message is displayed indicating invalid credentials.
- User remains on login page.
- No session is established.

---

## TC-003 – Login with Invalid Password

**Preconditions:**  
User has a valid registered account.

**Steps:**
1. Enter valid email address.
2. Enter incorrect password.
3. Click "Login".

**Expected Results:**
- Error message is displayed indicating invalid credentials.
- User remains on login page.
- Account is not locked after single failed attempt.

---

## TC-004 – Login with Empty Fields

**Preconditions:**  
User is on the login page.

**Steps:**
1. Leave email and/or password fields empty.
2. Click "Login".

**Expected Results:**
- Validation errors are displayed for empty required fields.
- Login form is not submitted.
- User remains on login page.

---

## TC-005 – Forgot Password Link

**Preconditions:**  
User is on the login page.

**Steps:**
1. Click "Forgot Password" link.

**Expected Results:**
- User is redirected to password reset page.
- Password reset form is displayed.
- User can enter email to receive reset instructions. 