---
title: Mini-Cart Test Cases
id: mini-cart
description: Test cases for mini-cart functionality
summary: Test cases covering mini-cart display, item quantity updates, item removal, cart totals calculation, and mini-cart to full cart navigation scenarios.
---

# Mini-cart

Test cases for mini-cart functionality

## TC-001 – Display Mini-Cart

**Preconditions:**  
User has items in the shopping cart.

**Steps:**
1. Hover over or click cart icon.
2. Review mini-cart display.

**Expected Results:**
- Mini-cart opens and displays correctly.
- All cart items are shown with correct details.
- Item quantities and prices are accurate.
- Cart total is calculated correctly.
- Mini-cart is properly styled and positioned.

---

## TC-002 – Update Item Quantity in Mini-Cart

**Preconditions:**  
User has items in cart and mini-cart is open.

**Steps:**
1. Change quantity of an item in mini-cart.
2. Verify updates are reflected.

**Expected Results:**
- Item quantity is updated successfully.
- Cart total recalculates automatically.
- Changes are reflected immediately.
- Updated quantity persists across sessions.

---

## TC-003 – Remove Item from Mini-Cart

**Preconditions:**  
User has items in cart and mini-cart is open.

**Steps:**
1. Click remove button for a cart item.
2. Confirm removal if prompted.

**Expected Results:**
- Item is removed from cart successfully.
- Cart total is updated accordingly.
- Mini-cart display refreshes.
- Cart counter is updated.

---

## TC-004 – Proceed to Checkout from Mini-Cart

**Preconditions:**  
User has items in cart and mini-cart is open.

**Steps:**
1. Click "Checkout" or "Proceed to Checkout" button.
2. Verify navigation to checkout page.

**Expected Results:**
- User is redirected to checkout page.
- All cart items are transferred correctly.
- Checkout process begins with current cart contents.
- Mini-cart closes after navigation.

---

## TC-005 – Mini-Cart Responsiveness

**Preconditions:**  
User is on different device sizes.

**Steps:**
1. Test mini-cart on desktop, tablet, and mobile.
2. Verify display and functionality across devices.

**Expected Results:**
- Mini-cart displays properly on all screen sizes.
- All functionality works on touch devices.
- Layout adapts to different screen sizes.
- User experience remains consistent across devices. 