---
title: Order Return Test Cases
id: order-return
description: Test cases for order return functionality
summary: Test cases covering order return process including return request initiation, item selection, reason specification, return authorization, and return status tracking scenarios.
---

# Order return

Test cases for order return functionality

## TC-001 – Initiate Order Return

**Preconditions:**  
User has a delivered order eligible for return.

**Steps:**
1. Navigate to order details page.
2. Click "Return Items" or "Request Return".
3. Select items to return and provide reason.

**Expected Results:**
- Return request is initiated successfully.
- Return form is displayed with order items.
- User can select items and quantities to return.
- Return reasons are provided as options.
- Return request is submitted and confirmed.

---

## TC-002 – Return Eligibility Check

**Preconditions:**  
User is attempting to return an order.

**Steps:**
1. Check return eligibility for different order statuses.
2. Verify return window and conditions.

**Expected Results:**
- Return eligibility is checked against policy.
- Orders outside return window are rejected.
- Non-returnable items are identified.
- Clear messages explain return eligibility.

---

## TC-003 – Return Shipping Label

**Preconditions:**  
User has initiated a return request.

**Steps:**
1. Complete return request process.
2. Verify return shipping label generation.

**Expected Results:**
- Return shipping label is generated automatically.
- Label can be downloaded or printed.
- Return instructions are provided clearly.
- Tracking information is available for return shipment.

---

## TC-004 – Return Status Tracking

**Preconditions:**  
User has submitted return request.

**Steps:**
1. Check return status in account section.
2. Verify status updates throughout process.

**Expected Results:**
- Return status is displayed accurately.
- Status updates are provided in real-time.
- User receives notifications of status changes.
- Return timeline is clearly communicated.

---

## TC-005 – Return Refund Processing

**Preconditions:**  
Returned items have been received and processed.

**Steps:**
1. Verify refund is processed correctly.
2. Check refund amount and method.

**Expected Results:**
- Refund is processed within stated timeframe.
- Refund amount is calculated correctly.
- Refund is issued to original payment method.
- User receives refund confirmation notification. 