---
title: Password Change Test Cases
id: password-change
description: Test cases for password change functionality
summary: Test cases covering password change process including current password validation, new password requirements, confirmation matching, and security notification scenarios.
---

# Password change

Test cases for password change functionality

## TC-001 – Change Password with Valid Data

**Preconditions:**  
User is logged in and on the password change page.

**Steps:**
1. Enter current password correctly.
2. Enter new password that meets requirements.
3. Confirm new password by entering it again.
4. Click "Change Password" or "Update".

**Expected Results:**
- Password is successfully updated.
- Confirmation message is displayed.
- User can login with new password.
- Old password no longer works.

---

## TC-002 – Change Password with Incorrect Current Password

**Preconditions:**  
User is logged in and on the password change page.

**Steps:**
1. Enter incorrect current password.
2. Enter valid new password.
3. Confirm new password.
4. Click "Change Password".

**Expected Results:**
- Error message is displayed indicating current password is incorrect.
- Password is not changed.
- User remains on password change page.

---

## TC-003 – Change Password with Weak New Password

**Preconditions:**  
User is logged in and on the password change page.

**Steps:**
1. Enter correct current password.
2. Enter weak new password (e.g., "123" or "password").
3. Confirm weak password.
4. Click "Change Password".

**Expected Results:**
- Validation error is displayed for weak password.
- Password requirements are shown.
- Password is not changed until requirements are met.

---

## TC-004 – Change Password with Mismatched Confirmation

**Preconditions:**  
User is logged in and on the password change page.

**Steps:**
1. Enter correct current password.
2. Enter valid new password.
3. Enter different password in confirmation field.
4. Click "Change Password".

**Expected Results:**
- Error message is displayed indicating passwords don't match.
- Password is not changed.
- User must correct the mismatch before proceeding.

---

## TC-005 – Password Change Security

**Preconditions:**  
User is logged in and on the password change page.

**Steps:**
1. Successfully change password.
2. Verify security measures are in place.

**Expected Results:**
- Password change is logged for security audit.
- User receives email notification of password change.
- All other sessions are invalidated (if applicable).
- User must re-login if configured to do so. 