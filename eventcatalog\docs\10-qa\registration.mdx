---
title: Registration Test Cases
id: registration
description: Test cases for user registration functionality
summary: Test cases covering user registration flow including form validation, error handling, email verification, and successful account creation scenarios.
---

# Registration

Test cases for user registration functionality

## TC-001 – Registration with Valid Data

**Preconditions:**  
User is on the registration page.

**Steps:**
1. Fill in all required fields with valid data (first name, last name, email, password).
2. Click "Register" or "Create Account".

**Expected Results:**
- Account is created successfully.
- User is redirected to account dashboard or confirmation page.
- Welcome email is sent to the registered email address.

---

## TC-002 – Registration with Invalid Email Format

**Preconditions:**  
User is on the registration page.

**Steps:**
1. Enter an invalid email format (e.g., "user@invalid" or "plaintext").
2. Fill in other required fields with valid data.
3. Click "Register".

**Expected Results:**
- Validation error is displayed for email field.
- Registration form is not submitted.
- User remains on registration page.

---

## TC-003 – Registration with Existing Email

**Preconditions:**  
User is on the registration page.
An account with the test email already exists.

**Steps:**
1. Enter an email address that is already registered.
2. Fill in other required fields with valid data.
3. Click "Register".

**Expected Results:**
- Error message is displayed indicating email is already in use.
- Registration form is not submitted.
- Option to login with existing account is provided.

---

## TC-004 – Registration with Empty Required Fields

**Preconditions:**  
User is on the registration page.

**Steps:**
1. Leave one or more required fields empty.
2. Click "Register".

**Expected Results:**
- Validation errors are displayed for empty required fields.
- Registration form is not submitted.
- User remains on registration page with error messages.

---

## TC-005 – Password Strength Validation

**Preconditions:**  
User is on the registration page.

**Steps:**
1. Enter a weak password (e.g., "123" or "password").
2. Fill in other required fields with valid data.
3. Click "Register".

**Expected Results:**
- Password strength validation error is displayed.
- Requirements for strong password are shown.
- Registration form is not submitted until password meets requirements. 