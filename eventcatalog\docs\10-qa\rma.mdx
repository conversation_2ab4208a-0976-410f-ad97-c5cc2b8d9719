---
title: RMA Test Cases
id: rma
description: Test cases for RMA (Return Merchandise Authorization) functionality
summary: Test cases covering RMA process including authorization request, documentation requirements, approval workflow, tracking, and resolution scenarios.
---

# RMA

Test cases for RMA (Return Merchandise Authorization) functionality

## TC-001 – Create RMA Request

**Preconditions:**  
User has an order with items eligible for RMA.

**Steps:**
1. Navigate to RMA request page.
2. Enter order information and select items.
3. Provide reason for return and submit request.

**Expected Results:**
- RMA request is created successfully.
- RMA number is generated and displayed.
- User receives RMA confirmation email.
- RMA details are saved in user account.

---

## TC-002 – RMA Authorization Process

**Preconditions:**  
User has submitted RMA request.

**Steps:**
1. Wait for RMA authorization from support team.
2. Check RMA status updates.

**Expected Results:**
- RMA request is reviewed by support team.
- Authorization decision is communicated to user.
- Approved RMA includes return instructions.
- Rejected RMA includes explanation and alternatives.

---

## TC-003 – RMA Return Shipping

**Preconditions:**  
User has authorized RMA.

**Steps:**
1. Package items according to RMA instructions.
2. Use provided return shipping label.
3. Ship items back to company.

**Expected Results:**
- Return shipping label is provided with RMA.
- Packaging instructions are clear and complete.
- Return tracking information is available.
- Items are received and processed correctly.

---

## TC-004 – RMA Processing and Resolution

**Preconditions:**  
RMA items have been received by company.

**Steps:**
1. Items are inspected and evaluated.
2. Resolution is determined (refund, replacement, repair).

**Expected Results:**
- RMA items are processed within stated timeframe.
- Resolution is communicated to user.
- Refund, replacement, or repair is executed.
- User receives confirmation of resolution.

---

## TC-005 – RMA Documentation and Tracking

**Preconditions:**  
User has active RMA requests.

**Steps:**
1. Review RMA documentation and tracking.
2. Verify all RMA information is accessible.

**Expected Results:**
- Complete RMA history is maintained.
- All RMA communications are documented.
- RMA status is trackable throughout process.
- User can access RMA information anytime. 