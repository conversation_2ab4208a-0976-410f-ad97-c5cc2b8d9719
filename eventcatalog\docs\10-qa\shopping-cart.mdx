---
title: Shopping Cart Test Cases
id: shopping-cart
description: Test cases for shopping cart functionality
summary: Test cases covering shopping cart management including item addition/removal, quantity updates, cart persistence, coupon application, and cart validation scenarios.
---

# Shopping cart

Test cases for shopping cart functionality

## TC-001 – View Shopping Cart

**Preconditions:**  
User has items in the shopping cart.

**Steps:**
1. Navigate to shopping cart page.
2. Review cart contents and layout.

**Expected Results:**
- All cart items are displayed correctly.
- Product details (name, image, price, quantity) are shown.
- Cart totals are calculated accurately.
- Shipping and tax information is displayed.
- Cart page layout is user-friendly.

---

## TC-002 – Update Item Quantities

**Preconditions:**  
User is on shopping cart page with items.

**Steps:**
1. Change quantity of cart items.
2. Update cart or apply changes.

**Expected Results:**
- Item quantities are updated successfully.
- Cart totals recalculate automatically.
- Changes are saved and persist.
- Inventory limits are respected.

---

## TC-003 – Remove Items from Cart

**Preconditions:**  
User is on shopping cart page with items.

**Steps:**
1. Remove one or more items from cart.
2. Confirm removal if prompted.

**Expected Results:**
- Items are removed from cart successfully.
- Cart totals are updated accordingly.
- Cart page refreshes with updated contents.
- Empty cart message appears if all items removed.

---

## TC-004 – Apply Discount Codes

**Preconditions:**  
User has items in cart and valid discount code.

**Steps:**
1. Enter discount code in cart.
2. Apply the discount code.

**Expected Results:**
- Discount code is validated and applied.
- Cart total is reduced by discount amount.
- Discount details are displayed clearly.
- Invalid codes show appropriate error messages.

---

## TC-005 – Proceed to Checkout

**Preconditions:**  
User has items in cart and is ready to checkout.

**Steps:**
1. Click "Proceed to Checkout" button.
2. Verify navigation to checkout process.

**Expected Results:**
- User is redirected to checkout page.
- All cart items are transferred to checkout.
- Cart session is maintained during checkout.
- Checkout process begins smoothly. 