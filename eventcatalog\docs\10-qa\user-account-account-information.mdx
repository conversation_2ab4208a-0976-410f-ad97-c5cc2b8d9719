---
title: User Account - Account Information Test Cases
id: user-account-account-information
description: Test cases for user account information management
summary: Test cases covering account information display, editing, validation, and update functionality including personal details, contact information, and data persistence scenarios.
---

# User Account - Account information

Test cases for user account information management

## TC-001 – View Account Information

**Preconditions:**  
User is logged in and has existing account information.

**Steps:**
1. Navigate to account information section.
2. Review displayed account details.

**Expected Results:**
- All account information is displayed correctly.
- Personal details (name, email, phone) are shown.
- Account creation date and last login are visible.
- Information is formatted properly and readable.

---

## TC-002 – Edit Account Information

**Preconditions:**  
User is logged in and on account information page.

**Steps:**
1. Click "Edit" or "Update" button.
2. Modify account information fields.
3. Click "Save" or "Update".

**Expected Results:**
- Account information is successfully updated.
- Changes are reflected immediately.
- Confirmation message is displayed.
- Updated information persists after page refresh.

---

## TC-003 – Edit Account Information with Invalid Data

**Preconditions:**  
User is logged in and editing account information.

**Steps:**
1. Enter invalid data (e.g., invalid email format).
2. Attempt to save changes.

**Expected Results:**
- Validation errors are displayed.
- Invalid data is not saved.
- User remains on edit page with error messages.
- Valid data remains unchanged.

---

## TC-004 – Cancel Account Information Edit

**Preconditions:**  
User is logged in and editing account information.

**Steps:**
1. Make changes to account information.
2. Click "Cancel" or "Back" without saving.

**Expected Results:**
- Changes are not saved.
- User returns to account information view.
- Original information is preserved.
- No confirmation message is shown.

---

## TC-005 – Account Information Security

**Preconditions:**  
User is logged in and viewing account information.

**Steps:**
1. Verify sensitive information handling.
2. Check for proper data protection measures.

**Expected Results:**
- Sensitive information is properly masked or protected.
- Only authorized user can view their own information.
- Account information is transmitted securely.
- Audit trail is maintained for changes. 