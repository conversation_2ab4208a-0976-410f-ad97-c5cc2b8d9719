---
title: User Account - Wishlist Test Cases
id: user-account-wishlist
description: Test cases for user wishlist functionality
summary: Test cases covering wishlist item management including adding/removing products, wishlist sharing, stock notifications, and wishlist-to-cart conversion scenarios.
---

# User Account - Wishlist

Test cases for user wishlist functionality

## TC-001 – Add Product to Wishlist

**Preconditions:**  
User is logged in and viewing a product page.

**Steps:**
1. Click "Add to Wishlist" button on product page.
2. Navigate to wishlist section.

**Expected Results:**
- Product is successfully added to wishlist.
- Confirmation message is displayed.
- Product appears in wishlist with correct details.
- Wishlist counter is updated if displayed.

---

## TC-002 – Remove Product from Wishlist

**Preconditions:**  
User is logged in and has products in wishlist.

**Steps:**
1. Navigate to wishlist section.
2. Click "Remove" button for a specific product.

**Expected Results:**
- Product is removed from wishlist.
- Confirmation message is displayed.
- Product no longer appears in wishlist.
- Wishlist counter is updated.

---

## TC-003 – Move Product from Wishlist to Cart

**Preconditions:**  
User is logged in and has products in wishlist.

**Steps:**
1. Navigate to wishlist section.
2. Click "Add to Cart" button for a wishlist item.

**Expected Results:**
- Product is added to shopping cart.
- Product remains in wishlist (unless configured otherwise).
- Cart counter is updated.
- User can proceed to checkout.

---

## TC-004 – View Wishlist Details

**Preconditions:**  
User is logged in and has products in wishlist.

**Steps:**
1. Navigate to wishlist section.
2. Review wishlist contents.

**Expected Results:**
- All wishlist products are displayed correctly.
- Product details (name, price, image) are shown.
- Product availability status is indicated.
- Date added to wishlist is visible.

---

## TC-005 – Share Wishlist

**Preconditions:**  
User is logged in and has products in wishlist.

**Steps:**
1. Navigate to wishlist section.
2. Use share functionality (if available).

**Expected Results:**
- Wishlist can be shared via email or social media.
- Shared wishlist is accessible to recipients.
- Privacy settings are respected.
- Share link works correctly. 