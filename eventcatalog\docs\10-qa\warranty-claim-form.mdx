---
title: Warranty Claim Form Test Cases
id: warranty-claim-form
description: Test cases for warranty claim form functionality
summary: Test cases covering warranty claim form including form display, validation, photo uploads, equipment registration, and submission workflow scenarios.
---

# Warranty Claim Form

Test cases for warranty claim form functionality

## TC-001 – Open Warranty Claim Form

**Preconditions:**  
User is on the website, where warranty claim is accessible.

**Steps:**
1. Click on "Warranty Claim" or equivalent link.

**Expected Results:**
- Full warranty claim form is displayed.
- All expected sections are visible: Personal Info, Contact Info, Shipping Address, Part Info, Additional Info, File Upload, Submit.
- Form is properly formatted and accessible.

---

## TC-002 – Submit Form With Required Valid Data

**Preconditions:**  
Warranty claim form is open.

**Steps:**
1. Fill in:
   - Personal Info: First Name, Last Name
   - Contact Info: Phone, Email
   - Shipping Address: Country, Street, City, State, Zip
   - Part 1: Model Number, Serial Number, Part Number
   - Claim Type: Replacement or Refund
2. Click "Submit".

**Expected Results:**
- Submission is successful.
- User sees confirmation message (e.g., "Your warranty claim has been submitted").
- Email confirmation may be sent.
- C<PERSON>m is logged in the system for processing.

---

## TC-003 – Submit Empty Form

**Preconditions:**  
Warranty claim form is open.

**Steps:**
1. Click "Submit" without filling any fields.

**Expected Results:**
- Required fields are marked with validation errors.
- Submission is blocked until errors are corrected.
- Error messages are clear and helpful.

---

## TC-004 – Add and Remove Multiple Part Sections

**Preconditions:**  
Warranty claim form is open.

**Steps:**
1. Click "Add another part".
2. Fill in data for Part 2.
3. Click "Add another part" again and fill Part 3.
4. Remove Part 2 by clicking "Delete".

**Expected Results:**
- New part sections are dynamically added.
- Deleted parts are removed without affecting others.
- Remaining parts retain their data.
- Form remains stable after changes.

---

## TC-005 – Upload and Remove Photos

**Preconditions:**  
Warranty claim form is open.

**Steps:**
1. Upload at least one image in the "Upload photo" section.
2. Verify preview appears.
3. Click "Delete" next to the uploaded file.

**Expected Results:**
- Uploaded file is shown with a preview or filename.
- Delete removes the file immediately.
- Form remains stable after changes.
- File upload supports appropriate image formats.

---

## TC-006 – Equipment Registration Toggle

**Preconditions:**  
Form is open, and field "Is your equipment registered?" is visible.

**Steps:**
1. Toggle between "Yes" and "No".

**Expected Results:**
- Selection persists when switching sections.
- No validation errors unless required by business logic.
- Toggle state is clearly indicated.

---

## TC-007 – Validation of Email and Zip Code Format

**Preconditions:**  
Form is open.

**Steps:**
1. Enter invalid email (e.g., "user@") or ZIP code (e.g., "abc").
2. Click "Submit".

**Expected Results:**
- Proper validation error shown next to invalid field.
- Form is not submitted.
- User can correct errors and resubmit. 