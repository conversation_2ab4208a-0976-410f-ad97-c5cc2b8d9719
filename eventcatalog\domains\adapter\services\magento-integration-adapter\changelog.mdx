---
id: magento-integration-adapter
createdAt: 2025-06-27
---

## Changes

### 2025-06-27 - Version v0.0.2

#### Added

- **API Key Authentication**: Added optional API key authentication for all endpoints
  - Configurable via `RequireInboundAuthentication` feature flag
  - When enabled, all requests must include `X-API-Key: {api-key}` header
  - Invalid or missing keys return 401 Unauthorized
  - API keys are configured per environment
  - Uses ASP.NET Core authentication with `AspNetCore.Authentication.ApiKey` package

- **Security Documentation**: Updated all endpoint specifications to include:
  - API key authentication security scheme
  - 401 Unauthorized response documentation
  - Authentication configuration instructions

#### How to Enable Authentication

1. Set `MagentoApi__RequireInboundAuthentication=true` in your environment configuration
2. Configure `MagentoApi__InboundApiKey` with your secure API key
3. Include the API key in all requests: `X-API-Key: your-api-key-here`

### 2025-05-14 - Version v0.0.1

- Base implementation of Magento Integration Adapter API
- Support for order management (create, fetch, status updates)
- Support for cart management
- Support for customer management
- Support for email subscription management
- Correlation ID tracking for all requests
- Standardized error responses