---
id: magento-integration-adapter
version: 0.0.2
name: Magento Integration Adapter API
summary: |
  Service that handles the communications between Magento and the integration layer
owners:
    - euvic
    - enterprise
receives:
  - id: order-created-query
    version: 1.0.0
  - id: order-fetch-query
    version: 0.0.1
  - id: cart-created-query
    version: 1.1.1
  - id: cart-updated-query
    version: 1.0.0
  - id: customer-created-query
    version: 0.0.1
  - id: customer-updated-query
    version: 0.0.1
  - id: customer-deleted-query
    version: 0.0.1
  - id: customer-address-created-query
    version: 0.0.1
  - id: customer-address-updated-query
    version: 0.0.1
  - id: customer-address-deleted-query
    version: 0.0.1
  - id: guest-email-subscribed-query
    version: 0.0.1
  - id: guest-email-unsubscribed-query
    version: 0.0.1
  - id: customer-email-subscribed-query
    version: 0.0.1
  - id: customer-email-unsubscribed-query
    version: 0.0.1
  - id: order-acknowledged-by-erp-event
    version: 0.0.1
  - id: cart-created-event
    version: 1.0.0
  - id: cart-creation-failed-event
    version: 1.0.0
sends:
  - id: record-order-created-command
    version: 0.0.1
  - id: record-cart-created-command
    version: 1.0.0
  - id: magento-order-status-update
    version: 0.0.1
  - id: magento-ship-order
    version: 0.0.1
repository:
  language: C#
  url: https://github.com/ALSSoftware/evolution-integration-layer
---

## Overview

The Magento Integration Adapter is a critical component of the system responsible for managing the communications between Magento and the integration layer. It handles various operations including order management, status updates, and synchronization between systems.

## Features

### Order Management
- Receives order creation events from Magento
- Processes and validates order data
- Forwards orders to appropriate systems

### Order Status Updates
- Sends order status updates to Magento
- Manages order comments and notifications
- Handles customer communication preferences

### Integration Layer Communication
- Maintains consistent data format between systems
- Handles error scenarios and retries
- Provides logging and monitoring

## API Versioning & Traceability

- **Base Path:** All endpoints are now versioned. Use `/api/v1.0/magento-integration-adapter` as the base path for all API calls.
- **Correlation ID:** Every request and response includes an `X-Correlation-ID` header for distributed tracing. You may provide this header, or the API will generate one for you.

## Authentication

The Magento Integration Adapter API supports optional API key authentication:

- **Authentication Method:** API Key
- **Header Format:** `X-API-Key: {api-key}`
- **Required:** Configurable (can be enabled/disabled via feature flag)

When authentication is enabled:
- All requests must include a valid API key in the `X-API-Key` header
- Invalid or missing API keys will receive a 401 Unauthorized response
- The API key must match the configured inbound key for the environment

Contact the Enterprise Integration team to obtain API credentials for your environment.

<Tiles>
    <Tile icon="DocumentIcon" href={`/docs/services/${frontmatter.id}/${frontmatter.version}/changelog`}  title="View the changelog" description="Want to know the history of this service? View the change logs" />
    <Tile icon="UserGroupIcon" href="/docs/teams/enterprise-integration" title="Contact the team" description="Any questions? Feel free to contact the owners" />
    {/* <Tile icon="BoltIcon" href={`/visualiser/services/${frontmatter.id}/${frontmatter.version}`} title={`Sends ${frontmatter.sends.length} messages`} description="This service sends messages to downstream consumers" /> */}
    <Tile icon="BoltIcon"  href={`/visualiser/services/${frontmatter.id}/${frontmatter.version}`} title={`Receives ${frontmatter.receives.length} messages`} description="This service receives messages from other services" />
</Tiles>

## Architecture diagram

<NodeGraph />


<Steps title="How to connect to the Magento Integration Adapter API">
  <Step title="Obtain API credentials">
    Request API credentials from the Enterprise Integration team. You will receive:
    - API Key for authenticating your requests
    - Base URL for the environment
    - Information about whether authentication is enabled
  </Step>
  <Step title="Configure your HTTP client">
    Set up your HTTP client with the required headers:

    ```bash
    # Example using curl
    curl -X POST https://your-environment/api/v1.0/magento-integration-adapter/order-created \
      -H "X-API-Key: your-api-key-here" \
      -H "Content-Type: application/json" \
      -d @order-data.json
    ```
  </Step>
  <Step title="Initialize the client">
    Configure your integration with authentication:

    ```php
    // PHP example
    $client = new HttpClient([
        'base_uri' => 'https://your-environment/api/v1.0/magento-integration-adapter/',
        'headers' => [
            'X-API-Key' => $apiKey,
            'Content-Type' => 'application/json'
        ]
    ]);
    ```
  </Step>
  <Step title="Handle authentication errors">
    Be prepared to handle authentication responses:

    - **401 Unauthorized**: Invalid or missing API key
    - **200/202 OK/Accepted**: Request processed successfully

    Always check the response status and handle errors appropriately.
  </Step>
</Steps>

