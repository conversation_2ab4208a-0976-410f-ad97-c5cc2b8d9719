---
createdAt: 2025-06-16
---

## Changes

### 2025-07-02 - Version 1.2.1
#### Documentation Updates
- Adjusted documentation to specify ISO 3166-2:US and ISO 3166-2:CA formatting standard for region codes in address-related fields.

### 2025-07-01 - Version 1.2.0
### Entity Updates
- Added `customer` attribute to Cart entity
- Removed `customer_email`, `customer_id` attributes from Cart entity (moved to `customer` attribute)
- Added `customer_note_notify`, `customer_tax_class_id` attributes to Cart entity

### 2025-06-18 - Version 1.1.1
#### Documentation Updates
- Changed response code to HTTP 202
- Added HTTP 409 conflict response payload

### 2025-06-16 - Version 1.1.0
#### Documentation Updates
- Updated OpenAPI docs to adjust Cart entity data types

### 2025-06-06 - Version 1.0.0
- **Initial version with POST endpoint**
- Added OpenAPI documentation with comprehensive examples
