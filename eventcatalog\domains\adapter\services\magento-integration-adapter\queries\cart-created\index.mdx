---
id: cart-created-query
name: Cart Created Query
version: 1.2.0
summary: |
  Query automatically triggered by <PERSON>gent<PERSON> when a new shopping cart is created, sending complete cart information to the integration layer
producers:
  - magento
owners:
  - euvic
specifications:
  - type: openapi
    path: 'openapi.yml'
channels:
  - id: magento-integration-adapter.{env}.rest.queries
    parameters:
      env: local
---

## Overview

This query is automatically triggered by <PERSON><PERSON><PERSON> whenever a new shopping cart is created in the system. When a cart is created (either by a customer, guest, or through B2B quote initiation), Magento sends the complete cart information to the integration layer, which can then be consumed by external systems for further processing.

## Architecture diagram

<NodeGraph />

## Query Details

### Trigger Point
- Automatically triggered after cart creation in Magento
- Triggers include:
  - Customer initiates a new cart
  - Guest creates a new cart
  - B2B customer initiates a new negotiable quote
- No manual invocation needed
- Part of Magento's cart management workflow

### Data Structure
The integration layer receives comprehensive cart data matching Magento's `quote-cart-interface` structure from the `GET /V1/carts/{cartId}` API endpoint. This includes:

- Cart identification and metadata
- Customer information (if available)
- Store and website context
- B2B-specific data (for negotiable quotes)

## Integration Guidelines

### Critical Fields
When processing the cart data, external systems MUST store these critical IDs:
- `cart-id`: Magento's internal cart/quote ID
- `customer.id`: Customer's ID in Magento (if not guest)
- `store_id`: Store view identifier

### Processing Requirements
External systems should:
1. Store all critical IDs for future reference
2. Handle both guest and registered customer carts
3. Support B2B quote initialization
4. Implement idempotency checks for duplicate messages

### Error Handling
Systems consuming this data should:
- Acknowledge receipt properly
- Implement retry mechanisms
- Log all received cart data
- Handle partial or malformed data gracefully
- Validate data integrity
- Handle B2B quote-specific validation

## Notes
- This query is part of Magento's automated cart management workflow
- The data structure matches Magento's internal cart/quote representation
- All monetary values include currency information and conversion rates
- B2B features (negotiable quotes) are included when applicable
- External systems must maintain ID mappings for future operations

## Magento API Reference
This query uses the response format from Magento 2 API endpoint:
`GET /V1/carts/{cartId}` or `GET /V1/carts/mine` for logged-in customers

For more details, see [Magento API Documentation](https://developer.adobe.com/commerce/webapi/rest/quick-reference/)
