openapi: "3.1.0"
info:
  title: Cart Created
  version: 1.2.0
  description: |
    OpenAPI specification for the Cart Created query in Magento Integration Adapter. This endpoint receives a new shopping cart initialization event in Magento for both customers and guests, supporting B2B features when applicable.
servers:
  - url: http://localhost:7501/api/v0.1/magento-integration-adapter
paths:
  /cart-created:
    post:
      summary: Create a new shopping cart
      description: Propagates a new Magento-initialized cart for a guest or logged in customer to the eligible event listeners.
      security:
        - apiKey: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CartCreatedRequest'
            example:
              cart_id: 12345
              store_id: 1
              customer:
                id: 54321
                email: "<EMAIL>"
                firstname: "<PERSON>"
                lastname: "Doe"
                group_id: 1
              customer_is_guest: false
              customer_note_notify: false
              customer_tax_class_id: 3
              items_count: 1
              items_qty: 2
              created_at: "2024-03-21T10:00:00Z"
              updated_at: "2024-03-21T10:00:00Z"
              currency: "USD"
              items:
                - item_id: 67890
                  sku: "PROD-123"
                  qty: 2
                  name: "Sample Product"
                  price: 99.99
                  product_type: "simple"
                  quote_id: 12345
              totals:
                subtotal: 199.98
                grand_total: 199.98
                discount_amount: 0.00
              is_negotiable_quote: false
      responses:
        '202':
          description: Cart successfully created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                data:
                  cart_id: 12345
                  store_id: 1
                  customer:
                    id: 54321
                    email: "<EMAIL>"
                    firstname: "John"
                    lastname: "Doe"
                    group_id: 1
                  customer_is_guest: false
                  customer_note_notify: false
                  customer_tax_class_id: 3
                  items_count: 1
                  items_qty: 2
                  created_at: "2024-03-21T10:00:00Z"
                  updated_at: "2024-03-21T10:00:00Z"
                  currency: "USD"
                  items:
                    - item_id: 67890
                      sku: "PROD-123"
                      qty: 2
                      name: "Sample Product"
                      price: 99.99
                      product_type: "simple"
                      quote_id: 12345
                  totals:
                    subtotal: 199.98
                    grand_total: 199.98
                    discount_amount: 0.00
                  is_negotiable_quote: false
                error: null
        '401':
          description: Unauthorized - Invalid or missing API key
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                  message:
                    type: string
                  type:
                    type: string
              example:
                error: "Unauthorized"
                message: "Invalid API key"
                type: "AuthenticationError"
        '400':
          description: Bad Request - Invalid cart data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiErrorResponse'
              example:
                data: null
                error:
                  message: "Invalid cart data provided"
                  type: "ValidationError"
                  details:
                    - field: "store_id"
                      message: "Store ID is required"
        '409':
          description: Conflict - Cart already exists
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiErrorResponse'
              example:
                data: null
                error:
                  message: "Cart with ID '12345' already exists."
                  type: "CONFLICT_ERROR"
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiErrorResponse'
              example:
                data: null
                error:
                  message: "An unexpected error occurred"
                  type: "InternalServerError"
components:
  schemas:
    ApiResponse:
      type: object
      description: Standard envelope wrapper for all API responses
      properties:
        data:
          nullable: true
          description: Response data (null for error responses)
          $ref: '#/components/schemas/CartCreatedResponse'
        error:
          type: object
          nullable: true
          description: Error details (null for successful responses)
      required:
        - data

    ApiErrorResponse:
      type: object
      description: Standard envelope wrapper for error responses
      properties:
        data:
          type: object
          nullable: true
          description: Always null for error responses
        error:
          type: object
          nullable: false
          description: Error details
          properties:
            message:
              type: string
              description: Human-readable error message
            type:
              type: string
              description: Error type classification
            details:
              type: array
              nullable: true
              description: Additional error details for validation errors
              items:
                type: object
                properties:
                  field:
                    type: string
                    description: Field name with validation error
                  message:
                    type: string
                    description: Field-specific error message
      required:
        - data
        - error

    CartCreatedRequest:
      type: object
      required:
        - cart_id
        - store_id
        - currency
        - items
      properties:
        cart_id:
          type: integer
          description: Unique Identifier for the cart generated by Magento (32 bit integer)
        store_id:
          type: integer
          description: Identifier of the store where the cart is being created
        customer:
          $ref: '#/components/schemas/customer-interface'
          nullable: true
        customer_is_guest:
          type: boolean
          description: Whether the customer is a guest
        customer_note_notify:
          type: boolean
          description: Whether to notify customer about the note
        customer_tax_class_id:
          type: integer
          description: Customer tax class ID
        created_at:
          type: string
          format: date-time
          description: Timestamp when the cart was created (ISO-8601)
        updated_at:
          type: string
          format: date-time
          description: Timestamp when the cart was last updated (ISO-8601)
        items_count:
          type: integer
          description: Total number of items in the cart
        items_qty:
          type: integer
          description: Total quantity of items in the cart
        currency:
          type: string
          description: Currency code for the cart (ISO-4217)
        items:
          type: array
          description: List of items to add to the cart
          items:
            type: object
            required:
              - sku
              - qty
              - product_type
            properties:
              item_id:
                type: integer
                description: Unique Identifier for each item in the cart. This id is unique for each cart.
              sku:
                type: string
                description: SKU of the product
              qty:
                type: integer
                description: Quantity of the product
              quote_id:
                type: integer
                description: Unique Identifier for the quote associated with the item (if applicable).
              name:
                type: string
                description: Name of the product
              product_type:
                type: string
                description: Type of the product
                enum:
                  - simple
                  - configurable
                  - bundle
                  - grouped
                  - virtual
                  - downloadable
                  - gift_card
              product_option:
                type: object
                description: Additional options for configurable, bundle, or custom products
                properties:
                  extension_attributes:
                    type: object
                    description: Custom attributes for the product option
                    properties:
                      configurable_item_options:
                        type: array
                        items:
                          type: object
                          properties:
                            option_id:
                              type: string
                              description: ID of the option
                            option_value:
                              type: string
                              description: Selected value for the option
                      bundle_options:
                        type: array
                        items:
                          type: object
                          properties:
                            option_id:
                              type: string
                              description: ID of the bundle option
                            option_qty:
                              type: integer
                              description: Quantity selected for the bundle option
                            option_selections:
                              type: array
                              items:
                                type: string
                              description: Selected product IDs for the bundle option
        totals:
          type: object
          description: Totals for the cart
          properties:
            subtotal:
              type: number
              description: Subtotal amount for the cart
            grand_total:
              type: number
              description: Grand total amount for the cart
            discount_amount:
              type: number
              description: Discount amount applied to the cart (if applicable)
        is_negotiable_quote:
          type: boolean
          description: Indicates if this is a B2B negotiable quote
          default: false
    CartCreatedResponse:
      type: object
      properties:
        cart_id:
          type: integer
        store_id:
          type: integer
        customer:
          $ref: '#/components/schemas/customer-interface'
        created_at:
          type: string
          format: date-time
          description: Timestamp when the cart was created (ISO-8601)
        updated_at:
          type: string
          format: date-time
          description: Timestamp when the cart was last updated (ISO-8601)
        customer_is_guest:
          type: boolean
          description: Whether the customer is a guest
        customer_note_notify:
          type: boolean
          description: Whether to notify customer about the note
        customer_tax_class_id:
          type: integer
          description: Customer tax class ID
        currency:
          type: string
        items_count:
          type: integer
          description: Total number of items in the cart
        items_qty:
          type: integer
          description: Total quantity of items in the cart
        items:
          type: array
          items:
            type: object
            properties:
              item_id:
                type: integer
              sku:
                type: string
              qty:
                type: integer
              name:
                type: string
              price:
                type: number
              product_type:
                type: string
              quote_id:
                type: integer
              product_option:
                type: object
                properties:
                  extension_attributes:
                    type: object
                    properties:
                      configurable_item_options:
                        type: array
                        items:
                          type: object
                          properties:
                            option_id:
                              type: string
                            option_value:
                              type: string
                      bundle_options:
                        type: array
                        items:
                          type: object
                          properties:
                            option_id:
                              type: string
                            option_qty:
                              type: integer
                            option_selections:
                              type: array
                              items:
                                type: string
        totals:
          type: object
          properties:
            subtotal:
              type: number
            grand_total:
              type: number
            discount_amount:
              type: number
        is_negotiable_quote:
          type: boolean
          description: Indicates if this is a B2B negotiable quote
          default: false
    customer-interface:
      type: object
      properties:
        id:
          type: integer
          description: Customer ID
        email:
          type: string
          format: email
          description: Customer email
        firstname:
          type: string
          description: Customer first name
        lastname:
          type: string
          description: Customer last name
        group_id:
          type: integer
          description: Customer group ID

  securitySchemes:
    apiKey:
      type: apiKey
      in: header
      name: X-API-Key
      description: |
        API Key authentication.
        Authentication may be optional depending on environment configuration.
        Contact the Enterprise Integration team for API credentials.
