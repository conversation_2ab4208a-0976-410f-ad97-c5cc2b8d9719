---
id: cart-updated-query
name: Cart Updated Query
version: 1.1.0
summary: |
  Query automatically triggered by <PERSON>gent<PERSON> when a shopping cart is updated, sending complete cart information to the integration layer
producers:
  - magento
owners:
  - euvic
specifications:
  - type: openapi
    path: 'openapi.yml'
channels:
  - id: magento-integration-adapter.{env}.rest.queries
    parameters:
      env: local
---

## Overview

This query is automatically triggered by <PERSON><PERSON><PERSON> whenever a shopping cart is modified in the system. When a cart is updated (items added, removed, or quantities changed), Magento sends the complete cart information to the integration layer, which can then be consumed by external systems for further processing.

## Architecture diagram

<NodeGraph />

## Query Details

### Trigger Point
- Automatically triggered after any cart modification in Magento
- Triggers include:
  - Adding items to cart
  - Removing items from cart
  - Updating item quantities
  - Applying/removing coupons
  - Updating shipping methods
  - Updating payment methods
  - Updating shipping/billing address
  - B2B quote updates
- No manual invocation needed
- Part of Magento's cart management workflow

### Data Structure
The integration layer receives comprehensive cart data matching Magento's `quote-cart-interface` structure from the `GET /V1/carts/{cartId}` API endpoint. This includes:

- Cart identification and metadata
- Complete customer information
- All cart items with full details
- Selected shipping/billing addresses
- Applied discounts and totals
- B2B negotiable quote information (when applicable)

## Integration Guidelines

### Critical Fields
When processing the cart data, external systems MUST store these critical IDs:
- `cart-id`: Magento's internal cart/quote ID
- `items[].item_id`: Unique identifier for each cart item
- `items[].quote_id`: Reference back to the parent cart
- `customer.id`: Customer's ID in Magento (if not guest)

### Processing Requirements
External systems should:
1. Store all critical IDs for future reference
2. Process cart items using the provided `item_id`
3. Handle both guest and registered customer carts
4. Support all product types (simple, configurable, etc.)
5. Implement idempotency checks for duplicate messages

### Error Handling
Systems consuming this data should:
- Acknowledge receipt properly
- Implement retry mechanisms
- Log all received cart data
- Handle partial or malformed data gracefully
- Validate data integrity
- Handle B2B quote-specific validation

## Notes
- This query is part of Magento's automated cart management workflow
- The data structure matches Magento's internal cart/quote representation
- All monetary values include currency information and conversion rates
- Address information includes standardized regional data
- B2B features (negotiable quotes) are included when applicable
- External systems must maintain ID mappings for future operations

## Magento API Reference
This query uses the response format from Magento 2 API endpoint:
`GET /V1/carts/{cartId}` or `GET /V1/carts/mine` for logged-in customers

For more details, see [Magento API Documentation](https://developer.adobe.com/commerce/webapi/rest/quick-reference/)
