openapi: "3.1.0"
info:
  title: Cart Updated
  version: 1.0.0
  description: |
    OpenAPI specification for the Cart Updated query in Magento Integration Adapter.
    This query is triggered automatically when a cart is updated in Magento and matches the response format of Magento's GET /V1/carts/{cartId} endpoint.
servers:
  - url: http://localhost:7501/api/v0.1/magento-integration-adapter
paths:
  /cart-updated:
    post:
      summary: Receive cart update notification from Magento
      description: Endpoint that receives cart data when Magento triggers a cart update notification.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/quote-cart-interface'
      responses:
        '200':
          description: Cart update notification processed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '400':
          description: Bad Request - Invalid cart data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiErrorResponse'
              example:
                data: null
                error:
                  message: "Invalid cart data provided"
                  type: "ValidationError"
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiErrorResponse'
              example:
                data: null
                error:
                  message: "An unexpected error occurred"
                  type: "InternalServerError"
components:
  schemas:
    ApiResponse:
      type: object
      description: Standard envelope wrapper for all API responses
      properties:
        data:
          type: object
          nullable: true
          description: Response data (null for error responses)
          $ref: '#/components/schemas/quote-cart-interface'
        error:
          type: object
          nullable: true
          description: Error details (null for successful responses)
      required:
        - data

    ApiErrorResponse:
      type: object
      description: Standard envelope wrapper for error responses
      properties:
        data:
          type: object
          nullable: true
          description: Always null for error responses
        error:
          type: object
          nullable: false
          description: Error details
          properties:
            message:
              type: string
              description: Human-readable error message
            type:
              type: string
              description: Error type classification
            details:
              type: array
              nullable: true
              description: Additional error details for validation errors
              items:
                type: object
                properties:
                  field:
                    type: string
                    description: Field name with validation error
                  message:
                    type: string
                    description: Field-specific error message
      required:
        - data
        - error

    quote-cart-interface:
      type: object
      description: Cart data structure matching Magento's quote-cart-interface
      properties:
        id:
          type: integer
          description: Cart/Quote ID
        created_at:
          type: string
          format: date-time
          description: Cart creation timestamp (ISO-8601)
        updated_at:
          type: string
          format: date-time
          description: Cart last update timestamp (ISO-8601)
        is_active:
          type: boolean
          description: Whether the cart is active
        is_virtual:
          type: boolean
          description: Whether the cart contains only virtual products
        items:
          type: array
          description: Array of items in the cart
          items:
            $ref: '#/components/schemas/quote-cart-item-interface'
        items_count:
          type: integer
          description: Number of different items in the cart
        items_qty:
          type: number
          description: Total quantity of all items in the cart
        customer:
          $ref: '#/components/schemas/customer-interface'
        billing_address:
          $ref: '#/components/schemas/cart-address-interface'
        orig_order_id:
          type: integer
          description: Original order ID if this quote is created from an order
        currency:
          $ref: '#/components/schemas/cart-currency-interface'
        customer_is_guest:
          type: boolean
          description: Whether the customer is a guest
        customer_note_notify:
          type: boolean
          description: Whether to notify customer about the note
        customer_tax_class_id:
          type: integer
          description: Customer tax class ID
        store_id:
          type: integer
          description: Store ID
        extension_attributes:
          type: object
          properties:
            shipping_assignments:
              type: array
              items:
                $ref: '#/components/schemas/shipping-assignment-interface'
            negotiable_quote:
              $ref: '#/components/schemas/negotiable-quote-interface'
              nullable: true

    quote-cart-item-interface:
      type: object
      required:
        - item_id
        - sku
        - qty
        - name
        - price
        - product_type
        - quote_id
      properties:
        item_id:
          type: integer
          description: Item ID
        sku:
          type: string
          description: Product SKU
        qty:
          type: number
          description: Quantity
        name:
          type: string
          description: Product name
        price:
          type: number
          description: Product price
        product_type:
          type: string
          description: Product type
          enum:
            - simple
            - configurable
            - bundle
            - grouped
            - virtual
            - downloadable
            - gift_card
        quote_id:
          type: integer
          description: Quote ID
        product_option:
          type: object
          properties:
            extension_attributes:
              type: object
              properties:
                configurable_item_options:
                  type: array
                  items:
                    type: object
                    properties:
                      option_id:
                        type: string
                        description: Option ID
                      option_value:
                        type: string
                        description: Selected option value

    customer-interface:
      type: object
      properties:
        id:
          type: integer
          description: Customer ID
        email:
          type: string
          format: email
          description: Customer email
        firstname:
          type: string
          description: Customer first name
        lastname:
          type: string
          description: Customer last name
        group_id:
          type: integer
          description: Customer group ID

    cart-address-interface:
      type: object
      properties:
        id:
          type: integer
          description: Address ID
        customer_address_id:
          type: integer
          description: Customer address ID if saved in the address book
        firstname:
          type: string
          description: First name
        lastname:
          type: string
          description: Last name
        street:
          type: array
          items:
            type: string
          description: Street address lines
        city:
          type: string
          description: City
        region:
          type: string
          description: Region name
        region_id:
          type: integer
          description: Region ID
        postcode:
          type: string
          description: Postal code
        country_id:
          type: string
          description: Country code (ISO-3166-1 alpha-2)
        telephone:
          type: string
          description: Phone number

    cart-currency-interface:
      type: object
      properties:
        global_currency_code:
          type: string
          description: Global currency code (ISO-4217)
        base_currency_code:
          type: string
          description: Base currency code (ISO-4217)
        store_currency_code:
          type: string
          description: Store currency code (ISO-4217)
        quote_currency_code:
          type: string
          description: Quote currency code (ISO-4217)
        store_to_base_rate:
          type: number
          description: Store currency to base currency rate
        store_to_quote_rate:
          type: number
          description: Store currency to quote currency rate
        base_to_global_rate:
          type: number
          description: Base currency to global currency rate
        base_to_quote_rate:
          type: number
          description: Base currency to quote currency rate

    shipping-assignment-interface:
      type: object
      properties:
        shipping:
          type: object
          properties:
            address:
              $ref: '#/components/schemas/cart-address-interface'
            method:
              type: string
              description: Shipping method code

    negotiable-quote-interface:
      type: object
      nullable: true
      properties:
        quote_id:
          type: integer
          description: Quote ID
        is_regular_quote:
          type: boolean
          description: Is regular quote
        status:
          type: string
          description: Quote status
        negotiated_price_type:
          type: integer
          description: Negotiated price type
        negotiated_price_value:
          type: number
          description: Negotiated price value
