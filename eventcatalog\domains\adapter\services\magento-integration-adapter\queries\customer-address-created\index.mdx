---
id: customer-address-created-query
name: Customer Address Created Query
version: 0.0.1
summary: |
  Asynchronous notification query triggered automatically by Magento after a customer address is successfully created
producers:
  - magento
owners:
  - euvic
specifications:
  - type: openapi
    path: 'openapi.yml'
channels:
  - id: magento-integration-adapter.{env}.rest.queries
    parameters:
      env: local
---

## Overview

This query represents an asynchronous notification from Magento that is automatically triggered after a customer address has been successfully created in the system. The notification is sent as a confirmation of the address creation, containing the final state of the created address including any server-side modifications or validations.

## Architecture diagram

<NodeGraph />

## Query Details

### Trigger Point
- Automatically triggered after successful address creation confirmation in Magento
- Represents a post-creation notification
- Triggered after:
  - Successful address creation
  - Batch address imports
  - System-initiated address creations
  - B2B company admin address management

### Data Structure
Uses response format from Magento 2 API endpoint:
`GET /V1/customers/addresses/{addressId}`
[Magento API Documentation](https://developer.adobe.com/commerce/webapi/rest/resources/customer/customerAddressRepositoryV1/)

For complete payload structure and examples, see the openapi.yml specification.

### Critical Fields
- `id` - Unique identifier for the created address
- `customer_id` - Link to the customer
- `region_id` and `country_id` - Validated Magento region/country codes
- `default_shipping` and `default_billing` - Final address usage flags
- `street` - Array of address lines
- Required contact information:
  - `telephone`
  - `postcode`
  - `city`
  - `firstname`
  - `lastname`

## Integration Guidelines

### Processing Requirements
- Verify address was successfully created
- Update local address records
- Process any post-creation workflows
- Handle address type assignments

### Error Handling
- Log any discrepancies between requested and created address
- Handle missing or invalid fields in the notification
- Process region/country validation results
- Handle duplicate address scenarios

## Notes

- This is an asynchronous notification of successful address creation
- The address data represents the final state after all Magento validations
- May include additional fields or modifications from the original creation request
- Should be used for confirming and synchronizing address creation
- Address IDs are unique within the scope of a Magento instance
- For US and CA addresses, the `region` field now uses the ISO 3166-2:US or ISO 3166-2:CA code (e.g., "NY" for New York, "ON" for Ontario) as per documentation update on 2025-07-02.
