openapi: "3.1.0"
info:
  title: Customer Address Created
  version: 0.0.1
  description: |
    OpenAPI specification for the Customer Address Created query in Magento Integration Adapter.
    This query represents an asynchronous notification sent after a customer address is successfully created in Magento.

    Uses the response format from Magento 2 API endpoint:
    GET /V1/customers/addresses/{addressId}

    For more details, see [Magento API Documentation](https://developer.adobe.com/commerce/webapi/rest/resources/customer/customerAddressRepositoryV1/)
servers:
  - url: http://localhost:7501/api/v0.1/magento-integration-adapter
paths:
  /customer-address-created:
    post:
      summary: Receive notification of successful customer address creation
      description: Endpoint that receives asynchronous notifications from Magento after a customer address has been successfully created.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/customer-address-data-interface'
            example:
              id: 4
              customer_id: 1
              region:
                region_code: "NY"
                region: "New York"
                region_id: 43
              region_id: 43
              country_id: "US"
              street:
                - "123 Oak Street"
                - "Apartment 4B"
              telephone: "************"
              postcode: "10001"
              city: "New York"
              firstname: "<PERSON>"
              lastname: "<PERSON>e"
              default_shipping: true
              default_billing: false
              company: "Example Corp"
              custom_attributes: []
      responses:
        '200':
          description: Notification successfully processed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                data:
                  success: true
                  message: "Customer address creation notification processed successfully"
                  address_id: 4
                error: null
        '400':
          description: Bad Request - Invalid address data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiErrorResponse'
              example:
                data: null
                error:
                  message: "Invalid address data provided"
                  type: "ValidationError"
                  details:
                    - field: "street"
                      message: "Street address is required"
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiErrorResponse'
              example:
                data: null
                error:
                  message: "An unexpected error occurred"
                  type: "InternalServerError"
components:
  schemas:
    ApiResponse:
      type: object
      description: Standard envelope wrapper for all API responses
      properties:
        data:
          type: object
          nullable: true
          description: Response data (null for error responses)
          properties:
            success:
              type: boolean
              description: Whether the notification was processed successfully
            message:
              type: string
              description: Processing status message
            address_id:
              type: integer
              description: ID of the created address
        error:
          type: object
          nullable: true
          description: Error details (null for successful responses)
      required:
        - data

    ApiErrorResponse:
      type: object
      description: Standard envelope wrapper for error responses
      properties:
        data:
          type: object
          nullable: true
          description: Always null for error responses
        error:
          type: object
          nullable: false
          description: Error details
          properties:
            message:
              type: string
              description: Human-readable error message
            type:
              type: string
              description: Error type classification
            details:
              type: array
              nullable: true
              description: Additional error details for validation errors
              items:
                type: object
                properties:
                  field:
                    type: string
                    description: Field name with validation error
                  message:
                    type: string
                    description: Field-specific error message
      required:
        - data
        - error

    customer-address-data-interface:
      type: object
      description: Customer address interface. This is the exact schema from Magento's customer-address-data-interface.
      properties:
        id:
          type: integer
          description: ID of the created address
        customer_id:
          type: integer
          description: Customer ID
        region:
          type: object
          description: Customer address region interface
          properties:
            region_code:
              type: string
              description: Region code in (ISO-3166-2)
            region:
              type: string
              description: Region name
            region_id:
              type: integer
              description: Region ID
        region_id:
          type: integer
          description: Region ID
        country_id:
          type: string
          description: Country code (ISO-3166-1 alpha-2)
        street:
          type: array
          description: Street address lines
          items:
            type: string
        company:
          type: string
          description: Company name
        telephone:
          type: string
          description: Phone number
        fax:
          type: string
          description: Fax number
        postcode:
          type: string
          description: Postal code
        city:
          type: string
          description: City name
        firstname:
          type: string
          description: First name
        lastname:
          type: string
          description: Last name
        middlename:
          type: string
          description: Middle name
        prefix:
          type: string
          description: Name prefix
        suffix:
          type: string
          description: Name suffix
        vat_id:
          type: string
          description: VAT number
        default_shipping:
          type: boolean
          description: If this is the default shipping address
        default_billing:
          type: boolean
          description: If this is the default billing address
        custom_attributes:
          type: array
          description: Custom attributes values
          items:
            type: object
            properties:
              attribute_code:
                type: string
              value:
                type: string
