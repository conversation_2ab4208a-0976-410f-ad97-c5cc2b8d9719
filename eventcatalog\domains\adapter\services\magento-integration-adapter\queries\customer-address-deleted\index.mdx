---
id: customer-address-deleted-query
name: Customer Address Deleted Query
version: 0.0.1
summary: |
  Asynchronous notification query triggered automatically by Magento after a customer address is successfully deleted
producers:
  - magento
owners:
  - euvic
specifications:
  - type: openapi
    path: 'openapi.yml'
channels:
  - id: magento-integration-adapter.{env}.rest.queries
    parameters:
      env: local
---

## Overview

This query represents an asynchronous notification from Magento that is automatically triggered after a customer address has been successfully deleted from the system. The notification confirms the address deletion and provides information about the deleted address.

## Architecture diagram

<NodeGraph />

## Query Details

### Trigger Point
- Automatically triggered after successful address deletion in Magento
- Part of Magento's customer address management workflow
- Triggered by:
  - Customer deleting address through storefront
  - Admin deleting address through admin panel
  - API calls to delete customer address
  - B2B company admin managing addresses

### Data Structure
Uses response format from Magento 2 API endpoint:
`DELETE /V1/customers/addresses/{addressId}`
[Magento API Documentation](https://developer.adobe.com/commerce/webapi/rest/resources/customer/customerAddressRepositoryV1/)

For complete payload structure and examples, see the openapi.yml specification.

### Critical Fields
- `address_id` - Unique identifier of the deleted address
- `customer_id` - Link to the customer
- `success` - Confirmation of successful deletion
- `default_shipping` and `default_billing` - Status of deleted address (if it was a default address)

## Integration Guidelines

### Processing Requirements
- Verify address was successfully deleted
- Update local address records
- Process any post-deletion workflows
- Handle default address reassignment if needed

### Error Handling
- Validate customer existence
- Handle missing address scenarios
- Process default address reassignment
- Manage concurrent deletion requests
- Handle system-level constraints (e.g., minimum required addresses)

## Notes

- This is an asynchronous notification of successful address deletion
- Cannot delete the last remaining address if required by system configuration
- If deleting a default address, another address should be designated as default if available
- Address deletion is permanent and cannot be undone
- Historical order data will retain the address information
- Consider GDPR and other privacy regulations when processing deletion notifications 