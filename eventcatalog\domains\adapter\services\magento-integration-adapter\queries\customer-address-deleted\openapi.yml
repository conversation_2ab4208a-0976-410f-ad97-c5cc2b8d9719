openapi: "3.1.0"
info:
  title: Customer Address Deleted
  version: 0.0.1
  description: |
    OpenAPI specification for the Customer Address Deleted query in Magento Integration Adapter.
    This represents an asynchronous notification that is triggered after a customer address is successfully deleted in Magento.

    Based on Magento 2 API endpoint: DELETE /V1/customers/addresses/{addressId}
servers:
  - url: http://localhost:7501/api/v0.1/magento-integration-adapter
paths:
  /customer-address-deleted:
    post:
      summary: Notification of customer address deletion
      description: Asynchronous notification triggered when a customer address is deleted in Magento.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CustomerAddressDeletedRequest'
            example:
              customer_id: 123
              address_id: 456
              deleted_at: "2024-03-19T14:30:00Z"
      responses:
        '200':
          description: Notification successfully processed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerAddressDeletedResponse'
              example:
                success: true
                timestamp: "2024-03-19T14:30:01Z"
                message: "Customer address deletion processed successfully"
                data:
                  customer_id: 123
                  address_id: 456
        '400':
          description: Bad request - validation error
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    description: Always false for errors
                  timestamp:
                    type: string
                    format: date-time
                    description: When the error occurred (ISO-8601)
                  message:
                    type: string
                    description: Error message
                  errors:
                    type: array
                    items:
                      type: object
                      properties:
                        field:
                          type: string
                        message:
                          type: string
              example:
                success: false
                timestamp: "2024-03-19T14:30:01Z"
                message: "Validation failed"
                errors: [
                  {
                    field: "address_id",
                    message: "Address not found"
                  }
                ]
        '403':
          description: Forbidden - address cannot be deleted
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
              example:
                message: "Address is default for customer so is not allowed to be deleted"
components:
  schemas:
    CustomerAddressDeletedRequest:
      type: object
      required:
        - customer_id
        - address_id
        - deleted_at
      properties:
        customer_id:
          type: integer
          description: Unique identifier of the customer
        address_id:
          type: integer
          description: Unique identifier of the deleted address
        deleted_at:
          type: string
          format: date-time
          description: When the address was deleted (ISO-8601)
    CustomerAddressDeletedResponse:
      type: object
      required:
        - success
        - timestamp
        - message
      properties:
        success:
          type: boolean
          description: Whether the notification was processed successfully
        timestamp:
          type: string
          format: date-time
          description: When the response was generated (ISO-8601)
        message:
          type: string
          description: Processing status message
        data:
          type: object
          description: Response data
          properties:
            customer_id:
              type: integer
              description: Unique identifier of the customer
            address_id:
              type: integer
              description: Unique identifier of the deleted address
