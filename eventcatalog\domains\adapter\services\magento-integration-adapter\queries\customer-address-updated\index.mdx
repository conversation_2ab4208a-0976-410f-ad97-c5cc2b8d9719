---
id: customer-address-updated-query
name: Customer Address Updated Query
version: 0.0.1
summary: |
  Asynchronous notification query triggered automatically by Magento after a customer address is successfully updated
producers:
  - magento
owners:
  - euvic
specifications:
  - type: openapi
    path: 'openapi.yml'
channels:
  - id: magento-integration-adapter.{env}.rest.queries
    parameters:
      env: local
---

## Overview

This query represents an asynchronous notification from Magento that is automatically triggered after a customer address has been successfully updated in the system. The notification confirms the address update and provides information about the modified address.

## Architecture diagram

<NodeGraph />

## Query Details

### Trigger Point
- Automatically triggered after successful address update in Magento
- Part of Magento's customer address management workflow
- Triggered by:
  - Customer updating address through storefront
  - Admin updating address through admin panel
  - API calls to update customer address
  - B2B company admin managing addresses

### Data Structure
Uses response format from Magento 2 API endpoint:
`PUT /V1/customers/addresses/{addressId}`
[Magento API Documentation](https://developer.adobe.com/commerce/webapi/rest/resources/customer/customerAddressRepositoryV1/)

For complete payload structure and examples, see the openapi.yml specification.

### Critical Fields
- `address_id` - Unique identifier of the updated address
- `customer_id` - Link to the customer
- `success` - Confirmation of successful update
- `default_shipping` and `default_billing` - Current status of address (if it is a default address)
- `changes` - Details of what was updated in the address

## Integration Guidelines

### Processing Requirements
- Verify address was successfully updated
- Update local address records
- Process any post-update workflows
- Handle default address reassignment if needed

### Error Handling
- Validate customer existence
- Handle missing address scenarios
- Process default address reassignment
- Manage concurrent update requests
- Handle system-level constraints (e.g., address format validation)

## Notes

- This is an asynchronous notification of successful address update
- Address updates may affect order processing if the address is used in pending orders
- If setting as default address, previous default address will be unset
- Historical order data will retain the original address information at time of order
- For US and CA addresses, the `region` field now uses the ISO 3166-2:US or ISO 3166-2:CA code (e.g., "NY" for New York, "ON" for Ontario) as per documentation update on 2025-07-02.
- Consider GDPR and other privacy regulations when processing address updates
