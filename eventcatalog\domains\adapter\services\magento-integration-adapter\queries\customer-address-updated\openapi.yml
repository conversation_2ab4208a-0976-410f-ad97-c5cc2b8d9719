openapi: "3.1.0"
info:
  title: Customer Address Updated
  version: 0.0.1
  description: |
    OpenAPI specification for the Customer Address Updated query in Magento Integration Adapter.
    This represents an asynchronous notification that is triggered after a customer address is successfully updated in Magento.

    Based on Magento 2 API endpoint: PUT /V1/customers/addresses/{addressId}
servers:
  - url: http://localhost:7501/api/v0.1/magento-integration-adapter
paths:
  /customer-address-updated:
    post:
      summary: Notification of customer address update
      description: Asynchronous notification triggered when a customer address is updated in Magento.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CustomerAddressUpdatedRequest'
            example:
              customer_id: 123
              address_id: 456
              firstname: "John"
              lastname: "Doe"
              street: ["123 Main St", "Apt 4B"]
              city: "New York"
              region: "New York"
              region_id: 43
              postcode: "10001"
              country_id: "US"
              telephone: "************"
              default_shipping: true
              default_billing: true
              updated_at: "2024-03-19T14:30:00Z"
      responses:
        '200':
          description: Notification successfully processed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerAddressUpdatedResponse'
              example:
                success: true
                timestamp: "2024-03-19T14:30:01Z"
                message: "Customer address update processed successfully"
                data:
                  customer_id: 123
                  address_id: 456
                  default_shipping: true
                  default_billing: true
        '400':
          description: Bad request - validation error
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    description: Always false for errors
                  timestamp:
                    type: string
                    format: date-time
                    description: When the error occurred (ISO-8601)
                  message:
                    type: string
                    description: Error message
                  errors:
                    type: array
                    items:
                      type: object
                      properties:
                        field:
                          type: string
                        message:
                          type: string
              example:
                success: false
                timestamp: "2024-03-19T14:30:01Z"
                message: "Validation failed"
                errors: [
                  {
                    field: "postcode",
                    message: "Invalid postal code format"
                  }
                ]
        '403':
          description: Forbidden - address cannot be updated
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
              example:
                message: "Customer does not have permission to update this address"
components:
  schemas:
    CustomerAddressUpdatedRequest:
      type: object
      required:
        - customer_id
        - address_id
        - firstname
        - lastname
        - street
        - city
        - region
        - region_id
        - postcode
        - country_id
        - telephone
        - updated_at
      properties:
        customer_id:
          type: integer
          description: Unique identifier of the customer
        address_id:
          type: integer
          description: Unique identifier of the address
        firstname:
          type: string
          description: First name for the address
        lastname:
          type: string
          description: Last name for the address
        street:
          type: array
          items:
            type: string
          description: Street address lines
        city:
          type: string
          description: City name
        region:
          type: string
          description: Region/state name
        region_id:
          type: integer
          description: Region/state ID
        postcode:
          type: string
          description: Postal code
        country_id:
          type: string
          description: Country code (ISO-3166-1 alpha-2)
        telephone:
          type: string
          description: Phone number
        default_shipping:
          type: boolean
          description: Whether this is the default shipping address
        default_billing:
          type: boolean
          description: Whether this is the default billing address
        updated_at:
          type: string
          format: date-time
          description: When the address was updated (ISO-8601)
    CustomerAddressUpdatedResponse:
      type: object
      required:
        - success
        - timestamp
        - message
      properties:
        success:
          type: boolean
          description: Whether the notification was processed successfully
        timestamp:
          type: string
          format: date-time
          description: When the response was generated (ISO-8601)
        message:
          type: string
          description: Processing status message
        data:
          type: object
          description: Response data
          properties:
            customer_id:
              type: integer
              description: Unique identifier of the customer
            address_id:
              type: integer
              description: Unique identifier of the address
            default_shipping:
              type: boolean
              description: Whether this is the default shipping address
            default_billing:
              type: boolean
              description: Whether this is the default billing address
