---
id: customer-created-query
name: Customer Created Query
version: 0.0.1
summary: |
  Asynchronous notification query triggered automatically by <PERSON>gent<PERSON> after a new customer account is successfully created
producers:
  - magento
owners:
  - euvic
specifications:
  - type: openapi
    path: 'openapi.yml'
channels:
  - id: magento-integration-adapter.{env}.rest.queries
    parameters:
      env: local
---

## Overview

This query represents an asynchronous notification from Magento that is automatically triggered after a new customer account has been successfully created in the system. The notification confirms the customer creation and provides information about the new customer account.

## Architecture diagram

<NodeGraph />

## Query Details

### Trigger Point
- Automatically triggered after successful customer creation in Magento
- Part of Magento's customer management workflow
- Triggered by:
  - Customer self-registration through storefront
  - Admin creating customer through admin panel
  - API calls to create customer account
  - B2B company admin creating sub-accounts

### Data Structure
Uses response format from Magento 2 API endpoint:
`POST /V1/customers`
[Magento API Documentation](https://developer.adobe.com/commerce/webapi/rest/resources/customer/customerAccountManagementV1/)

For complete payload structure and examples, see the openapi.yml specification.

### Critical Fields
- `customer_id` - Unique identifier of the created customer
- `email` - Customer's email address
- `firstname` and `lastname` - Customer's name
- `website_id` - Website where the customer was created
- `store_id` - Store view associated with the customer
- `group_id` - Customer group assignment
- `confirmation_required` - Whether email confirmation is needed
- `created_at` - Timestamp of account creation (ISO-8601)

## Integration Guidelines

### Processing Requirements
- Verify customer was successfully created
- Process customer group assignments
- Handle welcome email notifications
- Set up customer preferences
- Initialize customer data structures

### Error Handling
- Validate email uniqueness
- Handle confirmation requirements
- Process group assignment rules
- Manage concurrent registration requests
- Handle system-level constraints (e.g., website/store restrictions)

## Notes

- This is an asynchronous notification of successful customer creation
- Customer may require email confirmation before account is fully active
- Customer group assignment affects pricing and permissions
- Consider GDPR and other privacy regulations when processing customer data
- Customer account may be created with or without initial password

## Response Example

```json
{
  "id": 123,
  "group_id": 1,
  "created_at": "2024-03-21T10:00:00+00:00",
  "updated_at": "2024-03-21T10:00:00+00:00",
  "created_in": "Default Store View",
  "email": "<EMAIL>",
  "firstname": "John",
  "lastname": "Doe",
  "store_id": 1,
  "website_id": 1,
  "addresses": [
    {
      "id": 1,
      "customer_id": 123,
      "region": {
        "region_code": "NY",
        "region": "New York",
        "region_id": 43
      },
      "region_id": 43,
      "country_id": "US",
      "street": ["123 Main St"],
      "telephone": "**********",
      "postcode": "10001",
      "city": "New York",
      "firstname": "John",
      "lastname": "Doe",
      "default_billing": true,
      "default_shipping": true
    }
  ]
}
