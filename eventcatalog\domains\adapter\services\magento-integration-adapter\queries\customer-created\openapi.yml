openapi: "3.1.0"
info:
  title: Customer Created
  version: 0.0.1
  description: |
    OpenAPI specification for the Customer Created query in Magento Integration Adapter.
    This represents an asynchronous notification that is triggered after a new customer account is successfully created in Magento.

    Based on Magento 2 API endpoint: POST /V1/customers
servers:
  - url: http://localhost:7501/api/v0.1/magento-integration-adapter
paths:
  /customer-created:
    post:
      summary: Notification of customer creation
      description: Asynchronous notification triggered when a new customer account is created in Magento.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CustomerCreatedRequest'
            example:
              customer_id: 123
              email: "<EMAIL>"
              firstname: "<PERSON>"
              lastname: "<PERSON><PERSON>"
              website_id: 1
              store_id: 1
              group_id: 1
              confirmation_required: false
              created_at: "2024-03-19T14:30:00Z"
              addresses: [
                {
                  id: 456,
                  customer_id: 123,
                  firstname: "<PERSON>",
                  lastname: "<PERSON><PERSON>",
                  street: ["123 Main St", "Apt 4B"],
                  city: "New York",
                  region: "NY",
                  region_id: 43,
                  postcode: "10001",
                  country_id: "US",
                  telephone: "************",
                  default_shipping: true,
                  default_billing: true
                }
              ]
      responses:
        '200':
          description: Notification successfully processed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                data:
                  success: true
                  timestamp: "2024-03-19T14:30:01Z"
                  message: "Customer creation notification processed successfully"
                  customer_id: 123
                  email: "<EMAIL>"
                error: null
        '400':
          description: Bad request - validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiErrorResponse'
              example:
                data: null
                error:
                  message: "Validation failed"
                  type: "ValidationError"
                  details:
                    - field: "email"
                      message: "Email address is invalid"
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiErrorResponse'
              example:
                data: null
                error:
                  message: "An unexpected error occurred"
                  type: "InternalServerError"
components:
  schemas:
    ApiResponse:
      type: object
      description: Standard envelope wrapper for all API responses
      properties:
        data:
          type: object
          nullable: true
          description: Response data (null for error responses)
          $ref: '#/components/schemas/CustomerCreatedResponse'
        error:
          type: object
          nullable: true
          description: Error details (null for successful responses)
      required:
        - data

    ApiErrorResponse:
      type: object
      description: Standard envelope wrapper for error responses
      properties:
        data:
          type: object
          nullable: true
          description: Always null for error responses
        error:
          type: object
          nullable: false
          description: Error details
          properties:
            message:
              type: string
              description: Human-readable error message
            type:
              type: string
              description: Error type classification
            details:
              type: array
              nullable: true
              description: Additional error details for validation errors
              items:
                type: object
                properties:
                  field:
                    type: string
                    description: Field name with validation error
                  message:
                    type: string
                    description: Field-specific error message
      required:
        - data
        - error

    CustomerCreatedRequest:
      type: object
      required:
        - customer_id
        - email
        - firstname
        - lastname
        - website_id
        - store_id
        - group_id
        - created_at
      properties:
        customer_id:
          type: integer
          description: Unique identifier of the created customer
        email:
          type: string
          format: email
          description: Customer's email address
        firstname:
          type: string
          description: Customer's first name
        lastname:
          type: string
          description: Customer's last name
        website_id:
          type: integer
          description: Website where the customer was created
        store_id:
          type: integer
          description: Store view associated with the customer
        group_id:
          type: integer
          description: Customer group assignment
        confirmation_required:
          type: boolean
          description: Whether email confirmation is needed
        created_at:
          type: string
          format: date-time
          description: When the customer account was created (ISO-8601)
        addresses:
          type: array
          description: Customer's addresses (if provided during creation)
          items:
            type: object
            properties:
              id:
                type: integer
                description: Address ID
              customer_id:
                type: integer
                description: Reference to the customer
              firstname:
                type: string
              lastname:
                type: string
              street:
                type: array
                items:
                  type: string
              city:
                type: string
              region:
                type: string
                description: Region or state code (ISO 3166-2:US for US, ISO 3166-2:CA for Canada)
              region_id:
                type: integer
              postcode:
                type: string
              country_id:
                type: string
              telephone:
                type: string
              default_shipping:
                type: boolean
              default_billing:
                type: boolean
        custom_attributes:
          type: object
          description: Any custom attributes set during customer creation
          additionalProperties: true

    CustomerCreatedResponse:
      type: object
      required:
        - success
        - timestamp
        - message
      properties:
        success:
          type: boolean
          description: Whether the notification was processed successfully
        timestamp:
          type: string
          format: date-time
          description: When the response was generated (ISO-8601)
        message:
          type: string
          description: Processing status message
        data:
          type: object
          description: Response data from Magento API
          properties:
            customer_id:
              type: integer
              description: ID of the created customer
            email:
              type: string
              format: email
              description: Email of the created customer
