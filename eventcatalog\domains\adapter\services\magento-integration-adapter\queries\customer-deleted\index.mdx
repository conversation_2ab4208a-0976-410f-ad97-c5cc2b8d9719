---
id: customer-deleted-query
name: Customer Deleted Query
version: 0.0.1
summary: |
  Asynchronous notification query triggered automatically by <PERSON>gento after a customer account is successfully deleted
producers:
  - magento
owners:
  - euvic
specifications:
  - type: openapi
    path: 'openapi.yml'
channels:
  - id: magento-integration-adapter.{env}.rest.queries
    parameters:
      env: local
---

## Overview

This query represents an asynchronous notification from Magento that is automatically triggered after a customer account has been successfully deleted from the system. The notification confirms the customer deletion and provides information about the deleted account.

## Architecture diagram

<NodeGraph />

## Query Details

### Trigger Point
- Automatically triggered after successful customer deletion in Magento
- Part of Magento's customer management workflow
- Triggered by:
  - Customer self-deletion through account settings
  - Admin deleting customer through admin panel
  - API calls to delete customer account
  - GDPR/privacy compliance deletions
  - Automated cleanup of inactive accounts

### Data Structure
Uses response format from Magento 2 API endpoint:
`DELETE /V1/customers/{customerId}`
[Magento API Documentation](https://developer.adobe.com/commerce/webapi/rest/resources/customer/customerAccountManagementV1/)

For complete payload structure and examples, see the openapi.yml specification.

### Critical Fields
- `customer_id` - Unique identifier of the deleted customer
- `email` - Customer's email address (for reference)
- `deletion_reason` - Optional reason for account deletion
- `deletion_type` - Type of deletion (e.g., customer-requested, admin, automated)
- `success` - Confirmation of successful deletion
- `deleted_at` - Timestamp of account deletion

## Integration Guidelines

### Processing Requirements
- Verify customer was successfully deleted
- Clean up related customer data
- Handle associated orders and transactions
- Update any linked systems
- Archive necessary data for compliance
- Remove personal data as per GDPR requirements

### Error Handling
- Validate customer existence before processing
- Handle pending orders or transactions
- Process subscription cancellations
- Manage active sessions
- Handle system-level constraints
- Ensure compliance with data retention policies

## Notes

- This is an asynchronous notification of successful customer deletion
- Consider legal requirements for data retention
- Some customer data may need to be retained for order history
- GDPR and privacy regulations affect data handling
- Active orders or pending transactions may affect deletion
- Consider impact on related entities (reviews, wishlists, etc.)
- May need to handle customer reactivation scenarios 