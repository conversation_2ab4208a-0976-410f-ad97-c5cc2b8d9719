openapi: "3.1.0"
info:
  title: Customer Deleted
  version: 0.0.1
  description: |
    OpenAPI specification for the Customer Deleted query in Magento Integration Adapter.
    This represents an asynchronous notification that is triggered after a customer account is successfully deleted from Magento.

    Based on Magento 2 API endpoint: DELETE /V1/customers/{customerId}
servers:
  - url: http://localhost:7501/api/v0.1/magento-integration-adapter
paths:
  /customer-deleted:
    post:
      summary: Notification of customer deletion
      description: Asynchronous notification triggered when a customer account is deleted in Magento.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CustomerDeletedRequest'
            example:
              customer_id: 123
              email: "<EMAIL>"
              deletion_type: "customer-requested"
              deletion_reason: "Account no longer needed"
              deleted_at: "2024-03-19T14:30:00Z"
              data_retention: {
                orders: true,
                reviews: false,
                addresses: false
              }
      responses:
        '200':
          description: Notification successfully processed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerDeletedResponse'
              example:
                success: true
                message: "Customer deletion notification processed successfully"
        '400':
          description: Bad request - validation error
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    description: Error message
                  errors:
                    type: array
                    items:
                      type: object
                      properties:
                        field:
                          type: string
                        message:
                          type: string
              example:
                message: "Validation failed"
                errors: [
                  {
                    field: "customer_id",
                    message: "Customer does not exist"
                  }
                ]
        '403':
          description: Forbidden - customer cannot be deleted
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
              example:
                message: "Customer has pending orders and cannot be deleted"
components:
  schemas:
    CustomerDeletedRequest:
      type: object
      required:
        - customer_id
        - email
        - deletion_type
        - deleted_at
      properties:
        customer_id:
          type: integer
          description: Unique identifier of the deleted customer
        email:
          type: string
          format: email
          description: Email address of the deleted customer (for reference)
        deletion_type:
          type: string
          description: Type of deletion that occurred
          enum:
            - customer-requested
            - admin
            - gdpr-compliance
            - automated
            - system
        deletion_reason:
          type: string
          description: Optional reason for the deletion
        deleted_at:
          type: string
          format: date-time
          description: When the customer account was deleted (ISO-8601)
        data_retention:
          type: object
          description: Specifies which data types are being retained
          properties:
            orders:
              type: boolean
              description: Whether order history is retained
            reviews:
              type: boolean
              description: Whether product reviews are retained
            addresses:
              type: boolean
              description: Whether address information is retained
    CustomerDeletedResponse:
      type: object
      required:
        - success
      properties:
        success:
          type: boolean
          description: Whether the notification was processed successfully
        message:
          type: string
          description: Processing status message
