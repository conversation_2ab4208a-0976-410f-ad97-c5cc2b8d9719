---
id: customer-email-subscribed-query
name: Customer Email Subscribed Query
version: 0.0.1
summary: |
  Asynchronous notification query triggered automatically by Magento after a customer successfully subscribes to the newsletter
producers:
  - magento
owners:
  - euvic
specifications:
  - type: openapi
    path: 'openapi.yml'
channels:
  - id: magento-integration-adapter.{env}.rest.queries
    parameters:
      env: local
---

## Overview

This query represents an asynchronous notification from Magento that is automatically triggered after a customer has successfully subscribed to the newsletter. The notification confirms the subscription and provides information about the subscriber.

## Architecture diagram

<NodeGraph />

## Query Details

### Trigger Point
- Automatically triggered after successful newsletter subscription in Magento
- Part of Magento's newsletter management workflow
- Triggered by:
  - Customer subscribing through account settings
  - Customer subscribing during checkout
  - Admin subscribing customer through admin panel
  - API calls to subscribe customer
  - Bulk subscription imports

### Data Structure
Uses response format from Magento 2 API endpoint:
`POST /V1/customers/{customerId}/newsletter/subscribe`
[Magento API Documentation](https://developer.adobe.com/commerce/webapi/rest/resources/newsletter/subscriberManagementV1/)

For complete payload structure and examples, see the openapi.yml specification.

### Critical Fields
- `customer_id` - Unique identifier of the subscribed customer
- `email` - Customer's email address
- `store_id` - Store view where subscription occurred
- `subscription_source` - How the subscription was initiated
- `subscription_confirmed` - Whether double opt-in is required/completed
- `subscribed_at` - Timestamp of subscription
- `success` - Confirmation of successful subscription

## Integration Guidelines

### Processing Requirements
- Verify subscription was successful
- Handle double opt-in requirements if configured
- Process welcome email notifications
- Update marketing preferences
- Sync with external email systems
- Handle subscription preferences

### Error Handling
- Validate customer existence
- Handle duplicate subscriptions
- Process opt-in requirements
- Manage subscription status changes
- Handle system-level constraints
- Ensure compliance with email regulations

## Notes

- This is an asynchronous notification of successful newsletter subscription
- Consider email marketing regulations (CAN-SPAM, GDPR)
- Double opt-in may be required in some regions
- Subscription may be store-specific
- Customer may have multiple store subscriptions
- Consider impact on marketing automation systems
- May need to handle subscription confirmation workflows 