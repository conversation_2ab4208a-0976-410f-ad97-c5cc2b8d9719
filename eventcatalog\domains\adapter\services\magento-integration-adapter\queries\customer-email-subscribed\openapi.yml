openapi: "3.1.0"
info:
  title: Customer Email Subscribed
  version: 0.0.1
  description: |
    OpenAPI specification for the Customer Email Subscribed query in Magento Integration Adapter.
    This represents an asynchronous notification that is triggered after a customer email is subscribed to marketing communications.

    Based on Magento 2 API endpoint: POST /V1/newsletter/subscribe
servers:
  - url: http://localhost:7501/api/v0.1/magento-integration-adapter
paths:
  /customer-email-subscribed:
    post:
      summary: Notification of customer email subscription
      description: Asynchronous notification triggered when a customer email is subscribed to marketing communications.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CustomerEmailSubscribedRequest'
            example:
              customer_id: 123
              email: "<EMAIL>"
              store_id: 1
              source: "customer_account"
              gdpr_consent: true
              subscribed_at: "2024-03-19T14:30:00Z"
      responses:
        '200':
          description: Notification successfully processed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerEmailSubscribedResponse'
              example:
                success: true
                timestamp: "2024-03-19T14:30:01Z"
                message: "Customer email subscription processed successfully"
                data:
                  customer_id: 123
                  email: "<EMAIL>"
                  store_id: 1
                  subscription_id: "123"
        '400':
          description: Bad request - validation error
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    description: Always false for errors
                  timestamp:
                    type: string
                    format: date-time
                    description: When the error occurred (ISO-8601)
                  message:
                    type: string
                    description: Error message
                  errors:
                    type: array
                    items:
                      type: object
                      properties:
                        field:
                          type: string
                        message:
                          type: string
              example:
                success: false
                timestamp: "2024-03-19T14:30:01Z"
                message: "Validation failed"
                errors: [
                  {
                    field: "email",
                    message: "Email address is invalid"
                  }
                ]
components:
  schemas:
    CustomerEmailSubscribedRequest:
      type: object
      required:
        - customer_id
        - email
        - store_id
        - gdpr_consent
        - subscribed_at
      properties:
        customer_id:
          type: integer
          description: Unique identifier of the customer
        email:
          type: string
          format: email
          description: Customer's email address
        store_id:
          type: integer
          description: Store view ID where subscription occurred
        source:
          type: string
          enum:
            - customer_account
            - admin
            - api
            - import
          description: Source of the subscription request
        gdpr_consent:
          type: boolean
          description: Whether GDPR consent was given
        subscribed_at:
          type: string
          format: date-time
          description: When the subscription occurred (ISO-8601)

    CustomerEmailSubscribedResponse:
      type: object
      required:
        - success
        - timestamp
        - message
      properties:
        success:
          type: boolean
          description: Whether the notification was processed successfully
        timestamp:
          type: string
          format: date-time
          description: When the response was generated (ISO-8601)
        message:
          type: string
          description: Processing status message
        data:
          type: object
          description: Response data
          properties:
            customer_id:
              type: integer
              description: Unique identifier of the customer
            email:
              type: string
              format: email
              description: Customer's email address
            store_id:
              type: integer
              description: Store view ID where subscription occurred
            subscription_id:
              type: string
              description: Unique identifier for the subscription
