---
id: customer-email-unsubscribed-query
name: Customer Email Unsubscribed Query
version: 0.0.1
summary: |
  Asynchronous notification query triggered automatically by <PERSON>gent<PERSON> after a customer successfully unsubscribes from the newsletter
producers:
  - magento
owners:
  - euvic
specifications:
  - type: openapi
    path: 'openapi.yml'
channels:
  - id: magento-integration-adapter.{env}.rest.queries
    parameters:
      env: local
---

## Overview

This query represents an asynchronous notification from Magento that is automatically triggered after a customer has successfully unsubscribed from the newsletter. The notification confirms the unsubscription and provides information about the former subscriber.

## Architecture diagram

<NodeGraph />

## Query Details

### Trigger Point
- Automatically triggered after successful newsletter unsubscription in Magento
- Part of Magento's newsletter management workflow
- Triggered by:
  - Customer unsubscribing through account settings
  - Customer unsubscribing via unsubscribe link in email
  - Admin unsubscribing customer through admin panel
  - API calls to unsubscribe customer
  - Bulk unsubscription processes
  - Automated cleanup of invalid subscriptions

### Data Structure
Uses response format from Magento 2 API endpoint:
`POST /V1/customers/{customerId}/newsletter/unsubscribe`
[Magento API Documentation](https://developer.adobe.com/commerce/webapi/rest/resources/newsletter/subscriberManagementV1/)

For complete payload structure and examples, see the openapi.yml specification.

### Critical Fields
- `customer_id` - Unique identifier of the unsubscribed customer
- `email` - Customer's email address
- `store_id` - Store view where unsubscription occurred
- `unsubscription_source` - How the unsubscription was initiated
- `unsubscribed_at` - Timestamp of unsubscription
- `success` - Confirmation of successful unsubscription
- `reason` - Optional reason for unsubscription

## Integration Guidelines

### Processing Requirements
- Verify unsubscription was successful
- Process farewell email notifications if configured
- Update marketing preferences
- Sync with external email systems
- Handle subscription status changes
- Archive subscription history
- Update marketing segments

### Error Handling
- Validate customer existence
- Handle subscription status conflicts
- Process opt-out requirements
- Manage unsubscription confirmations
- Handle system-level constraints
- Ensure compliance with email regulations

## Notes

- This is an asynchronous notification of successful newsletter unsubscription
- Consider email marketing regulations (CAN-SPAM, GDPR)
- Unsubscription must be processed immediately
- Store unsubscription history for compliance
- Consider impact on marketing automation systems
- May need to handle resubscription cooling-off period
- Ensure proper handling of global vs store-specific unsubscriptions 