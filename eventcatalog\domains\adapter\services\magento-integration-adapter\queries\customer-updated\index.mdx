---
id: customer-updated-query
name: Customer Updated Query
version: 0.0.1
summary: |
  Asynchronous notification query triggered automatically by <PERSON>gento after a customer's information has been successfully updated
producers:
  - magento
owners:
  - euvic
specifications:
  - type: openapi
    path: 'openapi.yml'
channels:
  - id: magento-integration-adapter.{env}.rest.queries
    parameters:
      env: local
---

## Overview

This query represents an asynchronous notification from Magento that is automatically triggered after a customer's information has been successfully updated. The notification provides details about the changes made to the customer's profile and account settings.

## Architecture diagram

<NodeGraph />

## Query Details

### Trigger Point
- Automatically triggered after successful customer update in Magento
- Part of Magento's customer management workflow
- Triggered by:
  - Customer updating their own profile
  - Admin updating customer information
  - API calls modifying customer data
  - Bulk customer updates
  - Third-party integrations
  - Customer attribute changes

### Data Structure
Uses response format from Magento 2 API endpoint:
`PUT /V1/customers/{customerId}`
[Magento API Documentation](https://developer.adobe.com/commerce/webapi/rest/resources/customerAccountManagementV1/)

For complete payload structure and examples, see the openapi.yml specification.

### Critical Fields
- `customer_id` - Unique identifier of the updated customer
- `email` - Customer's email address (new or unchanged)
- `store_id` - Store view where update occurred
- `update_source` - How the update was initiated
- `updated_at` - Timestamp of update
- `changes` - Object containing modified fields
- `previous_values` - Object containing previous field values
- `success` - Confirmation of successful update

## Integration Guidelines

### Processing Requirements
- Verify update was successful
- Process notification emails if configured
- Update related systems and services
- Sync with external systems
- Handle customer group changes
- Update marketing segments
- Process attribute changes
- Handle website/store associations

### Error Handling
- Validate customer existence
- Handle data validation errors
- Process update conflicts
- Manage concurrent updates
- Handle system-level constraints
- Ensure data consistency
- Process partial updates
- Handle attribute validation

## Notes

- This is an asynchronous notification of successful customer update
- Consider GDPR and data privacy regulations
- Updates must be processed in order received
- Store update history for compliance
- Consider impact on:
  - Marketing automation
  - Customer segments
  - Loyalty programs
  - External integrations
- Handle multi-store customer data
- Process customer group changes
- Consider website-specific attributes

## Response Example

```json
{
  "id": 123,
  "group_id": 2,
  "created_at": "2024-03-20T10:00:00+00:00",
  "updated_at": "2024-03-21T15:30:00+00:00",
  "created_in": "Default Store View",
  "email": "<EMAIL>",
  "firstname": "John",
  "lastname": "Doe",
  "store_id": 1,
  "website_id": 1,
  "addresses": [
    {
      "id": 1,
      "customer_id": 123,
      "region": {
        "region_code": "NY",
        "region": "New York",
        "region_id": 43
      },
      "region_id": 43,
      "country_id": "US",
      "street": ["456 Updated St"],
      "telephone": "1234567890",
      "postcode": "10002",
      "city": "New York",
      "firstname": "John",
      "lastname": "Doe",
      "default_billing": true,
      "default_shipping": true
    }
  ],
  "custom_attributes": [
    {
      "attribute_code": "customer_type",
      "value": "premium"
    }
  ]
} 