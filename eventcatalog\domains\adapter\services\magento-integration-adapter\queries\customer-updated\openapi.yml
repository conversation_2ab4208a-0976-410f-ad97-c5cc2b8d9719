openapi: "3.1.0"
info:
  title: Customer Updated
  version: 0.0.1
  description: |
    OpenAPI specification for the Customer Updated query in Magento Integration Adapter.
    This represents an asynchronous notification that is triggered after a customer's information has been successfully updated.

    Based on Magento 2 API endpoint: PUT /V1/customers/{customerId}
servers:
  - url: http://localhost:7501/api/v0.1/magento-integration-adapter
paths:
  /customer-updated:
    post:
      summary: Notification of customer information update
      description: Asynchronous notification triggered when a customer's information is updated in Magento.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CustomerUpdatedRequest'
            example:
              customer_id: 123
              email: "<EMAIL>"
              store_id: 1
              update_source: "customer_account"
              updated_at: "2024-03-19T14:30:00Z"
              changes: {
                firstname: "<PERSON>",
                lastname: "<PERSON>",
                group_id: 2,
                custom_attributes: {
                  loyalty_tier: "gold"
                }
              }
              previous_values: {
                firstname: "<PERSON>",
                lastname: "<PERSON><PERSON>",
                group_id: 1,
                custom_attributes: {
                  loyalty_tier: "silver"
                }
              }
      responses:
        '200':
          description: Notification successfully processed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                data:
                  success: true
                  message: "Customer update notification processed successfully"
                error: null
        '400':
          description: Bad request - validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiErrorResponse'
              example:
                data: null
                error:
                  message: "Validation failed"
                  type: "ValidationError"
                  details:
                    - field: "email"
                      message: "Invalid email format"
        '403':
          description: Forbidden - update not allowed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiErrorResponse'
              example:
                data: null
                error:
                  message: "Customer update not permitted"
                  type: "ForbiddenError"
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiErrorResponse'
              example:
                data: null
                error:
                  message: "An unexpected error occurred"
                  type: "InternalServerError"
components:
  schemas:
    ApiResponse:
      type: object
      description: Standard envelope wrapper for all API responses
      properties:
        data:
          type: object
          nullable: true
          description: Response data (null for error responses)
          $ref: '#/components/schemas/CustomerUpdatedResponse'
        error:
          type: object
          nullable: true
          description: Error details (null for successful responses)
      required:
        - data

    ApiErrorResponse:
      type: object
      description: Standard envelope wrapper for error responses
      properties:
        data:
          type: object
          nullable: true
          description: Always null for error responses
        error:
          type: object
          nullable: false
          description: Error details
          properties:
            message:
              type: string
              description: Human-readable error message
            type:
              type: string
              description: Error type classification
            details:
              type: array
              nullable: true
              description: Additional error details for validation errors
              items:
                type: object
                properties:
                  field:
                    type: string
                    description: Field name with validation error
                  message:
                    type: string
                    description: Field-specific error message
      required:
        - data
        - error

    CustomerUpdatedRequest:
      type: object
      required:
        - customer_id
        - email
        - store_id
        - update_source
        - updated_at
        - changes
      properties:
        customer_id:
          type: integer
          description: Unique identifier of the updated customer
        email:
          type: string
          format: email
          description: Customer's email address
        store_id:
          type: integer
          description: Store view where update occurred
        update_source:
          type: string
          description: How the update was initiated
          enum:
            - customer_account
            - admin
            - api
            - bulk_update
            - integration
        updated_at:
          type: string
          format: date-time
          description: When the update occurred (ISO-8601)
        changes:
          type: object
          description: Fields that were modified in this update
          properties:
            firstname:
              type: string
              description: Customer's first name
            lastname:
              type: string
              description: Customer's last name
            group_id:
              type: integer
              description: Customer group ID
            custom_attributes:
              type: object
              description: Custom attribute changes
              additionalProperties: true
        previous_values:
          type: object
          description: Previous values of modified fields
          properties:
            firstname:
              type: string
              description: Previous first name
            lastname:
              type: string
              description: Previous last name
            group_id:
              type: integer
              description: Previous customer group ID
            custom_attributes:
              type: object
              description: Previous custom attribute values
              additionalProperties: true
    CustomerUpdatedResponse:
      type: object
      required:
        - success
      properties:
        success:
          type: boolean
          description: Whether the notification was processed successfully
        message:
          type: string
          description: Processing status message
