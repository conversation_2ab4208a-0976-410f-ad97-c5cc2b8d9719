---
id: guest-email-subscribed-query
name: Guest Email Subscribed Query
version: 0.0.1
summary: |
  Query to subscribe a guest email to the newsletter in Magento
producers:
  - magento
owners:
  - euvic
  - enterprise
specifications:
  - type: openapi
    path: 'openapi.yml'
channels:
  - id: magento-integration-adapter.{env}.rest.queries
    parameters:
      env: local
---

## Overview

This query is automatically triggered when a guest user subscribes to the newsletter through any of Magento's subscription points (footer, popup, checkout, etc.). It handles the subscription process and ensures proper validation and GDPR compliance.

## Architecture diagram

<NodeGraph />

## Query Details

### Trigger Point
- Triggered when guest submits newsletter subscription form
- Part of Magento's newsletter management workflow
- Can be triggered from multiple frontend locations
- No authentication required (guest flow)

### Data Structure
Uses response format from Magento 2 API endpoint:
`POST /V1/newsletter/guest/subscribe`
[Magento API Documentation](https://developer.adobe.com/commerce/webapi/rest/modules/newsletter/)

Key data components:
- Email address (required)
- Store view ID (required)
- Subscription source
- GDPR consent status

### Response Data
Example following Magento API standards:
```json
{
  "success": true,
  "message": "Email successfully subscribed to newsletter",
  "subscription_id": "123"
}
```

## Integration Guidelines

### Critical Fields
- `email`: Must be valid email format (RFC 5322)
- `store_id`: Must be valid Magento store view ID
- `gdpr_consent`: Required for EU store views
- `source`: Tracks subscription origin point

### Processing Requirements
1. Email format validation
2. Check for existing subscription
3. Store view validation
4. GDPR consent verification
5. Double opt-in handling if configured

### Error Handling
1. Invalid email format
2. Already subscribed email
3. Invalid store view
4. Missing GDPR consent
5. Failed confirmation email

## Notes

- Double opt-in may be required based on store configuration
- GDPR consent is mandatory for EU store views
- Store-specific subscriptions are supported
- Subscription source tracking aids in analytics
- Consider local privacy laws and regulations 