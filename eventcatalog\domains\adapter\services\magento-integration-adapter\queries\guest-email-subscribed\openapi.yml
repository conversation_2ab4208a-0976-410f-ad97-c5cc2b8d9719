openapi: "3.1.0"
info:
  title: Guest Email Subscribed
  version: 0.0.1
  description: |
    OpenAPI specification for the Guest Email Subscribed query in Magento Integration Adapter.
    This represents an asynchronous notification that is triggered after a guest email is subscribed to marketing communications.

    Based on Magento 2 API endpoint: POST /V1/newsletter/guest/subscribe
servers:
  - url: http://localhost:7501/api/v0.1/magento-integration-adapter
paths:
  /guest-email-subscribed:
    post:
      summary: Notification of guest email subscription
      description: Asynchronous notification triggered when a guest email is subscribed to marketing communications.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GuestEmailSubscribedRequest'
            example:
              email: "<EMAIL>"
              store_id: 1
              source: "footer"
              gdpr_consent: true
              subscribed_at: "2024-03-19T14:30:00Z"
      responses:
        '200':
          description: Notification successfully processed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GuestEmailSubscribedResponse'
              example:
                success: true
                timestamp: "2024-03-19T14:30:01Z"
                message: "Guest email subscription processed successfully"
                data:
                  email: "<EMAIL>"
                  store_id: 1
                  subscription_id: "123"
        '400':
          description: Bad request - validation error
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    description: Always false for errors
                  timestamp:
                    type: string
                    format: date-time
                    description: When the error occurred (ISO-8601)
                  message:
                    type: string
                    description: Error message
                  errors:
                    type: array
                    items:
                      type: object
                      properties:
                        field:
                          type: string
                        message:
                          type: string
              example:
                success: false
                timestamp: "2024-03-19T14:30:01Z"
                message: "Validation failed"
                errors: [
                  {
                    field: "email",
                    message: "Email address is invalid"
                  }
                ]
components:
  schemas:
    GuestEmailSubscribedRequest:
      type: object
      required:
        - email
        - store_id
        - gdpr_consent
        - subscribed_at
      properties:
        email:
          type: string
          format: email
          description: Guest's email address
        store_id:
          type: integer
          description: Store view ID where subscription occurred
        source:
          type: string
          enum:
            - footer
            - checkout
            - popup
            - campaign
            - import
          description: Source of the subscription request
        gdpr_consent:
          type: boolean
          description: Whether GDPR consent was given
        subscribed_at:
          type: string
          format: date-time
          description: When the subscription occurred (ISO-8601)

    GuestEmailSubscribedResponse:
      type: object
      required:
        - success
        - timestamp
        - message
      properties:
        success:
          type: boolean
          description: Whether the notification was processed successfully
        timestamp:
          type: string
          format: date-time
          description: When the response was generated (ISO-8601)
        message:
          type: string
          description: Processing status message
        data:
          type: object
          description: Response data
          properties:
            email:
              type: string
              format: email
              description: Guest's email address
            store_id:
              type: integer
              description: Store view ID where subscription occurred
            subscription_id:
              type: string
              description: Unique identifier for the subscription
