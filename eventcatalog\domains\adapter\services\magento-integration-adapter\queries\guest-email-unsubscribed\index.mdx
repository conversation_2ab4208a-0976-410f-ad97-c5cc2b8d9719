---
id: guest-email-unsubscribed-query
name: Guest Email Unsubscribed Query
version: 0.0.1
summary: |
  Query to unsubscribe a guest email from the newsletter in Magento
producers:
  - magento
owners:
  - euvic
  - enterprise
specifications:
  - type: openapi
    path: 'openapi.yml'
channels:
  - id: magento-integration-adapter.{env}.rest.queries
    parameters:
      env: local
---

## Overview

This query is automatically triggered when a guest user unsubscribes from the newsletter through any of Magento's unsubscription points (email link, footer, etc.). It handles the unsubscription process and ensures proper validation and GDPR compliance.

## Architecture diagram

<NodeGraph />

## Query Details

### Trigger Point
- Triggered when guest clicks unsubscribe link in email
- Triggered from newsletter unsubscribe form
- Part of Magento's newsletter management workflow
- No authentication required (guest flow)
- Can be triggered from multiple frontend locations

### Data Structure
Uses response format from Magento 2 API endpoint:
`POST /V1/newsletter/guest/unsubscribe`
[Magento API Documentation](https://developer.adobe.com/commerce/webapi/rest/modules/newsletter/)

Key data components:
- Email address (required)
- Store view ID (required)
- Unsubscription source
- GDPR consent status

### Response Data
Example following Magento API standards:
```json
{
  "success": true,
  "message": "Email successfully unsubscribed from newsletter",
  "subscription_id": "123"
}
```

## Integration Guidelines

### Critical Fields
- `email`: Must be valid email format (RFC 5322)
- `store_id`: Must be valid Magento store view ID
- `gdpr_consent`: Required for EU store views
- `source`: Tracks unsubscription origin point

### Processing Requirements
1. Email format validation
2. Verify existing subscription
3. Store view validation
4. GDPR consent verification
5. Unsubscription confirmation handling if configured

### Error Handling
1. Invalid email format
2. Non-existent subscription
3. Invalid store view
4. Missing GDPR consent
5. Failed confirmation email

## Notes

- Unsubscription confirmation may be required based on store configuration
- GDPR compliance is maintained for EU store views
- Store-specific unsubscriptions are supported
- Unsubscription source tracking aids in analytics
- Consider local privacy laws and regulations 