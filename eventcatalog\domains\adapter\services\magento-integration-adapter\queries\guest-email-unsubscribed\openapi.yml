openapi: "3.1.0"
info:
  title: Guest Email Unsubscribed
  version: 0.0.1
  description: |
    OpenAPI specification for the Guest Email Unsubscribed query in Magento Integration Adapter.
    This represents an asynchronous notification that is triggered after a guest email is unsubscribed from marketing communications.

    Based on Magento 2 API endpoint: POST /V1/newsletter/guest/unsubscribe
servers:
  - url: http://localhost:7501/api/v0.1/magento-integration-adapter
paths:
  /guest-email-unsubscribed:
    post:
      summary: Notification of guest email unsubscription
      description: Asynchronous notification triggered when a guest email is unsubscribed from marketing communications.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GuestEmailUnsubscribedRequest'
            example:
              email: "<EMAIL>"
              store_id: 1
              unsubscribed_at: "2024-03-19T14:30:00Z"
      responses:
        '200':
          description: Notification successfully processed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GuestEmailUnsubscribedResponse'
              example:
                success: true
                timestamp: "2024-03-19T14:30:01Z"
                message: "Guest email unsubscription processed successfully"
                data:
                  email: "<EMAIL>"
                  store_id: 1
        '400':
          description: Bad request - validation error
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    description: Always false for errors
                  timestamp:
                    type: string
                    format: date-time
                    description: When the error occurred
                  message:
                    type: string
                    description: Error message
                  errors:
                    type: array
                    items:
                      type: object
                      properties:
                        field:
                          type: string
                        message:
                          type: string
              example:
                success: false
                timestamp: "2024-03-19T14:30:01Z"
                message: "Validation failed"
                errors: [
                  {
                    field: "email",
                    message: "Email address is invalid"
                  }
                ]
components:
  schemas:
    GuestEmailUnsubscribedRequest:
      type: object
      required:
        - email
        - store_id
        - unsubscribed_at
      properties:
        email:
          type: string
          format: email
          description: Guest's email address
        store_id:
          type: integer
          description: Store view ID where unsubscription occurred
        unsubscribed_at:
          type: string
          format: date-time
          description: When the unsubscription occurred (ISO-8601)

    GuestEmailUnsubscribedResponse:
      type: object
      required:
        - success
        - timestamp
        - message
      properties:
        success:
          type: boolean
          description: Whether the notification was processed successfully
        timestamp:
          type: string
          format: date-time
          description: When the response was generated (ISO-8601)
        message:
          type: string
          description: Processing status message
        data:
          type: object
          description: Response data
          properties:
            email:
              type: string
              format: email
              description: Guest's email address
            store_id:
              type: integer
              description: Store view ID where unsubscription occurred
