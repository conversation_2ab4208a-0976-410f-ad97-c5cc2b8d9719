---
createdAt: 2025-06-04
---

## Changes

### 2025-07-02 - Version 1.0.1
#### Documentation Updates
- Adjusted documentation to specify ISO 3166-2:US and ISO 3166-2:CA formatting standard for region codes in address-related fields.

### 2025-06-06 - Version 1.0.0

- <PERSON> and <PERSON><PERSON><PERSON> reviewed the query and agreed that is will become v1.0.0

### 2025-06-04 - Version 0.1.1
Major conceptual change in the query's purpose and implementation:

#### Fundamental Change
- Corrected the misclassification of this integration pattern: This is not a query TO Magento (read API call) but rather a notification FROM Magento to the integration layer.
- Changed from incorrectly modeling this as an order creation query to properly representing it as a Magento notification endpoint
- Now correctly documented as an endpoint that receives order data when <PERSON><PERSON><PERSON> triggers the notification
- Previous version was missinterpreted by the cursor due to the fact that we are using "queries" as a replacement for "Notifications" which doesnt exist in eventcatalog.

#### Documentation Updates
- Updated summary and overview to reflect the new query purpose
- Added new sections:
  - Trigger Point documentation
  - Critical Fields section with detailed ID explanations
  - Comprehensive Processing Requirements
- Removed creation-specific sections (Use Cases, Integration Considerations)

#### API Specification Changes
1. Schema Alignment
   - Aligned request schema with Magento's "GET /V1/orders/[orderId]" response
   - Added proper OpenAPI documentation with endpoint descriptions

2. Added Critical Fields
   - `increment_id`: Customer-facing order number
   - `items[].item_id`: Unique identifier for order items (written as items array with item_id field)
   - Order state and status fields
   - Creation timestamp
   - Total quantity and grand total fields (base and regular)

3. Enhanced Order Items Structure
   - Added `item_id` as a critical field
   - Introduced base and regular price variants
   - Added row total calculations
   - Renamed quantity field to `qty_ordered`

4. Payment Information
   - Added ordered amount fields (base and regular)
   - Retained payment method information

5. Removed Creation-Specific Schemas
   - Removed ProductOption schema
   - Removed ShippingMethodQuery schema
   - Removed various order creation fields

6. HTTP Response Envelope Implementation
   - **New Response Format**: Implemented standardized HTTP response envelope wrapper for all API responses
   - **Envelope Structure**: All HTTP responses now wrapped in consistent format:
   ```json
   {
      "data": { /* actual response data */ },
      "error": null  /* error details for failures */
   }
   ```
   - **Error Response Format**: Standardized error responses with typed error structure:
   ```json
   {
      "data": null,
      "error": {
         "message": "Human-readable error message",
         "type": "ErrorType",
         "details": [/* field-specific validation errors */]
      }
   }
   ```

#### Processing Requirements
Added explicit requirements for consuming systems:
1. Critical ID storage requirements
2. Order item processing using item_id
3. Currency handling requirements
4. Idempotency implementation
5. B2C/B2B scenario handling
6. Error handling and validation procedures
