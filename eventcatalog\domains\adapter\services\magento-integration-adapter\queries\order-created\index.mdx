---
id: order-created-query
name: Order Created Query
version: 1.0.0
summary: Query that is triggered when a new order is created in Magento.
owners:
  - euvic
  - enterprise
specifications:
  - type: openapi
    path: 'openapi.yml'
channels:
  - id: magento-integration-adapter.{env}.rest.queries
    parameters:
      env: local
badges:
  - content: Contract Approved
    backgroundColor: green
    textColor: white
    icon: CheckCircleIcon
---

## Overview
This query is automatically triggered when a new order is created in Magento. It sends the complete order data to the integration adapter for further processing and synchronization with external systems.

## Architecture diagram
<NodeGraph />

## Query Details

### Trigger Point
- Automatically triggered when an order is successfully created in Magento
- Prerequisites:
  - Order must be successfully created in Magento
  - Order must have a valid status
  - All required order data must be present
- Part of the order creation notification workflow

### Data Structure
Uses response format from Magento 2 API endpoint:
`GET /V1/orders/{orderId}`

[Magento API Documentation](https://devdocs.magento.com/swagger/)

Key data components:
- Order details and metadata
- Customer information
- Billing and shipping addresses
- Order items with options
- Payment information
- Shipping details
- Discounts and promotions
- Tax calculations

For complete payload structure and examples, see openapi.yml.

### Critical Fields
When processing orders, external systems MUST store these critical IDs:
- `increment_id`: Customer-facing order number and the order number that the Axon Integration uses to identify the order with Magento.
- `items[].item_id`: Unique identifier for each order item

The `item_id` is particularly important because:
- Required for creating shipments
- Needed for processing refunds
- Used for updating order item status
- Cannot be derived from SKU or order ID
- Same SKU can appear multiple times with different item_ids

The `item_id` cannot be derived from the SKU or order ID alone, as the same SKU can appear multiple times in different order items. When creating shipments using the [`magento-ship-order`](/domains/adapter/services/magento/queries/magento-ship-order) query, you must provide this `item_id` as the `order_item_id` parameter.

### Processing Requirements
1. Store all critical IDs for future reference
2. Process order items using the provided `item_id`
3. Handle order state/status appropriately
4. Support both base and order currency values
5. Implement idempotency checks using `increment_id`
6. Process all order items
7. Handle both B2C and B2B scenarios
8. Maintain currency precision
9. Consider regional requirements

### Error Handling
Systems should:
- Validate received order data
- Handle missing required fields
- Process partial data appropriately
- Log validation failures
- Implement retry mechanisms
- Handle partial failures
- Report processing status back to Magento

## Notes
- Supports extension attributes for custom implementations
- B2B features included when applicable
- Guest orders handled with appropriate null values
- Schema supports all Magento product types
- Payment information handled according to PCI compliance
- Idempotency guaranteed through order increment ID 