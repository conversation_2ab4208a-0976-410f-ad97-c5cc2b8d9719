openapi: 3.1.0
info:
  title: Order Created Query
  version: 1.0.0
  description: |
    Query triggered when a new order is created in Magento. Uses the exact response structure from Magento's GET /V1/orders/{orderId} endpoint.
servers:
  - url: http://localhost:7501/api/v1.0/magento-integration-adapter
paths:
  /order-created:
    post:
      summary: Receive notification of new order creation in Magento
      description: |
        Endpoint that receives order data when a new order is created in Magento.
        The data structure matches exactly the Magento API GET /V1/orders/{orderId} response.
      security:
        - apiKey: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OrderCreatedRequest'
            example:
              increment_id: "000000123"
              state: "new"
              status: "pending"
              customer_id: 456
              customer_email: "<EMAIL>"
              customer_firstname: "<PERSON>"
              customer_lastname: "Doe"
              billing_address:
                firstname: "<PERSON>"
                lastname: "Doe"
                street:
                  - "123 Main Street"
                  - "Apt 4B"
                city: "New York"
                region: "New York"
                postcode: "10001"
                country_id: "US"
                telephone: "************"
              shipping_address:
                firstname: "<PERSON>"
                lastname: "Doe"
                street:
                  - "123 Main Street"
                  - "Apt 4B"
                city: "New York"
                region: "New York"
                postcode: "10001"
                country_id: "US"
                telephone: "************"
              items:
                - item_id: 789
                  sku: "24-MB01"
                  name: "Joust Duffle Bag"
                  qty_ordered: 2
                  price: 34.00
                  base_price: 34.00
                  row_total: 68.00
                  base_row_total: 68.00
                - item_id: 790
                  sku: "24-WG085"
                  name: "Luma Water Bottle"
                  qty_ordered: 1
                  price: 15.00
                  base_price: 15.00
                  row_total: 15.00
                  base_row_total: 15.00
              payment:
                method: "checkmo"
                amount_ordered: 88.95
                base_amount_ordered: 88.95
              total_qty_ordered: 3
              grand_total: 88.95
              base_grand_total: 88.95
              created_at: "2024-03-21T14:30:00Z"
      responses:
        '202':
          description: Accepted
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                data:
                  order_id: "123e4567-e89b-12d3-a456-426614174000"
                error: null
        '401':
          description: Unauthorized - Invalid or missing API key
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                  message:
                    type: string
                  type:
                    type: string
              example:
                error: "Unauthorized"
                message: "Invalid API key"
                type: "UNAUTHORIZED"
        '400':
          description: Bad Request - Invalid order data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiErrorResponse'
              example:
                data: null
                error:
                  message: "Invalid order data provided"
                  type: "ValidationError"
                  details:
                    - field: "customer_email"
                      message: "Invalid email format"
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiErrorResponse'
              example:
                data: null
                error:
                  message: "An unexpected error occurred"
                  type: "InternalServerError"
components:
  schemas:
    ApiResponse:
      type: object
      description: Standard envelope wrapper for all API responses
      properties:
        data:
          type: object
          nullable: true
          description: Response data (null for error responses)
          properties:
            order_id:
              type: string
              format: uuid
              description: The unique identifier for the created order
        error:
          type: object
          nullable: true
          description: Error details (null for successful responses)
      required:
        - data

    ApiErrorResponse:
      type: object
      description: Standard envelope wrapper for error responses
      properties:
        data:
          type: object
          nullable: true
          description: Always null for error responses
        error:
          type: object
          nullable: false
          description: Error details
          properties:
            message:
              type: string
              description: Human-readable error message
            type:
              type: string
              description: Error type classification
            details:
              type: array
              nullable: true
              description: Additional error details for validation errors
              items:
                type: object
                properties:
                  field:
                    type: string
                    description: Field name with validation error
                  message:
                    type: string
                    description: Field-specific error message
      required:
        - data
        - error

    OrderCreatedRequest:
      type: object
      description: Order data from Magento, matching GET /V1/orders/{orderId} response structure
      properties:
        increment_id:
          type: string
          description: Customer-facing order number. This is the order number that the Axon Integration uses to identify the order with Magento.
        state:
          type: string
          description: Order state
        status:
          type: string
          description: Order status
        customer_id:
          type: integer
          nullable: true
          description: Customer ID (null for guest orders)
        customer_email:
          type: string
          description: Customer email address
        customer_firstname:
          type: string
          nullable: true
          description: Customer first name
        customer_lastname:
          type: string
          nullable: true
          description: Customer last name
        billing_address:
          $ref: '#/components/schemas/Address'
        shipping_address:
          $ref: '#/components/schemas/Address'
        items:
          type: array
          description: Order items
          items:
            $ref: '#/components/schemas/OrderItem'
        payment:
          $ref: '#/components/schemas/Payment'
        total_qty_ordered:
          type: number
          format: float
          description: Total quantity ordered
        grand_total:
          type: number
          format: float
          description: Order grand total
        base_grand_total:
          type: number
          format: float
          description: Order base grand total
        created_at:
          type: string
          format: date-time
          description: Order creation timestamp (ISO-8601)

    Address:
      type: object
      properties:
        firstname:
          type: string
          nullable: true
          description: First name for billing address
        lastname:
          type: string
          nullable: true
          description: Last name for billing address
        street:
          type: array
          items:
            type: string
          nullable: true
          description: Street address lines
        city:
          type: string
          nullable: true
          description: City
        region:
          type: string
          nullable: true
          description: Region/State name
        postcode:
          type: string
          nullable: true
          description: Postal code
        country_id:
          type: string
          nullable: true
          description: Country code
        telephone:
          type: string
          nullable: true
          description: Telephone number
      additionalProperties: false

    OrderItem:
      type: object
      properties:
        item_id:
          type: integer
          description: Unique identifier for the order item
        sku:
          type: string
          description: Product SKU
        name:
          type: string
          description: Product name
        qty_ordered:
          type: number
          format: float
          description: Quantity ordered
        price:
          type: number
          format: float
          description: Item price
        base_price:
          type: number
          format: float
          description: Item base price
        row_total:
          type: number
          format: float
          description: Row total
        base_row_total:
          type: number
          format: float
          description: Base row total

    Payment:
      type: object
      properties:
        method:
          type: string
          description: Payment method code
        amount_ordered:
          type: number
          format: float
          description: Amount ordered
        base_amount_ordered:
          type: number
          format: float
          description: Base amount ordered


  securitySchemes:
    apiKey:
      type: apiKey
      in: header
      name: X-API-Key
      description: |
        API Key authentication.
        Authentication may be optional depending on environment configuration.
        Contact the Enterprise Integration team for API credentials.