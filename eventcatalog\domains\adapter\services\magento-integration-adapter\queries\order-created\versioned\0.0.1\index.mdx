---
id: order-created-query
name: Order Created Query
version: 0.0.1
summary: |
  Query to create a new order in Magento
owners:
  - euvic
  - enterprise
specifications:
  - type: openapi
    path: 'openapi.yml'
channels:
  - id: magento-integration-adapter.{env}.rest.queries
    parameters:
      env: local
---

## Overview

This query is used to create a new order in Magento. It handles the creation of orders with comprehensive information including customer details, billing/shipping addresses, items ordered, payment information, and order metadata.

## Architecture diagram

<NodeGraph />

## Query Details

### Use Cases
- Convert cart to order
- Create order from admin panel
- API-based order creation
- B2B quote conversion to order

### Key Information
- Order details and metadata
- Customer information
- Billing and shipping addresses
- Order items with options
- Payment information
- Shipping details
- Discounts and promotions
- Tax calculations

## Usage Guidelines

### Integration Considerations
- Validate inventory before order creation
- Handle both B2C and B2B scenarios
- Maintain currency precision
- Consider regional requirements
- Process payment methods correctly

### Error Handling
- Validate order data before creation
- Handle inventory issues
- Process payment errors
- Manage tax calculation errors
- Handle shipping method validation

## Notes
- The query supports extension attributes for custom implementations
- B2B features are included when applicable
- Guest orders are handled with appropriate null values
- The schema supports all Magento product types
- Payment information sensitivity is handled according to PCI compliance 