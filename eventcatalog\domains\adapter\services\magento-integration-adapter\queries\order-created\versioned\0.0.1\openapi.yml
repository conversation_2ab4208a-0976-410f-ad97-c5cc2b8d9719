openapi: 3.1.0
servers:
  - url: http://localhost:7501/api/v0.1/magento-integration-adapter
paths:
  order-created:
    post:
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OrderCreatedQuery'
            example:
              cart_id: "CART123"
              customer_email: "<EMAIL>"
              customer_firstname: "<PERSON>"
              customer_lastname: "Doe"
              store_id: 1
              items:
                - sku: "SKU123"
                  qty: 2
                  price: 19.99
                  name: "T-Shirt"
                  product_type: "simple"
              billing_address:
                firstname: "<PERSON>"
                lastname: "Doe"
                street: ["123 Main St"]
                city: "Metropolis"
                country_id: "US"
                telephone: "555-1234"
              payment:
                method: "credit_card"
      responses:
        '202':
          description: Accepted
          content:
            application/json:
              schema:
                type: object
                properties:
                  orderId:
                    type: string
                    format: uuid
                    description: The unique identifier for the created order
              example:
                orderId: "123e4567-e89b-12d3-a456-************"
components:
  schemas:
    Address:
      type: object
      properties:
        firstname:
          type: string
          nullable: true
          description: First name for billing address
        lastname:
          type: string
          nullable: true
          description: Last name for billing address
        street:
          type: array
          items:
            type: string
          nullable: true
          description: Street address lines
        city:
          type: string
          nullable: true
          description: City
        region:
          type: string
          nullable: true
          description: Region/State name
        postcode:
          type: string
          nullable: true
          description: Postal code
        country_id:
          type: string
          nullable: true
          description: Country code
        telephone:
          type: string
          nullable: true
          description: Telephone number
      additionalProperties: false
    OrderCreatedQuery:
      type: object
      properties:
        cart_id:
          type: string
          nullable: true
          description: ID of the cart to convert to order
        customer_email:
          type: string
          nullable: true
          description: Customer's email address
        customer_firstname:
          type: string
          nullable: true
          description: Customer's first name
        customer_lastname:
          type: string
          nullable: true
          description: Customer's last name
        store_id:
          type: integer
          format: int32
          description: ID of the store where the order is being placed
        items:
          type: array
          items:
            $ref: '#/components/schemas/OrderItemQuery'
          nullable: true
          description: List of items in the order
        billing_address:
          $ref: '#/components/schemas/Address'
          description: Billing address information
        shipping_address:
          $ref: '#/components/schemas/Address'
          description: Shipping address information (same structure as billing_address)
        payment:
          $ref: '#/components/schemas/PaymentQuery'
          description: Payment information
        shipping_method:
          $ref: '#/components/schemas/ShippingMethodQuery'
          description: Shipping method information
      additionalProperties: false
    OrderItemQuery:
      type: object
      properties:
        sku:
          type: string
          nullable: true
          description: SKU of the product
        qty:
          type: number
          format: double
          description: Quantity ordered
        price:
          type: number
          format: double
          description: Price per unit
        name:
          type: string
          nullable: true
          description: Product name
        product_type:
          type: string
          nullable: true
          description: Type of the product
        product_option:
          $ref: '#/components/schemas/ProductOption'
          description: Additional options for configurable, bundle, or custom products
      additionalProperties: false
    PaymentQuery:
      type: object
      properties:
        method:
          type: string
          nullable: true
          description: Payment method code
      additionalProperties: false
    ProductOption:
      type: object
      properties:
        extension_attributes:
          nullable: true
          description: Custom attributes for the product option
      additionalProperties: false
    ShippingMethodQuery:
      type: object
      properties:
        method_code:
          type: string
          nullable: true
          description: Shipping method code
        carrier_code:
          type: string
          nullable: true
          description: Shipping carrier code
      additionalProperties: false