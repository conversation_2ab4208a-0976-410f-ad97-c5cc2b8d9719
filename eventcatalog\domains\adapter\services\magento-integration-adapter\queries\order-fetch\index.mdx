---
id: order-fetch-query
name: Order Fetch Query
version: 0.0.1
summary: Query to fetch a complete order from Axon by its order ID
owners:
  - enterprise
specifications:
  - type: openapi
    path: 'openapi.yml'
channels:
  - id: magento-integration-adapter.{env}.rest.queries
    parameters:
      env: local
badges:
  - content: Draft
    backgroundColor: yellow
    textColor: black
    icon: PencilIcon
---

# Order Fetch Query

## Overview

This query allows you to fetch a complete order from Axon using its unique order ID. The endpoint returns the full order data structure that matches Magento's GET /V1/orders/\{orderId\} response format.

The order-fetch query is used to retrieve complete order information by providing the order's unique identifier in the URL path. This is particularly useful for:

- Order status inquiries
- Order detail verification
- Integration with external systems that need current order data

## Architecture Diagram

<NodeGraph />

## Query Details

### Trigger Point

- Called on-demand when order information is needed
- Typically used by customer service systems, reporting tools, or integration partners
- Part of order inquiry and status checking workflows

### Data Structure

Uses same format as the order-created endpoint

See openapi.yml for complete payload structure and examples.

## Integration Guidelines

### Processing Requirements

- Order ID must be a valid UUID format

### Error Handling

- 404: Order not found
- 500: Internal server error

## Notes

The data structure exactly matches what would be received from Magento, ensuring consistency across the integration. Response includes all order metadata, customer information, billing/shipping addresses, complete item details with pricing, payment information, and order totals.
