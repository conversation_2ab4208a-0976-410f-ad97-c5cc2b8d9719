openapi: 3.1.0
info:
  title: Order Fetch Query
  version: 0.0.1
  description: |
    Query to fetch an order from Axon by its order ID. Returns the complete order data structure
    matching Magento's GET /V1/orders/{orderId} response format.
servers:
  - url: http://localhost:7501/api/v0.1/magento-integration-adapter
paths:
  /orders/{orderId}:
    get:
      summary: Fetch an order by its Axon ID
      description: |
        Retrieves a complete order by its unique identifier. Returns the same data structure
        as the order-created notification, matching Magento's order format.
      security:
        - apiKey: []
      parameters:
        - name: orderId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: The unique identifier of the order to fetch
          example: "123e4567-e89b-12d3-a456-************"
      responses:
        '200':
          description: Order fetched successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                data:
                  increment_id: "000000123"
                  state: "processing"
                  status: "pending"
                  customer_id: 456
                  customer_email: "<EMAIL>"
                  customer_firstname: "<PERSON>"
                  customer_lastname: "<PERSON><PERSON>"
                  billing_address:
                    firstname: "<PERSON>"
                    lastname: "Doe"
                    street:
                      - "123 Main Street"
                      - "Apt 4B"
                    city: "New York"
                    region: "New York"
                    postcode: "10001"
                    country_id: "US"
                    telephone: "************"
                  shipping_address:
                    firstname: "John"
                    lastname: "Doe"
                    street:
                      - "123 Main Street"
                      - "Apt 4B"
                    city: "New York"
                    region: "New York"
                    postcode: "10001"
                    country_id: "US"
                    telephone: "************"
                  items:
                    - item_id: 789
                      sku: "24-MB01"
                      name: "Joust Duffle Bag"
                      qty_ordered: 2
                      price: 34.00
                      base_price: 34.00
                      row_total: 68.00
                      base_row_total: 68.00
                    - item_id: 790
                      sku: "24-WG085"
                      name: "Luma Water Bottle"
                      qty_ordered: 1
                      price: 15.00
                      base_price: 15.00
                      row_total: 15.00
                      base_row_total: 15.00
                  payment:
                    method: "checkmo"
                    amount_ordered: 88.95
                    base_amount_ordered: 88.95
                  total_qty_ordered: 3
                  grand_total: 88.95
                  base_grand_total: 88.95
                  created_at: "2024-03-21T14:30:00Z"
                error: null
        '401':
          description: Unauthorized - Invalid or missing API key
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                  message:
                    type: string
                  type:
                    type: string
              example:
                error: "Unauthorized"
                message: "Invalid API key"
                type: "AuthenticationError"
        '404':
          description: Order not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiErrorResponse'
              example:
                data: null
                error:
                  message: "Order not found"
                  type: "NotFoundError"
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiErrorResponse'
              example:
                data: null
                error:
                  message: "An unexpected error occurred"
                  type: "InternalServerError"

components:
  schemas:
    ApiResponse:
      type: object
      description: Standard envelope wrapper for successful API responses
      properties:
        data:
          $ref: '#/components/schemas/OrderData'
          description: The complete order data
        error:
          type: object
          nullable: true
          description: Error details (null for successful responses)
      required:
        - data

    ApiErrorResponse:
      type: object
      description: Standard envelope wrapper for error responses
      properties:
        data:
          type: object
          nullable: true
          description: Always null for error responses
        error:
          type: object
          nullable: false
          description: Error details
          properties:
            message:
              type: string
              description: Human-readable error message
            type:
              type: string
              description: Error type classification
            details:
              type: array
              nullable: true
              description: Additional error details for validation errors
              items:
                type: object
                properties:
                  field:
                    type: string
                    description: Field name with validation error
                  message:
                    type: string
                    description: Field-specific error message
      required:
        - data
        - error

    OrderData:
      type: object
      description: Complete order data from Magento, matching GET /V1/orders/{orderId} response structure
      properties:
        increment_id:
          type: string
          description: Customer-facing order number. This is the order number that the Axon Integration uses to identify the order with Magento.
        state:
          type: string
          description: Order state
        status:
          type: string
          description: Order status
        customer_id:
          type: integer
          nullable: true
          description: Customer ID (null for guest orders)
        customer_email:
          type: string
          description: Customer email address
        customer_firstname:
          type: string
          nullable: true
          description: Customer first name
        customer_lastname:
          type: string
          nullable: true
          description: Customer last name
        billing_address:
          $ref: '#/components/schemas/Address'
        shipping_address:
          $ref: '#/components/schemas/Address'
        items:
          type: array
          description: Order items
          items:
            $ref: '#/components/schemas/OrderItem'
        payment:
          $ref: '#/components/schemas/Payment'
        total_qty_ordered:
          type: number
          format: float
          description: Total quantity ordered
        grand_total:
          type: number
          format: float
          description: Order grand total
        base_grand_total:
          type: number
          format: float
          description: Order base grand total
        created_at:
          type: string
          format: date-time
          description: Order creation timestamp (ISO-8601)

    Address:
      type: object
      properties:
        firstname:
          type: string
          nullable: true
          description: First name for address
        lastname:
          type: string
          nullable: true
          description: Last name for address
        street:
          type: array
          items:
            type: string
          nullable: true
          description: Street address lines
        city:
          type: string
          nullable: true
          description: City
        region:
          type: string
          nullable: true
          description: Region/State name
        postcode:
          type: string
          nullable: true
          description: Postal code
        country_id:
          type: string
          nullable: true
          description: Country code
        telephone:
          type: string
          nullable: true
          description: Telephone number
      additionalProperties: false

    OrderItem:
      type: object
      properties:
        item_id:
          type: integer
          description: Unique identifier for the order item
        sku:
          type: string
          description: Product SKU
        name:
          type: string
          description: Product name
        qty_ordered:
          type: number
          format: float
          description: Quantity ordered
        price:
          type: number
          format: float
          description: Item price
        base_price:
          type: number
          format: float
          description: Item base price
        row_total:
          type: number
          format: float
          description: Row total
        base_row_total:
          type: number
          format: float
          description: Base row total

    Payment:
      type: object
      properties:
        method:
          type: string
          description: Payment method code
        amount_ordered:
          type: number
          format: float
          description: Amount ordered
        base_amount_ordered:
          type: number
          format: float
          description: Base amount ordered


  securitySchemes:
    apiKey:
      type: apiKey
      in: header
      name: X-API-Key
      description: |
        API Key authentication.
        Authentication may be optional depending on environment configuration.
        Contact the Enterprise Integration team for API credentials.
