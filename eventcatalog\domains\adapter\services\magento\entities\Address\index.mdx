---
id: Address
name: Address
version: 1.0.0
summary: |
  Represents a physical address in Magento, used for both customer addresses and order shipping/billing addresses.
properties:
  - name: entity_id
    type: int
    required: true
    description: Primary key for the address
  - name: parent_id
    type: int
    required: true
    description: ID of the parent entity (customer_id for customer addresses, order_id for order addresses)
    links:
      - type: Customer
        link: /domains/adapter/services/magento/entities/Customer
      - type: Order
        link: /domains/adapter/services/magento/entities/Order
  - name: firstname
    type: string
    required: true
    description: First name for the address
  - name: lastname
    type: string
    required: true
    description: Last name for the address
  - name: street
    type: array
    items:
      type: string
    required: true
    description: Street address lines (up to 3 lines)
  - name: city
    type: string
    required: true
    description: City name
  - name: region
    type: object
    required: false
    description: Region/State information
    properties:
      - name: region_code
        type: string
        description: Region code in ISO 3166-2 format (e.g., US-NY for New York state)
      - name: region
        type: string
        description: Region name
      - name: region_id
        type: int
        description: Region ID in the database
  - name: postcode
    type: string
    required: true
    description: Postal/ZIP code
  - name: country_id
    type: string
    required: true
    description: Two-letter country code (ISO)
  - name: telephone
    type: string
    required: true
    description: Contact phone number
  - name: company
    type: string
    required: false
    description: Company name
  - name: default_shipping
    type: boolean
    required: false
    description: Whether this is the default shipping address
  - name: default_billing
    type: boolean
    required: false
    description: Whether this is the default billing address
  - name: vat_id
    type: string
    required: false
    description: VAT number for the address
---

## Overview

The Address entity represents a physical location in Magento. It's used in multiple contexts:
- [Customer](/domains/adapter/services/magento/entities/Customer) saved addresses
- [Order](/domains/adapter/services/magento/entities/Order) shipping addresses
- [Order](/domains/adapter/services/magento/entities/Order) billing addresses
- Company addresses

### Entity Properties
<EntityPropertiesTable />

## Database Structure

The Address entity is stored in different tables depending on its context:

1. Customer Addresses
   - `customer_address_entity` - Base address information
   - `customer_address_entity_*` - EAV attribute tables

2. Order Addresses
   - `sales_order_address` - Order-specific addresses
   - Stores address as it was at the time of order

## Address Types

### Customer Address
- Saved in customer account
- Can be marked as default shipping/billing
- Reusable for future orders
- Editable by customer

### Order Address
- Snapshot of address at order time
- Immutable
- Preserved for order history
- May contain additional order-specific fields

## Integration Points

The Address entity is accessible through several REST API endpoints:

```http
POST /V1/customers/addresses                    # Create address
GET /V1/customers/addresses/{addressId}        # Retrieve address
PUT /V1/customers/addresses/{addressId}        # Update address
DELETE /V1/customers/addresses/{addressId}     # Delete address
```

## Examples

### Customer Address
```json
{
  "address": {
    "firstname": "John",
    "lastname": "Doe",
    "street": [
      "123 Main St",
      "Apt 4B"
    ],
    "city": "New York",
    "region": {
      "region_code": "NY",
      "region": "New York",
      "region_id": 43
    },
    "postcode": "10001",
    "country_id": "US",
    "telephone": "+**********",
    "default_shipping": true,
    "default_billing": true
  }
}
```

### Order Address
```json
{
  "address": {
    "firstname": "John",
    "lastname": "Doe",
    "street": ["123 Main St"],
    "city": "New York",
    "region": {
      "region_code": "NY",
      "region": "New York"
    },
    "postcode": "10001",
    "country_id": "US",
    "telephone": "+**********",
    "address_type": "shipping",
    "company": "Example Corp",
    "vat_id": "GB123456789"
  }
}
``` 