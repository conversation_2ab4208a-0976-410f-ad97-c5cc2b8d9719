---
id: Cart
version: 1.0.0
name: Cart
summary: |
  Shopping cart entity in Magento, representing both standard carts and B2B quotes
owners:
  - euvic
---

# Cart Entity

## Overview

The Cart entity represents a shopping cart in Magento, which can be either a standard customer cart or a B2B negotiable quote. It manages the collection of items a customer intends to purchase, along with pricing, discounts, and checkout information.

## Types

### Standard Cart
- Created when customers add items to their cart
- Supports guest and logged-in customers
- Manages product configurations and options
- Handles promotions and discounts
- Calculates totals and taxes

### B2B Quote
- Extended cart functionality for B2B customers
- Supports price negotiations
- Company-specific pricing
- Approval workflows
- Custom payment terms

## Structure

### Core Components

1. Cart Items
   - Product references
   - Quantities
   - Custom options
   - Configurable options
   - Bundle selections
   - Gift options

2. Customer Information
   - Customer ID (or guest)
   - Customer group
   - Company information (B2B)
   - Tax class

3. Addresses
   - Shipping address
   - Billing address
   - Address validation
   - Shipping methods

4. Pricing
   - Item prices
   - Discounts
   - Tax calculations
   - Shipping costs
   - Currency information

5. B2B Features
   - Quote status
   - Negotiation history
   - Company context
   - Approval chain
   - Custom payment terms

## Lifecycle

1. Creation
   - Empty cart creation
   - First item addition
   - Guest cart creation
   - Quote initiation (B2B)

2. Updates
   - Item addition/removal
   - Quantity updates
   - Option changes
   - Address updates
   - Shipping selection
   - Discount application

3. B2B Specific
   - Quote submission
   - Price negotiation
   - Quote approval/rejection
   - Quote expiration
   - Quote conversion

4. Conversion
   - Checkout initiation
   - Order creation
   - Cart cleanup
   - Quote finalization

## Integration Points

### Events
- cart-created
- cart-update
- cart-merged (guest to customer)
- cart-expired
- quote-submitted
- quote-approved
- quote-rejected

### APIs
- Cart management
- Quote management
- Item management
- Shipping estimation
- Total calculation
- Discount application

## Validation Rules

1. Item Validation
   - Stock availability
   - Price verification
   - Option requirements
   - Quantity limits
   - Product restrictions

2. Cart Rules
   - Maximum item count
   - Maximum quantity
   - Minimum order amount
   - Currency restrictions
   - Customer group limitations

3. B2B Rules
   - Company credit limits
   - Approval requirements
   - Negotiation thresholds
   - Company-specific restrictions

## Examples

### Standard Cart

```json
{
  "id": 12345,
  "customer_id": 67890,
  "items": [
    {
      "item_id": 1,
      "sku": "PROD-001",
      "qty": 2,
      "price": 29.99,
      "product_type": "simple"
    }
  ],
  "addresses": {
    "shipping": {
      "firstname": "John",
      "lastname": "Doe",
      "street": ["123 Main St"],
      "city": "New York",
      "country_id": "US"
    }
  },
  "totals": {
    "subtotal": 59.98,
    "tax": 5.99,
    "shipping": 10.00,
    "grand_total": 75.97
  }
}
```

### B2B Quote

```json
{
  "id": 12346,
  "customer_id": 67891,
  "is_quote": true,
  "quote_status": "pending_approval",
  "company_id": 789,
  "items": [
    {
      "item_id": 1,
      "sku": "BULK-001",
      "qty": 100,
      "original_price": 29.99,
      "negotiated_price": 24.99
    }
  ],
  "negotiation": {
    "history": [
      {
        "date": "2024-03-20T10:00:00Z",
        "user": "<EMAIL>",
        "action": "submitted",
        "comment": "Requesting bulk discount"
      }
    ],
    "expiration_date": "2024-04-20T10:00:00Z"
  },
  "totals": {
    "subtotal": 2499.00,
    "tax": 249.90,
    "shipping": 0.00,
    "grand_total": 2748.90
  }
}
```

## Related Entities

- [Order](/domains/adapter/services/magento/entities/Order)
- [Product](/domains/adapter/services/magento/entities/Product)
- [Customer](/domains/adapter/services/magento/entities/Customer)
- [Address](/domains/adapter/services/magento/entities/Address)

## Notes

1. Performance Considerations
   - Cache cart totals
   - Optimize quote calculations
   - Handle concurrent updates
   - Manage session storage

2. Security
   - Validate prices
   - Check permissions
   - Prevent quote tampering
   - Secure negotiation data

3. B2B Features
   - Company validation
   - Credit checking
   - Approval routing
   - Custom pricing rules

4. Integration
   - ERP synchronization
   - Inventory verification
   - Price calculation
   - Tax computation 