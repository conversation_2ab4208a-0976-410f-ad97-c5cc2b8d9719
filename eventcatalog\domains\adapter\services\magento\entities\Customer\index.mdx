---
id: Customer
name: Customer
version: 1.0.0
aggregateRoot: true
summary: |
  Represents a registered customer in Magento, including their personal information, addresses, and account details.
properties:
  - name: entity_id
    type: int
    required: true
    description: Primary key for the customer
  - name: email
    type: string
    required: true
    description: Customer's email address (unique)
  - name: firstname
    type: string
    required: true
    description: Customer's first name
  - name: lastname
    type: string
    required: true
    description: Customer's last name
  - name: group_id
    type: int
    required: true
    description: Customer group ID
  - name: store_id
    type: int
    required: true
    description: Store view ID where customer was created
  - name: website_id
    type: int
    required: true
    description: Website ID where customer was created
  - name: created_at
    type: datetime
    required: true
    description: Timestamp when the customer account was created
  - name: updated_at
    type: datetime
    required: true
    description: Timestamp of the last customer account update
  - name: is_active
    type: boolean
    required: true
    description: Whether the customer account is active
  - name: addresses
    type: array
    items:
      type: Address
      link: /domains/adapter/services/magento/entities/Address
    required: false
    description: List of customer addresses
  - name: custom_attributes
    type: array
    items:
      type: object
      properties:
        - name: attribute_code
          type: string
          description: Custom attribute code
        - name: value
          type: string
          description: Custom attribute value
    required: false
    description: Custom customer attributes
---

## Overview

The Customer entity represents a registered user in Magento. It maintains customer account information, [addresses](/domains/adapter/services/magento/entities/Address), and relationships with other entities like [orders](/domains/adapter/services/magento/entities/Order) and wishlists.

### Entity Properties
<EntityPropertiesTable />

## Database Structure

The Customer entity uses Magento's EAV model and spans multiple tables:

1. `customer_entity` - Base customer information
2. `customer_entity_*` - Attribute value tables (text, varchar, etc.)
3. `customer_address_entity` - Customer addresses
4. `customer_group` - Customer groups
5. `customer_grid_flat` - Flattened customer data for admin grid

## Customer Groups

Customers can be assigned to different groups for:
- Pricing rules
- Tax classes
- Catalog permissions
- Discount rules

Default groups include:
- General
- NOT LOGGED IN
- Wholesale
- Retailer

## Integration Points

The Customer entity is accessible through several REST API endpoints:

```http
POST /V1/customers                   # Create customer
GET /V1/customers/{customerId}      # Retrieve customer
PUT /V1/customers/{customerId}      # Update customer
GET /V1/customers/search           # Search customers
POST /V1/customers/password        # Reset password
GET /V1/customers/me              # Get current customer
```

## Examples

### Customer Creation
```json
{
  "customer": {
    "email": "<EMAIL>",
    "firstname": "John",
    "lastname": "Doe",
    "store_id": 1,
    "website_id": 1,
    "group_id": 1,
    "custom_attributes": [
      {
        "attribute_code": "phone_number",
        "value": "+1234567890"
      }
    ],
    "addresses": [
      {
        "firstname": "John",
        "lastname": "Doe",
        "street": ["123 Main St"],
        "city": "New York",
        "country_id": "US",
        "region": {
          "region_code": "NY",
          "region": "New York"
        },
        "postcode": "10001",
        "telephone": "+1234567890",
        "default_shipping": true,
        "default_billing": true
      }
    ]
  }
}
```

### Customer Update
```json
{
  "customer": {
    "id": 1,
    "email": "<EMAIL>",
    "firstname": "John",
    "lastname": "Doe",
    "website_id": 1,
    "extension_attributes": {
      "is_subscribed": true
    },
    "custom_attributes": [
      {
        "attribute_code": "preferred_language",
        "value": "en_US"
      }
    ]
  }
}
``` 