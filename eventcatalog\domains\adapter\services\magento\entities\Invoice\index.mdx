---
id: Invoice
version: 1.0.0
name: Invoice
summary: |
  Magento invoice entity representing financial documents for completed orders
owners:
  - euvic
---

# Invoice Entity

## Overview

The Invoice entity represents a financial document in Magento that records the payment for an order. It is created when an order is paid and serves as a proof of purchase and payment record.

## Structure

### Core Components

1. Basic Information
   - Invoice ID
   - Order reference
   - Creation date
   - Status
   - Store view ID

2. Financial Details
   - Subtotal
   - Shipping amount
   - Tax amount
   - Discount amount
   - Grand total
   - Base currency amounts
   - Transaction ID

3. Items
   - Product details
   - Quantity invoiced
   - Price
   - Tax
   - Discount

4. Billing Information
   - Customer details
   - Billing address
   - Payment method

## Lifecycle

1. Creation
   - Order payment received
   - Partial invoice creation
   - Full order invoice

2. States
   - Pending
   - Paid
   - Canceled
   - Refunded

3. Operations
   - Create invoice
   - Capture payment
   - Cancel invoice
   - Issue credit memo

## Integration Points

### Events
- invoice-created
- invoice-updated
- invoice-canceled

### APIs
- Invoice creation
- Invoice retrieval
- Payment capture
- Credit memo creation

## Validation Rules

1. Order Validation
   - Order must exist
   - Order must be in correct state
   - Items must be available for invoice

2. Financial Validation
   - Amount validation
   - Currency validation
   - Payment method validation

3. Business Rules
   - Partial invoice rules
   - Payment capture rules
   - Credit memo limitations

## Example

```json
{
  "id": 1000000001,
  "order_id": 2000000001,
  "created_at": "2024-03-20T10:00:00Z",
  "status": "paid",
  "items": [
    {
      "item_id": 1,
      "order_item_id": 1,
      "product_id": 123,
      "sku": "PROD-001",
      "qty": 2,
      "price": 29.99,
      "tax_amount": 6.00,
      "row_total": 59.98
    }
  ],
  "billing_address": {
    "firstname": "John",
    "lastname": "Doe",
    "street": ["123 Main St"],
    "city": "New York",
    "country_id": "US"
  },
  "payment": {
    "method": "credit_card",
    "transaction_id": "txn_123456789"
  },
  "totals": {
    "subtotal": 59.98,
    "tax": 6.00,
    "shipping": 10.00,
    "discount": 0.00,
    "grand_total": 75.98
  }
}
```

## Related Entities

- [Order](/domains/adapter/services/magento/entities/Order)
- [OrderItem](/domains/adapter/services/magento/entities/OrderItem)
- [Customer](/domains/adapter/services/magento/entities/Customer)
- [Shipment](/domains/adapter/services/magento/entities/Shipment)

## Notes

1. Performance Considerations
   - Optimize invoice generation
   - Handle large order invoices
   - Manage concurrent invoice creation

2. Security
   - Validate payment data
   - Secure financial information
   - Audit trail maintenance

3. Integration
   - ERP synchronization
   - Payment gateway integration
   - Tax calculation services
   - Accounting system integration 