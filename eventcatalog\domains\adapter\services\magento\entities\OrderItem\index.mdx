---
id: OrderItem
name: Order Item
version: 1.0.0
summary: |
  Represents an individual item within a Magento order, containing product details, quantities, and pricing information.
properties:
  - name: item_id
    type: int
    required: true
    description: Primary key for the order item
  - name: order_id
    type: int
    required: true
    description: Reference to the parent order
    link: /domains/adapter/services/magento/entities/Order
  - name: product_id
    type: int
    required: true
    description: Reference to the catalog product
    link: /domains/adapter/services/magento/entities/Product
  - name: sku
    type: string
    required: true
    description: Product SKU at the time of order
  - name: name
    type: string
    required: true
    description: Product name at the time of order
  - name: qty_ordered
    type: decimal
    required: true
    description: Quantity ordered
  - name: qty_shipped
    type: decimal
    required: false
    description: Quantity shipped
  - name: qty_invoiced
    type: decimal
    required: false
    description: Quantity invoiced
  - name: price
    type: decimal
    required: true
    description: Original price of the item
  - name: base_price
    type: decimal
    required: true
    description: Base price in store's base currency
  - name: row_total
    type: decimal
    required: true
    description: Total price for this line item
  - name: tax_amount
    type: decimal
    required: false
    description: Tax amount for this item
  - name: discount_amount
    type: decimal
    required: false
    description: Discount amount applied to this item
---

## Overview

The OrderItem entity represents a single product line within a Magento order. It maintains both the original [product](/domains/adapter/services/magento/entities/Product) information at the time of order and tracks the item's lifecycle through shipping and invoicing.

### Entity Properties
<EntityPropertiesTable />

## Database Structure

The OrderItem entity is primarily stored in the `sales_order_item` table, with relationships to:
- [`sales_order`](/domains/adapter/services/magento/entities/Order) (parent order)
- [`catalog_product_entity`](/domains/adapter/services/magento/entities/Product) (original product)
- `sales_shipment_item` (shipment records)
- `sales_invoice_item` (invoice records)

## Item States

Order items can be in various states throughout their lifecycle:

1. **Ordered**
   - Initial state when order is placed
   - Quantity is reserved in inventory

2. **Shipped**
   - Item has been shipped (partially or fully)
   - `qty_shipped` is updated

3. **Invoiced**
   - Item has been invoiced (partially or fully)
   - `qty_invoiced` is updated

4. **Refunded**
   - Item has been refunded (partially or fully)
   - Credit memo is created

## Examples

### Simple Product Order Item
```json
{
  "item_id": 1,
  "order_id": 1,
  "product_id": 123,
  "sku": "24-MB01",
  "name": "Joust Duffle Bag",
  "qty_ordered": 2.0000,
  "qty_shipped": 2.0000,
  "qty_invoiced": 2.0000,
  "price": 34.0000,
  "base_price": 34.0000,
  "row_total": 68.0000,
  "tax_amount": 5.6100,
  "discount_amount": 0.0000
}
```

### Configurable Product Order Item
```json
{
  "item_id": 2,
  "order_id": 1,
  "product_id": 456,
  "sku": "MH01-S-Gray",
  "name": "Chaz Kangeroo Hoodie",
  "qty_ordered": 1.0000,
  "qty_shipped": 0.0000,
  "qty_invoiced": 1.0000,
  "price": 84.0000,
  "base_price": 84.0000,
  "row_total": 84.0000,
  "tax_amount": 6.9300,
  "discount_amount": 8.4000
}
``` 