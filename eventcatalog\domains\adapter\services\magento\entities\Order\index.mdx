---
id: Order
name: Order
version: 1.0.0
aggregateRoot: true
summary: |
  Represents a customer's purchase in Magento, containing all information related to a transaction including items purchased, shipping details, payment information, and status.
properties:
  - name: entity_id
    type: int
    required: true
    description: Primary key for the order
  - name: increment_id
    type: string
    required: true
    description: Public order ID visible to customers
  - name: integration_order_id
    type: string
    required: false
    description: Reference to the corresponding order ID in SAP system, populated after successful order registration in SAP
  - name: customer_id
    type: int
    required: true
    description: Reference to the customer who placed the order
  - name: state
    type: string
    required: true
    description: Current state of the order in the order lifecycle
    enum: ['new', 'pending_payment', 'payment_review', 'processing', 'complete', 'closed', 'canceled', 'holded']
  - name: status
    type: string
    required: true
    description: Detailed status of the order, can be customized per store needs
    enum: ['pending', 'pending_payment', 'pending_paypal', 'processing', 'fraud', 'payment_review', 'holded', 'complete', 'closed', 'canceled', 'paypal_canceled_reversal', 'paypal_reversed', 'pending_ogone']
  - name: total_paid
    type: decimal
    required: false
    description: Total amount paid for the order
  - name: created_at
    type: datetime
    required: true
    description: Timestamp when the order was created
  - name: updated_at
    type: datetime
    required: true
    description: Timestamp of the last order update
  - name: items
    type: array
    items:
      type: OrderItem
      link: /domains/adapter/services/magento/entities/OrderItem
    required: true
    description: List of items included in the order
  - name: billing_address
    type: Address
    link: /domains/adapter/services/magento/entities/Address
    required: true
    description: Billing address for the order
  - name: shipping_address
    type: Address
    link: /domains/adapter/services/magento/entities/Address
    required: true
    description: Shipping address for the order
  - name: payment
    type: Payment
    required: true
    description: Payment information for the order
queries:
  - id: magento-ship-order
    version: 0.0.1
  - id: magento-order-status-update
    version: 0.0.1
---

## Overview

The Order entity is the central aggregate root for customer purchases in Magento. It coordinates all aspects of a transaction, including:
- [Order items](/domains/adapter/services/magento/entities/OrderItem) and their quantities
- [Customer](/domains/adapter/services/magento/entities/Customer) information
- [Shipping and billing addresses](/domains/adapter/services/magento/entities/Address)
- Payment details
- Order status and history
- SAP integration status

### Entity Properties
<EntityPropertiesTable />

## Order State and Status

### Order State
The `state` field represents the core order lifecycle in Magento. States are system-defined and cannot be customized:

- `new`: Order has been created but not yet processed
- `pending_payment`: Order is awaiting payment
- `payment_review`: Payment is under review
- `processing`: Order is being processed (items being shipped, etc.)
- `complete`: All items have been shipped and invoiced
- `closed`: Order has been archived (after refund, etc.)
- `canceled`: Order has been canceled
- `holded`: Order is on hold

### Order Status
The `status` field provides more detailed status information and can be customized per store needs:

- `pending`: Initial status for new orders
- `pending_payment`: Awaiting payment
- `pending_paypal`: Awaiting PayPal payment
- `processing`: Order is being processed
- `fraud`: Potential fraudulent order
- `payment_review`: Payment is under review
- `holded`: Order is on hold
- `complete`: Order has been fulfilled
- `closed`: Order has been archived
- `canceled`: Order has been canceled
- `paypal_canceled_reversal`: PayPal canceled reversal
- `paypal_reversed`: PayPal payment reversed
- `pending_ogone`: Awaiting Ogone payment

### State-Status Relationship
Each order state can have multiple associated statuses. The default relationships are:

1. State: `new`
   - Status: `pending`

2. State: `pending_payment`
   - Status: `pending_payment`
   - Status: `pending_paypal`
   - Status: `pending_ogone`

3. State: `payment_review`
   - Status: `payment_review`
   - Status: `fraud`

4. State: `processing`
   - Status: `processing`

5. State: `complete`
   - Status: `complete`

6. State: `closed`
   - Status: `closed`

7. State: `canceled`
   - Status: `canceled`
   - Status: `paypal_canceled_reversal`
   - Status: `paypal_reversed`

8. State: `holded`
   - Status: `holded`

## Database Structure

The Order entity is stored across multiple tables in Magento's database:

1. `sales_order` - Primary order information
2. `sales_order_item` - Individual items within the order
3. `sales_order_address` - Billing and shipping addresses
4. `sales_order_payment` - Payment information
5. `sales_order_status_history` - Order status changes and comments
6. `sales_order_status` - Status labels and codes
7. `sales_order_status_state` - State-status relationships

## Order States and Flow

Orders follow a defined lifecycle through various states:

1. **Creation (`new`)**
   - Order is placed
   - Payment is authorized
   - Inventory is reserved
   - Order is sent to SAP for registration

2. **Processing (`processing`)**
   - Items are being prepared
   - Shipments are created
   - Invoices are generated
   - SAP order ID is received and stored

3. **Completion (`complete`)**
   - All items shipped
   - Payment fully captured
   - Order fulfilled
   - SAP order status synchronized

4. **Post-completion (`closed`/`canceled`)**
   - Order archived
   - Refunds processed (if any)
   - Cancellations handled
   - SAP order status updated accordingly

## Integration Points

The Order entity is accessible through several REST API endpoints:

```http
POST /V1/orders                 # Create order
GET /V1/orders/{id}             # Retrieve order
POST /V1/orders/{id}            # Update order
GET /V1/orders                  # Search orders
```

### SAP Integration

The Order entity maintains synchronization with SAP through the following process:
1. When a new order is created in Magento, it triggers the `magento-order-created` event
2. The integration layer processes the event and registers the order in SAP
3. Upon successful registration, SAP returns its internal order ID
4. The `integration_order_id` field is updated in Magento to maintain the cross-reference
5. Subsequent order status updates are synchronized between both systems using this reference

## Examples

### New Order Creation
```json
{
  "entity_id": 1,
  "increment_id": "000000001",
  "integration_order_id": null,
  "customer_id": 123,
  "state": "new",
  "status": "pending",
  "items": [
    {
      "sku": "24-MB01",
      "qty": 2,
      "price": 34.00
    }
  ],
  "total_paid": 68.00,
  "created_at": "2023-01-01T10:00:00Z",
  "updated_at": "2023-01-01T10:00:00Z"
}
```

### Completed Order
```json
{
  "entity_id": 1,
  "increment_id": "000000001",
  "integration_order_id": "SAP123456",
  "customer_id": 123,
  "state": "complete",
  "status": "complete",
  "items": [
    {
      "sku": "24-MB01",
      "qty": 2,
      "price": 34.00
    }
  ],
  "total_paid": 68.00,
  "created_at": "2023-01-01T10:00:00Z",
  "updated_at": "2023-01-02T15:30:00Z"
}
```
