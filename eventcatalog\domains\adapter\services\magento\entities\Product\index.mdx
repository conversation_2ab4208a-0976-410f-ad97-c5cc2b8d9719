---
id: Product
name: Product
version: 1.0.0
aggregateRoot: true
summary: |
  Represents a product in the Magento catalog, including its attributes, pricing, and inventory information.
properties:
  - name: entity_id
    type: int
    required: true
    description: Primary key for the product
  - name: sku
    type: string
    required: true
    description: Stock Keeping Unit, unique identifier for the product
  - name: type_id
    type: string
    required: true
    description: Product type (simple, configurable, bundle, etc.)
    enum: ['simple', 'configurable', 'virtual', 'bundle', 'downloadable', 'grouped']
  - name: attribute_set_id
    type: int
    required: true
    description: Reference to the product's attribute set
  - name: name
    type: string
    required: true
    description: Product name
  - name: price
    type: decimal
    required: false
    description: Base price of the product
  - name: status
    type: int
    required: true
    description: Product status (enabled/disabled)
    enum: [1, 2]  # 1 = enabled, 2 = disabled
  - name: visibility
    type: int
    required: true
    description: Product visibility in catalog and search
    enum: [1, 2, 3, 4]  # 1 = Not Visible, 2 = Catalog, 3 = Search, 4 = Both
  - name: created_at
    type: datetime
    required: true
    description: Timestamp when the product was created
  - name: updated_at
    type: datetime
    required: true
    description: Timestamp of the last product update
  - name: weight
    type: decimal
    required: false
    description: Product weight
  - name: description
    type: string
    required: false
    description: Detailed product description
  - name: short_description
    type: string
    required: false
    description: Short product description
  - name: stock_data
    type: object
    required: false
    description: Comprehensive inventory and stock management information for the product
    properties:
      - name: qty
        type: decimal
        required: true
        description: Current available quantity of the product in stock
      - name: min_qty
        type: decimal
        required: false
        description: Minimum quantity threshold for out-of-stock status
      - name: min_sale_qty
        type: decimal
        required: false
        description: Minimum quantity that can be purchased in a single order
      - name: max_sale_qty
        type: decimal
        required: false
        description: Maximum quantity that can be purchased in a single order
      - name: is_in_stock
        type: boolean
        required: true
        description: Whether the product is currently available for purchase
      - name: manage_stock
        type: boolean
        required: false
        description: Whether to enable quantity tracking and management for this product
      - name: backorders
        type: int
        required: false
        description: Backorder status (0 = No Backorders, 1 = Allow Qty Below 0, 2 = Allow Qty Below 0 and Notify Customer)
        enum: [0, 1, 2]
      - name: use_config_min_qty
        type: boolean
        required: false
        description: Whether to use system configuration for minimum quantity
      - name: use_config_min_sale_qty
        type: boolean
        required: false
        description: Whether to use system configuration for minimum sale quantity
      - name: use_config_max_sale_qty
        type: boolean
        required: false
        description: Whether to use system configuration for maximum sale quantity
      - name: use_config_backorders
        type: boolean
        required: false
        description: Whether to use system configuration for backorders
      - name: notify_stock_qty
        type: decimal
        required: false
        description: Quantity threshold for stock notification
      - name: use_config_notify_stock_qty
        type: boolean
        required: false
        description: Whether to use system configuration for stock notification threshold
---

## Overview

The Product entity is a central aggregate root in Magento's catalog system. It represents saleable items and their characteristics, supporting various product types and complex attribute configurations.

### Entity Properties
<EntityPropertiesTable />

## Database Structure

The Product entity spans multiple tables in Magento's EAV (Entity-Attribute-Value) structure:

1. `catalog_product_entity` - Base product information
2. `catalog_product_entity_*` - Attribute value tables (text, varchar, decimal, etc.)
3. `cataloginventory_stock_item` - Stock information
4. `catalog_product_website` - Website assignments
5. `catalog_category_product` - Category assignments

## Product Types

### Simple Product
- Basic, single product
- Has its own SKU and inventory
- Can be sold individually or as part of other products

### Configurable Product
- Parent product with multiple variations
- Each variation is a simple product
- Common for items with size/color options

### Bundle Product
- Composed of multiple products
- Customer can customize the bundle
- Dynamic or fixed pricing

### Virtual Product
- Non-physical item
- No shipping required
- Used for services or digital items

### Downloadable Product
- Digital content
- Can have multiple download links
- Sample files possible

### Grouped Product
- Set of related products
- Sold individually
- Shown as a set on frontend

## Integration Points

The Product entity is accessible through several REST API endpoints:

```http
POST /V1/products                    # Create product
GET /V1/products/{sku}              # Retrieve product
PUT /V1/products/{sku}              # Update product
GET /V1/products                    # Search products
POST /V1/products/attributes        # Create attribute
GET /V1/products/types             # Get product types
```

## Inventory Management

The Product entity includes comprehensive inventory management through the `stock_data` object. This system allows for:

### Basic Stock Management
- Track available quantity (`qty`)
- Manage stock status (`is_in_stock`)
- Enable/disable inventory tracking per product (`manage_stock`)

### Order Quantity Controls
- Set minimum purchase quantity (`min_sale_qty`)
- Set maximum purchase quantity (`max_sale_qty`)
- Define out-of-stock threshold (`min_qty`)

### Backorder Management
- Configure backorder behavior (`backorders`)
  - No Backorders (0)
  - Allow Qty Below 0 (1)
  - Allow Qty Below 0 and Notify Customer (2)

### Stock Notifications
- Set notification threshold (`notify_stock_qty`)
- Configure when to alert for low stock

### Configuration Flexibility
Each stock management feature can either:
- Use product-specific settings
- Inherit from global configuration using the `use_config_*` flags

### Example Stock Configuration
```json
{
  "product": {
    "sku": "24-MB01",
    "name": "Example Product",
    "stock_data": {
      "qty": 100.0,
      "is_in_stock": true,
      "manage_stock": true,
      "min_sale_qty": 1.0,
      "max_sale_qty": 10.0,
      "backorders": 0,
      "notify_stock_qty": 10.0,
      "use_config_manage_stock": false,
      "use_config_min_sale_qty": true,
      "use_config_max_sale_qty": true,
      "use_config_notify_stock_qty": true,
      "use_config_backorders": true
    }
  }
}
```

## Examples

### Simple Product
```json
{
  "product": {
    "sku": "24-MB01",
    "name": "Joust Duffle Bag",
    "attribute_set_id": 4,
    "price": 34.00,
    "status": 1,
    "visibility": 4,
    "type_id": "simple",
    "weight": 1.5,
    "extension_attributes": {
      "stock_item": {
        "qty": 100,
        "is_in_stock": true
      }
    }
  }
}
```

### Configurable Product
```json
{
  "product": {
    "sku": "MH01",
    "name": "Chaz Kangeroo Hoodie",
    "attribute_set_id": 4,
    "status": 1,
    "visibility": 4,
    "type_id": "configurable",
    "weight": 0.5,
    "extension_attributes": {
      "configurable_product_options": [
        {
          "attribute_id": "93",
          "label": "Size",
          "values": [
            {
              "value_index": 9
            }
          ]
        }
      ]
    }
  }
}
``` 