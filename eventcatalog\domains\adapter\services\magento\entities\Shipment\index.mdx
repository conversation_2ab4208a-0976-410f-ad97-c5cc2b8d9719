---
id: Shipment
version: 1.0.0
name: Shipment
summary: |
  Magento shipment entity representing the physical delivery of ordered items
owners:
  - euvic
---

# Shipment Entity

## Overview

The Shipment entity represents the physical delivery process of an order in Magento. It tracks the packaging, shipping, and delivery status of ordered items, managing both full and partial shipments.

## Structure

### Core Components

1. Basic Information
   - Shipment ID
   - Order reference
   - Creation date
   - Status
   - Store view ID
   - Tracking numbers

2. Shipping Details
   - Shipping method
   - Carrier information
   - Tracking information
   - Estimated delivery date
   - Shipping labels
   - Package details

3. Items
   - Product details
   - Quantity shipped
   - Package assignment
   - Serial numbers
   - Lot numbers

4. Addresses
   - Shipping address
   - Return address
   - Pickup location (if applicable)

## Lifecycle

1. Creation
   - Order item availability check
   - Shipment creation
   - Label generation
   - Tracking number assignment

2. States
   - Pending
   - Ready for pickup
   - In transit
   - Delivered
   - Returned

3. Operations
   - Create shipment
   - Generate labels
   - Add tracking
   - Send notifications
   - Process returns

## Integration Points

### Events
- shipment-created
- shipment-updated
- shipment-delivered
- shipment-returned

### APIs
- Shipment creation
- Label generation
- Tracking updates
- Return processing

## Validation Rules

1. Order Validation
   - Order must exist
   - Items must be available
   - Address must be valid
   - Payment must be confirmed

2. Shipping Validation
   - Carrier availability
   - Method restrictions
   - Weight limits
   - Destination restrictions

3. Business Rules
   - Partial shipment rules
   - Multi-package rules
   - International shipping rules
   - Return policy rules

## Example

```json
{
  "id": 3000000001,
  "order_id": 2000000001,
  "created_at": "2024-03-20T11:00:00Z",
  "status": "in_transit",
  "items": [
    {
      "item_id": 1,
      "order_item_id": 1,
      "product_id": 123,
      "sku": "PROD-001",
      "qty": 2,
      "weight": 1.5,
      "package_id": "pkg_001"
    }
  ],
  "tracks": [
    {
      "carrier_code": "fedex",
      "title": "Federal Express",
      "tracking_number": "794698574536",
      "status": "in_transit"
    }
  ],
  "shipping_address": {
    "firstname": "John",
    "lastname": "Doe",
    "street": ["123 Main St"],
    "city": "New York",
    "country_id": "US"
  },
  "packages": [
    {
      "id": "pkg_001",
      "weight": 3.0,
      "length": 12,
      "width": 8,
      "height": 6
    }
  ],
  "shipping_method": {
    "carrier": "fedex",
    "method": "GROUND",
    "estimated_delivery": "2024-03-23T17:00:00Z"
  }
}
```

## Related Entities

- [Order](/domains/adapter/services/magento/entities/Order)
- [OrderItem](/domains/adapter/services/magento/entities/OrderItem)
- [Invoice](/domains/adapter/services/magento/entities/Invoice)
- [Address](/domains/adapter/services/magento/entities/Address)

## Notes

1. Performance Considerations
   - Optimize label generation
   - Handle bulk shipments
   - Manage tracking updates
   - Cache carrier responses

2. Security
   - Validate addresses
   - Secure tracking data
   - Protect customer information
   - Verify carrier credentials

3. Integration
   - Carrier API integration
   - Warehouse management
   - Order fulfillment systems
   - Customer notification systems

4. Special Handling
   - Hazardous materials
   - Temperature-controlled items
   - International customs
   - Insurance requirements 