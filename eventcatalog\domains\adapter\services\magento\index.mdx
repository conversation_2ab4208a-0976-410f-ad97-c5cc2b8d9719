---
id: magento
version: 1.0.0
name: Magento
summary: |
  Integration service for Magento e-commerce platform
owners:
    - Alliance Integration Team
sends:
  - id: order-created-query
    version: 1.0.0
  - id: order-fetch-query
    version: 0.0.1
  - id: cart-created-query
    version: 1.1.1
  - id: cart-updated-query
    version: 1.0.0
  - id: customer-created-query
    version: 0.0.1
  - id: customer-updated-query
    version: 0.0.1
  - id: customer-deleted-query
    version: 0.0.1
  - id: customer-address-created-query
    version: 0.0.1
  - id: customer-address-updated-query
    version: 0.0.1
  - id: customer-address-deleted-query
    version: 0.0.1
  - id: guest-email-subscribed-query
    version: 0.0.1
  - id: guest-email-unsubscribed-query
    version: 0.0.1
  - id: customer-email-subscribed-query
    version: 0.0.1
  - id: customer-email-unsubscribed-query
    version: 0.0.1
receives:
  - id: magento-order-status-update
    version: 0.0.1
  - id: magento-ship-order
    version: 0.0.1
  - id: magento-shipment-comment
    version: 0.0.1
  - id: create-product
    version: 0.0.1
  - id: product-update
    version: 0.0.1
  - id: product-stock-update
    version: 0.0.1
  - id: product-price-update
    version: 0.0.1
  - id: product-special-price-update
    version: 0.0.1
  - id: product-tier-price-update
    version: 0.0.1
entities:
  - id: Order
    version: 1.0.0
  - id: OrderItem
    version: 1.0.0
  - id: Product
    version: 1.0.0
  - id: Customer
    version: 1.0.0
  - id: Address
    version: 1.0.0
  - id: Cart
    version: 1.0.0
  - id: Invoice
    version: 1.0.0
  - id: Shipment
    version: 1.0.0
repository:
  language: PHP
  url: https://github.com/ALSSoftware/evolution-ecommerce
---

## Overview

The Magento Integration Service provides a comprehensive interface for interacting with the Magento e-commerce platform. It handles various operations including customer management, cart operations, order processing, and product management.

## Features

### Customer Management
- Create new customers
- Update customer information
- Delete customer accounts
- Manage customer addresses
- Handle customer authentication

### Cart Operations
- Create new shopping carts
- Update cart contents
- Convert carts to orders
- Handle B2B negotiable quotes

### Order Processing
- Create new orders
- Process order status updates
- Handle shipping updates
- Manage order tracking
- Process refunds

### Product Management
- Create and update products
- Manage product inventory
- Update product prices
- Handle product attributes
- Manage product categories

## Queries

### Customer Queries
- [`customer-created`](/domains/adapter/services/magento-integration-adapter/queries/customer-created-query) - Notification of customer creation (asynchronous)
- [`customer-updated`](/domains/adapter/services/magento-integration-adapter/queries/customer-updated-query) - Notification of customer information update (asynchronous)
- [`customer-deleted`](/domains/adapter/services/magento-integration-adapter/queries/customer-deleted-query) - Notification of customer deletion (asynchronous)

### Customer Address Queries
- [`customer-address-created`](/domains/adapter/services/magento-integration-adapter/queries/customer-address-created-query) - Notification of customer address creation (asynchronous)
- [`customer-address-updated`](/domains/adapter/services/magento-integration-adapter/queries/customer-address-updated-query) - Notification of customer address update (asynchronous)
- [`customer-address-deleted`](/domains/adapter/services/magento-integration-adapter/queries/customer-address-deleted-query) - Notification of customer address deletion (asynchronous)

### Email Subscription Queries
- [`guest-email-subscribed`](/domains/adapter/services/magento/queries/guest-email-subscribed-query) - Subscribe guest email to newsletter
- [`guest-email-unsubscribed`](/domains/adapter/services/magento/queries/guest-email-unsubscribed-query) - Unsubscribe guest email from newsletter
- [`customer-email-subscribed`](/domains/adapter/services/magento-integration-adapter/queries/customer-email-subscribed-query) - Notification of customer newsletter subscription (asynchronous)
- [`customer-email-unsubscribed`](/domains/adapter/services/magento-integration-adapter/queries/customer-email-unsubscribed-query) - Notification of customer newsletter unsubscription (asynchronous)

### Cart Queries
- [`cart-created`](/domains/adapter/services/magento/queries/cart-created-query) - Create a new shopping cart
- [`cart-updated`](/domains/adapter/services/magento/queries/cart-updated-query) - Update an existing cart

### Order Queries
- [`order-created`](/domains/adapter/services/magento/queries/order-created-query) - Create a new order
- `order-status-update` - Update order status
- `order-shipping-update` - Update shipping information

### Product Queries
- `create-product` - Create a new product
- `product-update` - Update product information
- `product-price-update` - Update product prices
- `product-stock-update` - Update product stock
- `product-special-price-update` - Update special prices
- `product-tier-price-update` - Update tier prices

## Integration Guidelines

### Authentication
- Uses OAuth 2.0 for API authentication
- Requires admin or integration tokens
- Supports different access levels based on roles

### Error Handling
- Implements standard HTTP status codes
- Provides detailed error messages
- Handles validation errors gracefully
- Supports retry mechanisms for transient failures

### Data Synchronization
- Supports real-time updates
- Handles bulk operations efficiently
- Maintains data consistency
- Provides conflict resolution mechanisms

### Performance Considerations
- Implements caching strategies
- Supports pagination for large datasets
- Optimizes API calls
- Handles rate limiting

## Best Practices

1. Always validate data before sending requests
2. Handle errors gracefully and implement retries
3. Use bulk operations when possible
4. Implement proper logging and monitoring
5. Follow security best practices
6. Maintain proper documentation
7. Test thoroughly in staging environment

## Security

- Implements secure communication over HTTPS
- Uses token-based authentication
- Follows PCI compliance guidelines
- Implements proper access control
- Handles sensitive data securely

## Monitoring

- Tracks API usage and performance
- Monitors error rates and types
- Implements alerting for critical issues
- Provides detailed logging
- Supports debugging tools

## Support

For support and questions, contact:
- Alliance Integration Team
- Technical Support Team
- System Administrators

# Magento Service

This service is the core integration point with Magento 2 Open Source e-commerce platform. It provides standardized access to Magento's functionality through REST APIs.

## Core Entities

The service manages several key entities that form the backbone of the e-commerce system:

### [Order](/domains/adapter/services/magento/entities/Order)
- Central entity for customer purchases
- Manages the complete order lifecycle
- Coordinates items, payments, and shipments

### [Product](/domains/adapter/services/magento/entities/Product)
- Represents saleable items in the catalog
- Supports multiple product types (simple, configurable, virtual, etc.)
- Manages inventory and pricing
- Handles product attributes and stock information

### [Customer](/domains/adapter/services/magento/entities/Customer)
- Handles customer accounts and authentication
- Manages customer groups and permissions
- Stores customer preferences and history

### [Address](/domains/adapter/services/magento/entities/Address)
- Used for both customer and order addresses
- Supports multiple address formats
- Handles shipping and billing information
- Manages customer address book operations
- Validates addresses against regional standards (ISO 3166-2)

### [Cart](/domains/adapter/services/magento/entities/Cart)
- Manages the shopping cart
- Handles cart creation, updates, and management
- Manages cart items and totals

### [Invoice](/domains/adapter/services/magento/entities/Invoice)
- Records financial transactions for orders
- Manages payment documentation
- Handles partial and full invoicing
- Supports credit memo operations

### [Shipment](/domains/adapter/services/magento/entities/Shipment)
- Manages order fulfillment and delivery
- Handles shipping labels and tracking
- Supports partial and complete shipments
- Coordinates with shipping carriers

## API Groups

The Magento API is organized into the following functional groups:

### Cart & Checkout APIs
- Shopping cart management
  - Cart creation and updates
  - Cart event notifications (cart-created, cart-update)
  - Cart item management
  - Cart totals and calculations
- Checkout process
- Shipping methods

### Product & Catalog APIs
- Product Management
  - Create new products (POST /V1/products)
  - Update existing products (PUT /V1/products/:sku)
  - Update product stock (POST /V1/inventory/source-items)
  - Manage product attributes
  - Configure product types

- Price Management
  - Update base prices (POST /V1/products/base-prices)
  - Update special prices (POST /V1/products/special-prices)
  - Update tier prices (POST /V1/products/tier-prices)
  - Support for:
    - Multi-store pricing
    - Time-based special prices
    - Quantity-based discounts
    - Customer group pricing
    - Website-specific pricing

- Inventory Management
  - Multi-source inventory management
  - Stock status updates
  - Quantity tracking

- Category Management
  - Category structure
  - Product assignments
  - Category attributes

### Customer & Account APIs
- Customer management
- Address management
- Customer groups
- Customer attributes
- Newsletter Management
  - Guest email subscriptions
  - Subscription status management
  - Email validation and verification
  - GDPR compliance handling

### Order & Payment APIs
- Order management
- Order status updates and comments
- Payment processing
- Invoices and refunds
- Order status management

### System & Integration APIs
- System configuration
- Integration settings
- Store management
- Cache management

## Authentication

Before using any API endpoint, authentication is required. To get access to the API, please contact the [Euvic team](/docs/teams/euvic).

## Service Configuration

### Environment Variables

```yaml
MAGENTO_BASE_URL: "https://magento.example.com"
MAGENTO_ADMIN_USERNAME: "admin"
MAGENTO_ADMIN_PASSWORD: "******"
MAGENTO_ACCESS_TOKEN_LIFETIME: "4h"
MAGENTO_API_VERSION: "V1"
MAGENTO_TIMEOUT: "30s"
MAGENTO_RETRY_ATTEMPTS: "3"
```

## Error Handling

The service implements robust error handling for:

1. Network Errors
   - Connection timeouts
   - DNS resolution failures
   - SSL/TLS errors

2. API Errors
   - Authentication failures (401)
   - Authorization failures (403)
   - Rate limiting (429)
   - Invalid requests (400)
   - Server errors (500)

3. Data Validation Errors
   - Schema validation
   - Business rule validation
   - Data integrity checks

## Monitoring

### Health Checks

The service includes the following health checks:

- API availability check
- Authentication status
- Connection pool status

### Metrics

1. Performance Metrics
   - API response times
   - Error rates
   - Request volumes

2. Business Metrics
   - Orders processed
   - Products synchronized
   - Customer operations
   - Inventory updates
   - Price updates

## Dependencies

- Database: MySQL/MariaDB
- Cache: Redis
- Message Queue: RabbitMQ
- Storage: S3-compatible

<Tiles>
    <Tile icon="DocumentIcon" href={`/docs/services/${frontmatter.id}/${frontmatter.version}/changelog`} title="View the changelog" description="Want to know the history of this service? View the change logs" />
    <Tile icon="UserGroupIcon" href="/docs/teams/euvic" title="Contact the team" description="Any questions? Feel free to contact the owners" />
    <Tile icon="BoltIcon" href={`/visualiser/services/${frontmatter.id}/${frontmatter.version}`} title={`Sends ${frontmatter.sends.length} messages`} description="This service sends messages to downstream consumers" />
</Tiles>

## Architecture diagram

<NodeGraph />

<Steps title="How to connect to the Magento API">
  <Step title="Obtain API credentials">
    Request API credentials from the Euvic team to get access to the Magento admin panel.
  </Step>
  <Step title="Generate access token">
    Use the authentication endpoints to generate an access token:
    ```http
    POST /V1/integration/admin/token
    {
      "username": "admin",
      "password": "admin123"
    }
    ```
  </Step>
  <Step title="Make API calls">
    Include the token in the Authorization header:
    ```http
    GET /V1/products
    Authorization: Bearer <your_access_token>
    ```
  </Step>
  <Step title="Handle responses">
    Process the API responses and implement proper error handling:
    ```json
    {
      "items": [],
      "search_criteria": {
        "filter_groups": [],
        "page_size": 20,
        "current_page": 1
      },
      "total_count": 0
    }
    ```
  </Step>
</Steps>
