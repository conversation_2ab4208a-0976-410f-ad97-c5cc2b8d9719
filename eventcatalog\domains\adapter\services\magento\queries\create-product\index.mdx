---
id: create-product
name: Create Product
version: 0.0.1
summary: |
  Query to create a new product in Magento using the REST API endpoint POST /rest/V1/products
producers:
  - magento-integration-adapter
consumers:
  - magento
owners:
  - euvic
  - enterprise
channels:
  - id: magento.{env}.rest.queries
    parameters:
      env: local
specifications:
  - type: openapi
    path: 'openapi.yml'
---

## Overview

The `create-product` query is used to create a new product in Magento. It supports all product types (simple, configurable, virtual, bundle, downloadable, grouped) and allows setting various product attributes including stock information.

## Architecture diagram

<NodeGraph />

### Required Fields
- `sku` - Stock Keeping Unit, unique identifier for the product
- `name` - Product name
- `attribute_set_id` - Reference to the product's attribute set
- `price` - Product price
- `status` - Product status (enabled/disabled)
- `type_id` - Product type

### Optional Fields
- `visibility` - Product visibility in catalog and search
- `weight` - Product weight
- `extension_attributes` - Additional product attributes
- `custom_attributes` - Custom product attributes

## Product Types

The query supports creating different types of products:

### Simple Product
Basic, single product with its own SKU and inventory.

### Configurable Product
Parent product that can have multiple variations (e.g., different sizes or colors).

### Virtual Product
Non-physical product that doesn't require shipping (e.g., services).

### Bundle Product
Product composed of multiple other products that can be customized.

### Downloadable Product
Digital product that can be downloaded.

### Grouped Product
Set of related products that can be purchased individually.

## Stock Management

The query allows configuring product stock through the `extension_attributes.stock_item` object:

- `qty` - Available quantity
- `is_in_stock` - Stock status
- `manage_stock` - Whether to track inventory
- `min_sale_qty` - Minimum quantity that can be purchased
- `max_sale_qty` - Maximum quantity that can be purchased
- `notify_stock_qty` - Quantity threshold for low stock notification


## Notes

- The `attribute_set_id` must reference a valid attribute set in Magento
- Stock information is optional but recommended for proper inventory management
- Custom attributes must be defined in the attribute set before they can be used
- The response includes additional fields like `created_at` and `updated_at` 