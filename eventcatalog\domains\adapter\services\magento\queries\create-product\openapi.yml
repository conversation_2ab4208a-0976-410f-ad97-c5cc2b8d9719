openapi: "3.1.0"
info:
  title: Create Product
  version: 0.0.1
  description: |
    Query to create a new product in Magento using the REST API endpoint POST /rest/V1/products.
    Supports all product types and allows setting various product attributes including stock information.
servers:
  - url: http://localhost:9999
paths:
  /rest/V1/products:
    post:
      summary: Create a new product
      operationId: createProduct
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - product
              properties:
                product:
                  type: object
                  required:
                    - sku
                    - name
                    - attribute_set_id
                    - type_id
                    - status
                    - visibility
                  properties:
                    sku:
                      type: string
                      description: Stock Keeping Unit, unique identifier for the product
                    name:
                      type: string
                      description: Product name
                    attribute_set_id:
                      type: integer
                      description: Reference to the product's attribute set
                    type_id:
                      type: string
                      description: Product type (simple, configurable, bundle, etc.)
                      enum: [simple, configurable, virtual, bundle, downloadable, grouped]
                    status:
                      type: integer
                      description: Product status (1 - enabled, 2 - disabled)
                      enum: [1, 2]
                    visibility:
                      type: integer
                      description: Product visibility (1 - Not Visible, 2 - Catalog, 3 - Search, 4 - Catalog & Search)
                      enum: [1, 2, 3, 4]
                    price:
                      type: number
                      format: float
                      description: Base price of the product
                    weight:
                      type: number
                      format: float
                      description: Product weight
                    extension_attributes:
                      type: object
                      description: Additional product attributes
                      properties:
                        stock_item:
                          type: object
                          properties:
                            qty:
                              type: number
                              description: Product quantity
                            is_in_stock:
                              type: boolean
                              description: Stock status
                            manage_stock:
                              type: boolean
                              description: Whether to manage stock
                    custom_attributes:
                      type: array
                      description: Custom product attributes
                      items:
                        type: object
                        required: [attribute_code, value]
                        properties:
                          attribute_code:
                            type: string
                            description: Attribute code
                          value:
                            type: string
                            description: Attribute value
            examples:
              simpleProduct:
                summary: Simple Product
                value:
                  product:
                    sku: "24-MB01"
                    name: "Joust Duffle Bag"
                    attribute_set_id: 4
                    price: 34.00
                    status: 1
                    visibility: 4
                    type_id: "simple"
                    weight: 1.5
                    extension_attributes:
                      stock_item:
                        qty: 100
                        is_in_stock: true
              virtualProduct:
                summary: Virtual Product
                value:
                  product:
                    sku: "virtual-service"
                    name: "Web Development Service"
                    attribute_set_id: 4
                    price: 150.00
                    status: 1
                    visibility: 4
                    type_id: "virtual"
                    extension_attributes:
                      stock_item:
                        qty: 1000
                        is_in_stock: true
                        manage_stock: false
              customAttributes:
                summary: Product with Custom Attributes
                value:
                  product:
                    sku: "custom-tshirt"
                    name: "Custom T-Shirt"
                    attribute_set_id: 4
                    price: 29.99
                    status: 1
                    visibility: 4
                    type_id: "simple"
                    weight: 0.5
                    custom_attributes:
                      - attribute_code: "color"
                        value: "blue"
                      - attribute_code: "size"
                        value: "M"
                      - attribute_code: "material"
                        value: "cotton"
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: integer
                  sku:
                    type: string
                  name:
                    type: string
                  attribute_set_id:
                    type: integer
                  price:
                    type: number
                  status:
                    type: integer
                  visibility:
                    type: integer
                  type_id:
                    type: string
                  created_at:
                    type: string
                  updated_at:
                    type: string
                  extension_attributes:
                    type: object
                    properties:
                      stock_item:
                        type: object
                        properties:
                          qty:
                            type: number
                          is_in_stock:
                            type: boolean
              examples:
                success:
                  summary: Successful creation
                  value:
                    id: 1
                    sku: "24-MB01"
                    name: "Joust Duffle Bag"
                    attribute_set_id: 4
                    price: 34.00
                    status: 1
                    visibility: 4
                    type_id: "simple"
                    created_at: "2024-01-20 10:00:00"
                    updated_at: "2024-01-20 10:00:00"
                    extension_attributes:
                      stock_item:
                        qty: 100
                        is_in_stock: true
        '400':
          description: Bad Request - Invalid product data, missing required fields, or invalid attribute values
        '401':
          description: Unauthorized - Invalid or missing authentication token
        '409':
          description: Conflict - SKU already exists 