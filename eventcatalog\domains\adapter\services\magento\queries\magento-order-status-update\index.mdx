---
id: magento-order-status-update
name: Magento Order Status Update
version: 0.0.1
summary: |
  Updates the status of a Magento order and optionally adds a status history comment
producers:
  - magento-integration-adapter
consumers:
  - magento
owners:
  - enterprise
channels:
  - id: magento.{env}.rest.queries
    parameters:
      env: local
specifications:
  - type: openapi
    path: 'openapi.yml'
---

## Overview

This query is used to update the status of a Magento order. It allows changing the order status and state, and optionally adding a comment that can be visible to the customer.

## Architecture diagram

<NodeGraph />

### Fields Description

- `state` (required): New state for the order
  - Must be one of:
    - `new`: Initial state when an order is created
    - `pending_payment`: Order is awaiting payment
    - `payment_review`: Payment is being reviewed
    - `processing`: Order is being processed
    - `complete`: Order has been fully shipped/delivered
    - `closed`: Order has been completed and closed
    - `canceled`: Order has been canceled
    - `holded`: Order is on hold
- `status` (required): New status for the order
  - Must be one of:
    - `pending`: Order is pending
    - `pending_payment`: Payment is pending
    - `pending_paypal`: PayPal payment is pending
    - `processing`: Order is being processed
    - `fraud`: Order is marked as fraudulent
    - `payment_review`: Payment is under review
    - `holded`: Order is on hold
    - `complete`: Order is complete
    - `closed`: Order is closed
    - `canceled`: Order is canceled
    - `paypal_canceled_reversal`: PayPal cancellation was reversed
    - `paypal_reversed`: PayPal payment was reversed
    - `pending_ogone`: Ogone payment is pending
- `notify`: Whether to notify the customer about the status change
- `comment`: Optional comment information
  - `comment`: The comment text
  - `is_visible_on_front`: Whether the comment is visible to the customer (1 for visible, 0 for not visible)

### State and Status Relationship

The order state represents the high-level position of an order in the sales workflow, while the status provides more detailed information about the order's current situation. Here's how they relate:

1. **State: new**
   - Typical status: `pending`
   - Indicates a newly created order

2. **State: pending_payment**
   - Typical statuses: `pending_payment`, `pending_paypal`, `pending_ogone`
   - Indicates the order is awaiting payment completion

3. **State: payment_review**
   - Typical statuses: `payment_review`, `fraud`
   - Used when payment needs verification or fraud check

4. **State: processing**
   - Typical status: `processing`
   - Order is being fulfilled/shipped

5. **State: complete**
   - Typical status: `complete`
   - All items have been shipped/delivered

6. **State: closed**
   - Typical status: `closed`
   - Order has been completed and closed (e.g., after refund)

7. **State: canceled**
   - Typical statuses: `canceled`, `paypal_canceled_reversal`, `paypal_reversed`
   - Order has been canceled

8. **State: holded**
   - Typical status: `holded`
   - Order is temporarily on hold

### Notes
- The status must be a valid Magento order status
- The state and status should be compatible according to the relationships described above
- The customer will receive an email notification if `notify` is set to true
- Comments are stored in the order history and can be viewed in the admin panel
- Customer-visible comments appear in the customer's order view if `is_visible_on_front` is set to 1 