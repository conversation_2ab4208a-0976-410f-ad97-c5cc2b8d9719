openapi: "3.1.0"
info:
  title: Magento Order Status Update
  version: 0.0.1
  description: |
    Updates the status of a Magento order and optionally adds a status history comment.
servers:
  - url: http://localhost:9999
paths:
  /rest/default/V1/orders/{orderId}/status:
    post:
      summary: Update the status of a Magento order
      operationId: updateOrderStatus
      parameters:
        - name: orderId
          in: path
          required: true
          schema:
            type: string
          description: The order ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - status
              properties:
                state:
                  type: string
                  description: The new state to set for the order
                  enum: [new, pending_payment, payment_review, processing, complete, closed, canceled, holded]
                status:
                  type: string
                  description: The new status to set for the order
                  enum:
                    - pending
                    - pending_payment
                    - pending_paypal
                    - processing
                    - fraud
                    - payment_review
                    - holded
                    - complete
                    - closed
                    - canceled
                    - paypal_canceled_reversal
                    - paypal_reversed
                    - pending_ogone
                notify:
                  type: boolean
                  description: Whether to notify the customer about the status change
                  default: false
                comment:
                  type: object
                  properties:
                    comment:
                      type: string
                      description: The comment text
                    is_visible_on_front:
                      type: integer
                      enum: [0, 1]
                      description: Whether the comment is visible on the frontend (1 for visible, 0 for not visible)
                  required: [comment, is_visible_on_front]
            examples:
              processing:
                summary: Processing Example
                value:
                  state: processing
                  status: processing
                  notify: true
                  comment:
                    comment: "Order is being processed and will be shipped soon."
                    is_visible_on_front: 1
              canceled:
                summary: Canceled Example
                value:
                  state: canceled
                  status: paypal_canceled_reversal
                  notify: true
                  comment:
                    comment: "PayPal payment has been canceled and reversed."
                    is_visible_on_front: 1
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
              examples:
                success:
                  summary: Successful update
                  value:
                    success: true
        '400':
          description: Bad Request - Invalid state, status, or comment
        '401':
          description: Unauthorized - Invalid or missing authentication token
        '404':
          description: Not Found - Order does not exist 