---
id: magento-ship-order
name: Magento Ship Order
version: 0.0.1
summary: |
  Creates a shipment for a Magento order with specified items, tracking information, and optional customer notification
producers:
  - magento-integration-adapter
consumers:
  - magento
owners:
  - enterprise
channels:
  - id: magento.{env}.rest.queries
    parameters:
      env: local
specifications:
  - type: openapi
    path: 'openapi.yml'
---

## Overview

This query is used to create a shipment for a Magento order. It allows specifying which items to ship, adding tracking information, and optionally notifying the customer with a comment.

## Architecture diagram

<NodeGraph />

### Fields Description

- `items` (required): Array of items to be shipped
  - `order_item_id`: The entity_id/item_id of the order item in the Magento database (not the SKU). This is the unique identifier from the sales_order_item table.
  - `qty`: Quantity to ship
- `notify`: Whether to notify the customer about the shipment
- `comment`: Optional comment information
  - `comment`: The comment text
  - `is_visible_on_front`: Whether the comment is visible to the customer (1 for visible, 0 for not visible)
- `tracks`: Array of tracking information
  - `track_number`: The tracking number
  - `title`: The carrier title
  - `carrier_code`: The carrier code (e.g., "ups", "fedex", "usps")

### Notes
- The `order_item_id` must match the `item_id` from the order item entity, not the product SKU
- You can find the `order_item_id` in the order details or through the order items API endpoint
- Multiple items can be shipped in a single request
- The quantity to ship cannot exceed the remaining unshipped quantity for the order item

### Integration with External Systems

External systems (like SAP) that need to create shipments in Magento must store the `order_item_id` when they receive the original order. This is crucial because:

1. The `order_item_id` is required for creating shipments and cannot be retrieved using the SKU alone
2. There is no direct mapping between SKU and `order_item_id` as the same SKU can appear multiple times in different order items
3. The `order_item_id` is included in the `magento-order-created` event payload, which external systems should store for future shipping operations

#### Example Flow for External Systems

1. When receiving a new order (via `magento-order-created` event):
   ```json
   {
     "order_id": "000000123",
     "items": [
       {
         "item_id": 456,         // Store this order_item_id
         "sku": "24-MB01",
         "qty_ordered": 2
       }
     ]
   }
   ```

2. When creating a shipment later:
   ```json
   {
     "items": [
       {
         "order_item_id": 456,   // Use the stored order_item_id
         "qty": 2
       }
     ]
   }
   ```

#### Best Practices for External Systems
- Store the `order_item_id` along with other order details when processing new orders
- Include the `order_item_id` in your system's order item mapping table
- Do not rely on SKU + order_id combination to identify items for shipping
- Consider creating a mapping table with structure:
  ```sql
  your_system_order_item_id | magento_order_item_id | sku | qty
  ``` 