openapi: "3.1.0"
info:
  title: Magento Ship Order
  version: 0.0.1
  description: |
    Creates a shipment for a Magento order with specified items, tracking information, and optional customer notification.
servers:
  - url: http://localhost:9999
paths:
  /rest/default/V1/order/{orderId}/ship:
    post:
      summary: Create a shipment for a Magento order
      operationId: shipOrder
      parameters:
        - name: orderId
          in: path
          required: true
          schema:
            type: string
          description: The order ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - items
              properties:
                items:
                  type: array
                  items:
                    type: object
                    required:
                      - order_item_id
                      - qty
                    properties:
                      order_item_id:
                        type: integer
                        description: The ID of the order item to ship
                      qty:
                        type: integer
                        description: The quantity to ship
                notify:
                  type: boolean
                  description: Whether to notify the customer
                comment:
                  type: object
                  properties:
                    comment:
                      type: string
                      description: The comment text
                    is_visible_on_front:
                      type: integer
                      description: Whether the comment is visible on the frontend (1 for visible, 0 for not visible)
                  required: [comment, is_visible_on_front]
                tracks:
                  type: array
                  items:
                    type: object
                    required:
                      - track_number
                      - title
                      - carrier_code
                    properties:
                      track_number:
                        type: string
                        description: The tracking number
                      title:
                        type: string
                        description: The carrier title
                      carrier_code:
                        type: string
                        description: The carrier code
            examples:
              shipment:
                summary: Example Shipment
                value:
                  items:
                    - order_item_id: 123
                      qty: 2
                    - order_item_id: 456
                      qty: 1
                  notify: true
                  comment:
                    comment: "This comment will be visible on fronted for customer, since the according flag is set to 1"
                    is_visible_on_front: 1
                  tracks:
                    - track_number: "1Y-9876543210"
                      title: "United Parcel Service"
                      carrier_code: "ups"
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
              examples:
                success:
                  summary: Successful shipment
                  value:
                    success: true
        '400':
          description: Bad Request - Invalid input data
        '401':
          description: Unauthorized - Invalid or missing authentication token
        '404':
          description: Not Found - Order or item does not exist 