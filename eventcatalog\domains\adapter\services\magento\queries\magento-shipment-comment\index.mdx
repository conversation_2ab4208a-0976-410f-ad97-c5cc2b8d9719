---
id: magento-shipment-comment
name: Magento Shipment Comment
version: 0.0.1
summary: |
  Add a comment to a shipment in Magento
producers:
  - magento-integration-adapter
consumers:
  - magento
owners:
  - enterprise
channels:
  - id: magento.{env}.rest.queries
    parameters:
      env: local
specifications:
  - type: openapi
    path: 'openapi.yml'
---

## Overview

This query allows adding comments to existing shipments in Magento. Comments can provide additional information about the shipment status, handling instructions, or any other relevant details.

## Architecture diagram

<NodeGraph />



## Notes

- Comments can be used to track important events or status changes in the shipment lifecycle
- Customer notifications can be controlled via the `is_customer_notified` parameter
- Comment visibility in the frontend can be controlled via the `is_visible_on_front` parameter
- The `parent_id` in the request must match the shipment ID in the URL 