openapi: "3.1.0"
info:
  title: Magento Shipment Comment
  version: 0.0.1
  description: |
    Add a comment to a shipment in Magento.
servers:
  - url: http://localhost:9999
paths:
  /rest/default/V1/shipment/{id}/comments:
    post:
      summary: Add a comment to a shipment
      operationId: addShipmentComment
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
          description: The shipment ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - data
              properties:
                data:
                  type: object
                  required:
                    - comment
                  properties:
                    comment:
                      type: object
                      required:
                        - comment
                        - parent_id
                      properties:
                        comment:
                          type: string
                          description: The comment text
                        is_visible_on_front:
                          type: boolean
                          description: Whether the comment is visible to customers in the frontend
                        is_customer_notified:
                          type: boolean
                          description: Whether to notify the customer about this comment
                        parent_id:
                          type: integer
                          description: The ID of the shipment this comment belongs to
            examples:
              addComment:
                summary: Add Shipment Comment
                value:
                  data:
                    comment:
                      comment: "Package has been picked up by carrier"
                      is_visible_on_front: true
                      is_customer_notified: true
                      parent_id: 123
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    properties:
                      comment_id:
                        type: integer
                      created_at:
                        type: string
                      entity_id:
                        type: integer
                      is_customer_notified:
                        type: boolean
                      is_visible_on_front:
                        type: boolean
                      parent_id:
                        type: integer
                      comment:
                        type: string
              examples:
                success:
                  summary: Successful comment
                  value:
                    data:
                      comment_id: 456
                      created_at: "2024-03-14 10:30:00"
                      entity_id: 456
                      is_customer_notified: true
                      is_visible_on_front: true
                      parent_id: 123
                      comment: "Package has been picked up by carrier"
        '400':
          description: Bad Request - Invalid input data
        '401':
          description: Unauthorized - Authentication required
        '404':
          description: Not Found - Shipment not found
        '500':
          description: Internal Server Error 