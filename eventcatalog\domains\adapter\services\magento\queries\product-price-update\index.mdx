---
id: product-price-update
name: Update Product Base Prices
version: 0.0.1
summary: |
  Query to update base prices for products in Magento using the REST API endpoint POST /rest/V1/products/base-prices
producers:
  - magento-integration-adapter
consumers:
  - magento
owners:
  - euvic
channels:
  - id: magento.{env}.rest.queries
    parameters:
      env: local
specifications:
  - type: openapi
    path: 'openapi.yml'
---

## Overview

The `product-price-update` query is used to update base prices for one or more products in Magento. This query supports updating prices for specific store views, allowing different base prices across different stores in a multi-store setup.

## Architecture diagram

<NodeGraph />

## Query Details

### Endpoint
```http
POST /rest/V1/products/base-prices
```

### Required Fields
Each price update in the array must include:
- `sku` - Stock Keeping Unit, unique identifier for the product
- `price` - New base price value for the product
- `store_id` - Store view ID where the price should be applied (0 for default/all stores)

## Multi-Store Pricing

The query supports Magento's multi-store functionality:

- Update prices for specific store views
- Set different prices per store
- Use store ID 0 for default price across all stores
- Override default prices at store view level


## Notes

- Multiple products can be updated in a single request
- Price must be a positive number
- Store ID must be valid and active
- Price updates may trigger indexing
- Consider catalog price rules that might affect final prices
- Price changes may affect:
  - Catalog listing prices
  - Shopping cart calculations
  - Order totals
  - Special prices and tier prices
- For large price updates, consider using bulk operations
- Price changes are immediately visible in the catalog
- Historical orders retain original prices 