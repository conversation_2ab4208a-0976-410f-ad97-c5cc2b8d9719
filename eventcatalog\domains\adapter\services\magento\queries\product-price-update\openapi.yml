openapi: "3.1.0"
info:
  title: Update Product Base Prices
  version: 0.0.1
  description: |
    Query to update base prices for products in Magento using the REST API endpoint POST /rest/V1/products/base-prices.
    Supports multi-store pricing for updating prices across different store views.
servers:
  - url: http://localhost:9999
paths:
  /rest/V1/products/base-prices:
    post:
      summary: Update base prices for one or more products
      operationId: updateProductBasePrices
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - prices
              properties:
                prices:
                  type: array
                  description: Array of product prices to update
                  items:
                    type: object
                    required:
                      - sku
                      - price
                      - store_id
                    properties:
                      sku:
                        type: string
                        description: SKU of the product
                      price:
                        type: number
                        format: float
                        description: New base price value for the product
                      store_id:
                        type: integer
                        description: Store ID where the price should be applied (0 for default/all stores)
            examples:
              singleProduct:
                summary: Update Single Product Price
                value:
                  prices:
                    - sku: "24-MB01"
                      price: 49.99
                      store_id: 0
              multipleProducts:
                summary: Update Multiple Products
                value:
                  prices:
                    - sku: "24-MB01"
                      price: 49.99
                      store_id: 0
                    - sku: "24-MB02"
                      price: 59.99
                      store_id: 0
              storeSpecific:
                summary: Update Store-Specific Prices
                value:
                  prices:
                    - sku: "24-MB01"
                      price: 49.99
                      store_id: 1
                    - sku: "24-MB01"
                      price: 45.99
                      store_id: 2
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: boolean
              examples:
                success:
                  summary: Successful update
                  value: true
        '400':
          description: Bad Request - Invalid price data, SKU, store ID, or negative price
        '401':
          description: Unauthorized - Invalid or missing authentication token
        '404':
          description: Not Found - Product or store does not exist
        '422':
          description: Unprocessable Entity - Price update not allowed for product type or invalid price format 