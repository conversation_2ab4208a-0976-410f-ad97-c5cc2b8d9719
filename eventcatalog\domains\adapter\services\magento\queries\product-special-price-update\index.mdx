---
id: product-special-price-update
name: Update Product Special Prices
version: 0.0.1
summary: |
  Query to update special prices for products in Magento using the REST API endpoint POST /rest/V1/products/special-prices
producers:
  - magento-integration-adapter
consumers:
  - magento
owners:
  - euvic
channels:
  - id: magento.{env}.rest.queries
    parameters:
      env: local
specifications:
  - type: openapi
    path: 'openapi.yml'
---

## Overview

The `product-special-price-update` query is used to update special prices for one or more products in Magento. Special prices are temporary discounted prices that are valid for a specific date range. This query supports updating special prices for specific store views, allowing different promotional prices across different stores in a multi-store setup.

## Architecture diagram

<NodeGraph />


## Multi-Store Special Pricing

The query supports Magento's multi-store functionality:

- Update special prices for specific store views
- Set different promotional prices per store
- Use store ID 0 for default special price across all stores
- Override default special prices at store view level



## Notes

- Multiple products can be updated in a single request
- Special price must be a positive number
- Special price should be lower than the regular price
- Store ID must be valid and active
- Date range must be valid and in the future
- Price updates may trigger indexing
- Consider catalog price rules that might affect final prices
- Special price changes affect:
  - Catalog listing prices
  - Shopping cart calculations
  - Order totals
  - Price display in layered navigation
- For large price updates, consider using bulk operations
- Price changes are immediately visible in the catalog after the start date
- Historical orders retain original prices
- Special prices take precedence over tier prices during their active period 