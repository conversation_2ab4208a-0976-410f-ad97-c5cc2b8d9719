openapi: "3.1.0"
info:
  title: Update Product Special Prices
  version: 0.0.1
  description: |
    Query to update special prices for products in Magento using the REST API endpoint POST /rest/V1/products/special-prices.
    Special prices are temporary discounted prices valid for a specific date range. Supports multi-store updates.
servers:
  - url: http://localhost:9999
paths:
  /rest/V1/products/special-prices:
    post:
      summary: Update special prices for one or more products
      operationId: updateProductSpecialPrices
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - prices
              properties:
                prices:
                  type: array
                  description: Array of product special prices to update
                  items:
                    type: object
                    required:
                      - sku
                      - price
                      - store_id
                      - price_from
                      - price_to
                    properties:
                      sku:
                        type: string
                        description: SKU of the product
                      price:
                        type: number
                        format: float
                        description: Special price value for the product
                      store_id:
                        type: integer
                        description: Store ID where the special price should be applied (0 for default/all stores)
                      price_from:
                        type: string
                        format: date-time
                        description: Start date for the special price (ISO-8601)
                      price_to:
                        type: string
                        format: date-time
                        description: End date for the special price (ISO 8601)
            examples:
              singleProduct:
                summary: Update Single Product Special Price
                value:
                  prices:
                    - sku: "24-MB01"
                      price: 39.99
                      store_id: 0
                      price_from: "2024-03-01T00:00:00Z"
                      price_to: "2024-03-31T23:59:59Z"
              multipleProducts:
                summary: Update Multiple Products
                value:
                  prices:
                    - sku: "24-MB01"
                      price: 39.99
                      store_id: 0
                      price_from: "2024-03-01T00:00:00Z"
                      price_to: "2024-03-31T23:59:59Z"
                    - sku: "24-MB02"
                      price: 49.99
                      store_id: 0
                      price_from: "2024-03-01T00:00:00Z"
                      price_to: "2024-03-31T23:59:59Z"
              storeSpecific:
                summary: Update Store-Specific Special Prices
                value:
                  prices:
                    - sku: "24-MB01"
                      price: 39.99
                      store_id: 1
                      price_from: "2024-03-01T00:00:00Z"
                      price_to: "2024-03-31T23:59:59Z"
                    - sku: "24-MB01"
                      price: 35.99
                      store_id: 2
                      price_from: "2024-03-01T00:00:00Z"
                      price_to: "2024-03-31T23:59:59Z"
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: boolean
              examples:
                success:
                  summary: Successful update
                  value: true
        '400':
          description: Bad Request - Invalid price data, SKU, store ID, negative price, invalid date, or end date before start date
        '401':
          description: Unauthorized - Invalid or missing authentication token
        '404':
          description: Not Found - Product or store does not exist
        '422':
          description: Unprocessable Entity - Special price higher than regular price, invalid price/date format, or invalid date range
