---
id: product-stock-update
name: Update Product Stock
version: 0.0.1
summary: |
  Query to update product stock information in Magento using the REST API endpoint POST /rest/V1/inventory/source-items
producers:
  - magento-integration-adapter
consumers:
  - magento
owners:
  - euvic
channels:
  - id: magento.{env}.rest.queries
    parameters:
      env: local
specifications:
  - type: openapi
    path: 'openapi.yml'
---

## Overview

The `product-stock-update` query is used to update stock information for one or more products across different inventory sources in Magento. This query supports Multi-Source Inventory (MSI) functionality, allowing stock management for products in multiple warehouses or locations.

## Architecture diagram

<NodeGraph />


## Multi-Source Inventory

The query supports Magento's Multi-Source Inventory (MSI) features:

- Update stock across multiple sources
- Manage different quantities per location
- Set source-specific notification rules
- Handle stock status independently per source


## Notes

- Multiple products and sources can be updated in a single request
- The source must be enabled and assigned to the product before updating stock
- Quantity must be a non-negative number
- Status 1 (In Stock) automatically sets when quantity > 0
- Changes affect the salable quantity calculation
- Updates trigger inventory indexing
- Consider using bulk operations for large updates 