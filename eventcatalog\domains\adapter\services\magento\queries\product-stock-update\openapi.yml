openapi: "3.1.0"
info:
  title: Update Product Stock
  version: 0.0.1
  description: |
    Query to update product stock information in Magento using the REST API endpoint POST /rest/V1/inventory/source-items.
    Supports Multi-Source Inventory (MSI) for managing stock across multiple warehouses or locations.
servers:
  - url: http://localhost:9999
paths:
  /rest/V1/inventory/source-items:
    post:
      summary: Update stock for one or more products
      operationId: updateProductStock
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - sourceItems
              properties:
                sourceItems:
                  type: array
                  description: Array of source items to update stock information
                  items:
                    type: object
                    required:
                      - sku
                      - source_code
                      - quantity
                      - status
                    properties:
                      sku:
                        type: string
                        description: SKU of the product
                      source_code:
                        type: string
                        description: Source code where the product is stored
                      quantity:
                        type: number
                        format: float
                        description: Quantity of the product
                      status:
                        type: integer
                        description: Stock status (1 - In Stock, 0 - Out of Stock)
                        enum: [0, 1]
                      notify_stock_qty:
                        type: number
                        description: The quantity that triggers out-of-stock notifications
                      notify_stock_qty_use_default:
                        type: boolean
                        description: Whether to use the default notification quantity
            examples:
              singleSourceItem:
                summary: Update Single Source Item
                value:
                  sourceItems:
                    - sku: "24-MB01"
                      source_code: "default"
                      quantity: 100
                      status: 1
              multipleSources:
                summary: Update Multiple Sources
                value:
                  sourceItems:
                    - sku: "24-MB01"
                      source_code: "warehouse_east"
                      quantity: 50
                      status: 1
                    - sku: "24-MB01"
                      source_code: "warehouse_west"
                      quantity: 75
                      status: 1
              withNotification:
                summary: Update with Notification Settings
                value:
                  sourceItems:
                    - sku: "24-MB01"
                      source_code: "default"
                      quantity: 100
                      status: 1
                      notify_stock_qty: 10
                      notify_stock_qty_use_default: false
              outOfStock:
                summary: Mark Product Out of Stock
                value:
                  sourceItems:
                    - sku: "24-MB01"
                      source_code: "default"
                      quantity: 0
                      status: 0
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: boolean
              examples:
                success:
                  summary: Successful update
                  value: true
        '400':
          description: Bad Request - Invalid source item data, SKU, source code, or negative quantity
        '401':
          description: Unauthorized - Invalid or missing authentication token
        '404':
          description: Not Found - Product or source does not exist
        '422':
          description: Unprocessable Entity - Product not assigned to source or invalid stock status 