---
id: product-tier-price-update
name: Update Product Tier Prices
version: 0.0.1
summary: |
  Query to update tier prices for products in Magento using the REST API endpoint POST /rest/V1/products/tier-prices
producers:
  - magento-integration-adapter
consumers:
  - magento
owners:
  - euvic
  - enterprise
channels:
  - id: magento.{env}.rest.queries
    parameters:
      env: local
specifications:
  - type: openapi
    path: 'openapi.yml'
---

## Overview

The `product-tier-price-update` query is used to update tier prices for one or more products in Magento. Tier prices are quantity-based discounts that offer better prices when customers purchase larger quantities. This query supports both fixed amounts and percentage discounts, and can be configured per website and customer group.

## Architecture diagram

<NodeGraph />


## Tier Pricing Types

### Fixed Amount
- Set an absolute price for the product
- Price value represents the actual amount
- Example: $45.99 per item when buying 5 or more

### Percentage Discount
- Set a percentage discount off the base price
- Price value represents the discount percentage
- Example: 10% off when buying 5 or more


## Notes

- Multiple tier prices can be updated in a single request
- Price must be a positive number
- For fixed prices, the amount should be lower than the base price
- For percentage discounts, the value should be between 0 and 100
- Website ID must be valid and active
- Customer group must exist in the system
- Quantity must be greater than or equal to 1
- Price updates may trigger indexing
- Consider the following when updating tier prices:
  - Impact on different customer groups
  - Interaction with other price types (special prices, catalog rules)
  - Effect on cart price calculations
  - Display in product listings and detail pages
- For large price updates, consider using bulk operations
- Price changes are immediately visible in the catalog
- Historical orders retain original prices
- Tier prices can be combined with:
  - Special prices (special prices take precedence)
  - Catalog price rules
  - Cart price rules
- When multiple tier prices exist, the best price for the customer is automatically applied 