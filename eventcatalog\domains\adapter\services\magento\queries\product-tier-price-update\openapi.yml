openapi: "3.1.0"
info:
  title: Update Product Tier Prices
  version: 0.0.1
  description: |
    Query to update tier prices for products in Magento using the REST API endpoint POST /rest/V1/products/tier-prices.
    Tier prices are quantity-based discounts that can be configured per website and customer group.
servers:
  - url: http://localhost:9999
paths:
  /rest/V1/products/tier-prices:
    post:
      summary: Update tier prices for one or more products
      operationId: updateProductTierPrices
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - prices
              properties:
                prices:
                  type: array
                  description: Array of product tier prices to update
                  items:
                    type: object
                    required:
                      - sku
                      - price
                      - price_type
                      - website_id
                      - customer_group
                      - quantity
                    properties:
                      sku:
                        type: string
                        description: SKU of the product
                      price:
                        type: number
                        format: float
                        description: Tier price value for the product
                      price_type:
                        type: string
                        enum: [fixed, discount]
                        description: Type of the tier price (fixed amount or percentage discount)
                      website_id:
                        type: integer
                        description: Website ID where the tier price should be applied (0 for all websites)
                      customer_group:
                        type: string
                        description: Customer group code (ALL_GROUPS for all customer groups)
                      quantity:
                        type: number
                        format: float
                        minimum: 1
                        description: Minimum quantity to qualify for the tier price
            examples:
              fixedAmount:
                summary: Fixed Amount Tier Price
                value:
                  prices:
                    - sku: "24-MB01"
                      price: 45.99
                      price_type: "fixed"
                      website_id: 0
                      customer_group: "ALL_GROUPS"
                      quantity: 5
              percentageDiscount:
                summary: Percentage Discount Tier Price
                value:
                  prices:
                    - sku: "24-MB01"
                      price: 10
                      price_type: "discount"
                      website_id: 0
                      customer_group: "WHOLESALE"
                      quantity: 10
              multipleTierPrices:
                summary: Multiple Tier Prices
                value:
                  prices:
                    - sku: "24-MB01"
                      price: 45.99
                      price_type: "fixed"
                      website_id: 0
                      customer_group: "ALL_GROUPS"
                      quantity: 5
                    - sku: "24-MB01"
                      price: 42.99
                      price_type: "fixed"
                      website_id: 0
                      customer_group: "ALL_GROUPS"
                      quantity: 10
                    - sku: "24-MB01"
                      price: 15
                      price_type: "discount"
                      website_id: 0
                      customer_group: "WHOLESALE"
                      quantity: 20
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: boolean
              examples:
                success:
                  summary: Successful update
                  value: true
        '400':
          description: Bad Request - Invalid price data, SKU, website ID, negative price, invalid quantity, customer group, or price type
        '401':
          description: Unauthorized - Invalid or missing authentication token
        '404':
          description: Not Found - Product, website, or customer group does not exist
        '422':
          description: Unprocessable Entity - Fixed price higher than base price, invalid price/quantity format, or duplicate tier price entries 