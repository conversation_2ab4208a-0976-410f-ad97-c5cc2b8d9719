---
id: product-update
name: Update Product
version: 0.0.1
summary: |
  Query to update an existing product in Magento using the REST API endpoint PUT /rest/V1/products/:sku
producers:
  - magento-integration-adapter
consumers:
  - magento
owners:
  - euvic
channels:
  - id: magento.{env}.rest.queries
    parameters:
      env: local
specifications:
  - type: openapi
    path: 'openapi.yml'
---

## Overview

The `product-update` query is used to update an existing product in Magento. It allows modifying various product attributes including basic information, stock data, and custom attributes.

## Architecture diagram

<NodeGraph />

### Path Parameters
- `sku` - Stock Keeping Unit, unique identifier for the product to update


## Stock Management

The query allows updating product stock through the `extension_attributes.stock_item` object:

- `qty` - Available quantity
- `is_in_stock` - Stock status
- `manage_stock` - Whether to track inventory
- `min_sale_qty` - Minimum quantity that can be purchased
- `max_sale_qty` - Maximum quantity that can be purchased
- `notify_stock_qty` - Quantity threshold for low stock notification


## Notes

- Only include the fields you want to update in the request
- Fields not included in the request will retain their current values
- The response includes the complete product data after the update
- Custom attributes must be defined in the attribute set before they can be updated
- Stock information updates are optional but should maintain inventory accuracy
- The `updated_at` timestamp is automatically set by Magento 