openapi: "3.1.0"
info:
  title: Update Product
  version: 0.0.1
  description: |
    Query to update an existing product in Magento using the REST API endpoint PUT /rest/V1/products/:sku.
    Allows modifying various product attributes including basic information, stock data, and custom attributes.
servers:
  - url: http://localhost:9999
paths:
  /rest/V1/products/{sku}:
    put:
      summary: Update an existing product
      operationId: updateProduct
      parameters:
        - name: sku
          in: path
          required: true
          schema:
            type: string
          description: Stock Keeping Unit, unique identifier for the product to update
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - sku
                - product
              properties:
                sku:
                  type: string
                  description: SKU of the product to update
                product:
                  type: object
                  properties:
                    name:
                      type: string
                      description: Product name
                    price:
                      type: number
                      format: float
                      description: Product price
                    status:
                      type: integer
                      description: Product status (1 - enabled, 2 - disabled)
                      enum: [1, 2]
                    visibility:
                      type: integer
                      description: Product visibility (1 - Not Visible, 2 - Catalog, 3 - Search, 4 - Catalog & Search)
                      enum: [1, 2, 3, 4]
                    type_id:
                      type: string
                      description: Product type (simple, configurable, etc.)
                      enum: [simple, configurable, virtual, bundle, downloadable, grouped]
                    weight:
                      type: number
                      format: float
                      description: Product weight
                    extension_attributes:
                      type: object
                      description: Additional product attributes
                      properties:
                        stock_item:
                          type: object
                          properties:
                            qty:
                              type: number
                              description: Product quantity
                            is_in_stock:
                              type: boolean
                              description: Stock status
                            manage_stock:
                              type: boolean
                              description: Whether to manage stock
                            min_sale_qty:
                              type: number
                              description: Minimum sale quantity
                            max_sale_qty:
                              type: number
                              description: Maximum sale quantity
                            notify_stock_qty:
                              type: number
                              description: Notify when quantity falls below
                    custom_attributes:
                      type: array
                      description: Custom product attributes
                      items:
                        type: object
                        required: [attribute_code, value]
                        properties:
                          attribute_code:
                            type: string
                            description: Attribute code
                          value:
                            type: string
                            description: Attribute value
            examples:
              updateBasic:
                summary: Update Basic Information
                value:
                  sku: "24-MB01"
                  product:
                    name: "Updated Duffle Bag"
                    price: 39.99
                    status: 1
                    visibility: 4
              updateStock:
                summary: Update Stock Information
                value:
                  sku: "24-MB01"
                  product:
                    extension_attributes:
                      stock_item:
                        qty: 150
                        is_in_stock: true
                        notify_stock_qty: 10
              updateCustom:
                summary: Update Custom Attributes
                value:
                  sku: "custom-tshirt"
                  product:
                    custom_attributes:
                      - attribute_code: "color"
                        value: "red"
                      - attribute_code: "size"
                        value: "L"
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              examples:
                success:
                  summary: Successful update
                  value:
                    data:
                      id: 1
                      sku: "24-MB01"
                      name: "Updated Duffle Bag"
                      price: 39.99
                      status: 1
                      visibility: 4
                      type_id: "simple"
                      created_at: "2024-01-20 10:00:00"
                      updated_at: "2024-01-20 15:30:00"
                      extension_attributes:
                        stock_item:
                          qty: 150
                          is_in_stock: true
                    error: null
        '400':
          description: Bad Request - Invalid product data, attribute values, or SKU format
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiErrorResponse'
              examples:
                validation_error:
                  summary: Validation error
                  value:
                    data: null
                    error:
                      message: "Invalid product data provided"
                      type: "ValidationError"
                      details:
                        - field: "price"
                          message: "Price must be greater than 0"
        '401':
          description: Unauthorized - Invalid or missing authentication token
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiErrorResponse'
              examples:
                unauthorized:
                  summary: Authentication failed
                  value:
                    data: null
                    error:
                      message: "Authentication required"
                      type: "UnauthorizedError"
        '404':
          description: Not Found - Product with specified SKU does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiErrorResponse'
              examples:
                not_found:
                  summary: Product not found
                  value:
                    data: null
                    error:
                      message: "Product with SKU '24-MB01' not found"
                      type: "NotFoundError"

components:
  schemas:
    ApiResponse:
      type: object
      description: Standard envelope wrapper for all API responses
      properties:
        data:
          type: object
          nullable: true
          description: Response data (null for error responses)
          properties:
            id:
              type: integer
            sku:
              type: string
            name:
              type: string
            price:
              type: number
            status:
              type: integer
            visibility:
              type: integer
            type_id:
              type: string
            created_at:
              type: string
            updated_at:
              type: string
            extension_attributes:
              type: object
              properties:
                stock_item:
                  type: object
                  properties:
                    qty:
                      type: number
                    is_in_stock:
                      type: boolean
        error:
          type: object
          nullable: true
          description: Error details (null for successful responses)
      required:
        - data

    ApiErrorResponse:
      type: object
      description: Standard envelope wrapper for error responses
      properties:
        data:
          type: object
          nullable: true
          description: Always null for error responses
        error:
          type: object
          nullable: false
          description: Error details
          properties:
            message:
              type: string
              description: Human-readable error message
            type:
              type: string
              description: Error type classification
            details:
              type: array
              nullable: true
              description: Additional error details for validation errors
              items:
                type: object
                properties:
                  field:
                    type: string
                    description: Field name with validation error
                  message:
                    type: string
                    description: Field-specific error message
      required:
        - data
        - error 