---
id: sap-sales-order-created-event
name: SAP Sales Order Created Event
version: 0.0.1
summary: Event published by the SAP Integration Adapter when a sales order has been created in SAP.
owners:
  - enterprise
channels:
  - id: aws.sns.{env}.sap-sales-order-created-event
schemaPath: 'schema.json'
---

## Overview

This event is published by the SAP Integration Adapter when a sales order has been created in SAP. It represents the canonical event for sales order creation in SAP.

## Architecture diagram

<NodeGraph/>

<SchemaViewer file="schema.json" title="JSON Schema" maxHeight="500" />

## Payload example

```json title="Payload example"
{
  "OrderNumber": "ORD123456",
  "Results": [
    {
      "Type": "S",
      "Id": "SUCCESS",
      "Number": "000",
      "Message": "Order created successfully",
      "LogNo": "",
      "LogMsgNo": "000000",
      "MessageV1": "",
      "MessageV2": "",
      "MessageV3": "",
      "MessageV4": "",
      "Parameter": "",
      "Row": 0,
      "Field": "",
      "System": ""
    }
  ]
}
``` 