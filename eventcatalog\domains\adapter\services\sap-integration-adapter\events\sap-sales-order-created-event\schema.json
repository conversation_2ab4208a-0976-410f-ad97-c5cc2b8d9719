{"$schema": "http://json-schema.org/draft-07/schema#", "title": "SapSalesOrderCreatedEvent", "type": "object", "properties": {"OrderNumber": {"type": "string", "description": "The unique identifier for the created SAP sales order."}, "Results": {"type": "array", "description": "The list of result messages returned by SAP for the order creation.", "items": {"type": "object", "properties": {"Type": {"type": "string", "description": "The type of the result message (e.g., 'S' for success)."}, "Id": {"type": "string", "description": "The ID of the result message (e.g., 'SUCCESS')."}, "Number": {"type": "string", "description": "The number code of the result message."}, "Message": {"type": "string", "description": "The message text returned by SAP."}, "LogNo": {"type": "string", "description": "The log number, if any."}, "LogMsgNo": {"type": "string", "description": "The log message number, if any."}, "MessageV1": {"type": "string", "description": "Additional message parameter 1."}, "MessageV2": {"type": "string", "description": "Additional message parameter 2."}, "MessageV3": {"type": "string", "description": "Additional message parameter 3."}, "MessageV4": {"type": "string", "description": "Additional message parameter 4."}, "Parameter": {"type": "string", "description": "The parameter name, if any."}, "Row": {"type": "integer", "description": "The row number, if applicable."}, "Field": {"type": "string", "description": "The field name, if any."}, "System": {"type": "string", "description": "The SAP system identifier."}}, "required": ["Type", "Id", "Number", "Message", "System"]}}}, "required": ["OrderNumber", "Results"]}