---
specifications:
  - type: openapi
    path: 'openapi.yml'
id: sap-material-classification-updated
name: SAP Material Classification Updated
version: 0.0.1
summary: |
  Event that indicates classification data for a material has been updated in SAP ECC 6
owners:
  - enterprise
channels:
  - id: sap-integration-adapter.{env}.events
    parameters:
      env: local
schemaPath: 'schema.json'
---

## Overview

This event is emitted when classification data for a material is updated in SAP ECC 6. It contains essential information about the material's classification data, including class assignments, characteristics, and values.

For the sake of discussion, the key components of the contract are defined as:
- Protocol: HTTP/HTTPS (Defined on Channel)
- Authentication: OAuth2 (Defined on Channel)
- Address: sap-integration-adapter.local.events/material-classification-updated (Defined on Channel)
- Schema: schema.json (in this document)

## SAP ECC 6 Details

### Technical Details
- **Integration Method**: IDoc (CLSMAS05)
- **SAP Tables**: 
  - AUSP (Characteristic Values)
  - KLAH (Class Header)
  - KSSK (Object Classification)
  - CABN (Characteristic)
- **Transaction Code**: CL20N (Classification)
- **Authorization Object**: C_CLASS (Classification)

### Business Process
1. **Classification Update Flow**:
   - Material classification is updated via CL20N transaction
   - IDoc CLSMAS05 is generated
   - System validates class and characteristics
   - Characteristic values are maintained
   - Classification data is updated
   - Changes are saved

2. **Key SAP Fields**:
   - MATNR (Material Number)
   - KLART (Class Type)
   - KLPOS (Class Number)
   - ATNAM (Characteristic Name)
   - ATWRT (Characteristic Value)
   - ATFLV (Float Value)
   - ATFLB (Float Value Lower Limit)
   - ATFLO (Float Value Upper Limit)

3. **Integration Points**:
   - Material Master (MM03)
   - Classification System (CL20N)
   - Variant Configuration (CU41)
   - Quality Management (QA01)

### Common SAP ECC 6 Considerations
- **Class Types**:
  - 001: Material Classes
  - 002: Batch Classes
  - 003: Equipment Classes
  - 022: Document Classes
  - 023: Business Partner Classes

- **Characteristic Types**:
  - CHAR: Character
  - NUM: Numeric
  - CURR: Currency
  - DATE: Date
  - TIME: Time
  - BOOL: Boolean

- **Value Assignment**:
  - Single Value
  - Multiple Values
  - Range of Values
  - Table Values
  - Formula Values

### Error Handling
Common SAP ECC 6 errors that may occur:
- **Class Not Found**: Check class master (CL20N)
- **Characteristic Not Found**: Verify characteristic (CT04)
- **Value Not Allowed**: Check allowed values (CT04)
- **Authorization Error**: Verify user permissions (SU01)
- **Validation Error**: Check characteristic rules (CT04)

## Architecture diagram

<NodeGraph/>
