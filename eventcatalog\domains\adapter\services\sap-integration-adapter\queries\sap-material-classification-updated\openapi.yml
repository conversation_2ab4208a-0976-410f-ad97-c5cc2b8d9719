openapi: "3.1.0"
info:
  title: SAP Material Classification Updated
  version: 0.0.1
  description: Event that indicates classification data for a material has been updated in SAP ECC 6.
servers:
  - url: http://localhost:7600
paths:
  /sap-material-classification-updated:
    post:
      summary: SAP Material Classification Updated Event
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SapMaterialClassificationUpdatedEvent'
            example:
              id: 7fa85f64-5717-4562-b3fc-2c963f66afa9
              source: SAP_ECC
              type: sap.material.classification.updated
              time: '2023-10-18T11:25:30Z'
              datacontenttype: application/json
              data:
                idoc:
                  idocNumber: '0000000000123456'
                  idocType: CLSMAS05
                  messageType: CLSMAS
                  direction: OUTBOUND
                  senderLogicalSystem: SAPECC
                  recipientLogicalSystem: INTEGRATION
                segments:
                  E1OCLFM:
                    OBTAB: MARA
                    OBJEK: MAT001
                    CLINT: '0000000123'
                  E1OCLFT:
                    - ATINN: '0000000123'
                      ATNAM: COLOR
                      ATWRT: RED
                    - ATINN: '0000000124'
                      ATNAM: WEIGHT
                      ATFLV: '1.5'
      responses:
        '200':
          description: Acknowledgement
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
components:
  schemas:
    SapMaterialClassificationUpdatedEvent:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for this event instance
        source:
          type: string
          enum: [SAP_ECC]
          description: Source system that emitted the event
        type:
          type: string
          enum: [sap.material.classification.updated]
          description: Type of event - material classification update
        time:
          type: string
          format: date-time
          description: Timestamp when the event occurred
        datacontenttype:
          type: string
          enum: [application/json]
          description: Content type of the data payload
        data:
          type: object
          properties:
            idoc:
              type: object
              properties:
                idocNumber:
                  type: string
                  description: IDoc number for tracking
                idocType:
                  type: string
                  enum: [CLSMAS05]
                  description: IDoc type
                messageType:
                  type: string
                  enum: [CLSMAS]
                  description: Message type
                direction:
                  type: string
                  enum: [OUTBOUND]
                  description: Direction of the IDoc
                senderLogicalSystem:
                  type: string
                  description: Logical system that sent the IDoc
                recipientLogicalSystem:
                  type: string
                  description: Logical system that received the IDoc
              required: [idocNumber, idocType, messageType, direction]
            segments:
              type: object
              properties:
                E1OCLFM:
                  type: object
                  properties:
                    OBTAB:
                      type: string
                      description: Object table name
                    OBJEK:
                      type: string
                      description: Object key (material number)
                    CLINT:
                      type: string
                      description: Class internal ID
                  required: [OBTAB, OBJEK, CLINT]
                E1OCLFT:
                  type: array
                  items:
                    type: object
                    properties:
                      ATWRT:
                        type: string
                        description: Characteristic value (character format)
                      ATFLV:
                        type: string
                        description: Characteristic value (numeric format)
                      ATINN:
                        type: string
                        description: Internal characteristic number
                      ATNAM:
                        type: string
                        description: Characteristic name
                    required: [ATINN]
              required: [E1OCLFM, E1OCLFT]
          required: [idoc, segments]
      required: [id, source, type, time, datacontenttype, data] 