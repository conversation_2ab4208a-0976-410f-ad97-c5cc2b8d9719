---
specifications:
  - type: openapi
    path: 'openapi.yml'
id: sap-material-cost-updated
name: SAP Material Cost Updated
version: 0.0.1
summary: |
  Event that indicates cost data for a material has been updated in SAP ECC 6
owners:
  - enterprise
channels:
  - id: sap-integration-adapter.{env}.events
    parameters:
      env: local
schemaPath: 'schema.json'
---

## Overview

This event is emitted when cost data for a material is updated in SAP ECC 6. It contains essential information about the material's cost data, including standard price, moving average price, and cost components.

For the sake of discussion, the key components of the contract are defined as:
- Protocol: HTTP/HTTPS (Defined on Channel)
- Authentication: OAuth2 (Defined on Channel)
- Address: sap-integration-adapter.local.events/material-cost-updated (Defined on Channel)
- Schema: schema.json (in this document)

## SAP ECC 6 Details

### Technical Details
- **Integration Method**: IDoc (MATMAS05)
- **SAP Tables**: 
  - MBEW (Material Valuation)
  - MBEWH (Material Valuation History)
  - CKMLHD (Cost Component Split)
  - CKMLPR (Cost Component Prices)
  - CKMLCR (Cost Component Results)
- **Transaction Code**: CK11N (Cost Estimate)
- **Authorization Object**: M_MATE_WRK (Material Master)

### Business Process
1. **Cost Update Flow**:
   - Material cost is updated via CK11N transaction
   - IDoc MATMAS05 is generated
   - System validates cost components
   - Price updates are maintained
   - Cost center assignments are updated
   - Changes are saved

2. **Key SAP Fields**:
   - MATNR (Material Number)
   - BWKEY (Valuation Area)
   - BWTAR (Valuation Type)
   - STPRS (Standard Price)
   - PEINH (Price Unit)
   - VPRSV (Price Control)
   - MLAST (Last Price Change)
   - ZPLP1 (Future Price)

3. **Integration Points**:
   - Material Master (MM03)
   - Cost Center Accounting (KS01)
   - Product Cost Planning (CK11N)
   - Profitability Analysis (KE30)

### Common SAP ECC 6 Considerations
- **Price Controls**:
  - S: Standard Price
  - V: Moving Average Price
  - Blank: Not Relevant

- **Cost Components**:
  - 1: Material
  - 2: Production
  - 3: Overhead
  - 4: External Processing
  - 5: Other

- **Valuation Types**:
  - 1: Standard
  - 2: Moving Average
  - 3: LIFO
  - 4: FIFO

### Error Handling
Common SAP ECC 6 errors that may occur:
- **Material Not Found**: Check material master (MM03)
- **Cost Center Not Found**: Verify cost center (KS01)
- **Price Control Error**: Check price control (OMT4)
- **Valuation Error**: Review valuation type (OMT4)
- **Authorization Error**: Verify user permissions (SU01)

## Architecture diagram

<NodeGraph/>
