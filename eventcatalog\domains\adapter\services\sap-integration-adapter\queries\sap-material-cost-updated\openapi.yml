openapi: "3.1.0"
info:
  title: SAP Material Cost Updated
  version: 0.0.1
  description: Event that indicates cost data for a material has been updated in SAP ECC 6.
servers:
  - url: http://localhost:7600
paths:
  /sap-material-cost-updated:
    post:
      summary: SAP Material Cost Updated Event
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SapMaterialCostUpdatedEvent'
            example:
              id: 7fa85f64-5717-4562-b3fc-2c963f66afa9
              source: SAP_ECC
              type: sap.material.cost.updated
              time: '2023-10-18T11:25:30Z'
              datacontenttype: application/json
              data:
                idoc:
                  idocNumber: '0000000000123456'
                  idocType: MATMAS05
                  messageType: MATMAS
                  direction: OUTBOUND
                  senderLogicalSystem: SAPECC
                  recipientLogicalSystem: INTEGRATION
                segments:
                  E1MARAM:
                    MATNR: MAT001
                    MTART: FERT
                    MEINS: EA
                  E1MBEWM:
                    - BWKEY: '1000'
                      VPRSV: S
                      STPRS: '130.75'
                      PEINH: '1'
                      BWPRS: '130.75'
                      BWPRH: '130.75'
                      VERPR: '128.90'
                      STPRV: '125.50'
                      LBKUM: '100'
                      SALK3: '13075.00'
      responses:
        '200':
          description: Acknowledgement
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
components:
  schemas:
    SapMaterialCostUpdatedEvent:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for this event instance
        source:
          type: string
          enum: [SAP_ECC]
          description: Source system that emitted the event
        type:
          type: string
          enum: [sap.material.cost.updated]
          description: Type of event - material cost update
        time:
          type: string
          format: date-time
          description: Timestamp when the event occurred
        datacontenttype:
          type: string
          enum: [application/json]
          description: Content type of the data payload
        data:
          type: object
          properties:
            idoc:
              type: object
              properties:
                idocNumber:
                  type: string
                  description: IDoc number for tracking
                idocType:
                  type: string
                  enum: [MATMAS05]
                  description: IDoc type
                messageType:
                  type: string
                  enum: [MATMAS]
                  description: Message type
                direction:
                  type: string
                  enum: [OUTBOUND]
                  description: Direction of the IDoc
                senderLogicalSystem:
                  type: string
                  description: Logical system that sent the IDoc
                recipientLogicalSystem:
                  type: string
                  description: Logical system that received the IDoc
              required: [idocNumber, idocType, messageType, direction]
            segments:
              type: object
              properties:
                E1MARAM:
                  type: object
                  properties:
                    MATNR:
                      type: string
                      description: Material number
                    MTART:
                      type: string
                      description: Material type
                    MEINS:
                      type: string
                      description: Base unit of measure
                  required: [MATNR]
                E1MBEWM:
                  type: array
                  items:
                    type: object
                    properties:
                      BWKEY:
                        type: string
                        description: Valuation area
                      VPRSV:
                        type: string
                        enum: [S, V, ""]
                        description: Price control indicator (S=Standard, V=Moving average)
                      STPRS:
                        type: string
                        description: Standard price
                      PEINH:
                        type: string
                        description: Price unit
                      BWPRS:
                        type: string
                        description: Valuation price
                      BWPRH:
                        type: string
                        description: Commercial price
                      VERPR:
                        type: string
                        description: Moving average price
                      STPRV:
                        type: string
                        description: Previous standard price
                      LBKUM:
                        type: string
                        description: Total valuated stock
                      SALK3:
                        type: string
                        description: Value of total valuated stock
                    required: [BWKEY]
              required: [E1MARAM, E1MBEWM]
          required: [idoc, segments]
      required: [id, source, type, time, datacontenttype, data] 