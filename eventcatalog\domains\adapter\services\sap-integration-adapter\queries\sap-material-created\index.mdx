---
specifications:
  - type: openapi
    path: 'openapi.yml'
id: sap-material-created
name: SAP Material Created
version: 0.0.1
summary: |
  Event that indicates a material has been created in SAP ECC 6
owners:
  - enterprise
channels:
  - id: sap-integration-adapter.{env}.events
    parameters:
      env: local
schemaPath: 'schema.json'
---

## Overview

This event is emitted when a new material is successfully created in SAP ECC 6. It contains essential information about the material including its SAP material number, description, and classification data.

For the sake of discussion, the key components of the contract are defined as:
- Protocol: HTTP/HTTPS (Defined on Channel)
- Authentication: OAuth2 (Defined on Channel)
- Address: sap-integration-adapter.local.events/material-created (Defined on Channel)
- Schema: schema.json (in this document)

## SAP ECC 6 Details

### Technical Details
- **Integration Method**: IDoc (MATMAS05)
- **SAP Tables**: 
  - MARA (Material Master General Data)
  - MARC (Material Master Plant Data)
  - MARD (Material Master Storage Location)
  - MAKT (Material Descriptions)
  - MVKE (Material Sales Data)
  - MARC (Material Plant Data)
- **Transaction Code**: MM01 (Create Material)
- **Authorization Object**: M_MATE_WRK (Material Master)

### Business Process
1. **Material Creation Flow**:
   - Material is created via MM01 transaction
   - IDoc MATMAS05 is generated
   - System validates material type and number range
   - Plant and storage location data is maintained
   - Sales organization data is maintained
   - Material is saved and number is assigned

2. **Key SAP Fields**:
   - MATNR (Material Number)
   - MTART (Material Type)
   - MEINS (Base Unit of Measure)
   - MATKL (Material Group)
   - WERKS (Plant)
   - LGORT (Storage Location)
   - VKORG (Sales Organization)
   - VTWEG (Distribution Channel)

3. **Integration Points**:
   - Material Master (MM03)
   - Classification (CL20N)
   - Purchasing Info Record (ME11)
   - Sales Price (VK11)

### Common SAP ECC 6 Considerations
- **Material Types**:
  - FERT: Finished Product
  - HALB: Semi-finished Product
  - ROH: Raw Material
  - DIEN: Service
  - HAWA: Trading Goods

- **Material Groups**:
  - 1000: Raw Materials
  - 2000: Semi-finished Products
  - 3000: Finished Products
  - 4000: Trading Goods
  - 5000: Services

- **MRP Types**:
  - PD: MRP
  - ND: No MRP
  - VB: Reorder Point
  - V1: Forecast-based

### Error Handling
Common SAP ECC 6 errors that may occur:
- **Material Number Range**: Check number range configuration (MMNR)
- **Material Type Not Found**: Verify material type configuration (OMSF)
- **Plant Not Found**: Check plant configuration (OX10)
- **Storage Location Not Found**: Verify storage location (OMJJ)
- **Sales Organization Not Found**: Check sales organization (OVX5)

## Architecture diagram

<NodeGraph/>
