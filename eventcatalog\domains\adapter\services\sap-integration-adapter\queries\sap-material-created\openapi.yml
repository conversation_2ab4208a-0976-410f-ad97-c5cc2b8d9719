openapi: "3.1.0"
info:
  title: SAP Material Created
  version: 0.0.1
  description: Event that indicates a material has been created in SAP ECC 6.
servers:
  - url: http://localhost:7600
paths:
  /sap-material-created:
    post:
      summary: SAP Material Created Event
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SapMaterialCreatedEvent'
            example:
              id: 3fa85f64-5717-4562-b3fc-2c963f66afa6
              source: SAP_ECC
              type: sap.material.created
              time: '2023-10-15T14:30:25Z'
              datacontenttype: application/json
              data:
                idoc:
                  idocNumber: '0000000000123456'
                  idocType: MATMAS05
                  messageType: MATMAS
                  direction: OUTBOUND
                  senderLogicalSystem: SAPECC
                  recipientLogicalSystem: INTEGRATION
                segments:
                  E1MARAM:
                    MATNR: MAT001
                    MTART: FERT
                    MBRSH: M
                    MEINS: EA
                    MATKL: '1000'
                    GEWEI: KG
                    BRGEW: '1.5'
                    NTGEW: '1.2'
                  E1MAKTM:
                    - SPRAS: E
                      MAKTX: Finished Product XYZ
                  E1MARCM:
                    - WERKS: '1000'
                      PSTAT: A
                      MMSTA: A
      responses:
        '200':
          description: Acknowledgement
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
components:
  schemas:
    SapMaterialCreatedEvent:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for this event instance
        source:
          type: string
          enum: [SAP_ECC]
          description: Source system that emitted the event
        type:
          type: string
          enum: [sap.material.created]
          description: Type of event - material creation
        time:
          type: string
          format: date-time
          description: Timestamp when the event occurred
        datacontenttype:
          type: string
          enum: [application/json]
          description: Content type of the data payload
        data:
          type: object
          properties:
            idoc:
              type: object
              properties:
                idocNumber:
                  type: string
                  description: IDoc number for tracking
                idocType:
                  type: string
                  enum: [MATMAS05]
                  description: IDoc type
                messageType:
                  type: string
                  enum: [MATMAS]
                  description: Message type
                direction:
                  type: string
                  enum: [OUTBOUND]
                  description: Direction of the IDoc
                senderLogicalSystem:
                  type: string
                  description: Logical system that sent the IDoc
                recipientLogicalSystem:
                  type: string
                  description: Logical system that received the IDoc
              required: [idocNumber, idocType, messageType, direction]
            segments:
              type: object
              properties:
                E1MARAM:
                  type: object
                  properties:
                    MATNR:
                      type: string
                      description: Material number
                    MTART:
                      type: string
                      description: Material type
                    MBRSH:
                      type: string
                      description: Industry sector
                    MEINS:
                      type: string
                      description: Base unit of measure
                    MATKL:
                      type: string
                      description: Material group
                    GEWEI:
                      type: string
                      description: Weight unit
                    BRGEW:
                      type: string
                      description: Gross weight
                    NTGEW:
                      type: string
                      description: Net weight
                  required: [MATNR, MTART, MEINS]
                E1MAKTM:
                  type: array
                  items:
                    type: object
                    properties:
                      SPRAS:
                        type: string
                        description: Language key
                      MAKTX:
                        type: string
                        description: Material description
                    required: [SPRAS, MAKTX]
                E1MARCM:
                  type: array
                  items:
                    type: object
                    properties:
                      WERKS:
                        type: string
                        description: Plant
                      PSTAT:
                        type: string
                        description: Maintenance status
                      MMSTA:
                        type: string
                        description: Material status
                    required: [WERKS]
              required: [E1MARAM]
          required: [idoc, segments]
      required: [id, source, type, time, datacontenttype, data] 