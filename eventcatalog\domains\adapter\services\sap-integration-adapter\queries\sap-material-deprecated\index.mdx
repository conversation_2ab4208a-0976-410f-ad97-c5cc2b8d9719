---
specifications:
  - type: openapi
    path: 'openapi.yml'
id: sap-material-deprecated
name: SAP Material Deprecated
version: 0.0.1
summary: |
  Event that indicates a material has been deprecated or marked for deletion in SAP ECC 6
owners:
  - enterprise
channels:
  - id: sap-integration-adapter.{env}.events
    parameters:
      env: local
schemaPath: 'schema.json'
---

## Overview

This event is emitted when a material is deprecated or marked for deletion in SAP ECC 6. It contains essential information about the material and its deprecation status, including the deletion flag, block indicators, and reason for deprecation.

For the sake of discussion, the key components of the contract are defined as:
- Protocol: HTTP/HTTPS (Defined on Channel)
- Authentication: OAuth2 (Defined on Channel)
- Address: sap-integration-adapter.local.events/material-deprecated (Defined on Channel)
- Schema: schema.json (in this document)

## SAP ECC 6 Details

### Technical Details
- **Integration Method**: IDoc (MATMAS05)
- **SAP Tables**: 
  - MARA (Material Master General Data)
  - MARC (Material Master Plant Data)
  - MARD (Material Master Storage Location)
  - MAKT (Material Descriptions)
  - MVKE (Material Sales Data)
  - MARC (Material Plant Data)
- **Transaction Code**: MM06 (Mark Material for Deletion)
- **Authorization Object**: M_MATE_WRK (Material Master)

### Business Process
1. **Material Deprecation Flow**:
   - Material is marked for deletion via MM06 transaction
   - IDoc MATMAS05 is generated
   - System validates material status
   - Deletion flag is set
   - Block indicators are set
   - Changes are saved

2. **Key SAP Fields**:
   - MATNR (Material Number)
   - MTART (Material Type)
   - LVORM (Deletion Flag)
   - MEINS (Base Unit of Measure)
   - WERKS (Plant)
   - MMSTA (Material Status)
   - PSTAT (Maintenance Status)

3. **Integration Points**:
   - Material Master (MM03)
   - Purchasing (ME21N)
   - Sales (VA01)
   - Production (CO01)

### Common SAP ECC 6 Considerations
- **Deletion Flags**:
  - X: Marked for Deletion
  - (blank): Not Marked for Deletion

- **Material Statuses**:
  - A: Active
  - B: Blocked
  - C: Discontinued
  - D: Deleted

- **Maintenance Statuses**:
  - A: Active
  - B: Blocked
  - C: In Maintenance
  - D: Deleted

### Error Handling
Common SAP ECC 6 errors that may occur:
- **Material Not Found**: Check material master (MM03)
- **Material in Use**: Verify material usage (MM60)
- **Authorization Error**: Check user permissions (SU01)
- **Status Change Not Allowed**: Verify status flow (OMSF)
- **Dependencies Exist**: Check material dependencies (MM60)

## Architecture diagram

<NodeGraph/>
