openapi: "3.1.0"
info:
  title: SAP Material Deprecated
  version: 0.0.1
  description: Event that indicates a material has been deprecated or marked for deletion in SAP ECC 6.
servers:
  - url: http://localhost:7600
paths:
  /sap-material-deprecated:
    post:
      summary: SAP Material Deprecated Event
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SapMaterialDeprecatedEvent'
            example:
              id: 7fa85f64-5717-4562-b3fc-2c963f66afb0
              source: SAP_ECC
              type: sap.material.deprecated
              time: '2023-10-19T10:15:30Z'
              datacontenttype: application/json
              data:
                idoc:
                  idocNumber: '0000000000123456'
                  idocType: MATMAS05
                  messageType: MATMAS
                  direction: OUTBOUND
                  senderLogicalSystem: SAPECC
                  recipientLogicalSystem: INTEGRATION
                segments:
                  E1MARAM:
                    MATNR: MAT001
                    MTART: FERT
                    LVORM: X
                    MEINS: EA
                  E1MAKTM:
                    - SPRAS: E
                      MAKTX: Finished Product XYZ
                  E1MARCM:
                    - WERKS: '1000'
                      MMSTA: B
                      PSTAT: B
      responses:
        '200':
          description: Acknowledgement
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
components:
  schemas:
    SapMaterialDeprecatedEvent:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for this event instance
        source:
          type: string
          enum: [SAP_ECC]
          description: Source system that emitted the event
        type:
          type: string
          enum: [sap.material.deprecated]
          description: Type of event - material deprecation
        time:
          type: string
          format: date-time
          description: Timestamp when the event occurred
        datacontenttype:
          type: string
          enum: [application/json]
          description: Content type of the data payload
        data:
          type: object
          properties:
            idoc:
              type: object
              properties:
                idocNumber:
                  type: string
                  description: IDoc number for tracking
                idocType:
                  type: string
                  enum: [MATMAS05]
                  description: IDoc type
                messageType:
                  type: string
                  enum: [MATMAS]
                  description: Message type
                direction:
                  type: string
                  enum: [OUTBOUND]
                  description: Direction of the IDoc
                senderLogicalSystem:
                  type: string
                  description: Logical system that sent the IDoc
                recipientLogicalSystem:
                  type: string
                  description: Logical system that received the IDoc
              required: [idocNumber, idocType, messageType, direction]
            segments:
              type: object
              properties:
                E1MARAM:
                  type: object
                  properties:
                    MATNR:
                      type: string
                      description: Material number
                    MTART:
                      type: string
                      description: Material type
                    LVORM:
                      type: string
                      enum: [X, ""]
                      description: Deletion flag
                    MEINS:
                      type: string
                      description: Base unit of measure
                  required: [MATNR]
                E1MAKTM:
                  type: array
                  items:
                    type: object
                    properties:
                      SPRAS:
                        type: string
                        description: Language key
                      MAKTX:
                        type: string
                        description: Material description
                    required: [SPRAS, MAKTX]
                E1MARCM:
                  type: array
                  items:
                    type: object
                    properties:
                      WERKS:
                        type: string
                        description: Plant
                      MMSTA:
                        type: string
                        description: Material status at plant level
                      PSTAT:
                        type: string
                        description: Maintenance status
                    required: [WERKS]
              required: [E1MARAM]
          required: [idoc, segments]
      required: [id, source, type, time, datacontenttype, data] 