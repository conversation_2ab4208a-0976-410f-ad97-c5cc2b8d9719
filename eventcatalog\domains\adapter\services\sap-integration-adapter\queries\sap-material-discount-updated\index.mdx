---
specifications:
  - type: openapi
    path: 'openapi.yml'
id: sap-material-discount-updated
name: SAP Material Discount Updated
version: 0.0.1
summary: |
  Event that indicates discount data for a material has been updated in SAP ECC 6
owners:
  - enterprise
channels:
  - id: sap-integration-adapter.{env}.events
    parameters:
      env: local
schemaPath: 'schema.json'
---

## Overview

This event is emitted when discount data for a material is updated in SAP ECC 6. It contains essential information about the material's pricing conditions, discounts, and special pricing arrangements.

For the sake of discussion, the key components of the contract are defined as:
- Protocol: HTTP/HTTPS (Defined on Channel)
- Authentication: OAuth2 (Defined on Channel)
- Address: sap-integration-adapter.local.events/material-discount-updated (Defined on Channel)
- Schema: schema.json (in this document)

## SAP ECC 6 Details

### Technical Details
- **Integration Method**: IDoc (COND_A05)
- **SAP Tables**: 
  - KONP (Pricing Conditions)
  - KONH (Pricing Header)
  - KONM (Pricing Scale)
  - KONW (Pricing Scale Values)
  - A304 (Material Discount)
- **Transaction Code**: VK11 (Create Condition)
- **Authorization Object**: V_KONH_VKO (Pricing)

### Business Process
1. **Discount Update Flow**:
   - Pricing condition is created/updated via VK11 transaction
   - IDoc COND_A05 is generated
   - System validates material and sales organization
   - Discount conditions are maintained
   - Scale prices are updated
   - Changes are saved

2. **Key SAP Fields**:
   - MATNR (Material Number)
   - VKORG (Sales Organization)
   - VTWEG (Distribution Channel)
   - KONDA (Condition Type)
   - KONDM (Material Pricing Group)
   - KONWS (Scale Basis)
   - KPEIN (Condition Pricing Unit)
   - KMEIN (Condition Unit)

3. **Integration Points**:
   - Material Master (MM03)
   - Customer Master (XD03)
   - Sales Price (VK11)
   - Pricing Procedure (OVKK)

### Common SAP ECC 6 Considerations
- **Condition Types**:
  - K007: Material Discount
  - K004: Customer Discount
  - K005: Special Price
  - K010: Volume Discount
  - K020: Promotional Discount

- **Pricing Scales**:
  - A: Amount Scale
  - B: Quantity Scale
  - C: Weight Scale
  - D: Volume Scale
  - E: Time Scale

- **Discount Calculation**:
  - Percentage
  - Fixed Amount
  - Scale-based
  - Time-based
  - Customer-specific

### Error Handling
Common SAP ECC 6 errors that may occur:
- **Material Not Found**: Check material master (MM03)
- **Sales Organization Not Found**: Verify sales organization (OVX5)
- **Pricing Error**: Review pricing conditions (VK11)
- **Scale Error**: Check pricing scales (VK12)
- **Authorization Error**: Verify user permissions (SU01)

## Architecture diagram

<NodeGraph/>
