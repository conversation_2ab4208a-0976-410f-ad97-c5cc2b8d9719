---
specifications:
  - type: openapi
    path: 'openapi.yml'
id: sap-material-distribution-classified
name: SAP Material Distribution Classified
version: 0.0.1
summary: |
  Event that indicates distribution classification data for a material has been updated in SAP ECC 6
owners:
  - enterprise
channels:
  - id: sap-integration-adapter.{env}.events
    parameters:
      env: local
schemaPath: 'schema.json'
---

## Overview

This event is emitted when distribution classification data for a material is updated in SAP ECC 6. It contains information about the assignment of distribution channels, sales organizations, and related classification data for a material.

For the sake of discussion, the key components of the contract are defined as:
- Protocol: HTTP/HTTPS (Defined on Channel)
- Authentication: OAuth2 (Defined on Channel)
- Address: sap-integration-adapter.local.events/material-distribution-classified (Defined on Channel)
- Schema: schema.json (in this document)

## SAP ECC 6 Details

### Technical Details
- **BAPI Used**: BAPI_MATERIAL_SAVEDATA
- **SAP Tables**: 
  - MVKE (Sales Data for Material)
  - MVKEH (Sales Data History)
  - TVKOV (Org. Unit: Distribution Channel)
- **Transaction Code**: MM02 (Change Material)
- **Authorization Object**: M_MATE_WRK (Material Master)

### Business Process
1. **Distribution Classification Update Flow**:
   - Distribution channel or sales org data is updated for a material
   - BAPI_MATERIAL_SAVEDATA is called
   - System validates distribution channel assignments
   - Data is saved and changes are logged

2. **Key SAP Fields**:
   - MATNR (Material Number)
   - VKORG (Sales Organization)
   - VTWEG (Distribution Channel)
   - SPART (Division)
   - DWERK (Delivering Plant)
   - MATKL (Material Group)
   - PRDHA (Product Hierarchy)

3. **Integration Points**:
   - Sales and Distribution (SD)
   - Material Master (MM03)
   - Pricing (VK11)

### Common SAP ECC 6 Considerations
- **Distribution Channels**: Ensure valid distribution channels are assigned to the material
- **Sales Org Assignments**: Material must be extended to all relevant sales orgs
- **Material Groupings**: Used for reporting and pricing
- **Availability Dates**: Control when material is available for sales

### Error Handling
Common SAP ECC 6 errors that may occur:
- **Material Not Found**: Check material master (MM03)
- **Invalid Distribution Channel**: Verify channel in TVKOV
- **Authorization Error**: Verify user permissions (SU01)
- **Data Inconsistency**: Check for missing assignments

## Architecture diagram

<NodeGraph/>
