openapi: "3.1.0"
info:
  title: SAP Material Distribution Classified
  version: 0.0.1
  description: Event that indicates distribution classification data for a material has been updated in SAP ECC 6.
servers:
  - url: http://localhost:7600
paths:
  /sap-material-distribution-classified:
    post:
      summary: SAP Material Distribution Classified Event
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SapMaterialDistributionClassifiedEvent'
            example:
              id: b1c2d3e4-5678-1234-9abc-def012345678
              source: SAP ECC 6
              type: sap.material.distribution.updated
              time: '2023-10-18T12:00:00Z'
              data:
                bapi: BAPI_MATERIAL_SAVEDATA
                parameters:
                  HEADDATA:
                    MATERIAL: MAT001
                    SALES_VIEW: X
                  SALESDATA:
                    - SALES_ORG: '1000'
                      DISTR_CHAN: '10'
                      DIVISION: '00'
                      SALES_STATUS: A
                      DEL_FLAG: ''
                      MATL_GROUP_2: '100'
                      MATL_GROUP_3: '200'
                      AVAIL_FROM: '2023-10-01'
                      AVAIL_TO: '2024-12-31'
                  SALESDATAX:
                    - SALES_ORG: '1000'
                      DISTR_CHAN: '10'
                      DIVISION: '00'
                      SALES_STATUS: X
                      DEL_FLAG: ''
                      MATL_GROUP_2: X
                      MATL_GROUP_3: X
                      AVAIL_FROM: X
                      AVAIL_TO: X
                  RETURN:
                    TYPE: S
                    MESSAGE: Material distribution data updated successfully
      responses:
        '200':
          description: Acknowledgement
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
components:
  schemas:
    SapMaterialDistributionClassifiedEvent:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for this event instance
        source:
          type: string
          description: Source system - SAP ECC 6
        type:
          type: string
          enum: [sap.material.distribution.updated]
          description: Material distribution channel availability update event
        time:
          type: string
          format: date-time
          description: Timestamp of when the event occurred
        data:
          type: object
          properties:
            bapi:
              type: string
              enum: [BAPI_MATERIAL_SAVEDATA]
              description: BAPI used for material sales data update
            parameters:
              type: object
              properties:
                HEADDATA:
                  type: object
                  properties:
                    MATERIAL:
                      type: string
                      description: Material number
                    SALES_VIEW:
                      type: string
                      enum: [X, ""]
                      description: Sales data view indicator
                SALESDATA:
                  type: array
                  items:
                    type: object
                    properties:
                      SALES_ORG:
                        type: string
                        description: Sales organization
                      DISTR_CHAN:
                        type: string
                        description: Distribution channel
                      DIVISION:
                        type: string
                        description: Division
                      SALES_STATUS:
                        type: string
                        description: Sales status
                      DEL_FLAG:
                        type: string
                        enum: [X, ""]
                        description: Deletion flag
                      MATL_GROUP_2:
                        type: string
                        description: Material group for sales
                      MATL_GROUP_3:
                        type: string
                        description: Product hierarchy
                      AVAIL_FROM:
                        type: string
                        format: date
                        description: Availability start date
                      AVAIL_TO:
                        type: string
                        format: date
                        description: Availability end date
                SALESDATAX:
                  type: array
                  items:
                    type: object
                    properties:
                      SALES_ORG:
                        type: string
                        description: Sales organization
                      DISTR_CHAN:
                        type: string
                        description: Distribution channel
                      DIVISION:
                        type: string
                        description: Division
                      SALES_STATUS:
                        type: string
                        enum: [X, ""]
                        description: Change indicator for sales status
                      DEL_FLAG:
                        type: string
                        enum: [X, ""]
                        description: Change indicator for deletion flag
                      MATL_GROUP_2:
                        type: string
                        enum: [X, ""]
                        description: Change indicator for material group for sales
                      MATL_GROUP_3:
                        type: string
                        enum: [X, ""]
                        description: Change indicator for product hierarchy
                      AVAIL_FROM:
                        type: string
                        enum: [X, ""]
                        description: Change indicator for availability start date
                      AVAIL_TO:
                        type: string
                        enum: [X, ""]
                        description: Change indicator for availability end date
                RETURN:
                  type: object
                  properties:
                    TYPE:
                      type: string
                      description: Message type: S Success, E Error, W Warning, I Info, A Abort
                    MESSAGE:
                      type: string
                      description: Message text
      required: [id, source, type, time, data] 