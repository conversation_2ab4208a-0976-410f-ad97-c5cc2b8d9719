---
specifications:
  - type: openapi
    path: 'openapi.yml'
id: sap-material-inventory-updated
name: SAP Material Inventory Updated
version: 0.0.1
summary: |
  Event that indicates inventory data for a material has been updated in SAP ECC 6
owners:
  - enterprise
channels:
  - id: sap-integration-adapter.{env}.events
    parameters:
      env: local
schemaPath: 'schema.json'
---

## Overview

This event is emitted when inventory data for a material is updated in SAP ECC 6. It contains essential information about the material's stock levels, storage locations, and inventory movements.

For the sake of discussion, the key components of the contract are defined as:
- Protocol: HTTP/HTTPS (Defined on Channel)
- Authentication: OAuth2 (Defined on Channel)
- Address: sap-integration-adapter.local.events/material-inventory-updated (Defined on Channel)
- Schema: schema.json (in this document)

## SAP ECC 6 Details

### Technical Details
- **Integration Method**: IDoc (MATMAS05)
- **SAP Tables**: 
  - MARD (Material Storage Location)
  - MARC (Material Plant Data)
  - MKOL (Material Special Stock)
  - MSEG (Material Document Segment)
  - MKPF (Material Document Header)
- **Transaction Code**: MIGO (Goods Movement)
- **Authorization Object**: M_MSEG_WMB (Goods Movement)

### Business Process
1. **Inventory Update Flow**:
   - Goods movement is created via MIGO transaction
   - IDoc MATMAS05 is generated
   - System validates material and storage location
   - Stock levels are updated
   - Material document is created
   - Changes are saved

2. **Key SAP Fields**:
   - MATNR (Material Number)
   - WERKS (Plant)
   - LGORT (Storage Location)
   - CHARG (Batch Number)
   - MENGE (Quantity)
   - MEINS (Unit of Measure)
   - BWART (Movement Type)
   - MJAHR (Material Document Year)
   - MBLNR (Material Document Number)

3. **Integration Points**:
   - Material Master (MM03)
   - Warehouse Management (LM01)
   - Quality Management (QA01)
   - Production Planning (CO01)

### Common SAP ECC 6 Considerations
- **Movement Types**:
  - 101: Goods Receipt for Purchase Order
  - 103: Goods Receipt for Production Order
  - 201: Goods Issue for Sales Order
  - 261: Goods Issue for Production Order
  - 309: Transfer Posting
  - 501: Initial Stock Entry

- **Special Stock Indicators**:
  - E: Sales Order Stock
  - K: Customer Consignment
  - O: Project Stock
  - Q: Quality Inspection
  - W: Special Stock with Vendor

- **Stock Types**:
  - Unrestricted Use
  - Quality Inspection
  - Blocked Stock
  - In Transit
  - Consignment

### Error Handling
Common SAP ECC 6 errors that may occur:
- **Material Not Found**: Check material master (MM03)
- **Storage Location Not Found**: Verify storage location (OMJJ)
- **Batch Not Found**: Check batch master (MSC3)
- **Quantity Error**: Verify stock levels (MMBE)
- **Authorization Error**: Check user permissions (SU01)

## Architecture diagram

<NodeGraph/>
