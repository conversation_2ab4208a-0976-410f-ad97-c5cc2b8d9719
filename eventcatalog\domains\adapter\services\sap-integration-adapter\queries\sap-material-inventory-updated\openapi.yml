openapi: "3.1.0"
info:
  title: SAP Material Inventory Updated
  version: 0.0.1
  description: Event that indicates inventory data for a material has been updated in SAP ECC 6.
servers:
  - url: http://localhost:7600
paths:
  /sap-material-inventory-updated:
    post:
      summary: SAP Material Inventory Updated Event
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SapMaterialInventoryUpdatedEvent'
            example:
              id: 3fa85f64-5717-4562-b3fc-2c963f66afa5
              source: SAP_ECC
              type: sap.material.inventory.updated
              time: '2023-10-24T15:20:30Z'
              datacontenttype: application/json
              data:
                idoc:
                  idocNumber: '0000000000123456'
                  idocType: MATMAS05
                  messageType: MATMAS
                  direction: OUTBOUND
                  senderLogicalSystem: SAPECC
                  recipientLogicalSystem: INTEGRATION
                segments:
                  E1MARAM:
                    MATNR: MAT001
                    MTART: FERT
                    MEINS: EA
                  E1MVKEM:
                    - VKORG: '1000'
                      VTWEG: '10'
                      SPART: '00'
                      VMSTA: A
                      VMSTD: '20231024'
                      TAXM1: '1'
      responses:
        '200':
          description: Acknowledgement
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
components:
  schemas:
    SapMaterialInventoryUpdatedEvent:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for this event instance
        source:
          type: string
          enum: [SAP_ECC]
          description: Source system that emitted the event
        type:
          type: string
          enum: [sap.material.inventory.updated]
          description: Type of event - inventory quantity update
        time:
          type: string
          format: date-time
          description: Timestamp when the event occurred
        datacontenttype:
          type: string
          enum: [application/json]
          description: Content type of the data payload
        data:
          type: object
          properties:
            idoc:
              type: object
              properties:
                idocNumber:
                  type: string
                  description: IDoc number for tracking
                idocType:
                  type: string
                  enum: [MATMAS05]
                  description: IDoc type
                messageType:
                  type: string
                  enum: [MATMAS]
                  description: Message type
                direction:
                  type: string
                  enum: [OUTBOUND]
                  description: Direction of the IDoc
                senderLogicalSystem:
                  type: string
                  description: Logical system that sent the IDoc
                recipientLogicalSystem:
                  type: string
                  description: Logical system that received the IDoc
              required: [idocNumber, idocType, messageType, direction]
            segments:
              type: object
              properties:
                E1MARAM:
                  type: object
                  properties:
                    MATNR:
                      type: string
                      description: Material number
                    MTART:
                      type: string
                      description: Material type
                    MEINS:
                      type: string
                      description: Base unit of measure
                  required: [MATNR]
                E1MVKEM:
                  type: array
                  items:
                    type: object
                    properties:
                      VKORG:
                        type: string
                        description: Sales organization
                      VTWEG:
                        type: string
                        description: Distribution channel
                      SPART:
                        type: string
                        description: Division
                      VMSTA:
                        type: string
                        description: Sales status
                      VMSTD:
                        type: string
                        description: Valid from date for sales status
                      TAXM1:
                        type: string
                        description: Tax classification
                    required: [VKORG, VTWEG, SPART]
              required: [E1MARAM, E1MVKEM]
          required: [idoc, segments]
      required: [id, source, type, time, datacontenttype, data] 