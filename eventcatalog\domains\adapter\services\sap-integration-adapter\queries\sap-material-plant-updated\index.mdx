---
specifications:
  - type: openapi
    path: 'openapi.yml'
id: sap-material-plant-updated
name: SAP Material Plant Updated
version: 0.0.1
summary: |
  Event that indicates plant data for a material has been updated in SAP ECC 6
owners:
  - enterprise
channels:
  - id: sap-integration-adapter.{env}.events
    parameters:
      env: local
schemaPath: 'schema.json'
---

## Overview

This event is emitted when plant data for a material is updated in SAP ECC 6. It contains essential information about the material's plant-specific data, including MRP, storage, and production data.

For the sake of discussion, the key components of the contract are defined as:
- Protocol: HTTP/HTTPS (Defined on Channel)
- Authentication: OAuth2 (Defined on Channel)
- Address: sap-integration-adapter.local.events/material-plant-updated (Defined on Channel)
- Schema: schema.json (in this document)

## SAP ECC 6 Details

### Technical Details
- **Integration Method**: IDoc (MATMAS05)
- **SAP Tables**: 
  - MARC (Material Plant Data)
  - MARD (Material Storage Location)
  - MAST (Material to BOM Link)
  - MAPL (Material to Task List)
  - MARC (Material Plant Data)
- **Transaction Code**: MM02 (Change Material)
- **Authorization Object**: M_MATE_WRK (Material Master)

### Business Process
1. **Plant Data Update Flow**:
   - Material plant data is updated via MM02 transaction
   - IDoc MATMAS05 is generated
   - System validates plant and storage location
   - MRP data is maintained
   - Storage data is updated
   - Changes are saved

2. **Key SAP Fields**:
   - MATNR (Material Number)
   - WERKS (Plant)
   - LGORT (Storage Location)
   - DISMM (MRP Type)
   - DISGR (MRP Group)
   - DISPO (MRP Controller)
   - BESKZ (Procurement Type)
   - SOBSL (Special Procurement)

3. **Integration Points**:
   - Material Master (MM03)
   - Production Planning (CO01)
   - Warehouse Management (LM01)
   - Quality Management (QA01)

### Common SAP ECC 6 Considerations
- **MRP Types**:
  - PD: MRP
  - ND: No MRP
  - VB: Reorder Point
  - V1: Forecast-based
  - V2: Time-phased

- **Procurement Types**:
  - E: In-house Production
  - F: External Procurement
  - X: Both
  - Blank: Not Relevant

- **Storage Location Types**:
  - 0001: Standard Storage
  - 0002: Quality Inspection
  - 0003: Blocked Stock
  - 0004: Returns

### Error Handling
Common SAP ECC 6 errors that may occur:
- **Plant Not Found**: Check plant configuration (OX10)
- **Storage Location Not Found**: Verify storage location (OMJJ)
- **MRP Controller Not Found**: Check MRP controller (OMD4)
- **Procurement Type Error**: Verify procurement type (OMD4)
- **Authorization Error**: Check user permissions (SU01)

## Architecture diagram

<NodeGraph/>
