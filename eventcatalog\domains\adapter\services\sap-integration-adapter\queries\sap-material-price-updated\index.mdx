---
specifications:
  - type: openapi
    path: 'openapi.yml'
id: sap-material-price-updated
name: SAP Material Price Updated
version: 0.0.1
summary: |
  Event that indicates price data for a material has been updated in SAP ECC 6
owners:
  - enterprise
channels:
  - id: sap-integration-adapter.{env}.events
    parameters:
      env: local
schemaPath: 'schema.json'
---

## Overview

This event is emitted when price data for a material is updated in SAP ECC 6. It contains essential information about the material's pricing data, including standard price, moving average price, and price components.

For the sake of discussion, the key components of the contract are defined as:
- Protocol: HTTP/HTTPS (Defined on Channel)
- Authentication: OAuth2 (Defined on Channel)
- Address: sap-integration-adapter.local.events/material-price-updated (Defined on Channel)
- Schema: schema.json (in this document)

## SAP ECC 6 Details

### Technical Details
- **Integration Method**: IDoc (COND_A05)
- **SAP Tables**: 
  - MBEW (Material Valuation)
  - MBEWH (Material Valuation History)
  - KONP (Pricing Conditions)
  - KONH (Pricing Header)
  - KONM (Pricing Scale)
- **Transaction Code**: MM02 (Change Material)
- **Authorization Object**: M_MATE_WRK (Material Master)

### Business Process
1. **Price Update Flow**:
   - Material price is updated via MM02 transaction
   - IDoc COND_A05 is generated
   - System validates price components
   - Price updates are maintained
   - Cost center assignments are updated
   - Changes are saved

2. **Key SAP Fields**:
   - MATNR (Material Number)
   - BWKEY (Valuation Area)
   - BWTAR (Valuation Type)
   - STPRS (Standard Price)
   - PEINH (Price Unit)
   - VPRSV (Price Control)
   - MLAST (Last Price Change)
   - ZPLP1 (Future Price)

3. **Integration Points**:
   - Material Master (MM03)
   - Cost Center Accounting (KS01)
   - Product Cost Planning (CK11N)
   - Profitability Analysis (KE30)

### Common SAP ECC 6 Considerations
- **Price Controls**:
  - S: Standard Price
  - V: Moving Average Price
  - Blank: Not Relevant

- **Price Components**:
  - 1: Material
  - 2: Production
  - 3: Overhead
  - 4: External Processing
  - 5: Other

- **Valuation Types**:
  - 1: Standard
  - 2: Moving Average
  - 3: LIFO
  - 4: FIFO

### Error Handling
Common SAP ECC 6 errors that may occur:
- **Material Not Found**: Check material master (MM03)
- **Cost Center Not Found**: Verify cost center (KS01)
- **Price Control Error**: Check price control (OMT4)
- **Valuation Error**: Review valuation type (OMT4)
- **Authorization Error**: Verify user permissions (SU01)

## Architecture diagram

<NodeGraph/>


 