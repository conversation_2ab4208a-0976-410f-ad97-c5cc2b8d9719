openapi: "3.1.0"
info:
  title: SAP Material Price Updated
  version: 0.0.1
  description: Event that indicates price data for a material has been updated in SAP ECC 6.
servers:
  - url: http://localhost:7600
paths:
  /sap-material-price-updated:
    post:
      summary: SAP Material Price Updated Event
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SapMaterialPriceUpdatedEvent'
            example:
              id: 7fa85f64-5717-4562-b3fc-2c963f66afa9
              source: SAP_ECC
              type: sap.material.price.updated
              time: '2023-10-18T11:25:30Z'
              datacontenttype: application/json
              data:
                idoc:
                  idocNumber: '0000000000123456'
                  idocType: COND_A05
                  messageType: COND_A
                  direction: OUTBOUND
                  senderLogicalSystem: SAPECC
                  recipientLogicalSystem: INTEGRATION
                segments:
                  E1KOMG:
                    KSCHL: PR00
                    MATNR: MAT001
                    VKORG: '1000'
                    VTWEG: '10'
                    SPART: '00'
                  E1KONH:
                    DATAB: '20231018'
                    DATBI: '99991231'
                    KNUMH: '0000123456'
                  E1KONP:
                    KBETR: '130.75'
                    KONWA: USD
                    KPEIN: '1'
                    KMEIN: EA
      responses:
        '200':
          description: Acknowledgement
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
components:
  schemas:
    SapMaterialPriceUpdatedEvent:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for this event instance
        source:
          type: string
          enum: [SAP_ECC]
          description: Source system that emitted the event
        type:
          type: string
          enum: [sap.material.price.updated]
          description: Type of event - sales price update
        time:
          type: string
          format: date-time
          description: Timestamp when the event occurred
        datacontenttype:
          type: string
          enum: [application/json]
          description: Content type of the data payload
        data:
          type: object
          properties:
            idoc:
              type: object
              properties:
                idocNumber:
                  type: string
                  description: IDoc number for tracking
                idocType:
                  type: string
                  enum: [COND_A05]
                  description: IDoc type
                messageType:
                  type: string
                  enum: [COND_A]
                  description: Message type
                direction:
                  type: string
                  enum: [OUTBOUND]
                  description: Direction of the IDoc
                senderLogicalSystem:
                  type: string
                  description: Logical system that sent the IDoc
                recipientLogicalSystem:
                  type: string
                  description: Logical system that received the IDoc
              required: [idocNumber, idocType, messageType, direction]
            segments:
              type: object
              properties:
                E1KOMG:
                  type: object
                  properties:
                    KSCHL:
                      type: string
                      description: Condition type (e.g., PR00 for price)
                    MATNR:
                      type: string
                      description: Material number
                    VKORG:
                      type: string
                      description: Sales organization
                    VTWEG:
                      type: string
                      description: Distribution channel
                    SPART:
                      type: string
                      description: Division
                  required: [KSCHL, MATNR]
                E1KONH:
                  type: object
                  properties:
                    DATAB:
                      type: string
                      description: Valid from date
                    DATBI:
                      type: string
                      description: Valid to date
                    KNUMH:
                      type: string
                      description: Condition record number
                  required: [DATAB, KNUMH]
                E1KONP:
                  type: object
                  properties:
                    KBETR:
                      type: string
                      description: Condition amount
                    KONWA:
                      type: string
                      description: Condition currency
                    KPEIN:
                      type: string
                      description: Condition pricing unit
                    KMEIN:
                      type: string
                      description: Condition unit
                  required: [KBETR, KONWA]
              required: [E1KOMG, E1KONH, E1KONP]
          required: [idoc, segments]
      required: [id, source, type, time, datacontenttype, data] 