openapi: "3.1.0"
info:
  title: SAP Material Sales Updated
  version: 0.0.1
  description: Event that indicates sales data for a material has been updated in SAP ECC 6.
servers:
  - url: http://localhost:7600
paths:
  /sap-material-sales-updated:
    post:
      summary: SAP Material Sales Updated Event
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SapMaterialSalesUpdatedEvent'
            example:
              id: 4fa85f64-5717-4562-b3fc-2c963f66afa6
              source: SAP_ECC
              type: sap.material.sales.updated
              time: '2023-10-25T16:40:30Z'
              datacontenttype: application/json
              data:
                idoc:
                  idocNumber: '0000000000123456'
                  idocType: MATMAS05
                  messageType: MATMAS
                  direction: OUTBOUND
                  senderLogicalSystem: SAPECC
                  recipientLogicalSystem: INTEGRATION
                segments:
                  E1MARAM:
                    MATNR: MAT001
                    MTART: FERT
                    MEINS: EA
                  E1MVKEM:
                    - VKORG: '1000'
                      VTWEG: '10'
                      SPART: '00'
                      VMSTA: A
                      VMSTD: '20231025'
                      MVGR1: '100'
                      MVGR2: '200'
                      MVGR3: '300'
                      MVGR4: '400'
                      MVGR5: '500'
                      PRODH: '00100200'
      responses:
        '200':
          description: Acknowledgement
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
components:
  schemas:
    SapMaterialSalesUpdatedEvent:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for this event instance
        source:
          type: string
          enum: [SAP_ECC]
          description: Source system that emitted the event
        type:
          type: string
          enum: [sap.material.sales.updated]
          description: Type of event - sales-relevant material attributes update
        time:
          type: string
          format: date-time
          description: Timestamp when the event occurred
        datacontenttype:
          type: string
          enum: [application/json]
          description: Content type of the data payload
        data:
          type: object
          properties:
            idoc:
              type: object
              properties:
                idocNumber:
                  type: string
                  description: IDoc number for tracking
                idocType:
                  type: string
                  enum: [MATMAS05]
                  description: IDoc type
                messageType:
                  type: string
                  enum: [MATMAS]
                  description: Message type
                direction:
                  type: string
                  enum: [OUTBOUND]
                  description: Direction of the IDoc
                senderLogicalSystem:
                  type: string
                  description: Logical system that sent the IDoc
                recipientLogicalSystem:
                  type: string
                  description: Logical system that received the IDoc
              required: [idocNumber, idocType, messageType, direction]
            segments:
              type: object
              properties:
                E1MARAM:
                  type: object
                  properties:
                    MATNR:
                      type: string
                      description: Material number
                    MTART:
                      type: string
                      description: Material type
                    MEINS:
                      type: string
                      description: Base unit of measure
                  required: [MATNR]
                E1MVKEM:
                  type: array
                  items:
                    type: object
                    properties:
                      VKORG:
                        type: string
                        description: Sales organization
                      VTWEG:
                        type: string
                        description: Distribution channel
                      SPART:
                        type: string
                        description: Division
                      VMSTA:
                        type: string
                        description: Sales status
                      VMSTD:
                        type: string
                        description: Valid from date for sales status
                      MVGR1:
                        type: string
                        description: Material group 1
                      MVGR2:
                        type: string
                        description: Material group 2
                      MVGR3:
                        type: string
                        description: Material group 3
                      MVGR4:
                        type: string
                        description: Material group 4
                      MVGR5:
                        type: string
                        description: Material group 5
                      PRODH:
                        type: string
                        description: Product hierarchy
                    required: [VKORG, VTWEG, SPART]
              required: [E1MARAM, E1MVKEM]
          required: [idoc, segments]
      required: [id, source, type, time, datacontenttype, data] 