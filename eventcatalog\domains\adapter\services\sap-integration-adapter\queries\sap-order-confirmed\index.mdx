---
specifications:
  - type: openapi
    path: 'openapi.yml'
id: sap-order-confirmed
name: SAP Order Confirmed
version: 0.0.1
summary: |
  Event that indicates a sales order has been confirmed in SAP ECC 6
owners:
  - enterprise
channels:
  - id: sap-integration-adapter.{env}.events
    parameters:
      env: local
schemaPath: 'schema.json'
---

## Overview

This event is emitted when a sales order is confirmed in SAP ECC 6. It contains essential information about the order's confirmation process, including availability checks, pricing, and credit management.

For the sake of discussion, the key components of the contract are defined as:
- Protocol: HTTP/HTTPS (Defined on Channel)
- Authentication: OAuth2 (Defined on Channel)
- Address: sap-integration-adapter.local.events/order-confirmed (Defined on Channel)
- Schema: schema.json (in this document)

## SAP ECC 6 Details

### Technical Details
- **BAPI Used**: BAPI_SALESORDER_CREATE
- **SAP Tables**: 
  - VBAK (Sales Document Header)
  - VBAP (Sales Document Item)
  - VBKD (Business Data)
  - VBPA (Partner Functions)
  - VBFA (Sales Document Flow)
- **Transaction Code**: VA01 (Create Sales Order)
- **Authorization Object**: V_VBAK_VKO (Sales Document Header)

### Business Process
1. **Order Confirmation Flow**:
   - Sales order is created via VA01 transaction
   - BAPI_SALESORDER_CREATE is called
   - System performs availability check
   - Pricing is determined
   - Credit check is performed
   - Order is confirmed

2. **Key SAP Fields**:
   - VBELN (Sales Document)
   - POSNR (Item Number)
   - MATNR (Material)
   - KWMENG (Order Quantity)
   - VRKME (Sales Unit)
   - NETWR (Net Value)
   - WAERK (Currency)
   - BSTKD (Customer PO)

3. **Integration Points**:
   - Sales Order (VA03)
   - Material Master (MM03)
   - Customer Master (XD03)
   - Credit Management (FD32)

### Common SAP ECC 6 Considerations
- **Order Types**:
  - OR: Standard Order
  - CR: Credit Order
  - RE: Return Order
  - ZR: Rush Order
  - ZS: Sample Order

- **Availability Checks**:
  - 01: No Check
  - 02: ATP Check
  - 03: MRP Check
  - 04: Production Check
  - 05: Custom Check

- **Pricing Types**:
  - A: Standard Price
  - B: Customer Price
  - C: Contract Price
  - D: Special Price
  - E: Promotional Price

### Error Handling
Common SAP ECC 6 errors that may occur:
- **Material Not Found**: Check material master (MM03)
- **Customer Not Found**: Verify customer master (XD03)
- **Price Not Found**: Check pricing condition (VK11)
- **Credit Limit Exceeded**: Review credit limit (FD32)
- **Authorization Error**: Verify user permissions (SU01)

## Architecture diagram

<NodeGraph/>

