openapi: "3.1.0"
info:
  title: SAP Order Confirmed Event API
  version: 0.0.1
  description: |
    Event that indicates a sales order has been confirmed in SAP ECC 6. Contains information about the order's confirmation process, including availability checks, pricing, and credit management.
servers:
  - url: http://localhost:7600
paths:
  /sap-order-confirmed:
    post:
      summary: SAP Order Confirmed Event
      operationId: sapOrderConfirmed
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SapOrderConfirmedEvent'
            example:
              id: "4fa85f64-5717-4562-b3fc-2c963f66afa7"
              source: "SAP ECC 6"
              type: "sap.order.confirmed"
              time: "2023-10-16T09:45:30Z"
              data:
                bapi: "BAPI_SALESORDER_CONFIRM"
                salesOrderNumber: "0000123456"
                customerNumber: "0001000123"
                confirmationNumber: "0000054321"
                confirmationDate: "2023-10-16"
                confirmedBy: "SAPUSER"
                confirmedAt: "2023-10-16T09:45:00Z"
                deliveryDate: "2023-10-25"
                deliveryBlock: false
                billingBlock: false
                items:
                  - itemNumber: "000010"
                    materialNumber: "MAT001"
                    confirmedQuantity: 5
                    unit: "EA"
                    plant: "1000"
                    scheduleLine:
                      - scheduleLineNumber: "0001"
                        confirmedQuantity: 5
                        deliveryDate: "2023-10-25"
                        availabilityConfirmed: true
                partners:
                  - partnerFunction: "SP"
                    partnerNumber: "0001000456"
                    partnerName: "Shipping Partner Inc."
      responses:
        '200':
          description: Event accepted
components:
  schemas:
    SapOrderConfirmedEvent:
      type: object
      required:
        - id
        - source
        - type
        - time
        - data
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for this event instance
        source:
          type: string
          description: Source system that emitted the event
        type:
          type: string
          description: Type of event - sales order confirmation
        time:
          type: string
          format: date-time
          description: Timestamp when the event occurred
        data:
          type: object
          description: Event payload with confirmation details
          properties:
            bapi:
              type: string
              description: BAPI used for the confirmation
            salesOrderNumber:
              type: string
              description: Sales order number
            customerNumber:
              type: string
              description: Customer number
            confirmationNumber:
              type: string
              description: Confirmation number
            confirmationDate:
              type: string
              description: Confirmation date
            confirmedBy:
              type: string
              description: User who confirmed the order
            confirmedAt:
              type: string
              format: date-time
              description: When the order was confirmed
            deliveryDate:
              type: string
              description: Delivery date
            deliveryBlock:
              type: boolean
              description: Delivery block status
            billingBlock:
              type: boolean
              description: Billing block status
            items:
              type: array
              description: List of items in the order
              items:
                type: object
                properties:
                  itemNumber:
                    type: string
                  materialNumber:
                    type: string
                  confirmedQuantity:
                    type: number
                  unit:
                    type: string
                  plant:
                    type: string
                  scheduleLine:
                    type: array
                    items:
                      type: object
                      properties:
                        scheduleLineNumber:
                          type: string
                        confirmedQuantity:
                          type: number
                        deliveryDate:
                          type: string
                        availabilityConfirmed:
                          type: boolean
            partners:
              type: array
              description: List of partners
              items:
                type: object
                properties:
                  partnerFunction:
                    type: string
                  partnerNumber:
                    type: string
                  partnerName:
                    type: string 