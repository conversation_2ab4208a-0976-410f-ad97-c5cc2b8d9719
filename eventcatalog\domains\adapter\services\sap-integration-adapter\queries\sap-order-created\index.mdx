---
specifications:
  - type: openapi
    path: 'openapi.yml'
id: sap-order-created
name: SAP Order Created
version: 0.0.1
summary: Event that indicates a sales order has been created in SAP ECC 6
owners:
  - enterprise
channels:
  - id: sap-integration-adapter.{env}.events
    parameters:
      env: local
schemaPath: 'schema.json'
---

## Overview

This event is emitted when a new sales order is created in SAP ECC 6. It contains essential information about the order, including header data, item details, partner information, and pricing.

For the sake of discussion, the key components of the contract are defined as:
- Protocol: HTTP/HTTPS (Defined on Channel)
- Authentication: OAuth2 (Defined on Channel)
- Address: sap-integration-adapter.local.events/order-created (Defined on Channel)
- Schema: schema.json (in this document)

## SAP ECC 6 Details

### Technical Details
- **BAPI Used**: BAPI_SALESORDER_CREATEFROMDAT2
- **SAP Tables**: 
  - VBAK (Sales Document Header)
  - VBAP (Sales Document Item)
  - VBPA (Sales Document Partner)
  - VBKD (Sales Document Business Data)
  - KONV (Conditions)
- **Transaction Code**: VA01 (Create Sales Order)
- **Authorization Object**: V_VBAK_VKO (Sales Document Header)

### Business Process
1. **Order Creation Flow**:
   - Sales order is created via VA01 transaction
   - BAPI_SALESORDER_CREATEFROMDAT2 is called
   - System performs availability check (ATP)
   - Credit check is executed
   - Pricing is calculated
   - Order is saved and number is assigned

2. **Key SAP Fields**:
   - VKORG (Sales Organization)
   - VTWEG (Distribution Channel)
   - SPART (Division)
   - AUART (Order Type)
   - KUNNR (Customer Number)
   - MATNR (Material Number)

3. **Integration Points**:
   - Material Master (MM03)
   - Customer Master (XD03)
   - Pricing (VK11)
   - Credit Management (FD32)

### Common SAP ECC 6 Considerations
- **Order Types**:
  - OR: Standard Order
  - TA: Cash Sale
  - RE: Returns
  - ZOR: Custom Order Type

- **Partner Functions**:
  - SP: Ship-to Party
  - BP: Bill-to Party
  - PY: Payer
  - SH: Ship-from Party

- **Item Categories**:
  - TAN: Standard Item
  - TANN: Free of Charge
  - TAS: Text Item
  - TAF: Service Item

### Error Handling
Common SAP ECC 6 errors that may occur:
- **Material Not Found**: Check material master (MM03)
- **Customer Not Found**: Verify customer master (XD03)
- **Pricing Error**: Review pricing conditions (VK11)
- **Credit Limit Exceeded**: Check credit management (FD32)
- **ATP Check Failed**: Review material availability (CO09)

## Architecture diagram

<NodeGraph/>
