openapi: "3.1.0"
info:
  title: SAP Order Created Event API
  version: 0.0.1
  description: |
    Event that indicates a sales order has been created in SAP ECC 6. Contains essential information about the order, including header data, item details, partner information, and pricing.
servers:
  - url: http://localhost:7600
paths:
  /sap-order-created:
    post:
      summary: SAP Order Created Event
      operationId: sapOrderCreated
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SapOrderCreatedEvent'
            example:
              id: "4fa85f64-5717-4562-b3fc-2c963f66afa6"
              source: "SAP ECC 6"
              type: "sap.order.created"
              time: "2023-10-25T16:30:30Z"
              data:
                bapi: "BAPI_SALESORDER_CREATEFROMDAT2"
                salesOrderNumber: "0000123457"
                orderType: "OR"
                createdBy: "SAPUSER"
                createdAt: "2023-10-25T16:30:00Z"
                headerData:
                  purchaseOrderNumber: "PO12345"
                  purchaseOrderDate: "2023-10-24"
                  requestedDeliveryDate: "2023-11-05"
                  salesOrg: "1000"
                  distributionChannel: "10"
                  division: "00"
                  salesGroup: "100"
                  salesOffice: "200"
                  paymentTerms: "0001"
                  incoterms1: "CIF"
                  incoterms2: "NEW YORK"
                  pricingDate: "2023-10-25"
                  orderReason: "001"
                  completeDelivery: "X"
                  shippingCondition: "01"
                partners:
                  - partnerFunction: "SP"
                    partnerNumber: "1000100"
                    partnerName: "ABC Corporation"
                    partnerType: "KU"
                    address:
                      street: "123 Main St"
                      city: "Anytown"
                      postalCode: "12345"
                      country: "US"
                      region: "CA"
                      contactPerson: "John Doe"
                      phoneNumber: "******-123-4567"
                      email: "<EMAIL>"
                  - partnerFunction: "SH"
                    partnerNumber: "1000101"
                    partnerName: "ABC Warehouse"
                    partnerType: "KU"
                    address:
                      street: "456 Warehouse Blvd"
                      city: "Anytown"
                      postalCode: "12345"
                      country: "US"
                      region: "CA"
                      contactPerson: "Jane Smith"
                      phoneNumber: "******-987-6543"
                      email: "<EMAIL>"
                  - partnerFunction: "BP"
                    partnerNumber: "1000100"
                    partnerName: "ABC Corporation"
                    partnerType: "KU"
                  - partnerFunction: "PY"
                    partnerNumber: "1000100"
                    partnerName: "ABC Corporation"
                    partnerType: "KU"
                items:
                  - itemNumber: "000010"
                    materialNumber: "MAT001"
                    description: "Finished Product XYZ"
                    orderQuantity: 10
                    salesUnit: "EA"
                    netPrice: 110.00
                    targetQuantity: 10
                    targetUnit: "EA"
                    plant: "1000"
                    storageLocation: "0001"
                    deliveryGroup: "1"
                    itemCategory: "TAN"
                    rejectionReason: ""
                    pricing:
                      - conditionType: "PR00"
                        conditionDescription: "Standard Price"
                        amount: 110.00
                        currency: "USD"
                      - conditionType: "K007"
                        conditionDescription: "Material discount"
                        amount: -11.00
                        currency: "USD"
                  - itemNumber: "000020"
                    materialNumber: "MAT002"
                    description: "New Finished Product ABC"
                    orderQuantity: 5
                    salesUnit: "EA"
                    netPrice: 55.00
                    targetQuantity: 5
                    targetUnit: "EA"
                    plant: "1000"
                    storageLocation: "0001"
                    deliveryGroup: "1"
                    itemCategory: "TAN"
                    rejectionReason: ""
                    pricing:
                      - conditionType: "PR00"
                        conditionDescription: "Standard Price"
                        amount: 55.00
                        currency: "USD"
                return:
                  - type: "S"
                    message: "Sales order 0000123457 created successfully"
      responses:
        '200':
          description: Event accepted
components:
  schemas:
    SapOrderCreatedEvent:
      type: object
      required:
        - id
        - source
        - type
        - time
        - data
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for this event instance
        source:
          type: string
          description: Source system that emitted the event
        type:
          type: string
          description: Type of event - sales order creation
        time:
          type: string
          format: date-time
          description: Timestamp when the event occurred
        data:
          type: object
          description: Event payload with order creation details
          properties:
            bapi:
              type: string
              description: BAPI used for the creation
            salesOrderNumber:
              type: string
              description: Sales order number
            orderType:
              type: string
              description: Order type
            createdBy:
              type: string
              description: User who created the order
            createdAt:
              type: string
              format: date-time
              description: When the order was created
            headerData:
              type: object
              properties:
                purchaseOrderNumber:
                  type: string
                purchaseOrderDate:
                  type: string
                requestedDeliveryDate:
                  type: string
                salesOrg:
                  type: string
                distributionChannel:
                  type: string
                division:
                  type: string
                salesGroup:
                  type: string
                salesOffice:
                  type: string
                paymentTerms:
                  type: string
                incoterms1:
                  type: string
                incoterms2:
                  type: string
                pricingDate:
                  type: string
                orderReason:
                  type: string
                completeDelivery:
                  type: string
                shippingCondition:
                  type: string
            partners:
              type: array
              description: List of partners
              items:
                type: object
                properties:
                  partnerFunction:
                    type: string
                  partnerNumber:
                    type: string
                  partnerName:
                    type: string
                  partnerType:
                    type: string
                  address:
                    type: object
                    properties:
                      street:
                        type: string
                      city:
                        type: string
                      postalCode:
                        type: string
                      country:
                        type: string
                      region:
                        type: string
                      contactPerson:
                        type: string
                      phoneNumber:
                        type: string
                      email:
                        type: string
            items:
              type: array
              description: List of items in the order
              items:
                type: object
                properties:
                  itemNumber:
                    type: string
                  materialNumber:
                    type: string
                  description:
                    type: string
                  orderQuantity:
                    type: number
                  salesUnit:
                    type: string
                  netPrice:
                    type: number
                  targetQuantity:
                    type: number
                  targetUnit:
                    type: string
                  plant:
                    type: string
                  storageLocation:
                    type: string
                  deliveryGroup:
                    type: string
                  itemCategory:
                    type: string
                  rejectionReason:
                    type: string
                  pricing:
                    type: array
                    items:
                      type: object
                      properties:
                        conditionType:
                          type: string
                        conditionDescription:
                          type: string
                        amount:
                          type: number
                        currency:
                          type: string
            return:
              type: array
              description: Return messages
              items:
                type: object
                properties:
                  type:
                    type: string
                  message:
                    type: string 