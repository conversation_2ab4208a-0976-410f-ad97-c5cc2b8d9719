---
specifications:
  - type: openapi
    path: 'openapi.yml'
id: sap-order-pickpack-updated
name: SAP Order Pickpack Updated
version: 0.0.1
summary: |
  Event that indicates the pick and pack status of a sales order has been updated in SAP ECC 6
owners:
  - enterprise
channels:
  - id: sap-integration-adapter.{env}.events
    parameters:
      env: local
schemaPath: 'schema.json'
---

## Overview

This event is emitted when the pick and pack status of a sales order is updated in SAP ECC 6. It contains essential information about the order's picking and packing process, including warehouse operations, handling units, and shipping units.

For the sake of discussion, the key components of the contract are defined as:
- Protocol: HTTP/HTTPS (Defined on Channel)
- Authentication: OAuth2 (Defined on Channel)
- Address: sap-integration-adapter.local.events/order-pickpack-updated (Defined on Channel)
- Schema: schema.json (in this document)

## SAP ECC 6 Details

### Technical Details
- **BAPI Used**: BAPI_HU_PACK
- **SAP Tables**: 
  - VEKP (Handling Unit Header)
  - VEPO (Handling Unit Item)
  - LIPS (Delivery Item)
  - LIKP (Delivery Header)
  - VHUP (HU Packing)
- **Transaction Code**: VL02N (Change Delivery)
- **Authorization Object**: V_LIKP_VKO (Delivery Header)

### Business Process
1. **Pick and Pack Update Flow**:
   - Delivery document is updated via VL02N transaction
   - BAPI_HU_PACK is called
   - System validates handling units
   - Picking status is maintained
   - Packing data is updated
   - Changes are saved

2. **Key SAP Fields**:
   - VBELN (Delivery Document)
   - POSNR (Item Number)
   - EXIDV (External HU Number)
   - VENUM (Internal HU Number)
   - VEPOS (HU Item)
   - VEMNG (Picked Quantity)
   - VEMEH (Unit of Measure)
   - WERKS (Plant)

3. **Integration Points**:
   - Warehouse Management (LM01)
   - Delivery (VL03)
   - Handling Unit Management (HU01)
   - Shipping (VT01)

### Common SAP ECC 6 Considerations
- **Handling Unit Types**:
  - 1: Standard HU
  - 2: Transport HU
  - 3: Storage HU
  - 4: Mixed HU
  - 5: Return HU

- **Picking Statuses**:
  - A: Not Started
  - B: Partially Picked
  - C: Completely Picked
  - D: Partially Packed
  - E: Completely Packed

- **Packing Types**:
  - BOX: Standard Box
  - PALLET: Standard Pallet
  - CART: Carton
  - BAG: Bag
  - CUSTOM: Custom Packing

### Error Handling
Common SAP ECC 6 errors that may occur:
- **Delivery Not Found**: Check delivery document (VL03)
- **HU Not Found**: Verify handling unit (HU03)
- **Quantity Error**: Check picking quantities (LM03)
- **Authorization Error**: Verify user permissions (SU01)
- **Warehouse Error**: Check warehouse configuration (OMJJ)

## Architecture diagram

<NodeGraph/>
