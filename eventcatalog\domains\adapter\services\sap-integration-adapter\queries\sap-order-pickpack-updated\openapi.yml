openapi: "3.1.0"
info:
  title: SAP Order Pickpack Updated Event API
  version: 0.0.1
  description: |
    Event that indicates the pick and pack status of a sales order has been updated in SAP ECC 6. Contains information about the order's picking and packing process, including warehouse operations, handling units, and shipping units.
servers:
  - url: http://localhost:7600
paths:
  /sap-order-pickpack-updated:
    post:
      summary: SAP Order Pickpack Updated Event
      operationId: sapOrderPickpackUpdated
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SapOrderPickpackUpdatedEvent'
            example:
              id: "1fa85f64-5717-4562-b3fc-2c963f66afa3"
              source: "SAP ECC 6"
              type: "sap.order.pickpack.updated"
              time: "2023-10-22T11:30:30Z"
              data:
                bapi: "BAPI_DELIVERY_CHANGE"
                salesOrderNumber: "0000123456"
                deliveryNumber: "0000054321"
                updatedBy: "SAPUSER"
                updatedAt: "2023-10-22T11:30:00Z"
                warehouseNumber: "001"
                warehouseDescription: "Main Warehouse"
                pickingStatus: "C"
                pickingStatusDescription: "Completed"
                previousPickingStatus: "P"
                previousPickingStatusDescription: "In Process"
                pickingCompletedAt: "2023-10-22T11:25:00Z"
                pickingCompletedBy: "WHUSER1"
                packingStatus: "C"
                packingStatusDescription: "Completed"
                previousPackingStatus: "P"
                previousPackingStatusDescription: "In Process"
                packingCompletedAt: "2023-10-22T11:28:00Z"
                packingCompletedBy: "WHUSER2"
                items:
                  - itemNumber: "000010"
                    materialNumber: "MAT001"
                    description: "Finished Product XYZ"
                    orderedQuantity: 5
                    pickedQuantity: 5
                    packedQuantity: 5
                    unit: "EA"
                    storageLocation: "0001"
                    storageType: "001"
                    storageBin: "A-01-01"
                    batchNumber: "BATCH123"
                    serialNumbers:
                      - "SN00001"
                      - "SN00002"
                      - "SN00003"
                      - "SN00004"
                      - "SN00005"
                handlingUnits:
                  - handlingUnitNumber: "HU00001"
                    packingMaterial: "CARTON-L"
                    grossWeight: 12.5
                    netWeight: 10.5
      responses:
        '200':
          description: Event accepted
components:
  schemas:
    SapOrderPickpackUpdatedEvent:
      type: object
      required:
        - id
        - source
        - type
        - time
        - data
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for this event instance
        source:
          type: string
          description: Source system that emitted the event
        type:
          type: string
          description: Type of event - picking and packing update
        time:
          type: string
          format: date-time
          description: Timestamp when the event occurred
        data:
          type: object
          description: Event payload with picking and packing details
          properties:
            bapi:
              type: string
              description: BAPI used for the update
            salesOrderNumber:
              type: string
              description: Sales order number
            deliveryNumber:
              type: string
              description: Delivery document number
            updatedBy:
              type: string
              description: User who performed the update
            updatedAt:
              type: string
              format: date-time
              description: When the update was performed
            warehouseNumber:
              type: string
              description: Warehouse number
            warehouseDescription:
              type: string
              description: Warehouse description
            pickingStatus:
              type: string
              description: Picking status
            pickingStatusDescription:
              type: string
              description: Description of picking status
            previousPickingStatus:
              type: string
              description: Previous picking status
            previousPickingStatusDescription:
              type: string
              description: Description of previous picking status
            pickingCompletedAt:
              type: string
              format: date-time
              description: When picking was completed
            pickingCompletedBy:
              type: string
              description: User who completed picking
            packingStatus:
              type: string
              description: Packing status
            packingStatusDescription:
              type: string
              description: Description of packing status
            previousPackingStatus:
              type: string
              description: Previous packing status
            previousPackingStatusDescription:
              type: string
              description: Description of previous packing status
            packingCompletedAt:
              type: string
              format: date-time
              description: When packing was completed
            packingCompletedBy:
              type: string
              description: User who completed packing
            items:
              type: array
              description: List of items in the shipment
              items:
                type: object
                properties:
                  itemNumber:
                    type: string
                  materialNumber:
                    type: string
                  description:
                    type: string
                  orderedQuantity:
                    type: number
                  pickedQuantity:
                    type: number
                  packedQuantity:
                    type: number
                  unit:
                    type: string
                  storageLocation:
                    type: string
                  storageType:
                    type: string
                  storageBin:
                    type: string
                  batchNumber:
                    type: string
                  serialNumbers:
                    type: array
                    items:
                      type: string
            handlingUnits:
              type: array
              description: List of handling units
              items:
                type: object
                properties:
                  handlingUnitNumber:
                    type: string
                  packingMaterial:
                    type: string
                  grossWeight:
                    type: number
                  netWeight:
                    type: number 