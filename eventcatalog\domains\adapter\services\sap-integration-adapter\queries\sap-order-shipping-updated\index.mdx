---
specifications:
  - type: openapi
    path: 'openapi.yml'
id: sap-order-shipping-updated
name: SAP Order Shipping Updated
version: 0.0.1
summary: |
  Event that indicates the shipping information for a sales order has been updated in SAP ECC 6
owners:
  - enterprise
channels:
  - id: sap-integration-adapter.{env}.events
    parameters:
      env: local
schemaPath: 'schema.json'
---

## Overview

This event is emitted when shipping information for a sales order is updated in SAP ECC 6. It contains essential information about the order's shipping process, including delivery scheduling, route determination, and shipping documentation.

For the sake of discussion, the key components of the contract are defined as:
- Protocol: HTTP/HTTPS (Defined on Channel)
- Authentication: OAuth2 (Defined on Channel)
- Address: sap-integration-adapter.local.events/order-shipping-updated (Defined on Channel)
- Schema: schema.json (in this document)

## SAP ECC 6 Details

### Technical Details
- **BAPI Used**: BAPI_SHIPMENT_CREATE
- **SAP Tables**: 
  - VTTK (Shipment Header)
  - VTTP (Shipment Item)
  - VTTS (Shipment Stage)
  - VTSP (Shipment Stage)
  - VTTK (Shipment Header)
- **Transaction Code**: VT01 (Create Shipment)
- **Authorization Object**: V_VTTK_VKO (Shipment Header)

### Business Process
1. **Shipping Update Flow**:
   - Shipment is created via VT01 transaction
   - BAPI_SHIPMENT_CREATE is called
   - System validates shipping data
   - Route is determined
   - Shipping documents are generated
   - Changes are saved

2. **Key SAP Fields**:
   - TKNUM (Shipment Number)
   - VBELN (Delivery Document)
   - POSNR (Item Number)
   - ROUTE (Route)
   - TDDAT (Planned Delivery Date)
   - TDZET (Planned Delivery Time)
   - TDDIS (Distance)
   - TDZON (Zone)

3. **Integration Points**:
   - Transportation (VT01)
   - Delivery (VL03)
   - Route Determination (VT01)
   - Shipping (VT01)

### Common SAP ECC 6 Considerations
- **Shipping Types**:
  - 01: Standard Shipping
  - 02: Express Shipping
  - 03: Freight Shipping
  - 04: Special Handling
  - 05: Return Shipping

- **Route Types**:
  - 1: Direct Route
  - 2: Multi-Stop Route
  - 3: Hub and Spoke
  - 4: Milk Run
  - 5: Custom Route

- **Shipping Documents**:
  - PACK: Packing List
  - BILL: Bill of Lading
  - CMR: CMR Document
  - CUST: Customs Document
  - CERT: Certificate of Origin

### Error Handling
Common SAP ECC 6 errors that may occur:
- **Delivery Not Found**: Check delivery document (VL03)
- **Route Not Found**: Verify route master (VT01)
- **Shipping Point Error**: Check shipping point (OVL2)
- **Document Error**: Review shipping documents (VT01)
- **Authorization Error**: Verify user permissions (SU01)

## Architecture diagram

<NodeGraph/>

