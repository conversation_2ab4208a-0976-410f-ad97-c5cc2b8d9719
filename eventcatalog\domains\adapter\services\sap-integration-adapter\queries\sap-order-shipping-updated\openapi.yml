openapi: "3.1.0"
info:
  title: SAP Order Shipping Updated Event API
  version: 0.0.1
  description: |
    Event that indicates the shipping information for a sales order has been updated in SAP ECC 6. Contains information about the order's shipping process, including delivery scheduling, route determination, and shipping documentation.
servers:
  - url: http://localhost:7600
paths:
  /sap-order-shipping-updated:
    post:
      summary: SAP Order Shipping Updated Event
      operationId: sapOrderShippingUpdated
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SapOrderShippingUpdatedEvent'
            example:
              id: "9fa85f64-5717-4562-b3fc-2c963f66afa1"
              source: "SAP ECC 6"
              type: "sap.order.shipping.updated"
              time: "2023-10-20T09:45:30Z"
              data:
                bapi: "BAPI_SALESORDER_CHANGE"
                salesOrderNumber: "0000123456"
                deliveryNumber: "0000054321"
                updatedBy: "SAPUSER"
                updatedAt: "2023-10-20T09:45:00Z"
                shippingData:
                  carrier: "UPS"
                  carrierService: "Ground"
                  shippingCondition: "01"
                  shippingPoint: "1000"
                  routeCode: "ROUTE01"
                  plannedGoodsIssueDate: "2023-10-25"
                  actualGoodsIssueDate: "2023-10-25"
                  plannedDeliveryDate: "2023-10-28"
                  deliveryBlockReleased: true
                  previousDeliveryDate: "2023-10-30"
                  deliveryPriority: "2"
                shippingAddress:
                  name: "Customer XYZ"
                  street: "123 Main St"
                  city: "Anytown"
                  postalCode: "12345"
                  country: "US"
                  region: "CA"
                  contactPerson: "John Doe"
                  phoneNumber: "******-123-4567"
                items:
                  - itemNumber: "000010"
                    materialNumber: "MAT001"
                    deliveryQuantity: 5
                    unit: "EA"
                    deliveryStatus: "C"
                    batchNumber: "BATCH123"
                    storageLocation: "0001"
      responses:
        '200':
          description: Event accepted
components:
  schemas:
    SapOrderShippingUpdatedEvent:
      type: object
      required:
        - id
        - source
        - type
        - time
        - data
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for this event instance
        source:
          type: string
          description: Source system that emitted the event
        type:
          type: string
          description: Type of event - shipping update (goods issue)
        time:
          type: string
          format: date-time
          description: Timestamp when the event occurred
        data:
          type: object
          description: Event payload with shipping details
          properties:
            bapi:
              type: string
              description: BAPI used for the update
            salesOrderNumber:
              type: string
              description: Sales order number
            deliveryNumber:
              type: string
              description: Delivery document number
            updatedBy:
              type: string
              description: User who performed the update
            updatedAt:
              type: string
              format: date-time
              description: When the update was performed
            shippingData:
              type: object
              properties:
                carrier:
                  type: string
                carrierService:
                  type: string
                shippingCondition:
                  type: string
                shippingPoint:
                  type: string
                routeCode:
                  type: string
                plannedGoodsIssueDate:
                  type: string
                actualGoodsIssueDate:
                  type: string
                plannedDeliveryDate:
                  type: string
                deliveryBlockReleased:
                  type: boolean
                previousDeliveryDate:
                  type: string
                deliveryPriority:
                  type: string
            shippingAddress:
              type: object
              properties:
                name:
                  type: string
                street:
                  type: string
                city:
                  type: string
                postalCode:
                  type: string
                country:
                  type: string
                region:
                  type: string
                contactPerson:
                  type: string
                phoneNumber:
                  type: string
            items:
              type: array
              description: List of items in the shipment
              items:
                type: object
                properties:
                  itemNumber:
                    type: string
                  materialNumber:
                    type: string
                  deliveryQuantity:
                    type: number
                  unit:
                    type: string
                  deliveryStatus:
                    type: string
                  batchNumber:
                    type: string
                  storageLocation:
                    type: string 