---
specifications:
  - type: openapi
    path: 'openapi.yml'
id: sap-order-tracking-updated
name: SAP Order Tracking Updated
version: 0.0.1
summary: |
  Event that indicates the tracking information for a sales order has been updated in SAP ECC 6
owners:
  - enterprise
channels:
  - id: sap-integration-adapter.{env}.events
    parameters:
      env: local
schemaPath: 'schema.json'
---

## Overview

This event is emitted when tracking information for a sales order is updated in SAP ECC 6. It contains essential information about the order's shipping status, tracking numbers, and delivery progress.

For the sake of discussion, the key components of the contract are defined as:
- Protocol: HTTP/HTTPS (Defined on Channel)
- Authentication: OAuth2 (Defined on Channel)
- Address: sap-integration-adapter.local.events/order-tracking-updated (Defined on Channel)
- Schema: schema.json (in this document)

## SAP ECC 6 Details

### Technical Details
- **BAPI Used**: BAPI_SHIPMENT_TRACKING
- **SAP Tables**: 
  - VTTP (Shipment Tracking)
  - VTTS (Shipment Stage)
  - VTTK (Shipment Header)
  - VTTP (Shipment Item)
  - VTSP (Shipment Stage)
- **Transaction Code**: VT03 (Display Shipment)
- **Authorization Object**: V_VTTK_VKO (Shipment Header)

### Business Process
1. **Tracking Update Flow**:
   - Shipment is updated via VT03 transaction
   - BAPI_SHIPMENT_TRACKING is called
   - System validates tracking data
   - Shipment status is maintained
   - Tracking information is updated
   - Changes are saved

2. **Key SAP Fields**:
   - TKNUM (Shipment Number)
   - TPSTA (Transport Status)
   - TRSTA (Tracking Status)
   - TRNUM (Tracking Number)
   - TRCAR (Carrier)
   - TRDAT (Tracking Date)
   - TRTIM (Tracking Time)
   - TRLOC (Location)

3. **Integration Points**:
   - Transportation (VT01)
   - Delivery (VL03)
   - Shipping (VT01)
   - Logistics (LE01)

### Common SAP ECC 6 Considerations
- **Transport Statuses**:
  - A: Not Started
  - B: In Transit
  - C: Delivered
  - D: Exception
  - E: Returned

- **Carrier Types**:
  - 01: Standard Carrier
  - 02: Express Carrier
  - 03: Freight Forwarder
  - 04: Customs Broker
  - 05: Special Carrier

- **Tracking Events**:
  - PICKUP: Pickup Confirmed
  - DEPART: Departed Facility
  - ARRIVE: Arrived at Facility
  - DELIV: Delivery Attempted
  - EXCEPT: Exception Occurred

### Error Handling
Common SAP ECC 6 errors that may occur:
- **Shipment Not Found**: Check shipment (VT03)
- **Tracking Number Invalid**: Verify tracking number
- **Carrier Not Found**: Check carrier master (VT01)
- **Status Update Failed**: Review status flow (VT01)
- **Authorization Error**: Verify user permissions (SU01)

## Architecture diagram

<NodeGraph/>

