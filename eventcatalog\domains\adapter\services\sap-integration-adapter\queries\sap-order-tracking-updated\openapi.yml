openapi: "3.1.0"
info:
  title: SAP Order Tracking Updated Event API
  version: 0.0.1
  description: |
    Event that indicates the tracking information for a sales order has been updated in SAP ECC 6. Contains information about the order's shipping status, tracking numbers, and delivery progress.
servers:
  - url: http://localhost:7600
paths:
  /sap-order-tracking-updated:
    post:
      summary: SAP Order Tracking Updated Event
      operationId: sapOrderTrackingUpdated
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SapOrderTrackingUpdatedEvent'
            example:
              id: "0fa85f64-5717-4562-b3fc-2c963f66afa2"
              source: "SAP ECC 6"
              type: "sap.order.tracking.updated"
              time: "2023-10-21T10:15:30Z"
              data:
                bapi: "BAPI_DELIVERY_CHANGE"
                salesOrderNumber: "0000123456"
                deliveryNumber: "0000054321"
                updatedBy: "SAPUSER"
                updatedAt: "2023-10-21T10:15:00Z"
                trackingData:
                  carrier: "UPS"
                  carrierService: "Ground"
                  mainTrackingNumber: "1Z999AA10123456784"
                  previousTrackingNumber: "1Z999AA10123456783"
                  trackingUrl: "https://www.ups.com/track?tracknum=1Z999AA10123456784"
                  shipDate: "2023-10-21"
                  estimatedDeliveryDate: "2023-10-25"
                  shippingCondition: "01"
                  numberOfPackages: 2
                packages:
                  - packageNumber: "1"
                    trackingNumber: "1Z999AA10123456784"
                    weight: 10.5
                    weightUnit: "KG"
                    dimensions:
                      length: 30
                      width: 20
                      height: 15
                      unit: "CM"
                    items:
                      - itemNumber: "000010"
                        materialNumber: "MAT001"
                        quantity: 3
                        unit: "EA"
                  - packageNumber: "2"
                    trackingNumber: "1Z999AA10123456785"
                    weight: 8.2
                    weightUnit: "KG"
                    dimensions:
                      length: 25
                      width: 18
                      height: 12
                      unit: "CM"
                    items:
                      - itemNumber: "000010"
                        materialNumber: "MAT001"
                        quantity: 2
                        unit: "EA"
      responses:
        '200':
          description: Event accepted
components:
  schemas:
    SapOrderTrackingUpdatedEvent:
      type: object
      required:
        - id
        - source
        - type
        - time
        - data
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for this event instance
        source:
          type: string
          description: Source system that emitted the event
        type:
          type: string
          description: Type of event - tracking update
        time:
          type: string
          format: date-time
          description: Timestamp when the event occurred
        data:
          type: object
          description: Event payload with tracking details
          properties:
            bapi:
              type: string
              description: BAPI used for the update
            salesOrderNumber:
              type: string
              description: Sales order number
            deliveryNumber:
              type: string
              description: Delivery document number
            updatedBy:
              type: string
              description: User who performed the update
            updatedAt:
              type: string
              format: date-time
              description: When the update was performed
            trackingData:
              type: object
              properties:
                carrier:
                  type: string
                carrierService:
                  type: string
                mainTrackingNumber:
                  type: string
                previousTrackingNumber:
                  type: string
                trackingUrl:
                  type: string
                shipDate:
                  type: string
                estimatedDeliveryDate:
                  type: string
                shippingCondition:
                  type: string
                numberOfPackages:
                  type: integer
            packages:
              type: array
              description: List of packages in the shipment
              items:
                type: object
                properties:
                  packageNumber:
                    type: string
                  trackingNumber:
                    type: string
                  weight:
                    type: number
                  weightUnit:
                    type: string
                  dimensions:
                    type: object
                    properties:
                      length:
                        type: number
                      width:
                        type: number
                      height:
                        type: number
                      unit:
                        type: string
                  items:
                    type: array
                    items:
                      type: object
                      properties:
                        itemNumber:
                          type: string
                        materialNumber:
                          type: string
                        quantity:
                          type: number
                        unit:
                          type: string 