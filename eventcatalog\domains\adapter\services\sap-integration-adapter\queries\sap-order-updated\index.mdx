---
specifications:
  - type: openapi
    path: 'openapi.yml'
id: sap-order-updated
version: 0.0.1
name: SAP Order Updated
summary: |
  Event emitted when an order is updated in SAP ECC 6
owners:
  - enterprise
channels:
  - id: sap-integration-adapter.{env}.events
    parameters:
      env: local
schemaPath: 'schema.json'
---

## Overview

This event is emitted when an order is updated in SAP ECC 6. It provides information about the order update, including the previous and new status values.

## Technical Details

### Protocol
- HTTP/HTTPS

### Authentication
- Basic Authentication
- SAP Client Certificate

### Address
- POST /api/sap/order/updated

### Schema
The event follows the CloudEvents specification with the following structure:

```json
{
  "id": "string",
  "source": "string",
  "type": "string",
  "time": "string",
  "datacontenttype": "application/json",
  "data": {
    "eventId": "string",
    "eventType": "Order.Updated",
    "eventTime": "string",
    "salesDocumentNumber": "string",
    "previousStatus": "string",
    "newStatus": "string",
    "statusText": "string"
  }
}
```

### Field Descriptions

| Field | Type | Description |
|-------|------|-------------|
| id | string | Unique identifier for the event |
| source | string | Source system identifier (SAP ECC 6) |
| type | string | Event type identifier |
| time | string | Timestamp of the event |
| datacontenttype | string | Content type of the data payload |
| data.eventId | string | Unique identifier for the event instance |
| data.eventType | string | Type of the event (Order.Updated) |
| data.eventTime | string | Timestamp when the event occurred |
| data.salesDocumentNumber | string | SAP sales document number |
| data.previousStatus | string | Previous status of the order |
| data.newStatus | string | New status of the order |
| data.statusText | string | Human-readable description of the status |

## Business Process

This event is part of the order management process in SAP ECC 6. It is triggered when:
- An order is manually updated
- An order is automatically updated based on business rules
- An order is processed through different stages (e.g., created, confirmed, shipped, delivered)

## Common Considerations

- The event is emitted for all order updates, including both manual and automatic updates
- The status values follow SAP's standard order status codes
- The event includes both the previous and new status for tracking changes
- The statusText field provides a human-readable description of the status

## Error Handling

- If the event cannot be processed, the system will retry up to 3 times
- Failed events are logged for manual review
- Critical updates may trigger additional notifications

## SAP ECC 6 Details

### Technical Details
- **Integration Method**: IDoc (ORDERS05)
- **SAP Tables**: 
  - VBAK (Sales Document Header)
  - VBAP (Sales Document Item)
  - VBUK (Sales Document Status)
  - VBUP (Sales Document Item Status)
  - VBFA (Sales Document Flow)
- **Transaction Code**: VA02 (Change Sales Order)
- **Authorization Object**: V_VBAK_VKO (Sales Document Header)

### Business Process
1. **Status Update Flow**:
   - Sales order status is updated via VA02 transaction
   - IDoc ORDERS05 is generated
   - System validates order status
   - Status changes are maintained
   - Related documents are updated
   - Changes are saved

2. **Key SAP Fields**:
   - VBELN (Sales Document)
   - POSNR (Item Number)
   - GBSTK (Overall Status)
   - LFSTK (Delivery Status)
   - FKSTK (Billing Status)
   - ABSTK (Rejection Status)
   - CMGST (Credit Status)
   - VBSTA (Processing Status)

3. **Integration Points**:
   - Sales Order (VA03)
   - Delivery (VL03)
   - Billing (VF03)
   - Credit Management (FD32)

### Common SAP ECC 6 Considerations
- **Order Statuses**:
  - A: Not Processed
  - B: Partially Processed
  - C: Completely Processed
  - D: Cancelled
  - E: Blocked

- **Delivery Statuses**:
  - A: Not Relevant
  - B: Partially Delivered
  - C: Completely Delivered

- **Billing Statuses**:
  - A: Not Relevant
  - B: Partially Billed
  - C: Completely Billed

### Error Handling
Common SAP ECC 6 errors that may occur:
- **Order Not Found**: Check sales order (VA03)
- **Status Change Not Allowed**: Verify status flow (OVAK)
- **Authorization Error**: Check user permissions (SU01)
- **Credit Check Failed**: Review credit limit (FD32)
- **Delivery Block**: Check delivery blocks (OVL2)

## Architecture diagram

<NodeGraph/>

