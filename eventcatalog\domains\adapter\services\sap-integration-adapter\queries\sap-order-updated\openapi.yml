openapi: "3.1.0"
info:
  title: SAP Order Updated Event API
  version: 0.0.1
  description: |
    Event emitted when an order is updated in SAP ECC 6. Provides information about the order update, including previous and new status values.
servers:
  - url: http://localhost:7600
paths:
  /sap-order-updated:
    post:
      summary: SAP Order Updated Event
      operationId: sapOrderUpdated
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SapOrderUpdatedEvent'
            example:
              id: "123e4567-e89b-12d3-a456-426614174000"
              source: "sap-ecc6"
              type: "sap.order.updated"
              time: "2024-03-20T10:30:00Z"
              datacontenttype: "application/json"
              data:
                eventId: "456e7890-e89b-12d3-a456-426614174000"
                eventType: "Order.Updated"
                eventTime: "2024-03-20T10:30:00Z"
                salesDocumentNumber: "0000123456"
                previousStatus: "CREATED"
                newStatus: "CONFIRMED"
                statusText: "Order has been confirmed"
      responses:
        '200':
          description: Event accepted
components:
  schemas:
    SapOrderUpdatedEvent:
      type: object
      required:
        - id
        - source
        - type
        - time
        - datacontenttype
        - data
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for this event instance
        source:
          type: string
          description: Source system identifier (SAP ECC 6)
        type:
          type: string
          description: Event type identifier
        time:
          type: string
          format: date-time
          description: Timestamp of the event
        datacontenttype:
          type: string
          description: Content type of the data payload
        data:
          type: object
          description: Event data
          properties:
            eventId:
              type: string
              description: Unique identifier for the event instance
            eventType:
              type: string
              description: Type of the event (Order.Updated)
            eventTime:
              type: string
              format: date-time
              description: Timestamp when the event occurred
            salesDocumentNumber:
              type: string
              description: SAP sales document number
            previousStatus:
              type: string
              description: Previous status of the order
            newStatus:
              type: string
              description: New status of the order
            statusText:
              type: string
              description: Human-readable description of the status 