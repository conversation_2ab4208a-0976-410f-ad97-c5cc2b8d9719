---
id: sap
version: 0.0.1
name: SAP API
summary: |
  The API that SAP ECC 6 exposes so that it can communicate with the integration layer
owners:
  - enterprise
sends:
  - id: sap-material-created
    version: 0.0.1
  - id: sap-material-inventory-updated
    version: 0.0.1
  - id: sap-order-status-updated
    version: 0.0.1
  - id: sap-material-sales-updated
    version: 0.0.1
  - id: sap-material-discount-updated
    version: 0.0.1
  - id: sap-material-classification-updated
    version: 0.0.1
  - id: sap-material-plant-updated
    version: 0.0.1
  - id: sap-material-cost-updated
    version: 0.0.1
  - id: sap-material-deprecated
    version: 0.0.1
  - id: sap-order-tracking-updated
    version: 0.0.1
  - id: sap-order-shipping-updated
    version: 0.0.1
  - id: sap-order-pickpack-updated
    version: 0.0.1
  - id: sap-material-price-updated
    version: 0.0.1
  - id: sap-material-distribution-classified
    version: 0.0.1
receives: 
  - id: create-sales-order
    version: 0.0.1
  - id: update-sales-order-status
    version: 0.0.1
  - id: create-sales-order-shipment
    version: 0.0.1
  - id: add-sales-order-shipment-comment
    version: 0.0.1    
repository:
  language: C#
  url: https://github.com/ALSSoftware/axon
---


## Overview

The SAP API is the API that SAP ECC 6 exposes so that it can communicate with the integration layer.

Add more context here.

<Tiles >
    <Tile icon="DocumentIcon" href={`/docs/services/${frontmatter.id}/${frontmatter.version}/changelog`}  title="View the changelog" description="Want to know the history of this service? View the change logs" />
    <Tile icon="UserGroupIcon" href="/docs/teams/enterprise" title="Contact the team" description="Any questions? Feel free to contact the owners" />
    <Tile icon="BoltIcon" href={`/visualiser/services/${frontmatter.id}/${frontmatter.version}`} title={`Sends ${frontmatter.sends.length} messages`} description="This service sends messages to downstream consumers" />
    {/* <Tile icon="BoltIcon"  href={`/visualiser/services/${frontmatter.id}/${frontmatter.version}`} title={`Receives ${frontmatter.receives.length} messages`} description="This service receives messages from other services" /> */}
</Tiles>

## Architecture diagram

<NodeGraph />


<Steps title="How to connect to the SAP API">
  <Step title="Obtain API credentials">
    Request API credentials from the Enterprise team.
  </Step>
  <Step title="Install the SDK">
   Some bash things here
    ```bash
    bash commands
    ```
  </Step>
  <Step title="Initialize the client">
  Use the following code to initialize the Magento API client:

  ```dotnet
  Some HTTP Client code here
```
  </Step>
  <Step title="Make API calls">
  
  You can now use the client to make API calls. For example, to get all things:

  ```dotnet
  more details
  ```
  </Step>
</Steps>
