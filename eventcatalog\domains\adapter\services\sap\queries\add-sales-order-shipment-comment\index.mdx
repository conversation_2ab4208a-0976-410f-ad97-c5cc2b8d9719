---
specifications:
  - type: openapi
    path: 'openapi.yml'
id: add-sales-order-shipment-comment
name: Add Sales Order Shipment Comment
version: 0.0.1
summary: |
  Query to add a shipment comment to an order in SAP ECC using RFC format. This query is used to track shipping status updates and delivery information.
owners:
  - enterprise
channels:
  - id: sap.{env}.rest.queries
    parameters:
      env: local
schemaPath: 'schema.json'
---

## Overview

The `add-shipment-comment` query is used to add a shipment comment to an order in SAP ECC using RFC format. It supports adding various types of shipping-related comments including status updates, delivery notes, and exception information.

## Architecture diagram

<NodeGraph />

## Query Details

### Endpoint
```http
POST /rest/V1/shipment/comment
```

### Required Fields
- `CHANNEL` - Distribution Channel
- `DIVISION` - Division
- `ORDER_TYPE` - Sales Document Type
- `PURCH_NO` - Customer purchase order number
- `SALES_ORG` - Sales Organization
- `SHIP_TO` - Ship to partner
- `SOLD_TO` - Sold to partner

### Optional Fields
- `CITY` - City
- `COUNTRY` - Country Key
- `NAME1` - Name
- `ORDER_REASON` - Order reason (reason for the business transaction)
- `POST_CODE` - Postal Code
- `REGION` - Region (State, Province, County)
- `STREET` - House number and street

## Response

On successful addition, SAP returns the following structure:

```
{
  "Results": [
    {
      "Type": "S",
      "Id": "SUCCESS",
      "Number": "000",
      "Message": "Shipment comment added successfully",
      "System": "SAP_ECC"
    }
  ]
}
```

## Error Handling

The query handles various error scenarios:

### 400 Bad Request
- Missing required fields
- Invalid field values
- Invalid order type
- Invalid sales organization

### 401 Unauthorized
- Invalid or missing authentication token
- Insufficient permissions

### 404 Not Found
- Customer not found
- Sales organization not found

### 409 Conflict
- Invalid comment sequence
- Order is locked by another process

## Notes

- All required fields must be provided in the correct format
- Field values must match SAP's master data
- Common values:
  - ORDER_TYPE: "OR" (Standard Order)
  - CHANNEL: "10" (Internet)
  - DIVISION: "00" (Standard)
- The response includes a BAPIRET2 structure for detailed error information
- All partner numbers (SHIP_TO, SOLD_TO) must be valid customer numbers in SAP
- Comments are timestamped and tracked in SAP's shipping history 