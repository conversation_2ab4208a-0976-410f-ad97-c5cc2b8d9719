openapi: "3.1.0"
info:
  title: Add Sales Order Shipment Comment API
  version: 0.0.1
  description: |
    Query to add a shipment comment to an order in SAP ECC using RFC format. This query is used to track shipping status updates and delivery information.
servers:
  - url: http://localhost:7600
paths:
  /rest/V1/shipment/comment:
    post:
      summary: Add a shipment comment to an order in SAP ECC
      operationId: addSalesOrderShipmentComment
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddSalesOrderShipmentCommentRequest'
            example:
              DOC_NUMBER: "1234567890"
              ITM_NUMBER: "000001"
              TEXT_LINE: "Shipment delivered to customer."
      responses:
        '200':
          description: Shipment comment added successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AddSalesOrderShipmentCommentResponse'
              example:
                Results:
                  - Type: "S"
                    Id: "SUCCESS"
                    Number: "000"
                    Message: "Shipment comment added successfully"
                    System: "SAP_ECC"
        '400':
          description: Bad Request - Missing required fields or invalid values
        '401':
          description: Unauthorized - Invalid or missing authentication token
        '404':
          description: Not Found - Customer or sales organization not found
        '409':
          description: Conflict - Invalid comment sequence or order is locked
components:
  schemas:
    AddSalesOrderShipmentCommentRequest:
      type: object
      required:
        - DOC_NUMBER
        - ITM_NUMBER
        - TEXT_LINE
      properties:
        DOC_NUMBER:
          type: string
          description: Sales Document Number
          pattern: '^[0-9A-Za-z]{1,10}$'
        ITM_NUMBER:
          type: string
          description: Item Number (6 digits)
          pattern: '^[0-9]{6}$'
        TEXT_LINE:
          type: string
          description: Shipment comment text
          maxLength: 130
    AddSalesOrderShipmentCommentResponse:
      type: object
      properties:
        Results:
          type: array
          description: List of result messages (BAPIRET2 structure)
          items:
            type: object
            properties:
              Type:
                type: string
                description: Message type (e.g., S for success)
              Id:
                type: string
                description: Message ID
              Number:
                type: string
                description: Message number
              Message:
                type: string
                description: Message text
              System:
                type: string
                description: System name 