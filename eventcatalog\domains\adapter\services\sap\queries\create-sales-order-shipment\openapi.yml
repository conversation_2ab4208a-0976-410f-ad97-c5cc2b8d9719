openapi: "3.1.0"
info:
  title: Create Sales Order Shipment API
  version: 0.0.1
  description: |
    Query to create a new shipment for an existing order in SAP ECC using RFC format. This query is used to initiate the shipping process for orders.
servers:
  - url: http://localhost:7600
paths:
  /rest/V1/shipment:
    post:
      summary: Create a new shipment for an existing order in SAP ECC
      operationId: createSalesOrderShipment
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateSalesOrderShipmentRequest'
            example:
              - SHIP_REF: "SHIP123456"
                TO_NUM: "TRACK987654"
                SHIP_DATE: "20240601"
                SHIP_TIME: "153000"
                WEIGHT: "10.5"
                CUSTOMER_TOT: "100.00"
                FREIGHT_TOT: "15.00"
      responses:
        '200':
          description: Shipment created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateSalesOrderShipmentResponse'
              example:
                Results:
                  - Type: "S"
                    Id: "SUCCESS"
                    Number: "000"
                    Message: "Shipment created successfully"
                    System: "SAP_ECC"
        '400':
          description: Bad Request - Missing required fields or invalid values
        '401':
          description: Unauthorized - Invalid or missing authentication token
        '404':
          description: Not Found - Customer or sales organization not found
        '409':
          description: Conflict - Order already shipped or order is locked
components:
  schemas:
    CreateSalesOrderShipmentRequest:
      type: array
      items:
        type: object
        required:
          - SHIP_REF
          - TO_NUM
          - SHIP_DATE
          - SHIP_TIME
        properties:
          SHIP_REF:
            type: string
            description: Shipping reference number
          TO_NUM:
            type: string
            description: Tracking number
          SHIP_DATE:
            type: string
            description: Shipping date in YYYYMMDD format
            pattern: '^[0-9]{8}$'
          SHIP_TIME:
            type: string
            description: Shipping time in HHMMSS format
            pattern: '^[0-9]{6}$'
          WEIGHT:
            type: string
            description: Shipment weight
          CUSTOMER_TOT:
            type: string
            description: Customer total
          FREIGHT_TOT:
            type: string
            description: Freight total
    CreateSalesOrderShipmentResponse:
      type: object
      properties:
        Results:
          type: array
          description: List of result messages (BAPIRET2 structure)
          items:
            type: object
            properties:
              Type:
                type: string
                description: Message type (e.g., S for success)
              Id:
                type: string
                description: Message ID
              Number:
                type: string
                description: Message number
              Message:
                type: string
                description: Message text
              System:
                type: string
                description: System name 