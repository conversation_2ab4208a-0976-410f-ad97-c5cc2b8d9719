---
specifications:
  - type: openapi
    path: 'openapi.yml'
id: create-sales-order
name: Create Sales Order
version: 0.0.1
summary: |
  Query to create a new sales order in SAP ECC
owners:
  - enterprise
channels:
  - id: sap.{env}.rest.queries
    parameters:
      env: local
schemaPath: 'schema.json'
---

## Overview

The `create-sales-order` query is used to create a new sales order in SAP ECC. It supports all product types and allows setting various product attributes including stock information.

## Architecture diagram

<NodeGraph />

## Order Types

The SAP system supports creating different types of orders:

### Standard Order (ZMM_POST_SALESORDER)
Basic order with standard processing and shipping.

### Finished Goods Order (ZMM_POST_SALESORDER_FG)
Order for completed products with additional required fields:
- `PLANT` - Plant code
- `DIVISION` - Division code
- `ORDER_SRC` - Order source (E = WWW, X = Batch)
- `DELIVERY_DATE` - Schedule line date
- `CHANNEL` - Distribution Channel

## Response

On successful creation, SAP returns the following structure:

```
{
  "ORDER_NUMBER": "ORD123456",
  "RESULTS": [
    {
      "TYPE": "S",
      "ID": "SUCCESS",
      "NUMBER": "000",
      "MESSAGE": "Order created successfully",
      "LOG_NO": "",
      "LOG_MSG_NO": "000000",
      "MESSAGE_V1": "",
      "MESSAGE_V2": "",
      "MESSAGE_V3": "",
      "MESSAGE_V4": "",
      "PARAMETER": "",
      "ROW": 0,
      "FIELD": "",
      "SYSTEM": ""
    }
  ]
}
```

## Error Handling

The SAP system handles various error scenarios:

### 400 Bad Request
- Invalid order data
- Missing required fields
- Invalid field values
- Invalid material number format
- Invalid quantity format

### 401 Unauthorized
- Invalid or missing authentication token
- Insufficient permissions

### 409 Conflict
- Duplicate order number
- Invalid customer number
- Material not found
- Plant not found

## Notes

- Customer numbers must be padded to 10 characters
- Material numbers must be padded to 18 characters if numeric
- Quantities must be padded to 10 characters
- Dates must be in SAP format (YYYYMMDD)
- Text lines are limited to 130 characters
- The response includes a BAPIRET2 structure for detailed error information
- All monetary values should be provided in the specified currency
- For finished goods orders, plant and division are required
- The order source must be either 'E' (WWW) or 'X' (Batch)