openapi: "3.1.0"
info:
  title: Create Sales Order API
  version: 0.0.1
  description: |
    Query to create a new sales order in SAP ECC. Supports all product types and allows setting various product attributes including stock information.
servers:
  - url: http://localhost:7600
paths:
  /create-sales-order:
    post:
      summary: Create a new sales order in SAP ECC
      operationId: createSalesOrder
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateSalesOrderRequest'
            examples:
              StandardOrder:
                summary: Standard Order
                value:
                  CUSTOMER_NUMBER_SH: "0000001234"
                  CUSTOMER_NUMBER_SP: "0000001234"
                  CUSTOMER_PO_NUM: "PO123456"
                  ORDER_TYPE: "STD"
                  SHIP_TYPE: "GROUND"
                  SHIP_TO_ADDR:
                    NAME1: "John Doe"
                    STREET1: "123 Main St"
                    CITY: "Anytown"
                    REGION: "CA"
                    ZIP_CODE: "12345"
                    COUNTRY: "USA"
                    TEL1_NUMBR: "555-0123"
                    E_MAIL: "<EMAIL>"
                  MATERIAL_ITEMS:
                    - MATNR: "000000000000000123"
                      TARGET_QTY: "0000000002"
                      PRICE_PER: 99.99
                  ORDER_TEXT:
                    - TEXT_LINE: "Order comments line 1"
                    - TEXT_LINE: "Order comments line 2"
              FinishedGoodsOrder:
                summary: Finished Goods Order
                value:
                  CUSTOMER_NUMBER_SH: "0000005678"
                  CUSTOMER_NUMBER_SP: "0000005678"
                  CUSTOMER_PO_NUM: "PO789012"
                  ORDER_TYPE: "FG"
                  SHIP_TYPE: "EXPRESS"
                  PLANT: "PLANT1"
                  DIVISION: "DIV1"
                  ORDER_SRC: "E"
                  DELIVERY_DATE: "20240320"
                  CHANNEL: "WEB"
                  SHIP_TO_ADDR:
                    NAME1: "Jane Smith"
                    STREET1: "456 Oak Ave"
                    CITY: "Othertown"
                    REGION: "NY"
                    ZIP_CODE: "67890"
                    COUNTRY: "USA"
                    TEL1_NUMBR: "555-4567"
                    E_MAIL: "<EMAIL>"
                  MATERIAL_ITEMS:
                    - MATNR: "000000000000000456"
                      TARGET_QTY: "0000000001"
                      PLANT: "PLANT1"
                      PRICE_PER: 199.99
      responses:
        '200':
          description: Order created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateSalesOrderResponse'
              example:
                ORDER_NUMBER: "ORD123456"
                RESULTS:
                  - TYPE: "S"
                    ID: "SUCCESS"
                    NUMBER: "000"
                    MESSAGE: "Order created successfully"
                    LOG_NO: ""
                    LOG_MSG_NO: "000000"
                    MESSAGE_V1: ""
                    MESSAGE_V2: ""
                    MESSAGE_V3: ""
                    MESSAGE_V4: ""
                    PARAMETER: ""
                    ROW: 0
                    FIELD: ""
                    SYSTEM: ""
        '400':
          description: Bad Request - Invalid order data or missing/invalid fields
        '401':
          description: Unauthorized - Invalid or missing authentication token
        '409':
          description: Conflict - Duplicate order or invalid customer/material/plant
components:
  schemas:
    CreateSalesOrderRequest:
      type: object
      required:
        - CUSTOMER_NUMBER_SH
        - CUSTOMER_NUMBER_SP
        - CUSTOMER_PO_NUM
        - ORDER_TYPE
        - SHIP_TYPE
        - MATERIAL_ITEMS
      properties:
        CUSTOMER_NUMBER_SH:
          type: string
          description: Ship-to customer number. Must be exactly 10 digits.
          pattern: '^[0-9]{10}$'
        CUSTOMER_NUMBER_SP:
          type: string
          description: Sold-to customer number. Must be exactly 10 digits
          pattern: '^[0-9]{10}$'
        CUSTOMER_PO_NUM:
          type: string
          description: Customer purchase order number
        ORDER_TYPE:
          type: string
          description: Sales Document Type
          enum: [STD, FG]
        SHIP_TYPE:
          type: string
          description: Shipping type
        NAME_2:
          type: string
          description: Additional name information. Maximum of 40 characters allowed.
          maxLength: 40
        NAME_3:
          type: string
          description: Additional name information. Maximum of 40 characters allowed.
          maxLength: 40
        NAME_4:
          type: string
          description: Additional name information Maximum of 40 characters allowed.
          maxLength: 40
        ORDER_BY_NAME:
          type: string
          description: Name of person placing the order
        ORDER_BY_PHONE:
          type: string
          description: Contact phone number
        RUSH_ORDER:
          type: string
          description: Rush order indicator. Must be 'X' for rush order.
          enum: [X]
        FRTACCT:
          type: string
          description: Freight account
        INCOTERMS:
          type: string
          description: International commercial terms
        SHIPPING_CONTACT_NAME:
          type: string
          description: Name of shipping contact
        SHIPPING_CONTACT_PHONE:
          type: string
          description: Phone number of shipping contact
        PLANT:
          type: string
          description: Plant code (required for finished goods orders)
        DIVISION:
          type: string
          description: Division code (required for finished goods orders)
        ORDER_SRC:
          type: string
          description: Order source (required for finished goods orders). Must be 'E' for e-commerce or 'X' for batch.
          enum: [E, X]
        DELIVERY_DATE:
          type: string
          description: Schedule line date (required for finished goods orders)
          pattern: '^[0-9]{8}$'
        CHANNEL:
          type: string
          description: Distribution Channel (required for finished goods orders)
        SHIP_TO_ADDR:
          type: object
          description: Shipping address information
          required: [NAME1, STREET1, CITY, REGION, ZIP_CODE, COUNTRY]
          properties:
            NAME1:
              type: string
              description: Recipient name. Maximum of 40 characters allowed.
              maxLength: 40
            NAME2:
              type: string
              description: Additional name information. Maximum of 40 characters allowed.
              maxLength: 40
            STREET1:
              type: string
              description: Street address. Maximum of 60 characters allowed.
              maxLength: 60
            STREET2:
              type: string
              description: Additional street information. Maximum of 40 characters allowed.
              maxLength: 40
            CITY:
              type: string
              description: City. Maximum of 40 characters allowed.
              maxLength: 40
            REGION:
              type: string
              description: State/Province. Maximum of 3 characters allowed.
              maxLength: 3
            ZIP_CODE:
              type: string
              description: Postal code. Maximum of 10 characters allowed.
              maxLength: 10
            COUNTRY:
              type: string
              description: Country. Maximum of 3 characters allowed.
              maxLength: 3
            TEL1_NUMBR:
              type: string
              description: Contact phone. Maximum of 16 characters allowed.
              maxLength: 16
            E_MAIL:
              type: string
              description: Contact email. Maximum of 241 characters allowed.
              maxLength: 241
            FAX_NUMBER:
              type: string
              description: Contact fax. Maximum of 31 characters allowed.
              maxLength: 31
        MATERIAL_ITEMS:
          type: array
          description: List of ordered items
          items:
            type: object
            required: [MATNR, TARGET_QTY]
            properties:
              MATNR:
                type: string
                description: Material Number. Must be exactly 18 digits. Padded with leading zeros.
                pattern: '^[0-9]{18}$'
              TARGET_QTY:
                type: string
                description: Target quantity. Must be exactly 10 digits. Padded with leading zeros.
                pattern: '^[0-9]{10}$'
              PLANT:
                type: string
                description: Plant code (for finished goods)
              PRICE_PER:
                type: number
                description: Price per unit
              IT_CONDITIONS:
                type: array
                description: Item conditions array
                items:
                  type: object
                  properties:
                    KSCHL:
                      type: string
                      description: Condition type
                    ZZAMOUNT:
                      type: string
                      description: Condition amount
        ORDER_TEXT:
          type: array
          description: Order comments
          items:
            type: object
            required: [TEXT_LINE]
            properties:
              TEXT_LINE:
                type: string
                description: Text line
                maxLength: 130
    CreateSalesOrderResponse:
      type: object
      properties:
        ORDER_NUMBER:
          type: string
          description: The SAP order number created
        RESULTS:
          type: array
          description: List of result messages (BAPIRET2 structure)
          items:
            type: object
            properties:
              TYPE:
                type: string
                description: Message type (e.g., S for success)
              ID:
                type: string
                description: Message ID
              NUMBER:
                type: string
                description: Message number
              MESSAGE:
                type: string
                description: Message text
              LOG_NO:
                type: string
                description: Log number
              LOG_MSG_NO:
                type: string
                description: Log message number
              MESSAGE_V1:
                type: string
                description: Message variable 1
              MESSAGE_V2:
                type: string
                description: Message variable 2
              MESSAGE_V3:
                type: string
                description: Message variable 3
              MESSAGE_V4:
                type: string
                description: Message variable 4
              PARAMETER:
                type: string
                description: Parameter name
              ROW:
                type: integer
                description: Row number
              FIELD:
                type: string
                description: Field name
              SYSTEM:
                type: string
                description: System name 