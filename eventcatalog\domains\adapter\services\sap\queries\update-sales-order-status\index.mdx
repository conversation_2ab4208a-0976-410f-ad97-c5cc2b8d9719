---
specifications:
  - type: openapi
    path: 'openapi.yml'
id: update-sales-order-status
name: Update Sales Order Status
version: 0.0.1
summary: |
  Query to update the status of a sales order in SAP ECC using RFC format. This query is used to change the processing status of an order.
owners:
  - enterprise
channels:
  - id: sap.{env}.rest.queries
    parameters:
      env: local
schemaPath: 'schema.json'
---

## Overview

The `update-sales-order-status` query is used to update the status of an existing sales order in SAP ECC using RFC format. It supports various status transitions and includes validation to ensure only valid status changes are allowed.

## Architecture diagram

<NodeGraph />

## Query Details

### Endpoint
```http
PUT /rest/V1/order/status
```

### Required Fields
- `CHANNEL` - Distribution Channel
- `DIVISION` - Division
- `ORDER_TYPE` - Sales Document Type
- `PURCH_NO` - Customer purchase order number
- `SALES_ORG` - Sales Organization
- `SHIP_TO` - Ship to partner
- `SOLD_TO` - Sold to partner

### Optional Fields
- `CITY` - City
- `COUNTRY` - Country Key
- `NAME1` - Name
- `ORDER_REASON` - Order reason (reason for the business transaction)
- `POST_CODE` - Postal Code
- `REGION` - Region (State, Province, County)
- `STREET` - House number and street

## Response

On successful update, SAP returns the following structure:

```
{
  "Results": [
    {
      "Type": "S",
      "Id": "SUCCESS",
      "Number": "000",
      "Message": "Order status updated successfully",
      "System": "SAP_ECC"
    }
  ]
}
```

## Error Handling

The query handles various error scenarios:

### 400 Bad Request
- Missing required fields
- Invalid field values
- Invalid order type
- Invalid sales organization

### 401 Unauthorized
- Invalid or missing authentication token
- Insufficient permissions

### 404 Not Found
- Customer not found
- Sales organization not found

### 409 Conflict
- Invalid status for current order state
- Status change not allowed
- Order is locked by another process

## Notes

- All required fields must be provided in the correct format
- Field values must match SAP's master data
- Common values:
  - ORDER_TYPE: "OR" (Standard Order)
  - CHANNEL: "10" (Internet)
  - DIVISION: "00" (Standard)
- The response includes a BAPIRET2 structure for detailed error information
- Status changes may be restricted based on user permissions
- All partner numbers (SHIP_TO, SOLD_TO) must be valid customer numbers in SAP 