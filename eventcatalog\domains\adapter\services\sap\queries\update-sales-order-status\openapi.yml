openapi: "3.1.0"
info:
  title: Update Sales Order Status API
  version: 0.0.1
  description: |
    Query to update the status of a sales order in SAP ECC using RFC format. This query is used to change the processing status of an order.
servers:
  - url: http://localhost:7600
paths:
  /rest/V1/order/status:
    put:
      summary: Update the status of a sales order in SAP ECC
      operationId: updateSalesOrderStatus
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateSalesOrderStatusRequest'
            example:
              CHANNEL: "10"
              DIVISION: "00"
              ORDER_TYPE: "OR"
              PURCH_NO: "PO123456"
              SALES_ORG: "1000"
              SHIP_TO: "0000001234"
              SOLD_TO: "0000001234"
              CITY: "Anytown"
              COUNTRY: "US"
              NAME1: "John Doe"
              ORDER_REASON: "01"
              POST_CODE: "12345"
              REGION: "CA"
              STREET: "123 Main St"
      responses:
        '200':
          description: Order status updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateSalesOrderStatusResponse'
              example:
                Results:
                  - Type: "S"
                    Id: "SUCCESS"
                    Number: "000"
                    Message: "Order status updated successfully"
                    System: "SAP_ECC"
        '400':
          description: Bad Request - Missing required fields or invalid values
        '401':
          description: Unauthorized - Invalid or missing authentication token
        '404':
          description: Not Found - Customer or sales organization not found
        '409':
          description: Conflict - Invalid status for current order state, status change not allowed, or order is locked
components:
  schemas:
    UpdateSalesOrderStatusRequest:
      type: object
      required:
        - CHANNEL
        - DIVISION
        - ORDER_TYPE
        - PURCH_NO
        - SALES_ORG
        - SHIP_TO
        - SOLD_TO
      properties:
        CHANNEL:
          type: string
          description: Distribution Channel
        CITY:
          type: string
          description: City
        COUNTRY:
          type: string
          description: Country Key
        DIVISION:
          type: string
          description: Division
        NAME1:
          type: string
          description: Name
        ORDER_REASON:
          type: string
          description: Order reason (reason for the business transaction)
        ORDER_TYPE:
          type: string
          description: Sales Document Type
        PURCH_NO:
          type: string
          description: Customer purchase order number
        POST_CODE:
          type: string
          description: Postal Code
        REGION:
          type: string
          description: Region (State, Province, County)
        SALES_ORG:
          type: string
          description: Sales Organization
        SHIP_TO:
          type: string
          description: Ship to partner
        SOLD_TO:
          type: string
          description: Sold to partner
        STREET:
          type: string
          description: House number and street
    UpdateSalesOrderStatusResponse:
      type: object
      properties:
        Results:
          type: array
          description: List of result messages (BAPIRET2 structure)
          items:
            type: object
            properties:
              Type:
                type: string
                description: Message type (e.g., S for success)
              Id:
                type: string
                description: Message ID
              Number:
                type: string
                description: Message number
              Message:
                type: string
                description: Message text
              System:
                type: string
                description: System name 