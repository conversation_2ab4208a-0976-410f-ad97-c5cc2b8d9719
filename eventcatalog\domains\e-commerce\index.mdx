---
id: e-commerce
name: E-Commerce Domain
version: 0.0.1
summary: The core business domain orchestrating all critical operations for the Alliance Evolution E-Commerce platform.
owners:
  - enterprise
domains:
  - id: order
  - id: product
  - id: rma
  - id: adapter
badges:
  - content: Core domain
    backgroundColor: blue
    textColor: blue
    icon: RectangleGroupIcon
  - content: Business Critical
    backgroundColor: yellow
    textColor: yellow
    icon: ShieldCheckIcon

---

The E-Commerce domain is the core business domain of Alliance Evolution E-Commerce, our modern multi-channel e-commerce platform. This domain orchestrates all critical business operations.

<Tiles>
    <Tile 
        icon="UserGroupIcon" 
        href="/docs/teams/enterprise"
        title="Engineering Support"
        description="Questions? Contact our enterprise digitalizationteam for technical support"
    />
    <Tile 
        icon="RectangleGroupIcon" 
        href={`/visualiser/domains/${frontmatter.id}/${frontmatter.version}`} 
        title="Domain Architecture"
        description="Explore our domain structure and service interactions"
    />
</Tiles>


## Design Principles

### Coupling

#### Measuring Coupling
- Afferent Coupling (Ca) - The number of other components/modules that depend on (reference) your component. A measure of how “widely used” or “responsible” your component is.
- Efferent Coupling (Ce) - The number of external components/modules your component depends on. A measure of how many moving parts your component is tied to.

#### Understanding the Measurement
- High Ca --> your component is stable (many clients rely on it) --> you must minimize breaking changes and push abstraction into it.
- High Ce --> your component is volatile (it talks to a lot of others) --> it lives on the “edge” of the system where change is more acceptable.
- Instability Formula: `I = Ce / (Ce + Ca)`
  - Instability values range from 0 to 1. A value closer to 0 indicates high stability, while a value closer to 1 indicates high instability. A value close to 0.5 on it's own isn't an issue, a value close to 0.5 where Ca or Ce > 3 indicates a design problem (a component is both stable and volatile). Neither stable enough to be a reusable core, nor unstable enough to be a throwaway adapter. Changes to you ripple out and you fight frequent upstream changes.

#### Designing for Coupling
- Core/domain components: low instability (I), high abstractness
- Infrastructure/UI: higher instability, more concrete
- Push abstractions inward toward the centre of your app (Stable Dependency Principle).
- Depend in the direction of stability: inner layers don't reference outer ones.
- Invert dependencies for domain-infrastructure links (DI, hexagonal ports & adapters, publish/subscribe). Inner (domain) layers have zero knowledge of outer (infrastructure) layers, and all concrete details are pushed to the perimeter.
- Measure Ca/Ce to validate your component boundaries: large spikes often signal misplaced responsibilities or missing abstractions.
- Use messaging (events, queues) to turn hard efferent dependencies into implicit, decoupled afferent relationships. Consumers “pull” work via events rather than “calling” producers directly.

## Sub domains

The E-Commerce domain is built on the following sub domains:

- <ResourceLink id="adapter" type="domain">Adapter</ResourceLink> - Outer domain that deals with infrastructure links to Magento, SAP, and other highly volitale integrations.
- <ResourceLink id="order" type="domain">Order</ResourceLink> - Core domain for order management
- <ResourceLink id="product" type="domain">Product</ResourceLink> - Core domain for product management
- <ResourceLink id="rma" type="domain">Return Merchandise Authorization</ResourceLink> - Core domain for return merchandise authorization

## Current Architecture

Our current event-driven architecture powering Alliance's Evolution E-Commerce domain:

<NodeGraph />

## Performance SLAs

- Order Processing: TBD
- Payment Processing: N/A
- Inventory Updates: TBD
- Notification Delivery: TBD

## Monitoring & Alerts

- TBD

