---
dictionary:
  - id: Purchase Order
    name: Purchase Order
    summary: "A formal document issued by a buyer to a seller, specifying the types, quantities, and agreed prices for products or services."
    description: |
      A purchase order (PO) is a legally binding document that authorizes a purchase transaction. It details the items or services requested, quantities, pricing, delivery instructions, and terms and conditions. Purchase orders help organizations manage procurement, track spending, and maintain accurate records for accounting and auditing purposes.
    icon: FileText
  - id: Purchase Order Types
    name: Purchase Order Types
    summary: "The different types of purchase orders used in SAP ECC."
    description: |
      Core PO types in SAP ECC:
      - Standard Purchase Order (NB): One-time purchases with specific delivery dates
      - Framework Purchase Order (FO): Long-term agreements with total value and validity period
      - Contract Purchase Order (OK): Long-term agreements with specific items and quantities
      - Scheduling Agreement (SA): Regular, recurring deliveries with delivery schedule
      - Service Purchase Order (D): For purchasing services with service specifications
      - Consignment Purchase Order (K): For consignment stock where material remains supplier's property
      - Subcontracting Purchase Order (L): When external vendor performs work on materials
      - Stock Transfer Purchase Order (U): For internal stock transfers between plants
    icon: FileText
  - id: Receipt Types
    name: Receipt Types
    summary: "The different types of receipts used in the procurement and inventory management process."
    description: |
      Core receipt types in the procurement cycle:
      - Goods Receipt (GR): Physical receipt of materials
      - Invoice Receipt (IR): Supplier invoice for payment
      - Quality Receipt: Quality inspection results
      - Return Receipt: Returned materials
      - Consignment Receipt: Consignment stock

      Part of three-way matching:
      1. Purchase Order (ordered)
      2. Goods Receipt (received)
      3. Invoice Receipt (paid)
    icon: ClipboardList
  - id: Goods Receipt
    name: Goods Receipt
    summary: "A document that records the physical receipt of goods or services into inventory."
    description: |
      Confirms physical receipt of materials and updates inventory. Types include standard, consignment, subcontracting, stock transfer, and returns. Records quantity, date, location, and batch information. Triggers inventory updates, accounting entries, and quality inspection if required.
    icon: Package
  - id: Invoice Receipt
    name: Invoice Receipt
    summary: "A document that records the receipt of a supplier's invoice for goods or services."
    description: |
      Records supplier invoices and enables payment processing. Key components include invoice details, payment terms, and references to PO and GR. Triggers accounts payable entries and payment processing.
    icon: Receipt
  - id: RMA
    name: Return Merchandise Authorization (RMA)
    summary: "The process and approval for returning products."
    icon: RefreshCw
  - id: PIM
    name: Product Information Management (PIM)
    summary: "A system for managing and centralizing product data."
    icon: Database
    description: |
      We use Pimcore as our Product Information Management (PIM) system. Pimcore provides a flexible and robust platform for managing all product-related data and digital assets.

      Key features of Pimcore include:
      - Centralized management of product information and digital assets
      - Data modeling and classification for complex product catalogs
      - Workflow management for product data enrichment and approval
      - Integration capabilities with e-commerce, ERP, and other systems
      - Multi-channel publishing and syndication of product data
  - id: SKU
    name: Stock Keeping Unit (SKU)
    summary: "A unique identifier for each distinct product and service that can be purchased."
    icon: Tag
    description: |
      SKUs are the cornerstone of effective inventory management systems. Each SKU represents a unique combination of product attributes:

      - Product variations (size, color, style)
      - Storage location identifiers
      - Supplier information
      - Reorder points and quantities

      SKUs enable precise inventory tracking, automated reordering, and detailed sales analytics. They are crucial for maintaining optimal stock levels and preventing stockouts or overstock situations.

      **Note:** SAP currently has a 18 character limit for SKUs. This may be extended in the future to 24 characters.
  - id: Sales Order
    name: Sales Order
    summary: "A document generated by the seller confirming a purchase from the buyer."
    icon: FileText
  - id: Purchase Request
    name: Purchase Request
    summary: "An internal document requesting the purchase of goods or services."
    icon: FilePlus
  - id: Marcone
    name: Marcone (Supplier)
    summary: "A key supplier in the e-commerce supply chain."
    icon: Truck
  - id: Distributor
    name: Distributor
    summary: "An entity that purchases products from suppliers and sells them to retailers or customers."
    icon: Users
  - id: MRP
    name: Material Requirements Planning (MRP)
    summary: "A system for calculating the materials and components needed to manufacture a product."
    icon: Calculator
  - id: SAP
    name: SAP
    summary: "An enterprise resource planning (ERP) software used for business operations and customer relations."
    icon: Cpu
    description: |
      Our SAP system is an on-premises installation running in Ripon, WI. It is used for managing core business processes, including finance, supply chain, procurement, and inventory management. The on-prem deployment ensures data residency and integration with local manufacturing and distribution operations.
  - id: Material Number
    name: Material Number
    summary: "A unique identifier assigned to a material in inventory."
    icon: Hash
  - id: Product Id
    name: Product Id
    summary: "A unique identifier for a product in the catalog."
    icon: Hash
  - id: Model Number
    name: Model Number
    summary: "A unique code assigned to a specific model of a product."
    icon: Hash
  - id: Serial Number
    name: Serial Number
    summary: "A unique code assigned to an individual item for identification and tracking."
    icon: Hash
---