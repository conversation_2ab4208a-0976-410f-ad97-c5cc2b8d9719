---
id: magento-cart-created-flow
name: Magento Cart Created
version: 0.0.1
summary: Business flow for processing a new cart creation from Magento through adapters to downstream systems
steps:
  - id: parts_west_customer_initiates_cart
    title: Parts West Customer Initiates Cart
    actor:
      name: "Parts West Customer"
    next_step:
      id: "magento_creates_cart"

  - id: parts_king_customer_initiates_cart
    title: PartsKing Customer Initiates Cart
    actor:
      name: "PartsKing Customer"
    next_step:
      id: "magento_creates_cart"

  - id: "magento_creates_cart"
    title: "Magento"
    service:
      id: "magento"
    next_steps:
      - id: "magento_sends_cart_created_query"

  - id: magento_sends_cart_created_query
    title: Magento Sends Cart Created Query
    message:
      id: cart-created-query
      version: "1.2.0"
    next_step:
      id: "magento_adapter_receives_query"

  - id: "magento_adapter_receives_query"
    title: "Magento Integration Adapter"
    service:
      id: "magento-integration-adapter"
      version: "0.0.2"
    next_steps:
      - id: "magento_adapter_validates_cart_data"

  - id: magento_adapter_validates_cart_data
    title: Magento Integration Adapter Validates Cart Data
    next_step:
      id: "magento_adapter_sends_record_cart_created_command"

  - id: magento_adapter_sends_record_cart_created_command
    title: Magento Integration Adapter Sends Record Cart Created Command
    message:
      id: record-cart-created-command
    next_step:
      id: "order_domain_service_receives_command"

  - id: "order_domain_service_receives_command"
    title: "Order Domain Service"
    service:
      id: "order-domain-service"
      version: "0.0.1"
    next_steps:
      - id: order_domain_service_validates_command

  - id: order_domain_service_validates_command
    title: Order Domain Service Validates Command
    next_steps:
      - id: "order_domain_service_publishes_cart_created_event"
        condition: "Cart ID is unique"
      - id: "order_domain_service_publishes_cart_creation_failed_event"
        condition: "Cart ID already exists"

  - id: order_domain_service_publishes_cart_created_event
    title: Order Domain Service Responds with Cart Created Event
    message:
      id: cart-created-event
    next_steps:
      - id: "magento_adapter_receives_success_response"
      - id: "downstream_systems_receive_event"

  - id: magento_adapter_receives_success_response
    title: Magento Integration Adapter Receives Success Response
    service:
      id: "magento-integration-adapter"
      version: "0.0.2"
    next_step:
      id: "magento_adapter_returns_202_response"

  - id: magento_adapter_returns_202_response
    title: Magento Integration Adapter Returns 202 Accepted Response

  - id: order_domain_service_publishes_cart_creation_failed_event
    title: Order Domain Service Publishes Cart Creation Failed Event
    message:
      id: cart-creation-failed-event
    next_step:
      id: "magento_adapter_handles_failure"

  - id: magento_adapter_handles_failure
    title: Magento Integration Adapter Handles Failure
    service:
      id: "magento-integration-adapter"
      version: "0.0.2"
    next_steps:
      - id: "magento_adapter_returns_409_response"

  - id: magento_adapter_returns_409_response
    title: Magento Integration Adapter Returns 409 Conflict Response

  - id: downstream_systems_receive_event
    title: Downstream Systems Receive Cart Created Event
    service:
      id: "downstream-systems"
    next_steps:
      - id: "order_domain_service_publishes_cart_initialized_event"


---

### Flow of feature
<NodeGraph />

## Sequence Diagram

```mermaid
sequenceDiagram
    actor Customer
    participant Magento
    participant MagentoAdapter as Magento Integration Adapter
    participant OrderDomainService as Order Domain Service
    participant DownstreamSystems as Downstream Systems

    alt Parts West Customer
      Customer->>Magento: Create Cart
    else PartsKing Customer
      Customer->>Magento: Create Cart
    end

    Magento->>Magento: Generate Cart ID
    Magento->>MagentoAdapter: Send Cart Created Query
    MagentoAdapter->>MagentoAdapter: Validate Cart Query
    MagentoAdapter->>OrderDomainService: Send Record Cart Created Command
    OrderDomainService->>OrderDomainService: Validate Business Rules

    alt Cart ID is unique
        OrderDomainService->>OrderDomainService: Apply Domain Logic
        OrderDomainService->>OrderDomainService: Persist immutable Cart version
        OrderDomainService->>MagentoAdapter: Respond with Cart Created Event
        MagentoAdapter->>Magento: Send Success Response (HTTP 202)
        OrderDomainService->>DownstreamSystems: Publish Cart Created Event
        DownstreamSystems->>DownstreamSystems: Process Cart Creation
    else Cart ID already exists
        OrderDomainService->>MagentoAdapter: Respond with Cart Creation Failed Event
        MagentoAdapter->>Magento: Send Conflict Response (HTTP 409)
    end
```

## Key Integration Points

### Cart Creation Triggers
- Parts West or PartsKing customer initiates a new shopping session
- Guest user adds first item to cart
- B2B customer initiates a negotiable quote
- Cart is created via API integration
- Both customer sources use the same Magento instance and integration adapter

### Data Flow Considerations
- **Critical IDs**: The adapter must store `cart_id`, `customer_id` (if available), and `store_id` for future reference
- **Guest Support**: The flow handles both guest and authenticated customers
- **B2B Features**: Supports negotiable quotes when `is_negotiable_quote` is true
- **Currency Support**: All monetary values include currency information

### Error Handling
- Validation failures return HTTP 400 with detailed error messages
- Duplicate cart attempts return HTTP 409 Conflict with clear error message
- Domain validation ensures business rules are enforced
- Idempotency checks prevent duplicate cart creation
- Failed downstream processing does not block the cart creation response
- Cart Creation Failed Event is published for duplicate cart scenarios

## Technical Details

### REST API Endpoint
- **URL**: `POST /api/v0.1/magento-integration-adapter/cart-created`
- **Channel**: `magento-integration-adapter.{env}.rest.queries`
- **Authentication**: Required (implementation specific)

### Message Types
- **Query**: `cart-created-query` (v1.2.0) - Initiated by Magento
- **Command**: `record-cart-created-command` - Internal command for domain processing
- **Events**:
  - `cart-created-event` - Published to downstream systems on successful creation
  - `cart-creation-failed-event` - Published when cart creation fails (e.g., duplicate ID)
  - `cart-initialized-event` - Confirms successful cart initialization

### Data Mapping
The integration layer receives comprehensive cart data matching Magento's `quote-cart-interface` structure:
- Cart metadata (ID, timestamps, store context)
- Customer information (if authenticated)
- Item details with product options
- Pricing and totals
- B2B-specific attributes

## Owners
- euvic
