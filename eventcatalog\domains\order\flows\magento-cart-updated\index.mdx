---
id: magento-cart-updated-flow
name: Magento Cart Updated
version: 0.0.1
summary: Business flow for processing updates to an existing cart through adapters to downstream systems
steps:
  - id: parts_west_customer_updates_cart
    title: Parts West Customer Updates Cart
    actor:
      name: "Parts West Customer"
    next_step:
      id: "magento_updates_cart"

  - id: parts_king_customer_updates_cart
    title: PartsKing Customer Updates Cart
    actor:
      name: "PartsKing Customer"
    next_step:
      id: "magento_updates_cart"

  - id: "magento_updates_cart"
    title: "Magento"
    service:
      id: "magento"
    next_steps:
      - id: "magento_sends_cart_updated_query"

  - id: magento_sends_cart_updated_query
    title: Magento Sends Cart Updated Query
    message:
      id: cart-updated-query
      version: "1.1.0"
    next_step:
      id: "magento_adapter_receives_query"

  - id: "magento_adapter_receives_query"
    title: "Magento Integration Adapter"
    service:
      id: "magento-integration-adapter"
      version: "0.0.2"
    next_steps:
      - id: "magento_adapter_validates_update"

  - id: magento_adapter_validates_update
    title: Magento Integration Adapter Validates Update
    next_step:
      id: "magento_adapter_sends_record_cart_updated_command"

  - id: magento_adapter_sends_record_cart_updated_command
    title: Magento Integration Adapter Sends Record Cart Updated Command
    message:
      id: record-cart-updated-command
    next_step:
      id: "order_domain_service_receives_command"

  - id: "order_domain_service_receives_command"
    title: "Order Domain Service"
    service:
      id: "order-domain-service"
      version: "0.0.1"
    next_steps:
      - id: order_domain_service_validates_command

  - id: order_domain_service_validates_command
    title: Order Domain Service Validates Command
    next_steps:
      - id: "order_domain_service_publishes_cart_updated_event"
        condition: "Cart ID exists"
      - id: "order_domain_service_publishes_cart_update_failed_event"
        condition: "Cart ID doesn't exist"

  - id: order_domain_service_publishes_cart_updated_event
    title: Order Domain Service Responds with Cart Updated Event
    message:
      id: cart-updated-event
    next_steps:
      - id: "magento_adapter_receives_success_response"
      - id: "downstream_systems_receive_event"

  - id: magento_adapter_receives_success_response
    title: Magento Integration Adapter Receives Success Response
    service:
      id: "magento-integration-adapter"
      version: "0.0.2"
    next_step:
      id: "magento_adapter_returns_202_response"

  - id: magento_adapter_returns_202_response
    title: Magento Integration Adapter Returns 202 Accepted Response

  - id: order_domain_service_publishes_cart_update_failed_event
    title: Order Domain Service Publishes Cart Update Failed Event
    message:
      id: cart-update-failed-event
    next_step:
      id: "magento_adapter_handles_failure"

  - id: magento_adapter_handles_failure
    title: Magento Integration Adapter Handles Failure
    service:
      id: "magento-integration-adapter"
      version: "0.0.2"
    next_steps:
      - id: "magento_adapter_returns_409_response"

  - id: magento_adapter_returns_409_response
    title: Magento Integration Adapter Returns 409 Conflict Response

  - id: downstream_systems_receive_event
    title: Downstream Systems Receive Cart Updated Event
    service:
      id: "downstream-systems"


---

### Flow of feature
<NodeGraph />

## Sequence Diagram

```mermaid
sequenceDiagram
    actor Customer
    participant Magento
    participant MagentoAdapter as Magento Integration Adapter
    participant OrderDomainService as Order Domain Service
    participant DownstreamSystems as Downstream Systems

    alt Parts West Customer
      Customer->>Magento: Update Cart
    else PartsKing Customer
      Customer->>Magento: Update Cart
    end

    Magento->>Magento: Retrieve Cart ID
    Magento->>MagentoAdapter: Send Cart Updated Query
    MagentoAdapter->>MagentoAdapter: Validate Update Query
    MagentoAdapter->>OrderDomainService: Send Record Cart Updated Command
    OrderDomainService->>OrderDomainService: Validate Business Rules

    alt Cart ID exists
        OrderDomainService->>OrderDomainService: Apply Domain Logic
        OrderDomainService->>OrderDomainService: Persist immutable Cart version
        OrderDomainService->>MagentoAdapter: Respond with Cart Updated Event
        MagentoAdapter->>Magento: Send Success Response (HTTP 202)
        OrderDomainService->>DownstreamSystems: Publish Cart Updated Event
        DownstreamSystems->>DownstreamSystems: Process Cart Update
    else Cart ID doesn't exist
        OrderDomainService->>MagentoAdapter: Respond with Cart Update Failed Event
        MagentoAdapter->>Magento: Send Conflict Response (HTTP 409)
    end
```

## Key Integration Points

### Cart Update Triggers
  - User Has Existing Cart **and either**
    - User Modifies item data in cart
    - User adds an item to the cart

### Data Flow Considerations
- **Critical IDs**: The adapter must store `cart_id`, `customer_id` (if available), and `store_id` for future reference
- **Guest Support**: The flow handles both guest and authenticated customers
- **B2B Features**: Supports negotiable quotes when `is_negotiable_quote` is true
- **Currency Support**: All monetary values include currency information

### Error Handling
- Validation failures return HTTP 400 with detailed error messages
- Nonexistent cart update attempts return HTTP 409 Conflict with clear error message
- Domain validation ensures business rules are enforced
- Failed downstream processing does not block the cart creation response

## Technical Details

### REST API Endpoint
- **URL**: `POST /api/v0.1/magento-integration-adapter/cart-updated`
- **Channel**: `magento-integration-adapter.{env}.rest.queries`
- **Authentication**: Required (implementation specific)

### Message Types
- **Query**: `cart-updated-query` (v1.1.0) - Initiated by Magento
- **Command**: `record-cart-updated-command` - Internal command for domain processing
- **Events**:
  - `cart-updated-event` - Published to downstream systems on successful creation
  - `cart-update-failed-event` - Published when cart update fails (e.g., non-existent ID)

### Data Mapping
The integration layer receives comprehensive cart data matching Magento's `quote-cart-interface` structure:
- Cart metadata (ID, timestamps, store context)
- Customer information (if authenticated)
- Item details with product options
- Pricing and totals
- B2B-specific attributes

## Owners
- euvic
