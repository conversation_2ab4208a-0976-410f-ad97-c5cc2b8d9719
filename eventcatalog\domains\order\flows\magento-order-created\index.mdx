---
id: magento-order-created-flow
name: Magento Order Created
version: 0.0.1
summary: Business flow for processing a new order from Magento through adapters to SAP ECC 6
steps:
  - id: customer_places_order
    title: Customer Places Order
    actor:
      name: "Parts West Customer"
    next_step:
      id: "magento_accepts_order"

  - id: "magento_accepts_order"
    title: "Magento"
    service:
      id: "magento"
    next_steps:
      - id: "magento_adapter_receives_request"

  - id: magento_adapter_receives_request
    title: Magento Integration Adapter Receives Request
    message:
      id: order-created
    next_step:
      id: "magento_integration_adapter"

  - id: "magento_integration_adapter"
    title: "Magento Integration Adapter"
    service:
      id: "magento-integration-adapter"
      version: "0.0.2"
    next_steps:
      - id: "record_order_created_command_sent"

  - id: record_order_created_command_sent
    title: Magento Integration Adapter Sends Record Order Created Command
    message:
      id: record-order-created-command
    next_step:
      id: "order_domain_service_receives_command"

  - id: "order_domain_service_receives_command"
    title: "Order Domain Service"
    service:
      id: "order-domain-service"
      version: "0.0.1"
    next_steps:
      - id: order_domain_service_validates_command

  - id: order_domain_service_validates_command
    title: Order Domain Service Validates Command
    next_step:
      id: "order_domain_service_publishes_order_created_event"

  - id: order_domain_service_publishes_order_created_event
    title: Order Domain Service Publishes Order Created Event
    message:
      id: order-created-event
    next_step:
      id: "sap_integration_adapter_receives_order_created_event"

  - id: sap_integration_adapter_receives_order_created_event
    title: SAP Integration Adapter Consumes Order Created Event
    service:
      id: "sap-integration-adapter"
      version: "0.0.1"
    next_steps:
      - id: "sap_integration_adapter_sends_order_created_query"

  - id: sap_integration_adapter_sends_order_created_query
    title: SAP Integration Adapter Sends Order Created Query
    message:
      id: create-sales-order
    next_step:
      id: "sap_receives_query"

  - id: sap_receives_query
    title: SAP Receives Query
    service:
      id: sap-integration-adapter
    next_steps:
      - id: "sap_integration_adapter_sends_sap_sales_order_created_event"

  - id: sap_integration_adapter_sends_sap_sales_order_created_event
    title: SAP Integration Adapter Sends SAP Sales Order Created Event
    message:
      id: sap-sales-order-created-event
      version: "0.0.1"
    next_step:
      id: "domain_service_receives_sap_sales_order_created_event"

  - id: domain_service_receives_sap_sales_order_created_event
    title: Domain Service Receives SAP Sales Order Created Event
    service:
      id: "order-domain-service"
      version: "0.0.1"
    next_steps:
      - id: "domain_service_publishes_order_acknowledged_event"

  - id: domain_service_publishes_order_acknowledged_by_erp_event
    title: Domain Service Publishes Order Acknowledged By ERP Event
    message:
      id: order-acknowledged-by-erp-event
      version: "0.0.1"
    next_step:
      id: "magento_integration_adapter_receives_order_acknowledged_event"

  - id: magento_integration_adapter_receives_order_acknowledged_event
    title: Magento Integration Adapter Receives Order Acknowledged Event
    service:
      id: "magento-integration-adapter"
      version: "0.0.2"
    next_step:
      id: "magento_integration_adapter_sends_order_updated_query"

  - id: magento_integration_adapter_sends_order_updated_query
    title: Magento Integration Adapter Sends Order Updated Query
    message:
      id: magento-order-status-update
    next_step:
      id: "magento_receives_order_updated_query"

  - id: magento_receives_order_updated_query
    title: Magento Receives Order Updated Query
    service:
      id: "magento"

---

### Flow of feature
<NodeGraph />

## Sequence Diagram

```mermaid
sequenceDiagram
    participant Customer
    participant Magento
    participant MagentoAdapter as Magento Integration Adapter
    participant OrderDomainService as Order Domain Service
    participant SAPAdapter as SAP Integration Adapter
    participant SAP

    Customer->>Magento: Place Order
    Magento->>MagentoAdapter: Initiate Order Request
    MagentoAdapter->>MagentoAdapter: Validate Order Model
    MagentoAdapter->>MagentoAdapter: Transform Order Model to Order Created Event
    MagentoAdapter->>Magento: Send Query Response (order-created)
    Magento->>Customer: Order Acknowledged
    MagentoAdapter->>OrderDomainService: Send Record Order Created Command
    OrderDomainService->>SAPAdapter: Publish Order Created Event
    SAPAdapter->>SAP: Send Query (create sales order)
    SAP->>SAP: Order Created in SAP ECC 6
    SAP->>SAPAdapter: Query Response (create sales order)
    SAPAdapter->>OrderDomainService: Publish SAP Sales Order Created Event
    OrderDomainService->>MagentoAdapter: Publish Order Acknowledged by ERP Event
    MagentoAdapter->>Magento: Send Order Updated Query
```

## Owners
- euvic
- enterprise
