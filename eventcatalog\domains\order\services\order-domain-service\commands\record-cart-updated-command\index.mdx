---
id: record-cart-updated-command
name: Record Cart Updated Command
version: 0.0.1
summary: Command to record updated to an existing shopping cart in the order domain
owners:
  - enterprise
channels:
  - id: aws.sqs.{env}.record-cart-updated-command
    description: Record Cart Updated Command SQS Queue
    parameters:
      env: local
schemaPath: "schema.json"
---

# Overview

The `RecordCartUpdatedCommand` is sent by the Magento Integration Adapter to the 
Order Domain Service to record an update to an existing shopping cart.
This command contains all necessary information to create an update record in the order domain.

## Architecture

<NodeGraph />

<SchemaViewer file="schema.json" title="JSON Schema" maxHeight="500" />

## Example Payload

```json
{
  "cartId": "1234567890",
  "storeId": "3",
  "createdAt": "2025-01-15T10:30:00Z",
  "isNegotiableQuote": false,
  "isMultiShipping": false,
  "itemsCount": 0,
  "itemsQty": 0,
  "items": [
    {
      "item_id": "item-1",
      "sku": "SKU001",
      "qty": 2,
      "product_type": "simple"
    },
    {
      
      "item_id": "item-2",
      "sku": "SKU002",
      "qty": 1,
      "product_type": "simple"
    }
  ],
  "billingAddress": null,
  "shippingAddress": null,
  "idempotencyKey": "cart-1234567890-created"
}
```