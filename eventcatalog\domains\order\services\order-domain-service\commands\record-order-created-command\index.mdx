---
id: record-order-created-command
name: Record Order Created Command
version: 0.0.1
summary: Command sent to the Order Domain Service to record a new order creation.
owners:
  - enterprise
channels:
  - id: aws.sqs.{env}.record-order-created-command
    name: Record Order Created Command SQS Queue
    type: sqs
    parameters:
      env: local
schemaPath: 'schema.json'
---

## Overview

This command is sent to the Order Domain Service to record a new order creation. It represents the canonical command for order creation in the system.

## Architecture diagram

<NodeGraph/>

<SchemaViewer file="schema.json" title="JSON Schema" maxHeight="500" />

## Payload example

```json title="Payload example"
{
  "cartId": "cart-12345",
  "customerEmail": "<EMAIL>",
  "customerFirstname": "<PERSON>",
  "customerLastname": "Doe",
  "storeId": 1,
  "items": [
    {
      "sku": "SKU-001",
      "qty": 2,
      "price": 19.99,
      "name": "Product Name",
      "productType": "simple",
      "productOption": {
        "extensionAttributes": {}
      }
    }
  ],
  "billingAddress": {
    "firstname": "<PERSON>",
    "lastname": "Doe",
    "street": ["123 Main St"],
    "city": "Metropolis",
    "region": "NY",
    "postcode": "12345",
    "countryId": "US",
    "telephone": "555-1234"
  },
  "shippingAddress": {
    "firstname": "John",
    "lastname": "Doe",
    "street": ["123 Main St"],
    "city": "Metropolis",
    "region": "NY",
    "postcode": "12345",
    "countryId": "US",
    "telephone": "555-1234"
  },
  "payment": {
    "method": "credit_card"
  },
  "shippingMethod": {
    "methodCode": "flatrate",
    "carrierCode": "fedex"
  }
}
``` 