{"$schema": "http://json-schema.org/draft-07/schema#", "title": "RecordOrderCreatedCommand", "type": "object", "properties": {"cartId": {"type": "string"}, "customerEmail": {"type": "string"}, "customerFirstname": {"type": ["string", "null"]}, "customerLastname": {"type": ["string", "null"]}, "storeId": {"type": "integer"}, "items": {"type": "array", "items": {"type": "object", "properties": {"sku": {"type": "string"}, "qty": {"type": "number"}, "price": {"type": "number"}, "name": {"type": "string"}, "productType": {"type": "string"}, "productOption": {"type": ["object", "null"], "properties": {"extensionAttributes": {}}, "required": ["extensionAttributes"]}}, "required": ["sku", "qty", "price", "name", "productType"]}}, "billingAddress": {"type": "object", "properties": {"firstname": {"type": "string"}, "lastname": {"type": "string"}, "street": {"type": "array", "items": {"type": "string"}}, "city": {"type": "string"}, "region": {"type": ["string", "null"]}, "postcode": {"type": ["string", "null"]}, "countryId": {"type": "string"}, "telephone": {"type": "string"}}, "required": ["firstname", "lastname", "street", "city", "countryId", "telephone"]}, "shippingAddress": {"type": ["object", "null"], "properties": {"firstname": {"type": "string"}, "lastname": {"type": "string"}, "street": {"type": "array", "items": {"type": "string"}}, "city": {"type": "string"}, "region": {"type": ["string", "null"]}, "postcode": {"type": ["string", "null"]}, "countryId": {"type": "string"}, "telephone": {"type": "string"}}, "required": ["firstname", "lastname", "street", "city", "countryId", "telephone"]}, "payment": {"type": "object", "properties": {"method": {"type": "string"}}, "required": ["method"]}, "shippingMethod": {"type": ["object", "null"], "properties": {"methodCode": {"type": "string"}, "carrierCode": {"type": "string"}}, "required": ["methodCode", "carrierCode"]}}, "required": ["cartId", "customerEmail", "storeId", "items", "billing<PERSON><PERSON>ress", "payment"]}