{"$schema": "http://json-schema.org/draft-07/schema#", "title": "CartCreationFailedEvent", "type": "object", "description": "Event published when a cart creation attempt fails", "required": ["cartId", "reason", "errorCode", "timestamp"], "properties": {"cartId": {"type": "string", "description": "The unique identifier of the cart that failed to be created"}, "reason": {"type": "string", "description": "Human-readable description of why the cart creation failed"}, "errorCode": {"type": "string", "description": "Machine-readable error code for categorizing the failure", "enum": ["DUPLICATE_CART", "INVALID_CUSTOMER", "SYSTEM_ERROR"]}, "timestamp": {"type": "string", "format": "date-time", "description": "The timestamp when the failure occurred (ISO 8601 format)"}, "eventMetadata": {"type": "object", "description": "Standard event metadata", "required": ["eventId", "timestamp", "source", "version"], "properties": {"eventId": {"type": "string", "description": "Unique identifier for this specific event instance"}, "timestamp": {"type": "string", "format": "date-time", "description": "When this event was generated"}, "source": {"type": "string", "description": "The service that generated this event"}, "version": {"type": "string", "description": "The version of the event schema"}}}}}