---
id: cart-update-failed-event
name: Cart Update Failed Event
version: 0.0.1
summary: Event published by the Order Domain Service when an attempt to update an existing cart fails
owners:
  - enterprise
channels:
  - id: aws.sns.{env}.cart-update-failed-event
    name: Cart Update Failed Event Topic
    type: sns
    parameters:
      env: local
schemaPath: 'schema.json'
---

## Overview

This event is published by the Order Domain Service when a cart update attempt fails in the Axon integration layer.
This event typically occurs when attempting to update a cart with an ID that doesn't exist in the system.

Downstream systems can subscribe to this event to:
- Log duplicate cart attempts for monitoring
- Update error tracking and metrics
- Trigger error recovery workflows
- Notify customer service systems of potential issues
- Maintain data consistency across systems

## Common Failure Scenarios

- **Non-Existent Cart ID**: Attempting to update a cart that doesn't exist (most common)
- **Invalid Customer Data**: Customer validation failures
- **System Constraints**: Business rule violations during cart update

## Architecture diagram

<NodeGraph/>

<SchemaViewer file="schema.json" title="JSON Schema" maxHeight="500" />

## Payload example

```json title="Payload example"
{
  "cartId": "1234567890",
  "reason": "Cart with ID '1234567890' doesn't exist.",
  "errorCode": "NONEXISTENT_CART",
  "timestamp": "2025-01-15T10:30:00Z",
  "eventMetadata": {
    "eventId": "evt_fail_1234567890",
    "timestamp": "2025-01-15T10:30:00Z",
    "source": "order-domain-service",
    "version": "1.0.0"
  }
}
```

## Error Codes

| Error Code | Description | Recovery Action |
|------------|-------------|-----------------|
| NONEXISTENT_CART | Cart with the given ID doesn't exist | Use existing cart |
| INVALID_CUSTOMER | Customer data validation failed | Correct customer data and retry |
| SYSTEM_ERROR | Internal system error occurred | Retry after delay or escalate |