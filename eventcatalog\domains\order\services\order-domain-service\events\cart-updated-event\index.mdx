---
id: cart-updated-event
name: <PERSON>t Updated Event
version: 0.0.1
summary: Event published by the Order Domain Service when a cart is updated in the Axon integration layer
owners:
  - enterprise
channels:
  - id: aws.sns.{env}.cart-updated-event
    name: Cart Updated Event Topic
    type: sns
    parameters:
      env: local
schemaPath: 'schema.json'
---

## Overview

This event is published by the Order Domain Service when a cart has been successfully updated in the Axon integration layer.
This event signifies that an existing shopping cart has been updated for a customer session and is ready to receive items.

Downstream systems can subscribe to this event to:
- Initialize session tracking
- Prepare personalization data
- Update analytics and reporting
- Trigger marketing automation workflows
- Synchronize cart state across channels

## Architecture diagram

<NodeGraph/>

<SchemaViewer file="schema.json" title="JSON Schema" maxHeight="500" />

## Payload example

```json title="Payload example"
{
  "cartId": "1234567890",
  "storeId": "3",
  "updatedAt": "2025-01-15T10:30:00Z",
  "isNegotiableQuote": false,
  "isMultiShipping": false,
  "itemsCount": 2,
  "itemsQty": 3,
  "items": [
    {
      "item_id": "item-1",
      "sku": "SKU001",
      "qty": 2,
      "product_type": "simple"
    },
    {
      
      "item_id": "item-2",
      "sku": "SKU002",
      "qty": 1,
      "product_type": "simple"
    }
  ],
  "billingAddress": null,
  "shippingAddress": null,
  "paymentMethod": null,
  "shippingMethod": null,
  "eventMetadata": {
    "eventId": "evt_1234567890",
    "timestamp": "2025-01-15T10:30:00Z",
    "source": "order-domain-service",
    "version": "0.0.1"
  }
}
```