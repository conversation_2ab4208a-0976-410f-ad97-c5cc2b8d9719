{"$schema": "http://json-schema.org/draft-07/schema#", "title": "CartUpdatedEvent", "type": "object", "required": ["cartId", "storeId", "createdAt", "updatedAt", "currency", "totals", "isActive", "isVirtual", "itemsCount", "itemsQty", "items", "customer", "eventMetadata"], "properties": {"cartId": {"type": "string", "description": "Unique identifier for the cart"}, "customerId": {"type": ["string", "null"], "description": "Customer ID if authenticated, null for guest"}, "storeId": {"type": "string", "description": "Store ID where cart was created"}, "createdAt": {"type": "string", "format": "date-time", "description": "Cart creation timestamp in ISO 8601 format"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Cart last update timestamp in ISO 8601 format"}, "currency": {"type": "object", "required": ["baseCurrencyCode", "quoteCurrencyCode"], "properties": {"baseCurrencyCode": {"type": "string", "description": "Base currency code (e.g., USD)", "pattern": "^[A-Z]{3}$"}, "quoteCurrencyCode": {"type": "string", "description": "Quote currency code", "pattern": "^[A-Z]{3}$"}}}, "totals": {"type": "object", "required": ["grandTotal", "baseTaxAmount", "taxAmount", "baseSubtotal", "subtotal"], "properties": {"grandTotal": {"type": "number", "description": "Grand total amount including tax and shipping"}, "baseTaxAmount": {"type": "number", "description": "Base tax amount in base currency"}, "taxAmount": {"type": "number", "description": "Tax amount in quote currency"}, "baseSubtotal": {"type": "number", "description": "Base subtotal amount excluding tax and shipping"}, "subtotal": {"type": "number", "description": "Subtotal amount in quote currency"}}}, "isActive": {"type": "boolean", "description": "Whether the cart is active"}, "isVirtual": {"type": "boolean", "description": "Whether the cart contains only virtual products"}, "isNegotiableQuote": {"type": "boolean", "description": "Whether this is a B2B negotiable quote", "default": false}, "isMultiShipping": {"type": "boolean", "description": "Whether multi-shipping is enabled", "default": false}, "itemsCount": {"type": "integer", "description": "Number of distinct items in cart", "minimum": 0}, "itemsQty": {"type": "number", "description": "Total quantity of all items", "minimum": 0}, "items": {"type": "array", "description": "Cart items", "items": {"type": "object", "required": ["itemId", "sku", "name", "qty", "price"], "properties": {"itemId": {"type": "string", "description": "Cart item ID"}, "sku": {"type": "string", "description": "Product SKU"}, "name": {"type": "string", "description": "Product name"}, "qty": {"type": "number", "description": "Item quantity", "minimum": 0}, "price": {"type": "number", "description": "Item price per unit"}, "productType": {"type": "string", "description": "Product type (simple, configurable, etc.)"}, "productOption": {"type": ["object", "null"], "description": "Product configuration options"}}}}, "customer": {"type": "object", "required": ["isGuest"], "properties": {"email": {"type": ["string", "null"], "format": "email", "description": "Customer email address"}, "groupId": {"type": ["integer", "null"], "description": "Customer group ID"}, "isGuest": {"type": "boolean", "description": "Whether this is a guest checkout"}, "firstName": {"type": ["string", "null"], "description": "Customer first name"}, "lastName": {"type": ["string", "null"], "description": "Customer last name"}}}, "billingAddress": {"type": ["object", "null"], "description": "Billing address if set", "properties": {"id": {"type": "string", "description": "Address ID"}, "region": {"type": ["string", "null"], "description": "State/Province"}, "country": {"type": "string", "description": "Country code"}, "street": {"type": "array", "items": {"type": "string"}, "description": "Street address lines"}, "city": {"type": "string", "description": "City"}, "postcode": {"type": "string", "description": "Postal code"}, "firstname": {"type": "string", "description": "First name"}, "lastname": {"type": "string", "description": "Last name"}, "telephone": {"type": ["string", "null"], "description": "Phone number"}}}, "shippingAddress": {"type": ["object", "null"], "description": "Shipping address if set", "properties": {"id": {"type": "string", "description": "Address ID"}, "region": {"type": ["string", "null"], "description": "State/Province"}, "country": {"type": "string", "description": "Country code"}, "street": {"type": "array", "items": {"type": "string"}, "description": "Street address lines"}, "city": {"type": "string", "description": "City"}, "postcode": {"type": "string", "description": "Postal code"}, "firstname": {"type": "string", "description": "First name"}, "lastname": {"type": "string", "description": "Last name"}, "telephone": {"type": ["string", "null"], "description": "Phone number"}}}, "paymentMethod": {"type": ["object", "null"], "description": "Selected payment method", "properties": {"method": {"type": "string", "description": "Payment method code"}, "poNumber": {"type": ["string", "null"], "description": "Purchase order number for B2B"}}}, "shippingMethod": {"type": ["object", "null"], "description": "Selected shipping method", "properties": {"carrierCode": {"type": "string", "description": "Carrier code"}, "methodCode": {"type": "string", "description": "Method code"}, "methodTitle": {"type": "string", "description": "Method display name"}, "amount": {"type": "number", "description": "Shipping cost"}}}, "eventMetadata": {"type": "object", "required": ["eventId", "timestamp", "source", "version"], "properties": {"eventId": {"type": "string", "description": "Unique event identifier"}, "timestamp": {"type": "string", "format": "date-time", "description": "Event generation timestamp"}, "source": {"type": "string", "description": "Service that published the event"}, "version": {"type": "string", "description": "Event schema version"}}}}}