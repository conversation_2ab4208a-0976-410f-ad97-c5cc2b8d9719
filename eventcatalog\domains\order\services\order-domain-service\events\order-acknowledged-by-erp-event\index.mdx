---
id: order-acknowledged-by-erp-event
name: Order Acknowledged by ERP Event
version: 0.0.1
summary: |
  Event published by the Order Domain Service when an order has been acknowledged by the ERP system
  Downstream systems can use this event to update their systems with the order acknowledgement.
owners:
  - enterprise
channels:
  - id: aws.sns.{env}.order-acknowledged-by-erp-event
    name: Order Acknowledged by ERP Event Topic
    type: sns
    parameters:
      env: local
schemaPath: 'schema.json'
---

## Overview

This event is published by the Order Domain Service when an order has been acknowledged by the ERP system.
Downstream systems can use this event to update their systems with the order acknowledgement.

## Architecture diagram

<NodeGraph/>

<SchemaViewer file="schema.json" title="JSON Schema" maxHeight="500" />

## Payload example

```json title="Payload example"
{
  "ecomm": {
    "orderId": "1234567890"
  },
  "erp": {
    "orderId": "1234567890"
  }
}
``` 