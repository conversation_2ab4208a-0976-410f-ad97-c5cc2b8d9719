{"$schema": "http://json-schema.org/draft-07/schema#", "title": "RecordOrderCreatedCommand", "type": "object", "properties": {"ecomm": {"type": "object", "properties": {"orderId": {"type": "string", "description": "The ID of the order that was created by the e-commerce system (Magento)"}}, "required": ["orderId"]}, "erp": {"type": "object", "properties": {"orderId": {"type": "string", "description": "The ID of the order that was created by the ERP system (SAP)"}}, "required": ["orderId"]}}, "required": ["ecomm", "erp"]}