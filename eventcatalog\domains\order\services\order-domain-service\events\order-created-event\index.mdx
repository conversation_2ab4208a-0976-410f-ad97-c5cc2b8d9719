---
id: order-created-event
name: Order Created Event
version: 1.0.0
summary: Event published by the Order Domain Service when an order created in the Axon integration layer. 
owners:
  - enterprise
channels:
  - id: aws.sns.{env}.order-created-event
    name: Order Created Event Topic
    type: sns
    parameters:
      env: local
schemaPath: 'schema.json'
---

## Overview

This event is published by the Order Domain Service when an order has been created in the Axon integration layer. This event does not signify that the order has been acknowledged by the ERP system.

## Architecture diagram

<NodeGraph/>

<SchemaViewer file="schema.json" title="JSON Schema" maxHeight="500" />

## Payload example

```json title="Payload example"
{
  "CartId": "CART12345",
  "CustomerEmail": "<EMAIL>",
  "CustomerFirstname": "<PERSON>",
  "CustomerLastname": "Doe",
  "StoreId": 1,
  "Items": [
    {
      "Sku": "SKU123",
      "Qty": 2,
      "Price": 19.99,
      "Name": "T-Shirt",
      "ProductType": "simple",
      "ProductOption": {
        "ExtensionAttributes": {}
      }
    }
  ],
  "BillingAddress": {
    "Firstname": "<PERSON>",
    "Lastname": "Doe",
    "Street": ["123 Main St"],
    "City": "Metropolis",
    "Region": "NY",
    "Postcode": "12345",
    "CountryId": "US",
    "Telephone": "555-1234"
  },
  "ShippingAddress": {
    "Firstname": "John",
    "Lastname": "Doe",
    "Street": ["123 Main St"],
    "City": "Metropolis",
    "Region": "NY",
    "Postcode": "12345",
    "CountryId": "US",
    "Telephone": "555-1234"
  },
  "Payment": {
    "Method": "credit_card"
  },
  "ShippingMethod": {
    "MethodCode": "flatrate",
    "CarrierCode": "fedex"
  },
  "PurchaseOrderNumber": "PO123456",
  "OrderType": "OR",
  "SoldToParty": "CUST7890",
  "ShipToParty": "CUST7891",
  "Currency": "USD",
  "RequestedDeliveryDate": "2024-03-21T10:00:00Z",
  "SapItems": [
    {
      "MaterialNumber": "SKU123",
      "Quantity": 2,
      "UnitOfMeasure": "EA",
      "Plant": "PLANT1"
    }
  ]
}
``` 