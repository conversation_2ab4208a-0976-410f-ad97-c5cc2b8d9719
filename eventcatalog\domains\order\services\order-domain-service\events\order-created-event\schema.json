{"$schema": "http://json-schema.org/draft-07/schema#", "title": "OrderCreatedEvent", "type": "object", "properties": {"CartId": {"type": "string", "description": "The unique identifier for the cart associated with the order."}, "CustomerEmail": {"type": "string", "description": "The email address of the customer placing the order."}, "CustomerFirstname": {"type": ["string", "null"], "description": "The first name of the customer."}, "CustomerLastname": {"type": ["string", "null"], "description": "The last name of the customer."}, "StoreId": {"type": "integer", "description": "The identifier of the store where the order was placed."}, "Items": {"type": "array", "items": {"$ref": "#/definitions/OrderItem"}, "description": "The list of items included in the order."}, "BillingAddress": {"$ref": "#/definitions/Address", "description": "The billing address for the order."}, "ShippingAddress": {"anyOf": [{"$ref": "#/definitions/Address"}, {"type": "null"}], "description": "The shipping address for the order, if different from billing."}, "Payment": {"$ref": "#/definitions/Payment", "description": "The payment information for the order."}, "ShippingMethod": {"anyOf": [{"$ref": "#/definitions/ShippingMethod"}, {"type": "null"}], "description": "The shipping method selected for the order."}, "PurchaseOrderNumber": {"type": "string", "description": "The purchase order number, typically mapped from CartId or an external order number."}, "OrderType": {"type": "string", "description": "The type of order, e.g., 'OR' for standard order."}, "SoldToParty": {"type": "string", "description": "The identifier for the party to whom the order is sold."}, "ShipToParty": {"type": ["string", "null"], "description": "The identifier for the party to whom the order is shipped, if different from SoldToParty."}, "Currency": {"type": "string", "description": "The currency code for the order (e.g., 'USD')."}, "RequestedDeliveryDate": {"type": ["string", "null"], "format": "date-time", "description": "The requested delivery date for the order, if specified."}, "SapItems": {"type": "array", "items": {"$ref": "#/definitions/SapOrderItem"}, "description": "The list of SAP-specific order items for integration with SAP ECC 6."}}, "required": ["CartId", "CustomerEmail", "StoreId", "Items", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Payment", "PurchaseOrderNumber", "OrderType", "SoldToParty", "<PERSON><PERSON><PERSON><PERSON>", "SapItems"], "definitions": {"OrderItem": {"type": "object", "properties": {"Sku": {"type": "string", "description": "The SKU (Stock Keeping Unit) of the product."}, "Qty": {"type": "number", "description": "The quantity of the product ordered."}, "Price": {"type": "number", "description": "The price per unit of the product."}, "Name": {"type": "string", "description": "The name of the product."}, "ProductType": {"type": "string", "description": "The type of the product (e.g., 'simple', 'configurable')."}, "ProductOption": {"anyOf": [{"$ref": "#/definitions/ProductOption"}, {"type": "null"}], "description": "Additional options for the product, if applicable."}}, "required": ["S<PERSON>", "Qty", "Price", "Name", "ProductType"], "description": "An item included in the order."}, "SapOrderItem": {"type": "object", "properties": {"MaterialNumber": {"type": "string", "description": "The material number in SAP, mapped from the SKU."}, "Quantity": {"type": "number", "description": "The quantity of the material ordered."}, "UnitOfMeasure": {"type": "string", "description": "The unit of measure for the material (e.g., 'EA' for each)."}, "Plant": {"type": ["string", "null"], "description": "The plant code in SAP, if applicable."}}, "required": ["MaterialNumber", "Quantity", "UnitOfMeasure"], "description": "A SAP-specific item for integration with SAP ECC 6."}, "ProductOption": {"type": "object", "properties": {"ExtensionAttributes": {"description": "Custom extension attributes for the product option."}}, "description": "Additional options or attributes for a product."}, "Address": {"type": "object", "properties": {"Firstname": {"type": "string", "description": "The first name of the recipient."}, "Lastname": {"type": "string", "description": "The last name of the recipient."}, "Street": {"type": "array", "items": {"type": "string"}, "description": "The street address lines."}, "City": {"type": "string", "description": "The city of the address."}, "Region": {"type": ["string", "null"], "description": "The region or state of the address."}, "Postcode": {"type": ["string", "null"], "description": "The postal code of the address."}, "CountryId": {"type": "string", "description": "The country code of the address (e.g., 'US')."}, "Telephone": {"type": "string", "description": "The telephone number for the address."}}, "required": ["Firstname", "Lastname", "Street", "City", "CountryId", "Telephone"], "description": "A postal address for billing or shipping."}, "Payment": {"type": "object", "properties": {"Method": {"type": "string", "description": "The payment method used for the order (e.g., 'credit_card')."}}, "required": ["Method"], "description": "Payment information for the order."}, "ShippingMethod": {"type": "object", "properties": {"MethodCode": {"type": "string", "description": "The code for the shipping method (e.g., 'flatrate')."}, "CarrierCode": {"type": "string", "description": "The code for the shipping carrier (e.g., 'fedex')."}}, "required": ["MethodCode", "CarrierCode"], "description": "The shipping method selected for the order."}}}