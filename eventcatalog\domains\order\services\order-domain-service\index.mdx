---
id: order-domain-service
name: Order Domain Service
version: 0.0.1
summary: Service responsible for core order domain logic and orchestration.
owners:
  - enterprise
sends:
  - id: order-acknowledged-by-erp-event
    version: 0.0.1
  - id: order-created-event
    version: 1.0.0
  - id: cart-created-event
    version: 1.0.0
  - id: cart-creation-failed-event
    version: 1.0.0
receives:
  - id: record-order-created-command
    version: 0.0.1
  - id: record-cart-created-command
    version: 1.0.0
  - id: sap-sales-order-created-event
    version: 0.0.1
---

# Order Domain Service

This service handles the core business logic and orchestration for the Order domain, including order creation, updates, and fulfillment processes. 