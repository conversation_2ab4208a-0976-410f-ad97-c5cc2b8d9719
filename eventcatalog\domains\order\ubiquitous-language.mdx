---
dictionary:
  - id: CustomerNumber
    name: Customer Number
    summary: "A unique identifier for customers in the SAP system, with distinct numbers for ship-to and sold-to relationships."
    description: |
      The system uses two types of customer numbers: CUSTOMER_NUMBER_SH (ship-to) and CUSTOMER_NUMBER_SP (sold-to). These 10-character identifiers are used to track customer relationships, orders, and shipping information. Ship-to numbers indicate where goods should be delivered, while sold-to numbers represent the purchasing entity.
    icon: User
  - id: ShippingType
    name: Shipping Type
    summary: "Specifies the method and terms of delivery for an order."
    description: |
      The shipping type (SHIP_TYPE) determines how products will be delivered to the customer. It includes information about the carrier, delivery method, and any special handling requirements. This information is used for logistics planning and customer communication.
    icon: Truck
---