---
dictionary:
  - id: TargetQuantity
    name: Target Quantity
    summary: "The requested quantity of a product in a sales order."
    description: |
      Target quantity (TARGET_QTY) represents the number of units requested in an order. It is stored as a 10-character padded number and is used for inventory checks, order fulfillment, and delivery planning.
    icon: Hash
  - id: PricePerUnit
    name: Price Per Unit
    summary: "The unit price of a product in a sales order."
    description: |
      Price per unit (PRICE_PER) represents the cost of a single unit of a product. It is used in conjunction with target quantity to calculate the total order value and is subject to various conditions and discounts.
    icon: DollarSign
---