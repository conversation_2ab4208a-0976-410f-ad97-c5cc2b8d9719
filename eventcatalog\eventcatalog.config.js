/** @type {import('@eventcatalog/core/bin/eventcatalog.config').Config} */
import path from 'path';
import { fileURLToPath } from 'url';
import rehypeRaw from 'rehype-raw'

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export default {
  title: 'Axon Event Catalog',
  tagline: 'This internal platform provides a comprehensive view of our event-driven architecture across the Evolution E-commerce systems. Use this portal to discover existing domains, explore services and their dependencies, and understand the message contracts that connect our infrastructure',
  organizationName: 'Alliance Laundry Systems',
  homepageLink: 'https://tbd.dev/',
  editUrl: 'https://github.com/ALSSoftware/evolution-integration-layer/edit/main',
  // By default set to false, add true to get urls ending in /
  trailingSlash: false,
  // Change to make the base url of the site different, by default https://{website}.com/docs,
  // changing to /company would be https://{website}.com/company/docs,
  base: '/',
  // Customize the logo, add your logo to public/ folder
  logo: {
    alt: 'Alliance Laundry Systems Logo',
    src: '/axon.png',
    text: 'Axon'
  },
  // Enable RSS feed for your eventcatalog
  rss: {
    enabled: false // Disable RSS if not needed
  },
  docs: {
    sidebar: {
      // TREE_VIEW will render the DOCS as a tree view and map your file system folder structure
      // LIST_VIEW will render the DOCS that look familiar to API documentation websites
      type: 'LIST_VIEW'
    },
  },
  customDocs: {
    sidebar: [
      {
        label: '01.Getting Started',
        collapsed: true,
        items: [
          { label: 'Getting Started', slug: '01-getting-started/getting-started' },
        ]
      },
      {
        label: '02. Working Agreements',
        collapsed: true,
        items: [
          { label: 'Versioning', slug: '02-working-agreements/01-versioning' },
        ]
      },
      {
        label: '03. Infrastructure',
        badge: {
          text: 'New', color: 'green'
        },
        collapsed: true,
        items: [
          { label: 'Overview', slug: '03-infrastructure/00-overview' },
          { label: 'AWS Network Diagram', slug: '03-infrastructure/01-aws-network-diagram' },
        ]
      },
      {
        label: '04. Cybersecurity Meetings',
        collapsed: true,
        items: [
          { label: '2025-06-03', slug: '04-cybersecurity-meetings/20250603' },
          { label: '2025-06-10', slug: '04-cybersecurity-meetings/20250610' },
          { label: '2025-06-17', slug: '04-cybersecurity-meetings/20250617' },
          { label: '2025-06-24', slug: '04-cybersecurity-meetings/20250624' },
          { label: '2025-07-01', slug: '04-cybersecurity-meetings/20250701' },
        ]
      },
      {
        label: '05. Business Documentation',
        badge: {
          text: 'Confluence', color: 'purple'
        },
        collapsed: true,
        items: [
          { label: 'RMA', slug: '05-business-documentation/rma' },
          { label: 'Stock Synchronization', slug: '05-business-documentation/stock-synchronization' },
          { label: 'Product Management', slug: '05-business-documentation/product-management' },
          { label: 'Price Management in PIMCORE', slug: '05-business-documentation/price-management-in-pimcore' },
          { label: 'PIMCORE Product Structure', slug: '05-business-documentation/pimcore-product-structure' },
          { label: 'Email System Communication', slug: '05-business-documentation/email-system-communication' },
          { label: 'Replacement Parts Management', slug: '05-business-documentation/replacement-parts-management' },
          { label: 'UX Pages', slug: '05-business-documentation/ux-pages' },
          { label: 'Discounts', slug: '05-business-documentation/discounts' },
        ]
      },
      {
        label: '06. Magento Backend Documentation',
        badge: {
          text: 'Confluence', color: 'purple'
        },
        collapsed: true,
        items: [
          // [PU] Purchase - has sub-pages in Confluence
          {
            label: '[PU] Purchase',
            collapsed: true,
            items: [
              { label: 'Purchase Module', slug: '06-magento-backend-documentation/purchase/purchase-module' },
              { label: '[PU] Processes', slug: '06-magento-backend-documentation/purchase/processes/pu-processes' },
              { label: 'PRO-PU-MA001 Order split', slug: '06-magento-backend-documentation/purchase/processes/pro-pu-ma001-order-split' },
            ]
          },
          
          // Root level pages (depth 1) - following ALS Confluence structure
          { label: '[PC] Product Catalogue', slug: '06-magento-backend-documentation/product-catalogue-module' },
          { label: '[RC] Returns and Complaints', slug: '06-magento-backend-documentation/returns-complaints-module' },
          
          // [CA] Customer Account - has subfolders in Confluence
          {
            label: '[CA] Customer Account',
            collapsed: true,
            items: [
              { label: 'Customer Account Module', slug: '06-magento-backend-documentation/customer-account/customer-account-module' },
              // Pages from [MOD] Modifications folder
              { label: 'MOD-CA-MA001 Suppliers', slug: '06-magento-backend-documentation/customer-account/modifications/mod-ca-ma001-suppliers' },
              // Pages from [CA] Interfaces folder
              { label: 'INT-CA-MA001 Newsletter Integration Layer', slug: '06-magento-backend-documentation/customer-account/interfaces/int-ca-ma001-newsletter-integration-layer' },
            ]
          },
          
          { label: '[SE] Search Engine', slug: '06-magento-backend-documentation/search-engine-module' },
          { label: '[SP] Static Pages', slug: '06-magento-backend-documentation/static-pages-module' },
          { label: '[AP] Admin Panel', slug: '06-magento-backend-documentation/admin-panel-module' },
          
          // [OP] Order Process - has subfolders in Confluence  
          {
            label: '[OP] Order Process',
            collapsed: true,
            items: [
              { label: 'Order Process Module', slug: '06-magento-backend-documentation/order-process/order-process-module' },
              // Pages from [OP] Processes folder
              { label: 'PRO-OP-MA001 Dimensional Shipping', slug: '06-magento-backend-documentation/order-process/processes/pro-op-ma001-dimensional-shipping' },
              // Pages from [OP] Modifications folder
              { label: 'MOD-OP-MA001 SAP Order ID', slug: '06-magento-backend-documentation/order-process/modifications/mod-op-ma001-sap-order-id' },
            ]
          },
          
          // [IN] INTEGRATION with INT-EVE-MA services
          {
            label: '[IN] INTEGRATION',
            collapsed: true,
            items: [
              { label: 'Integration Overview', slug: '06-magento-backend-documentation/integration/integration-module' },
              { label: 'INT-EVE-MA000 General', slug: '06-magento-backend-documentation/integration/int-eve-ma000-general' },
              { label: 'INT-EVE-MA001 Order Service', slug: '06-magento-backend-documentation/integration/int-eve-ma001-orderservice' },
              { label: 'INT-EVE-MA002 Shipment Service', slug: '06-magento-backend-documentation/integration/int-eve-ma002-shipmentservice' },
              { label: 'INT-EVE-MA003 Customer Service', slug: '06-magento-backend-documentation/integration/int-eve-ma003-customerservice' },
              { label: 'INT-EVE-MA004 Customer Address Service', slug: '06-magento-backend-documentation/integration/int-eve-ma004-customeraddressservice' },
              { label: 'INT-EVE-MA005 Product Service', slug: '06-magento-backend-documentation/integration/int-eve-ma005-productservice' },
              { label: 'INT-EVE-MA006 Cart Service', slug: '06-magento-backend-documentation/integration/int-eve-ma006-cartservice' },
              { label: 'INT-EVE-MA007 Return Service', slug: '06-magento-backend-documentation/integration/int-eve-ma007-returnservice' },
              { label: 'INT-EVE-MA008 RMA Service', slug: '06-magento-backend-documentation/integration/int-eve-ma008-rmaservice' },
              { label: 'INT-EVE-MA009 Invoice Service', slug: '06-magento-backend-documentation/integration/int-eve-ma009-invoiceservice' },
            ]
          },
          
          // Other root level pages
          { label: '[OM] Order Management', slug: '06-magento-backend-documentation/order-management' },
          
          // IS Services
          {
            label: 'Integration Services',
            collapsed: true,
            items: [
              { label: '[IS] Product Service', slug: '06-magento-backend-documentation/is-product-service' },
              { label: '[IS] Payment Service', slug: '06-magento-backend-documentation/is-payment-service' },
              { label: '[IS] Newsletter Service', slug: '06-magento-backend-documentation/is-newsletter-service' },
              { label: '[IS] Stock Service', slug: '06-magento-backend-documentation/is-stock-service' },
            ]
          },
        ]
      },
      {
        label: '07. PWA Frontend Documentation',
        badge: {
          text: 'New', color: 'green'
        },
        collapsed: true,
        items: [
          { label: 'Overview', slug: '07-pwa-frontend-documentation' },
          { label: 'Pages Data Source', slug: '07-pwa-frontend-documentation/pages-data-source' },
          { label: 'Homepage Values Block', slug: '07-pwa-frontend-documentation/homepage-values-block' },
          { label: 'Category Left Right Block', slug: '07-pwa-frontend-documentation/category-left-right-block' },
          { label: 'Brands Block', slug: '07-pwa-frontend-documentation/brands-block' },
          { label: 'Preheader', slug: '07-pwa-frontend-documentation/preheader' },
          { label: 'Header', slug: '07-pwa-frontend-documentation/header' },
          { label: 'Footer', slug: '07-pwa-frontend-documentation/footer' },
          { label: 'Category Page', slug: '07-pwa-frontend-documentation/category-page' }
        ]
      },
      {
        label: '08. PIMCORE Documentation',
        badge: {
          text: 'New', color: 'green'
        },
        collapsed: true,
        items: [
          { label: 'Overview', slug: '08-pimcore-documentation' },
          { label: 'PIMCORE Attributes', slug: '08-pimcore-documentation/pimcore-attributes' },
          { label: 'PIMCORE - Magento Integration', slug: '08-pimcore-documentation/pimcore-magento' },
          { label: 'Dynamic Descriptions', slug: '08-pimcore-documentation/dynamic-descriptions' },
          { label: 'Pimcore GraphQL', slug: '08-pimcore-documentation/pimcore-graphql' }
        ]
      },
      {
        label: '09. SSO',
        collapsed: true,
        badge: {
          text: 'Draft', color: 'orange'
        },
        items: [
          { label: 'Project Plan', slug: '09-sso/project-plan' },
          { label: 'Network', slug: '09-sso/network' },
          { label: 'Integrations', slug: '09-sso/integrations' },
        ]
      }
    ]
  },
  llmsTxt: {
    enabled: true
  },
  port: 7500,
  // required random generated id used by eventcatalog
  cId: '6097d846-aaed-4419-843c-5dad6b4d0393',
  performance: {
    maxWorkers: 4, // Limit the number of workers
    cacheDirectory: '.cache', // Enable caching
    minify: true, // Enable minification
    prefetch: false // Disable prefetching for better initial load
  },
  markdown: {
    rehypePlugins: [
      rehypeRaw,          // turn raw HTML/SVG into a HAST tree
    ]
  }
}
