# Confluence Image Download Script

This script downloads images from Confluence pages and saves them locally. It can be used to backup images or migrate them to other documentation systems.

## Setup

### Option 1: Using Virtual Environment (Recommended)

```bash
# Create a virtual environment
python3 -m venv confluence-downloader-env

# Activate the virtual environment
source confluence-downloader-env/bin/activate

# Install dependencies
pip install -r requirements.txt
```

### Option 2: Using pipx (Alternative)

```bash
# Install pipx if not already installed
sudo apt install pipx

# Install requests globally for scripts
pipx install requests
```

### Option 3: System Package (Ubuntu/Debian)

```bash
# Install python3-requests system package
sudo apt install python3-requests
```

## Usage

### Download images from a specific page

```bash
python3 confluence-image-download.py https://your-confluence.com \
  --page-id 123456 \
  --username <EMAIL> \
  --api-token your-api-token \
  --output-dir ./page-images
```

### Download images from an entire space

```bash
python3 confluence-image-download.py https://your-confluence.com \
  --space-key MYSPACE \
  --username <EMAIL> \
  --api-token your-api-token \
  --output-dir ./space-images
```

### Download without authentication (public content only)

```bash
python3 confluence-image-download.py https://your-confluence.com \
  --page-id 123456 \
  --output-dir ./public-images
```

## Parameters

- `base_url`: Base URL of your Confluence instance (required)
- `--username`: Your Confluence username/email (optional)
- `--api-token`: Your Confluence API token (optional)
- `--page-id`: ID of specific page to download images from
- `--space-key`: Key of space to download all images from
- `--output-dir`: Output directory for downloaded images (default: ./confluence-images)

**Note**: Either `--page-id` or `--space-key` must be specified.

## Getting Confluence API Token

1. Go to your Confluence instance
2. Click on your profile picture → Account Settings
3. Go to Security → Create and manage API tokens
4. Create a new token and copy it

## Finding Page ID

The page ID can be found in the URL when viewing a Confluence page:
- URL: `https://your-confluence.com/pages/viewpage.action?pageId=123456`
- Page ID: `123456`

## Output

- Images are saved in the specified output directory
- When downloading from a space, each page gets its own subdirectory
- Existing files are skipped to avoid re-downloading
- A log file `confluence-image-download.log` is created with detailed information

## Troubleshooting

### Virtual Environment Issues

If you get "externally-managed-environment" error:

```bash
# Make sure you have python3-venv installed
sudo apt install python3-venv python3-full

# Create and activate virtual environment
python3 -m venv confluence-downloader-env
source confluence-downloader-env/bin/activate

# Then install requirements
pip install -r requirements.txt
```

### Authentication Issues

- Make sure your API token is correct
- Verify you have access to the pages/spaces you're trying to download from
- Some organizations may have additional security restrictions

### Network Issues

- Check if you can access the Confluence instance from your network
- Some corporate networks may require proxy settings 