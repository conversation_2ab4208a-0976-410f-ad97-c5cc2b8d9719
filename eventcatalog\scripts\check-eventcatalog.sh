#!/bin/bash

# Check if port 7500 is in use
if lsof -i :7500 > /dev/null 2>&1; then
    echo "⚠️  EventCatalog is already running on port 7500"
    read -p "Do you want to terminate the existing instance? (y/n) " choice
    if [ "$choice" = "y" ]; then
        echo "🛑 Terminating existing EventCatalog instance..."
        pkill -f "eventcatalog dev"
        # Wait for the process to fully terminate
        sleep 2
        echo "✨ Starting new EventCatalog instance..."
    else
        echo "❌ Aborting to prevent duplicate instances"
        exit 1
    fi
else
    echo "✨ Starting EventCatalog..."
fi 