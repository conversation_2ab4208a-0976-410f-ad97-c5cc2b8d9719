#!/usr/bin/env python3
"""
Compare Confluence Backend Documentation with EventCatalog Files
================================================================

This script compares the backend documentation structure between:
- Confluence ALS space (Backend documentation section)
- EventCatalog 06-magento-backend-documentation directory

It identifies alignment issues and provides recommendations.
Updated: 2025-01-27 - Based on actual ALS Confluence API structure
"""

import os
import re
from pathlib import Path

# Confluence pages from the ALS space Backend documentation
# Based on actual API response from getConfluencePageDescendants (ALS space only)
confluence_pages = [    
    # Root level pages (depth 1)
    {"id": "**********", "title": "[PU] Purchase", "type": "page", "depth": 1},
    {"id": "**********", "title": "[PC] Product catalogue", "type": "page", "depth": 1},
    {"id": "**********", "title": "[RC] Returns and complaints", "type": "page", "depth": 1},
    {"id": "**********", "title": "[CA] Customer Account", "type": "page", "depth": 1},
    {"id": "**********", "title": "[SE] Search Engine", "type": "page", "depth": 1},
    {"id": "**********", "title": "[SP] Static Pages", "type": "page", "depth": 1},
    {"id": "**********", "title": "[AP] Admin Panel", "type": "page", "depth": 1},
    {"id": "**********", "title": "[OP] Order Process", "type": "page", "depth": 1},
    {"id": "**********", "title": "[IN] INTEGRATION", "type": "page", "depth": 1},
    {"id": "**********", "title": "[OM] Order Management", "type": "page", "depth": 1},
    {"id": "**********", "title": "[IS] Product Service", "type": "page", "depth": 1},
    {"id": "**********", "title": "[IS] Payment Service", "type": "page", "depth": 1},
    {"id": "**********", "title": "[IS] Newsletter Service", "type": "page", "depth": 1},
    {"id": "**********", "title": "[IS] Stock Service", "type": "page", "depth": 1},
    
    # Sub-pages (depth 2)
    {"id": "**********", "title": "[PU] Processes", "type": "page", "depth": 2, "parent": "[PU] Purchase"},
    
    # Folders under [CA] Customer Account (depth 2)
    {"id": "**********", "title": "[MOD] Modifications", "type": "folder", "depth": 2, "parent": "[CA] Customer Account"},
    {"id": "**********", "title": "[CA] Interfaces", "type": "folder", "depth": 2, "parent": "[CA] Customer Account"},
    
    # Folders under [OP] Order Process (depth 2) 
    {"id": "**********", "title": "[OP] Processes", "type": "folder", "depth": 2, "parent": "[OP] Order Process"},
    {"id": "**********", "title": "[OP] Modifications", "type": "folder", "depth": 2, "parent": "[OP] Order Process"},
    
    # INT-EVE-MA Services under [IN] INTEGRATION (depth 2)
    {"id": "**********", "title": "INT-EVE-MA000 General", "type": "page", "depth": 2, "parent": "[IN] INTEGRATION"},
    {"id": "**********", "title": "INT-EVE-MA001 Order Service", "type": "page", "depth": 2, "parent": "[IN] INTEGRATION"},
    {"id": "**********", "title": "INT-EVE-MA002 ShipmentService", "type": "page", "depth": 2, "parent": "[IN] INTEGRATION"},
    {"id": "**********", "title": "INT-EVE-MA003 CustomerService", "type": "page", "depth": 2, "parent": "[IN] INTEGRATION"},
    {"id": "**********", "title": "INT-EVE-MA004 CustomerAddressService", "type": "page", "depth": 2, "parent": "[IN] INTEGRATION"},
    {"id": "4609867819", "title": "INT-EVE-MA005 ProductService", "type": "page", "depth": 2, "parent": "[IN] INTEGRATION"},
    {"id": "4609736706", "title": "INT-EVE-MA006 Cart Service", "type": "page", "depth": 2, "parent": "[IN] INTEGRATION"},
    {"id": "4611506177", "title": "INT-EVE-MA007 Return Service", "type": "page", "depth": 2, "parent": "[IN] INTEGRATION"},
    {"id": "4620353537", "title": "INT-EVE-MA008 RmaService", "type": "page", "depth": 2, "parent": "[IN] INTEGRATION"},
    {"id": "4638474243", "title": "INT-EVE-MA009 InvoiceService", "type": "page", "depth": 2, "parent": "[IN] INTEGRATION"},
    
    # Pages inside folders (depth 3)
    {"id": "4580376583", "title": "PRO-PU-MA001 Order split", "type": "page", "depth": 3, "parent": "[PU] Processes"},
    {"id": "4599644182", "title": "MOD-CA-MA001 Suppliers", "type": "page", "depth": 3, "parent": "[MOD] Modifications"},
    {"id": "4678189120", "title": "INT-CA-MA001 Newsletter Integration Layer", "type": "page", "depth": 3, "parent": "[CA] Interfaces"},
    {"id": "4641783811", "title": "PRO-OP-MA001 Dimensional Shipping", "type": "page", "depth": 3, "parent": "[OP] Processes"},
    {"id": "4642897921", "title": "MOD-OP-MA001 SAP Order ID", "type": "page", "depth": 3, "parent": "[OP] Modifications"},
]

def normalize_title_to_filename(title, page_type):
    """Convert Confluence title to expected EventCatalog filename with proper folder structure"""
    # Skip folders - they don't need .mdx files
    if page_type == "folder":
        return None
    
    # Remove brackets and special characters
    filename = re.sub(r'^\[[^\]]+\]\s*', '', title)  # Remove [XX] prefix
    
    # Special mappings with folder structure reflecting Confluence hierarchy
    mappings = {
        "Purchase": "purchase/purchase-module.mdx",  # [PU] Purchase - main module
        "Product catalogue": "product-catalogue-module.mdx", 
        "Returns and complaints": "returns-complaints-module.mdx",
        "Customer Account": "customer-account/customer-account-module.mdx",  # Goes in folder
        "Search Engine": "search-engine-module.mdx",
        "Static Pages": "static-pages-module.mdx",
        "Admin Panel": "admin-panel-module.mdx",
        "Order Process": "order-process/order-process-module.mdx",  # Goes in folder
        "INTEGRATION": "integration/integration-module.mdx",  # Goes in folder
        "Order Management": "order-management.mdx",
        "Product Service": "is-product-service.mdx",
        "Payment Service": "is-payment-service.mdx", 
        "Newsletter Service": "is-newsletter-service.mdx",
        "Stock Service": "is-stock-service.mdx",
        "Processes": "purchase/processes/pu-processes.mdx",  # [PU] Processes - under [PU] Purchase
        "INT-EVE-MA000 General": "integration/int-eve-ma000-general.mdx",
        "INT-EVE-MA001 Order Service": "integration/int-eve-ma001-orderservice.mdx",
        "INT-EVE-MA002 ShipmentService": "integration/int-eve-ma002-shipmentservice.mdx",
        "INT-EVE-MA003 CustomerService": "integration/int-eve-ma003-customerservice.mdx",
        "INT-EVE-MA004 CustomerAddressService": "integration/int-eve-ma004-customeraddressservice.mdx",
        "INT-EVE-MA005 ProductService": "integration/int-eve-ma005-productservice.mdx",
        "INT-EVE-MA006 Cart Service": "integration/int-eve-ma006-cartservice.mdx",
        "INT-EVE-MA007 Return Service": "integration/int-eve-ma007-returnservice.mdx",
        "INT-EVE-MA008 RmaService": "integration/int-eve-ma008-rmaservice.mdx",
        "INT-EVE-MA009 InvoiceService": "integration/int-eve-ma009-invoiceservice.mdx",
        # Depth 3 pages - inside proper subfolders matching Confluence structure
        "PRO-PU-MA001 Order split": "purchase/processes/pro-pu-ma001-order-split.mdx",  # Under [PU] Processes
        "MOD-CA-MA001 Suppliers": "customer-account/modifications/mod-ca-ma001-suppliers.mdx",  # Under [MOD] Modifications folder
        "INT-CA-MA001 Newsletter Integration Layer": "customer-account/interfaces/int-ca-ma001-newsletter-integration-layer.mdx",  # Under [CA] Interfaces folder
        "PRO-OP-MA001 Dimensional Shipping": "order-process/processes/pro-op-ma001-dimensional-shipping.mdx",  # Under [OP] Processes folder
        "MOD-OP-MA001 SAP Order ID": "order-process/modifications/mod-op-ma001-sap-order-id.mdx",  # Under [OP] Modifications folder
    }
    
    if filename in mappings:
        return mappings[filename]
    
    # Default normalization for other titles
    filename = filename.lower()
    filename = re.sub(r'[^a-z0-9]+', '-', filename)
    filename = filename.strip('-') + ".mdx"
    
    return filename

def get_eventcatalog_files():
    """Get all EventCatalog .mdx files from backend documentation"""
    base_path = Path("eventcatalog/docs/06-magento-backend-documentation")
    files = []
    
    if not base_path.exists():
        return files
    
    for mdx_file in base_path.rglob("*.mdx"):
        relative_path = mdx_file.relative_to(base_path)
        files.append(str(relative_path))
    
    return sorted(files)

def main():
    print("🔍 CONFLUENCE vs EVENTCATALOG BACKEND DOCUMENTATION COMPARISON")
    print("=" * 80)
    print("Updated: 2025-01-27 - Based on ALS Confluence API structure")
    
    # Get EventCatalog files
    eventcatalog_files = get_eventcatalog_files()
    
    # Create mapping - only for pages, not folders
    matches = []
    missing_from_eventcatalog = []
    confluence_map = {}
    folders_skipped = []
    
    for page in confluence_pages:
        if page["type"] == "folder":
            folders_skipped.append(page)
            continue
            
        expected_filename = normalize_title_to_filename(page["title"], page["type"])
        if expected_filename:
            confluence_map[expected_filename] = page
            
            if expected_filename in eventcatalog_files:
                matches.append((expected_filename, page))
            else:
                missing_from_eventcatalog.append((expected_filename, page))
    
    # Find EventCatalog files without Confluence source
    confluence_filenames = set(confluence_map.keys())
    eventcatalog_without_source = [f for f in eventcatalog_files if f not in confluence_filenames]
    
    # Print summary
    total_pages = len([p for p in confluence_pages if p["type"] == "page"])
    total_folders = len([p for p in confluence_pages if p["type"] == "folder"])
    
    print(f"\n📊 SUMMARY:")
    print(f"   Confluence pages: {total_pages}")
    print(f"   Confluence folders: {total_folders} (skipped - no .mdx needed)")
    print(f"   EventCatalog files: {len(eventcatalog_files)}")
    
    # Print folder structure
    if folders_skipped:
        print(f"\n📁 CONFLUENCE FOLDER STRUCTURE ({len(folders_skipped)} folders):")
        for folder in folders_skipped:
            parent_info = f" (under {folder.get('parent', 'root')})" if folder.get('parent') else ""
            print(f"   📁 {folder['title']} (ID: {folder['id']}){parent_info}")
    
    # Print matches
    print(f"\n✅ MATCHES ({len(matches)}):")
    for filename, page in matches:
        depth_info = f" (depth {page.get('depth', 1)})" if page.get('depth', 1) > 1 else ""
        print(f"   ✓ {filename} ↔ {page['title']} (ID: {page['id']}){depth_info}")
    
    # Print missing from EventCatalog
    if missing_from_eventcatalog:
        print(f"\n❌ CONFLUENCE PAGES MISSING FROM EVENTCATALOG ({len(missing_from_eventcatalog)}):")
        for filename, page in missing_from_eventcatalog:
            depth_info = f" (depth {page.get('depth', 1)})" if page.get('depth', 1) > 1 else ""
            print(f"   ✗ {filename} ← {page['title']} (ID: {page['id']}){depth_info}")
    
    # Print EventCatalog files without Confluence source
    if eventcatalog_without_source:
        print(f"\n⚠️  EVENTCATALOG FILES WITHOUT CONFLUENCE SOURCE ({len(eventcatalog_without_source)}):")
        for filename in eventcatalog_without_source:
            print(f"   ⚠ {filename}")
    
    # Calculate compliance
    matched = len(matches)
    compliance_rate = (matched / total_pages) * 100 if total_pages > 0 else 0
    
    print(f"\n📈 COMPLIANCE ANALYSIS:")
    print(f"   Compliance Rate: {compliance_rate:.1f}% ({matched}/{total_pages})")
    print(f"   Files to DELETE: {len(eventcatalog_without_source)}")
    print(f"   Files to CREATE: {len(missing_from_eventcatalog)}")
    print(f"   Folders (no action needed): {total_folders}")
    
    # Final status
    if compliance_rate == 100 and len(eventcatalog_without_source) == 0:
        print(f"\n🎉 SUCCESS: Perfect 1:1 alignment achieved!")
        print(f"   All Confluence pages have corresponding EventCatalog files.")
        print(f"   No EventCatalog files exist without Confluence sources.")
        print(f"   Folder structure reflects Confluence hierarchy.")
    else:
        print(f"\n🚨 CRITICAL: Backend documentation NOT in 1:1 alignment!")
        print(f"   This violates the confluence migration rule requirements.")
    
    return 0

if __name__ == "__main__":
    exit(main()) 