#!/usr/bin/env python3
"""
Compare Confluence QA Critical Paths with EventCatalog QA Files
===============================================================

This script compares the QA test case documentation structure between:
- Confluence ALS space (Critical paths section)
- EventCatalog 10-qa directory

It identifies alignment issues and provides recommendations.
Created: 2025-01-27 - Based on migrated QA test cases from Confluence
"""

import os
import re
from pathlib import Path

# Confluence pages from the ALS space Critical paths section
# Based on the migrated QA test cases from Confluence API
confluence_pages = [    
    # Main Critical paths page
    {"id": "**********", "title": "Critical paths", "type": "page", "depth": 1},
    
    # User Authentication & Account Management
    {"id": "**********", "title": "Registration", "type": "page", "depth": 2, "parent": "Critical paths"},
    {"id": "**********", "title": "Login", "type": "page", "depth": 2, "parent": "Critical paths"},
    {"id": "**********", "title": "Logout", "type": "page", "depth": 2, "parent": "Critical paths"},
    {"id": "**********", "title": "Password change", "type": "page", "depth": 2, "parent": "Critical paths"},
    
    # User Account Features
    {"id": "**********", "title": "User Account - Account information", "type": "page", "depth": 2, "parent": "Critical paths"},
    {"id": "**********", "title": "User Account - My orders", "type": "page", "depth": 2, "parent": "Critical paths"},
    {"id": "**********", "title": "User Account - Newsletter", "type": "page", "depth": 2, "parent": "Critical paths"},
    {"id": "**********", "title": "User Account - Wishlist", "type": "page", "depth": 2, "parent": "Critical paths"},
    {"id": "**********", "title": "User Account - Anonymization", "type": "page", "depth": 2, "parent": "Critical paths"},
    
    # Product & Shopping Experience
    {"id": "**********", "title": "Product page", "type": "page", "depth": 2, "parent": "Critical paths"},
    {"id": "**********", "title": "Product display", "type": "page", "depth": 2, "parent": "Critical paths"},
    {"id": "**********", "title": "Search", "type": "page", "depth": 2, "parent": "Critical paths"},
    {"id": "**********", "title": "Mini-cart", "type": "page", "depth": 2, "parent": "Critical paths"},
    {"id": "**********", "title": "Shopping cart", "type": "page", "depth": 2, "parent": "Critical paths"},
    
    # Checkout & Orders
    {"id": "**********", "title": "Checkout", "type": "page", "depth": 2, "parent": "Critical paths"},
    {"id": "**********", "title": "Order return", "type": "page", "depth": 2, "parent": "Critical paths"},
    
    # Customer Service
    {"id": "**********", "title": "RMA", "type": "page", "depth": 2, "parent": "Critical paths"},
    {"id": "4694966299", "title": "Warranty claim", "type": "page", "depth": 2, "parent": "Critical paths"},
    
    # Communication & Navigation
    {"id": "4695949313", "title": "Newsletter", "type": "page", "depth": 2, "parent": "Critical paths"},
    {"id": "4695916551", "title": "Contact form", "type": "page", "depth": 2, "parent": "Critical paths"},
    {"id": "4696014855", "title": "Sitemap", "type": "page", "depth": 2, "parent": "Critical paths"},
]

def normalize_title_to_filename(title, page_type):
    """Convert Confluence title to expected EventCatalog filename"""
    # Skip folders - they don't need .mdx files
    if page_type == "folder":
        return None
    
    # Special mappings for QA test case pages
    mappings = {
        "Critical paths": "index.mdx",  # Main QA overview page
        "Registration": "registration.mdx",
        "Login": "login.mdx",
        "Logout": "logout.mdx",
        "Password change": "password-change.mdx",
        "User Account - Account information": "user-account-account-information.mdx",
        "User Account - My orders": "user-account-my-orders.mdx",
        "User Account - Newsletter": "user-account-newsletter.mdx",
        "User Account - Wishlist": "user-account-wishlist.mdx",
        "User Account - Anonymization": "user-account-anonymization.mdx",
        "Product page": "product-page.mdx",
        "Product display": "product-display.mdx",
        "Search": "search.mdx",
        "Mini-cart": "mini-cart.mdx",
        "Shopping cart": "shopping-cart.mdx",
        "Checkout": "checkout.mdx",
        "Order return": "order-return.mdx",
        "RMA": "rma.mdx",
        "Warranty claim": "warranty-claim.mdx",
        "Newsletter": "newsletter-subscription.mdx",  # Mapped to newsletter-subscription
        "Contact form": "footer-links.mdx",  # Mapped to footer-links (contact form is part of footer)
        "Sitemap": "footer-links.mdx",  # Mapped to footer-links (sitemap is part of footer)
    }
    
    if title in mappings:
        return mappings[title]
    
    # Default normalization for other titles
    filename = title.lower()
    filename = re.sub(r'[^a-z0-9]+', '-', filename)
    filename = filename.strip('-') + ".mdx"
    
    return filename

def get_eventcatalog_files():
    """Get all EventCatalog .mdx files from QA documentation"""
    base_path = Path("../../docs/10-qa")
    files = []
    
    if not base_path.exists():
        return files
    
    for mdx_file in base_path.rglob("*.mdx"):
        relative_path = mdx_file.relative_to(base_path)
        files.append(str(relative_path))
    
    return sorted(files)

def get_additional_eventcatalog_files():
    """Get additional EventCatalog files that were created but don't have direct Confluence mapping"""
    additional_files = [
        "checkout-guest-user.mdx",  # Extended checkout functionality
        "warranty-claim-form.mdx",  # Extended warranty claim functionality
    ]
    return additional_files

def main():
    print("🔍 CONFLUENCE vs EVENTCATALOG QA DOCUMENTATION COMPARISON")
    print("=" * 80)
    print("Created: 2025-01-27 - Based on migrated QA test cases from Confluence")
    
    # Get EventCatalog files
    eventcatalog_files = get_eventcatalog_files()
    additional_files = get_additional_eventcatalog_files()
    
    # Create mapping
    matches = []
    missing_from_eventcatalog = []
    confluence_map = {}
    
    for page in confluence_pages:
        expected_filename = normalize_title_to_filename(page["title"], page["type"])
        if expected_filename:
            confluence_map[expected_filename] = page
            
            if expected_filename in eventcatalog_files:
                matches.append((expected_filename, page))
            else:
                missing_from_eventcatalog.append((expected_filename, page))
    
    # Find EventCatalog files without Confluence source
    confluence_filenames = set(confluence_map.keys())
    eventcatalog_without_source = [f for f in eventcatalog_files if f not in confluence_filenames]
    
    # Separate additional files from truly orphaned files
    additional_files_found = [f for f in eventcatalog_without_source if f in additional_files]
    truly_orphaned = [f for f in eventcatalog_without_source if f not in additional_files]
    
    # Print summary
    total_pages = len([p for p in confluence_pages if p["type"] == "page"])
    
    print(f"\n📊 SUMMARY:")
    print(f"   Confluence pages: {total_pages}")
    print(f"   EventCatalog files: {len(eventcatalog_files)}")
    print(f"   Additional files (extended functionality): {len(additional_files_found)}")
    
    # Print matches
    print(f"\n✅ MATCHES ({len(matches)}):")
    for filename, page in matches:
        depth_info = f" (depth {page.get('depth', 1)})" if page.get('depth', 1) > 1 else ""
        print(f"   ✓ {filename} ↔ {page['title']} (ID: {page['id']}){depth_info}")
    
    # Print missing from EventCatalog
    if missing_from_eventcatalog:
        print(f"\n❌ CONFLUENCE PAGES MISSING FROM EVENTCATALOG ({len(missing_from_eventcatalog)}):")
        for filename, page in missing_from_eventcatalog:
            depth_info = f" (depth {page.get('depth', 1)})" if page.get('depth', 1) > 1 else ""
            print(f"   ✗ {filename} ← {page['title']} (ID: {page['id']}){depth_info}")
    
    # Print additional files (extended functionality)
    if additional_files_found:
        print(f"\n➕ ADDITIONAL EVENTCATALOG FILES (Extended Functionality) ({len(additional_files_found)}):")
        for filename in additional_files_found:
            print(f"   ➕ {filename} (Extended test case functionality)")
    
    # Print truly orphaned files
    if truly_orphaned:
        print(f"\n⚠️  EVENTCATALOG FILES WITHOUT CONFLUENCE SOURCE ({len(truly_orphaned)}):")
        for filename in truly_orphaned:
            print(f"   ⚠ {filename}")
    
    # Special handling for merged pages
    merged_pages = []
    newsletter_pages = [p for p in confluence_pages if p["title"] in ["Newsletter", "Contact form", "Sitemap"]]
    if len(newsletter_pages) > 1:
        merged_pages.extend(newsletter_pages)
    
    if merged_pages:
        print(f"\n🔄 MERGED PAGES ({len(merged_pages)} → fewer files):")
        for page in merged_pages:
            mapped_file = normalize_title_to_filename(page["title"], page["type"])
            print(f"   🔄 {page['title']} (ID: {page['id']}) → {mapped_file}")
    
    # Calculate compliance
    matched = len(matches)
    # Note: Some Confluence pages map to the same file (merged pages)
    compliance_rate = (matched / total_pages) * 100 if total_pages > 0 else 0
    
    print(f"\n📈 COMPLIANCE ANALYSIS:")
    print(f"   Compliance Rate: {compliance_rate:.1f}% ({matched}/{total_pages})")
    print(f"   Additional files (extended): {len(additional_files_found)}")
    print(f"   Truly orphaned files: {len(truly_orphaned)}")
    print(f"   Merged pages: {len(merged_pages)}")
    
    # Test case coverage analysis
    print(f"\n🧪 TEST CASE COVERAGE:")
    categories = {
        "User Authentication & Account Management": ["registration.mdx", "login.mdx", "logout.mdx", "password-change.mdx"],
        "User Account Features": ["user-account-account-information.mdx", "user-account-my-orders.mdx", "user-account-newsletter.mdx", "user-account-wishlist.mdx", "user-account-anonymization.mdx"],
        "Product & Shopping Experience": ["product-page.mdx", "product-display.mdx", "search.mdx", "mini-cart.mdx", "shopping-cart.mdx"],
        "Checkout & Orders": ["checkout.mdx", "checkout-guest-user.mdx", "order-return.mdx"],
        "Customer Service": ["rma.mdx", "warranty-claim.mdx", "warranty-claim-form.mdx"],
        "Communication & Navigation": ["newsletter-subscription.mdx", "footer-links.mdx"]
    }
    
    for category, files in categories.items():
        present = sum(1 for f in files if f in eventcatalog_files)
        total = len(files)
        coverage = (present / total) * 100 if total > 0 else 0
        print(f"   {category}: {coverage:.1f}% ({present}/{total})")
    
    # Final status
    if compliance_rate >= 95 and len(truly_orphaned) == 0:
        print(f"\n🎉 SUCCESS: Excellent QA test case coverage achieved!")
        print(f"   All critical Confluence pages have corresponding EventCatalog files.")
        print(f"   Additional files provide extended test case functionality.")
        print(f"   QA documentation structure is well-organized.")
    elif compliance_rate >= 80:
        print(f"\n✅ GOOD: QA documentation has good coverage!")
        print(f"   Most critical paths are covered in EventCatalog.")
        if truly_orphaned:
            print(f"   Consider reviewing {len(truly_orphaned)} orphaned files.")
    else:
        print(f"\n🚨 CRITICAL: QA documentation coverage is insufficient!")
        print(f"   This violates the confluence migration rule requirements.")
    
    return 0

if __name__ == "__main__":
    exit(main()) 