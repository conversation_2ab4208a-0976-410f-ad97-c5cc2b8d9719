#!/usr/bin/env python3
"""
Confluence Image Download Script

This script downloads images from Confluence pages and saves them locally.
It can be used to backup images or migrate them to other documentation systems.
"""

import os
import sys
import requests
import argparse
from urllib.parse import urljoin, urlparse
from pathlib import Path
import json
import logging

# Set up logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('confluence-image-download.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)


class ConfluenceImageDownloader:
    """Downloads images from Confluence pages."""
    
    def __init__(self, base_url, username=None, api_token=None):
        """
        Initialize the Confluence Image Downloader.
        
        Args:
            base_url (str): Base URL of the Confluence instance
            username (str): Username for authentication (optional)
            api_token (str): API token for authentication (optional)
        """
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        
        if username and api_token:
            self.session.auth = (username, api_token)
            logger.info("Authentication configured")
        else:
            logger.warning("No authentication provided - only public content will be accessible")
    
    def get_page_attachments(self, page_id):
        """
        Get all attachments for a specific Confluence page.
        
        Args:
            page_id (str): The ID of the Confluence page
            
        Returns:
            list: List of attachment metadata
        """
        url = f"{self.base_url}/rest/api/content/{page_id}/child/attachment"
        
        try:
            response = self.session.get(url)
            response.raise_for_status()
            
            data = response.json()
            attachments = data.get('results', [])
            
            # Filter for image attachments
            image_attachments = [
                att for att in attachments 
                if att.get('metadata', {}).get('mediaType', '').startswith('image/')
            ]
            
            logger.info(f"Found {len(image_attachments)} image attachments on page {page_id}")
            return image_attachments
            
        except requests.RequestException as e:
            logger.error(f"Error fetching attachments for page {page_id}: {e}")
            return []
    
    def download_image(self, attachment, output_dir):
        """
        Download a single image attachment.
        
        Args:
            attachment (dict): Attachment metadata from Confluence API
            output_dir (Path): Directory to save the image
            
        Returns:
            bool: True if download was successful, False otherwise
        """
        try:
            # Get download URL
            download_url = attachment['_links']['download']
            
            # Fix the download URL for Confluence Cloud
            if not download_url.startswith('http'):
                # For relative URLs, construct the full URL
                if download_url.startswith('/'):
                    download_url = self.base_url + download_url
                else:
                    download_url = urljoin(self.base_url, download_url)
            
            # For Confluence Cloud, we might need to adjust the URL format
            # Replace /download/attachments/ with /wiki/download/attachments/ if needed
            if '/download/attachments/' in download_url and '/wiki/' not in download_url:
                download_url = download_url.replace('/download/attachments/', '/wiki/download/attachments/')
            
            # Get filename
            filename = attachment['title']
            filepath = output_dir / filename
            
            # Skip if file already exists
            if filepath.exists():
                logger.info(f"File already exists, skipping: {filename}")
                return True
            
            # Download the image
            logger.info(f"Downloading: {filename}")
            logger.debug(f"Download URL: {download_url}")
            
            response = self.session.get(download_url)
            response.raise_for_status()
            
            # Save the image
            with open(filepath, 'wb') as f:
                f.write(response.content)
            
            logger.info(f"Successfully downloaded: {filename}")
            return True
            
        except Exception as e:
            logger.error(f"Error downloading {attachment.get('title', 'unknown')}: {e}")
            # Try alternative download method
            return self._try_alternative_download(attachment, output_dir)
    
    def _try_alternative_download(self, attachment, output_dir):
        """
        Try alternative download methods for Confluence attachments.
        
        Args:
            attachment (dict): Attachment metadata from Confluence API
            output_dir (Path): Directory to save the image
            
        Returns:
            bool: True if download was successful, False otherwise
        """
        try:
            # Method 1: Try using the attachment ID directly
            attachment_id = attachment.get('id')
            if attachment_id:
                # Try different URL patterns
                url_patterns = [
                    f"{self.base_url}/wiki/download/attachments/{attachment_id}/{attachment['title']}",
                    f"{self.base_url}/download/attachments/{attachment_id}/{attachment['title']}",
                    f"{self.base_url}/rest/api/content/{attachment_id}/download",
                ]
                
                for url_pattern in url_patterns:
                    try:
                        logger.debug(f"Trying alternative URL: {url_pattern}")
                        response = self.session.get(url_pattern)
                        response.raise_for_status()
                        
                        # Save the image
                        filename = attachment['title']
                        filepath = output_dir / filename
                        
                        with open(filepath, 'wb') as f:
                            f.write(response.content)
                        
                        logger.info(f"Successfully downloaded via alternative method: {filename}")
                        return True
                        
                    except Exception as alt_e:
                        logger.debug(f"Alternative URL failed: {alt_e}")
                        continue
            
            return False
            
        except Exception as e:
            logger.debug(f"Alternative download method failed: {e}")
            return False
    
    def download_page_images(self, page_id, output_dir):
        """
        Download all images from a specific Confluence page.
        
        Args:
            page_id (str): The ID of the Confluence page
            output_dir (str): Directory to save images
            
        Returns:
            int: Number of successfully downloaded images
        """
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        attachments = self.get_page_attachments(page_id)
        successful_downloads = 0
        
        for attachment in attachments:
            if self.download_image(attachment, output_path):
                successful_downloads += 1
        
        logger.info(f"Downloaded {successful_downloads}/{len(attachments)} images from page {page_id}")
        return successful_downloads
    
    def download_space_images(self, space_key, output_dir):
        """
        Download all images from all pages in a Confluence space.
        
        Args:
            space_key (str): The key of the Confluence space
            output_dir (str): Directory to save images
            
        Returns:
            int: Total number of successfully downloaded images
        """
        # Get all pages in the space
        url = f"{self.base_url}/rest/api/content"
        params = {
            'spaceKey': space_key,
            'type': 'page',
            'limit': 1000  # Adjust as needed
        }
        
        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            pages = data.get('results', [])
            
            logger.info(f"Found {len(pages)} pages in space {space_key}")
            
            total_downloads = 0
            for page in pages:
                page_id = page['id']
                page_title = page['title']
                
                logger.info(f"Processing page: {page_title} (ID: {page_id})")
                
                # Create subdirectory for this page
                page_output_dir = Path(output_dir) / f"{page_id}_{page_title.replace('/', '_')}"
                downloads = self.download_page_images(page_id, page_output_dir)
                total_downloads += downloads
            
            logger.info(f"Total images downloaded from space {space_key}: {total_downloads}")
            return total_downloads
            
        except requests.RequestException as e:
            logger.error(f"Error fetching pages from space {space_key}: {e}")
            return 0

    def list_spaces(self):
        """
        List all available spaces in the Confluence instance.
        
        Returns:
            list: List of space information
        """
        url = f"{self.base_url}/rest/api/space"
        params = {
            'limit': 1000  # Adjust as needed
        }
        
        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            spaces = data.get('results', [])
            
            logger.info(f"Found {len(spaces)} spaces")
            
            print("\nAvailable Spaces:")
            print("-" * 80)
            print(f"{'Key':<15} {'Name':<40} {'Type':<15}")
            print("-" * 80)
            
            for space in spaces:
                space_key = space.get('key', 'N/A')
                space_name = space.get('name', 'N/A')
                space_type = space.get('type', 'N/A')
                
                # Truncate long names
                if len(space_name) > 37:
                    space_name = space_name[:37] + "..."
                
                print(f"{space_key:<15} {space_name:<40} {space_type:<15}")
            
            print("-" * 80)
            print(f"Total: {len(spaces)} spaces\n")
            
            return spaces
            
        except requests.RequestException as e:
            logger.error(f"Error fetching spaces: {e}")
            return []


def main():
    """Main function to handle command line arguments and execute the download."""
    parser = argparse.ArgumentParser(description='Download images from Confluence')
    parser.add_argument('base_url', help='Base URL of the Confluence instance')
    parser.add_argument('--username', help='Username for authentication')
    parser.add_argument('--api-token', help='API token for authentication')
    parser.add_argument('--page-id', help='ID of specific page to download images from')
    parser.add_argument('--space-key', help='Key of space to download all images from')
    parser.add_argument('--list-spaces', action='store_true', 
                       help='List all available spaces in the Confluence instance')
    parser.add_argument('--output-dir', default='./confluence-images', 
                       help='Output directory for downloaded images (default: ./confluence-images)')
    
    args = parser.parse_args()
    
    # Initialize downloader
    downloader = ConfluenceImageDownloader(
        base_url=args.base_url,
        username=args.username,
        api_token=args.api_token
    )
    
    # Handle list spaces option
    if args.list_spaces:
        downloader.list_spaces()
        return
    
    # Validate that either page-id or space-key is provided for download operations
    if not args.page_id and not args.space_key:
        logger.error("Either --page-id, --space-key, or --list-spaces must be specified")
        sys.exit(1)
    
    # Download images
    if args.page_id:
        downloader.download_page_images(args.page_id, args.output_dir)
    elif args.space_key:
        downloader.download_space_images(args.space_key, args.output_dir)


if __name__ == '__main__':
    main() 