#!/bin/bash
# escape-mdx.sh - Helper script to escape MDX content
# This script implements the cursor rule for Confluence to EventCatalog migration

escape_file() {
    local file="$1"
    
    if [ ! -f "$file" ]; then
        echo "❌ File not found: $file"
        return 1
    fi
    
    local backup="${file}.backup.$(date +%Y%m%d_%H%M%S)"
    
    # Create backup
    cp "$file" "$backup"
    echo "📋 Created backup: $backup"
    
    # Escape angle brackets (avoiding HTML tags and existing entities)
    # This regex matches < that is not followed by / or a letter (HTML tags)
    # and is not preceded by & (existing entities)
    sed -i -E 's/([^&])<([^/a-zA-Z])/\1\&lt;\2/g' "$file"
    
    # Escape > that is not preceded by a letter (end of HTML tags)
    # and is not preceded by ; (existing entities)
    sed -i -E 's/([^a-zA-Z;])>([^&])/\1\&gt;\2/g' "$file"
    
    # Handle edge cases at beginning of lines
    sed -i -E 's/^<([^/a-zA-Z])/\&lt;\1/g' "$file"
    sed -i -E 's/^([^a-zA-Z])>/\1\&gt;/g' "$file"
    
    echo "✅ Escaped characters in $file"
    
    # Show what was changed
    if command -v diff >/dev/null 2>&1; then
        echo "🔍 Changes made:"
        diff "$backup" "$file" | head -20 || true
    fi
}

validate_file() {
    local file="$1"
    
    if [ ! -f "$file" ]; then
        echo "❌ File not found: $file"
        return 1
    fi
    
    echo "🔍 Validating MDX content in $file..."
    
    # Create a temporary file without code blocks
    local temp_file=$(mktemp)
    
    # Remove content between ``` markers (code blocks)
    awk '
    /^```/ { 
        in_code = !in_code
        next
    }
    !in_code { print }
    ' "$file" > "$temp_file"
    
    # Check for unescaped angle brackets in the filtered content
    local issues=0
    
    # Look for < that might be unescaped (excluding code blocks)
    if grep -n -E '[^&]<[^/a-zA-Z]' "$temp_file" >/dev/null 2>&1; then
        echo "⚠️  Potential unescaped < characters found:"
        # Show line numbers from original file
        grep -n -E '[^&]<[^/a-zA-Z]' "$temp_file" | head -5
        issues=$((issues + 1))
    fi
    
    # Look for > that might be unescaped (excluding code blocks)
    if grep -n -E '[^a-zA-Z;]>[^&]' "$temp_file" >/dev/null 2>&1; then
        echo "⚠️  Potential unescaped > characters found (excluding code blocks):"
        # Show line numbers from original file
        grep -n -E '[^a-zA-Z;]>[^&]' "$temp_file" | head -5
        issues=$((issues + 1))
    fi
    
    # Clean up
    rm "$temp_file"
    
    if [ $issues -eq 0 ]; then
        echo "✅ No unescaped angle brackets found"
        return 0
    else
        echo "❌ Found $issues types of potential issues"
        return 1
    fi
}

# Main script logic
case "${1:-}" in
    --validate|-v)
        if [ -z "$2" ]; then
            echo "Usage: $0 --validate <file.mdx>"
            exit 1
        fi
        validate_file "$2"
        ;;
    --help|-h)
        echo "MDX Character Escaping Tool"
        echo ""
        echo "Usage:"
        echo "  $0 <file.mdx>           - Escape characters in file"
        echo "  $0 --validate <file>    - Validate file for unescaped characters"
        echo "  $0 --help              - Show this help"
        echo ""
        echo "This tool escapes < and > characters that are not part of HTML tags"
        echo "or existing HTML entities to prevent MDX parsing errors."
        ;;
    "")
        echo "❌ No file specified"
        echo "Usage: $0 <file.mdx> or $0 --help"
        exit 1
        ;;
    *)
        escape_file "$1"
        ;;
esac 