---
id: euvic
name: <PERSON><PERSON><PERSON>
summmary: <PERSON><PERSON><PERSON> specializes in implementing headless frontend applications with modern architectures, focusing on e-commerce solutions. Their expertise includes integrating various third-party systems and managing robust infrastructures.
members:
    - piotr.dzierzega
email: <EMAIL>
---

## Overview

The Euvic Team is responsible for providing cutting-edge e-commerce solutions leveraging a headless architecture. Their focus is on utilizing modern front-end technologies and backend systems to deliver a fast, responsive, and scalable user experience. <PERSON><PERSON><PERSON>'s expertise lies in integrating comprehensive e-commerce functionalities using Magento and Pimcore, coupled with robust infrastructural solutions on cloud platforms like AWS.

## Responsibilities

### Key Responsibilities

- **Front-End Implementation:**
  - Develop a headless frontend application using React.js based on the Magento PWA Studio Venia theme.
  - Ensure the application delivers a modern, fast, and responsive user experience decoupled from backend systems.

- **E-Commerce Engine Management:**
  - Utilize Magento 2 Open Source for managing product catalogs, pricing, customer accounts, order processing, checkout, and promotions.
  - Implement promotional engines for catalog and cart pricing rules.

- **Centralized Product Information Management:**
  - Use Pimcore for managing product information and digital assets, providing a single source of truth for product data.

- **Event-Driven Architecture:**
  - Establish a real-time communication framework using RabbitMQ for service interaction and integration with external systems.

- **Authentication and Security:**
  - Implement a centralized authentication service for user identity management and secure access across all modules and channels.

- **Third-Party Integration:**
  - Integrate systems including ShipExec, online marketplaces, payment gateways (Stripe), Salesforce, Snowflake, SAP ERP, and suppliers' systems.
  - Implement real-time tax calculation with Vertex.

- **Infrastructure Management:**
  - Build and manage infrastructure on AWS, ensuring scalability and availability.
  - Automate infrastructure provisioning with Terraform and create CI/CD pipelines using GitHub CI for seamless deployment.

- **Testing and Quality Assurance:**
  - Define and implement an automated testing strategy using tools like Cypress or Playwright integrated into CI/CD for functional quality assurance.

- **API-Driven Development:**
  - Ensure an API-driven approach to support extensibility, third-party applications, analytics, and mobile functionalities.

- **E-Commerce Storefront Implementation:**
  - Develop and manage e-commerce storefronts for specified online brands, ensuring a cohesive and high-quality user experience.