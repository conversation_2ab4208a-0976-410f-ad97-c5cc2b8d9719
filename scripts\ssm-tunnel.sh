#!/bin/bash
# Script to connect to Aurora PostgreSQL via SSM port forwarding

set -e

# Configuration
AWS_REGION="us-east-2"
AWS_PROFILE="${AWS_PROFILE:-default}"  # Use default profile unless overridden
LOCAL_PORT="${LOCAL_PORT:-54321}"
REMOTE_PORT="5432"

# Color definitions
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

echo -e "${CYAN}=========================================${NC}"
echo -e "${CYAN}${BOLD}Evolution Axon SSM Connection Tool${NC}"
echo -e "${CYAN}=========================================${NC}"
echo

# Ask for environment selection
echo -e "${YELLOW}Select environment:${NC}"
echo -e "  ${BOLD}1)${NC} dev"
echo -e "  ${BOLD}2)${NC} qa"
echo -e "  ${BOLD}3)${NC} prod"
echo
echo -ne "${YELLOW}Enter number (1-3): ${NC}"
read ENV_CHOICE

case $ENV_CHOICE in
    1)
        ENVIRONMENT="dev"
        ;;
    2)
        ENVIRONMENT="qa"
        ;;
    3)
        ENVIRONMENT="prod"
        ;;
    *)
        echo -e "${RED}Error: Invalid selection. Please enter 1, 2, or 3.${NC}"
        exit 1
        ;;
esac

echo
echo -e "${GREEN}✓ Selected environment: ${BOLD}$ENVIRONMENT${NC}"
echo

# Look for bastion instance by tags (Name tag containing "bastion" and environment)
echo -e "${BLUE}Looking for bastion instance...${NC}"

# Get all bastion instances by Name tag and environment
INSTANCE_LIST=$(aws ec2 describe-instances \
    --filters "Name=tag:Name,Values=*bastion*$ENVIRONMENT*,*$ENVIRONMENT*bastion*" \
              "Name=instance-state-name,Values=running" \
    --query 'Reservations[].Instances[].[InstanceId,Tags[?Key==`Name`].Value|[0]]' \
    --output json \
    --region "$AWS_REGION" \
    --profile "$AWS_PROFILE" 2>/dev/null)

# If not found by Name tag, try looking for instances with bastion role
if [ -z "$INSTANCE_LIST" ] || [ "$INSTANCE_LIST" == "[]" ]; then
    INSTANCE_LIST=$(aws ec2 describe-instances \
        --filters "Name=tag:Role,Values=bastion" \
                  "Name=instance-state-name,Values=running" \
        --query 'Reservations[].Instances[].[InstanceId,Tags[?Key==`Name`].Value|[0]]' \
        --output json \
        --region "$AWS_REGION" \
        --profile "$AWS_PROFILE" 2>/dev/null)
fi

# Check if we found any instances
if [ -z "$INSTANCE_LIST" ] || [ "$INSTANCE_LIST" == "[]" ]; then
    echo -e "${RED}Error: Could not find a running bastion instance for $ENVIRONMENT environment.${NC}"
    echo -e "${YELLOW}Make sure there's a running EC2 instance with either:${NC}"
    echo -e "${YELLOW}  - A Name tag containing both 'bastion' and '$ENVIRONMENT'${NC}"
    echo -e "${YELLOW}  - A Role tag with value 'bastion'${NC}"
    exit 1
fi

# Count how many instances were found
INSTANCE_COUNT=$(echo "$INSTANCE_LIST" | jq 'length')

if [ "$INSTANCE_COUNT" -gt 1 ]; then
    echo -e "${YELLOW}Warning: Found $INSTANCE_COUNT bastion instances:${NC}"
    echo "$INSTANCE_LIST" | jq -r '.[] | "\(..[0]) - \(..[1])"'
    echo -e "${YELLOW}Using the first instance.${NC}"
fi

# Get the first instance ID
INSTANCE_ID=$(echo "$INSTANCE_LIST" | jq -r '.[0][0]')
INSTANCE_NAME=$(echo "$INSTANCE_LIST" | jq -r '.[0][1] // "unnamed"')

echo -e "${GREEN}✓ Found bastion instance: ${BOLD}$INSTANCE_ID${NC} (${INSTANCE_NAME})"

# Get database secret from AWS Secrets Manager
echo -e "${BLUE}Looking for database secret...${NC}"

# Look for the specific secret based on environment
SECRET_NAME="evolution/$ENVIRONMENT/axon/domain-service/postgresql-axondb-connection"
echo -e "${BLUE}Checking for secret: ${BOLD}$SECRET_NAME${NC}"

# Try to get the secret
SECRET_EXISTS=$(aws secretsmanager describe-secret \
    --secret-id "$SECRET_NAME" \
    --query 'ARN' \
    --output text \
    --region "$AWS_REGION" \
    --profile "$AWS_PROFILE" 2>/dev/null)

if [ -n "$SECRET_EXISTS" ] && [ "$SECRET_EXISTS" != "None" ]; then
    SECRET_ARN="$SECRET_NAME"
    echo -e "${GREEN}✓ Found database secret${NC}"
else
    echo -e "${RED}Error: Could not find database secret '$SECRET_NAME'${NC}"
    echo -e "${YELLOW}Please ensure this secret exists in AWS Secrets Manager.${NC}"
    exit 1
fi

# Get credentials from the secret
if [ -n "$SECRET_ARN" ]; then
    echo -e "${BLUE}Retrieving database credentials...${NC}"
    SECRET_JSON=$(aws secretsmanager get-secret-value \
        --secret-id "$SECRET_ARN" \
        --query 'SecretString' \
        --output text \
        --region "$AWS_REGION" \
        --profile "$AWS_PROFILE" 2>/dev/null)
    
    if [ -n "$SECRET_JSON" ]; then
        # Check if the secret has a SharedDb key
        HAS_SHARED_DB=$(echo "$SECRET_JSON" | jq -r 'has("SharedDb")')
        
        if [ "$HAS_SHARED_DB" == "true" ]; then
            # Extract from SharedDb key
            echo -e "${BLUE}Extracting database details from SharedDb configuration...${NC}"
            DB_CONNECTION_STRING=$(echo "$SECRET_JSON" | jq -r '.SharedDb // empty')
            
            # Parse PostgreSQL connection string
            # Format: Host=host;Port=5432;Database=dbname;Username=username;Password=password
            # Use sed for portable parsing
            DB_ENDPOINT=$(echo "$DB_CONNECTION_STRING" | sed -n 's/.*Host=\([^;]*\).*/\1/p')
            DB_PORT=$(echo "$DB_CONNECTION_STRING" | sed -n 's/.*Port=\([^;]*\).*/\1/p')
            DB_NAME=$(echo "$DB_CONNECTION_STRING" | sed -n 's/.*Database=\([^;]*\).*/\1/p')
            DB_USERNAME=$(echo "$DB_CONNECTION_STRING" | sed -n 's/.*Username=\([^;]*\).*/\1/p')
            DB_PASSWORD=$(echo "$DB_CONNECTION_STRING" | sed -n 's/.*Password=\([^;]*\).*/\1/p')
            
            # Default port if not specified
            DB_PORT="${DB_PORT:-5432}"
            
            # Use port from connection string
            if [ -n "$DB_PORT" ]; then
                REMOTE_PORT="$DB_PORT"
            fi
        else
            # Fallback to standard secret format
            DB_USERNAME=$(echo "$SECRET_JSON" | jq -r '.username // empty')
            DB_PASSWORD=$(echo "$SECRET_JSON" | jq -r '.password // empty')
            DB_ENDPOINT=$(echo "$SECRET_JSON" | jq -r '.host // empty')
            DB_PORT=$(echo "$SECRET_JSON" | jq -r '.port // empty')
            DB_NAME=$(echo "$SECRET_JSON" | jq -r '.dbname // empty')
            
            # Use port from secret if available
            if [ -n "$DB_PORT" ]; then
                REMOTE_PORT="$DB_PORT"
            fi
        fi
    fi
fi

# Default values if not found
DB_USERNAME="${DB_USERNAME:-axondbadmin}"
DB_NAME="${DB_NAME:-axondb}"

# Validate we have required connection details
if [ -z "$DB_ENDPOINT" ]; then
    echo -e "${RED}Error: Could not determine database endpoint.${NC}"
    echo -e "${YELLOW}Make sure the database secret contains a 'host' field.${NC}"
    exit 1
fi

echo
echo -e "${CYAN}${BOLD}Connection Details:${NC}"
echo -e "${CYAN}===================${NC}"
echo -e "${BOLD}Database Endpoint:${NC} $DB_ENDPOINT"
echo -e "${BOLD}Database Name:${NC}     $DB_NAME"
echo -e "${BOLD}Username:${NC}          $DB_USERNAME"
echo -e "${BOLD}Password:${NC}          $DB_PASSWORD"
echo -e "${BOLD}Local Port:${NC}        $LOCAL_PORT"
echo

# Check if SSM plugin is installed
if ! command -v session-manager-plugin &> /dev/null; then
    echo -e "${RED}Error: AWS Session Manager Plugin is not installed.${NC}"
    echo -e "${YELLOW}Please install it from: https://docs.aws.amazon.com/systems-manager/latest/userguide/session-manager-working-with-install-plugin.html${NC}"
    exit 1
fi

# Check if port is already in use
if lsof -Pi :$LOCAL_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
    echo -e "${YELLOW}Warning: Port $LOCAL_PORT is already in use.${NC}"
    echo -e "${YELLOW}You can specify a different port with: LOCAL_PORT=5433 $0${NC}"
    echo
    echo -ne "${YELLOW}Do you want to continue anyway? (y/N) ${NC}"
    read -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Create a temporary file for the password
PGPASS_FILE=$(mktemp)
chmod 600 "$PGPASS_FILE"
echo "$DB_ENDPOINT:$REMOTE_PORT:$DB_NAME:$DB_USERNAME:$DB_PASSWORD" > "$PGPASS_FILE"

# Function to cleanup on exit
cleanup() {
    echo
    echo -e "${BLUE}Cleaning up...${NC}"
    rm -f "$PGPASS_FILE"
    # Kill the SSM session if it's still running
    if [ -n "$SSM_PID" ]; then
        kill $SSM_PID 2>/dev/null || true
    fi
}
trap cleanup EXIT INT TERM

echo -e "${BLUE}Starting SSM port forwarding session...${NC}"
echo -e "${CYAN}=======================================${NC}"
echo

# Start SSM port forwarding in the background
aws ssm start-session \
    --target "$INSTANCE_ID" \
    --document-name AWS-StartPortForwardingSessionToRemoteHost \
    --parameters "{\"host\":[\"$DB_ENDPOINT\"],\"portNumber\":[\"$REMOTE_PORT\"],\"localPortNumber\":[\"$LOCAL_PORT\"]}" \
    --region "$AWS_REGION" \
    --profile "$AWS_PROFILE" &

SSM_PID=$!

# Wait for port to be available
echo -e "${BLUE}Waiting for port forwarding to establish...${NC}"
for i in {1..30}; do
    if nc -z localhost $LOCAL_PORT 2>/dev/null; then
        echo -e "${GREEN}✓ Port forwarding established!${NC}"
        break
    fi
    if [ $i -eq 30 ]; then
        echo -e "${RED}Error: Port forwarding failed to establish after 30 seconds${NC}"
        exit 1
    fi
    sleep 1
done

echo
echo -e "${GREEN}=========================================${NC}"
echo -e "${GREEN}${BOLD}Database is now accessible!${NC}"
echo -e "${GREEN}=========================================${NC}"
echo
echo -e "${CYAN}${BOLD}Connection commands:${NC}"
echo -e "${CYAN}-------------------${NC}"
echo
echo -e "${BOLD}Using psql:${NC}"
echo -e "${YELLOW}  PGPASSFILE='$PGPASS_FILE' psql -h localhost -p $LOCAL_PORT -U $DB_USERNAME -d $DB_NAME${NC}"
echo
echo -e "${BOLD}Using psql with password prompt:${NC}"
echo -e "${YELLOW}  psql -h localhost -p $LOCAL_PORT -U $DB_USERNAME -d $DB_NAME${NC}"
echo -e "${YELLOW}  Password: $DB_PASSWORD${NC}"
echo
echo -e "${BOLD}Connection string for other tools:${NC}"
echo -e "${YELLOW}  postgresql://$DB_USERNAME:$DB_PASSWORD@localhost:$LOCAL_PORT/$DB_NAME${NC}"
echo
echo -e "${RED}=========================================${NC}"
echo -e "${RED}${BOLD}Press Ctrl+C to close the connection${NC}"
echo -e "${RED}=========================================${NC}"
echo

# Keep the script running
wait $SSM_PID