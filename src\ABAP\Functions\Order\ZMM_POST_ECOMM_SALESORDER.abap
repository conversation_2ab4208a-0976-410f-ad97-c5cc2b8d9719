FUNCTION zmm_post_salesorder.
*"----------------------------------------------------------------------
*"*"Remote Function Module:
*"  IMPORTING
*"     VALUE(CUSTOMER_NUMBER_SP) TYPE  KUNNR
*"     VALUE(CUSTOMER_NUMBER_SH) TYPE  KUNNR
*"     VALUE(CUSTOMER_PO_NUM) TYPE  BSTNK
*"     VALUE(ORDER_BY_NAME) TYPE  BNAME
*"     VALUE(ORDER_BY_PHONE) TYPE  TELF1
*"     VALUE(SHIP_TYPE) TYPE  VSARTTR
*"     VALUE(ORDER_SRC) TYPE  CHAR01 OPTIONAL
*"     VALUE(SHIP_TO_ADDR) TYPE  ZMME_ADDRESS OPTIONAL
*"     VALUE(RUSH_ORDER) TYPE  CHAR01 OPTIONAL
*"     VALUE(NAME_2) TYPE  NAME2_GP OPTIONAL
*"  EXPORTING
*"     VALUE(ORDER_NUMBER) TYPE  VBELN
*"  TABLES
*"      MATERIAL_ITEMS STRUCTURE  ZMME_MATERIALS
*"      PROMOTIONS STRUCTURE  ZMME_PROMO OPTIONAL
*"      ORDER_TEXT STRUCTURE  ZMME_TEXT OPTIONAL
*"      RESULTS STRUCTURE  BAPIRET2
*"      BOL_TEXT STRUCTURE  ZMME_TEXT OPTIONAL
*"  EXCEPTIONS
*"      ERROR
*"      NO_DROP_STATE
*"----------------------------------------------------------------------

  DATA: ls_company_codes TYPE ty_company_codes,
        ls_header        TYPE bapisdh1,
        lt_items         TYPE bapisditm_t,
        lt_partners      TYPE bapiparnr_t,
        lt_conditions    TYPE bapicond_t,
        lt_texts         TYPE bapisdtext_t,
        lt_extensionin   TYPE TABLE OF bapiparex,
        lv_char          TYPE c,
        lv_errflag       TYPE c,
        ls_kna1          TYPE kna1,
        ls_adrc          TYPE adrc,
        ls_knvv          TYPE knvv.

  " Determine company codes based on customer
  lv_char = customer_number_sp+4(1).

  IF lv_char = '9'.  " Thailand
    ls_company_codes-bukrs = '6010'.
    ls_company_codes-werks = '6010'.
    ls_company_codes-vkorg = '6010'.
    ls_company_codes-lgort = '6010'.
    ls_company_codes-kkber = '2000'.
  ELSE.  " Ripon
    ls_company_codes-bukrs = '3000'.
    ls_company_codes-werks = '3010'.
    ls_company_codes-vkorg = '3000'.
    ls_company_codes-lgort = '3010'.
    ls_company_codes-kkber = '1000'.
  ENDIF.

  " Build order header
  IF rush_order IS NOT INITIAL.
    ls_header-doc_type = 'ZIRS'.
  ELSE.
    ls_header-doc_type = 'ZPT'.
  ENDIF.

  ls_header-sales_org   = ls_company_codes-vkorg.
  ls_header-distr_chan  = '10'.
  ls_header-division    = '20'.
  ls_header-purch_date  = sy-datum.
  ls_header-po_method   = 'DFUE'.
  ls_header-name        = order_by_name.
  ls_header-telephone   = order_by_phone.
  ls_header-purch_no_c  = customer_po_num.
  ls_header-dlv_block   = 'Z2'.
  ls_header-sd_doc_cat  = 'C'.

  IF order_src = 'X'.
    ls_header-created_by = 'XMLEXTRA'.
  ELSE.
    ls_header-created_by = 'EXTRANET'.
  ENDIF.

  ls_header-ship_type   = ship_type.

  " Build order items
  DATA: lv_line_number TYPE posnr.
  LOOP AT material_items INTO DATA(ls_material).
    lv_line_number = lv_line_number + 10.

    DATA: ls_item TYPE bapisditm.
    ls_item-itm_number = lv_line_number.
    ls_item-material   = ls_material-matnr.
    ls_item-plant      = ls_company_codes-werks.
    ls_item-store_loc  = ls_company_codes-lgort.
    ls_item-created_by = 'EXTRANET'.

    DATA: ls_schedule TYPE bapischdl.
    ls_schedule-itm_number = lv_line_number.
    ls_schedule-sched_line = '0001'.
    ls_schedule-req_qty    = ls_material-target_qty.

    APPEND ls_item TO lt_items.
    APPEND ls_schedule TO lt_items-schedules.
  ENDLOOP.

  " Build order partners and handle shipping address
  DATA: ls_partner TYPE bapiparnr.
  ls_partner-partn_role = 'AG'.
  ls_partner-partn_numb = customer_number_sp.
  APPEND ls_partner TO lt_partners.

  " Get customer data
  SELECT SINGLE * FROM knvv INTO ls_knvv
    WHERE kunnr  = customer_number_sp
      AND vkorg  = ls_company_codes-vkorg
      AND vtweg  = '10'
      AND spart  = '20'.

  IF ship_to_addr IS INITIAL.
    " Get ship-to party address from customer master
    SELECT SINGLE * FROM kna1 INTO ls_kna1
      WHERE kunnr = customer_number_sh.

    IF sy-subrc = 0.
      SELECT SINGLE * FROM adrc INTO ls_adrc
        WHERE addrnumber = ls_kna1-adrnr.

      IF sy-subrc = 0.
        " Check drop ship status
        IF ls_adrc-country = 'US'.
          PERFORM check_drop_ship USING customer_number_sh ls_adrc-region lv_errflag.
          IF lv_errflag = 'X'.
            RAISE no_drop_state.
          ENDIF.
        ENDIF.

        " Add shipping address
        DATA: ls_order_addr TYPE bapiaddr1.
        ls_order_addr-addr_no    = '0000000001'.
        ls_order_addr-name       = ls_adrc-name1.
        ls_order_addr-name_2     = ls_adrc-name2.
        ls_order_addr-street     = ls_adrc-street.
        ls_order_addr-str_suppl1 = ls_adrc-str_suppl1.
        ls_order_addr-city       = ls_adrc-city1.
        ls_order_addr-postl_cod1 = ls_adrc-post_code1.
        ls_order_addr-region     = ls_adrc-region.
        ls_order_addr-country    = ls_adrc-country.
        ls_order_addr-tel1_numbr = ls_adrc-tel_number.
        ls_order_addr-fax_number = ls_adrc-fax_number.
        ls_order_addr-transpzone = 'Z000000001'.
        ls_order_addr-langu      = sy-langu.
        APPEND ls_order_addr TO lt_partners-addresses.

        " Add ship-to partner
        CLEAR ls_partner.
        ls_partner-partn_role = 'WE'.
        ls_partner-partn_numb = customer_number_sh.
        IF name_2 IS NOT INITIAL.
          ls_partner-name_2 = name_2.
        ENDIF.
        APPEND ls_partner TO lt_partners.

        " Check for drop ship charge
        IF ls_knvv-kvgr3 NE 'Y'.
          lv_line_number = lv_line_number + 10.
          CLEAR ls_item.
          ls_item-itm_number = lv_line_number.
          ls_item-material   = 'DROP SHIP CHARGE'.
          ls_item-plant      = ls_company_codes-werks.
          ls_item-store_loc  = ls_company_codes-lgort.
          ls_item-created_by = 'EXTRANET'.
          APPEND ls_item TO lt_items.

          CLEAR ls_schedule.
          ls_schedule-itm_number = lv_line_number.
          ls_schedule-sched_line = '0001'.
          ls_schedule-req_qty    = '1.000'.
          APPEND ls_schedule TO lt_items-schedules.

          " Add drop ship charge condition
          DATA: ls_condition TYPE bapicond.
          ls_condition-itm_number = '000000'.
          ls_condition-cond_type  = 'ZDRP'.
          ls_condition-cond_value = '5.00'.
          ls_condition-currency   = 'USD'.
          APPEND ls_condition TO lt_conditions.
        ENDIF.
      ENDIF.
    ELSE.
      lv_errflag = 'X'.
      DATA: ls_result TYPE bapiret2.
      ls_result-message = 'No valid Customer #'.
      ls_result-type    = 'E'.
      APPEND ls_result TO results.
    ENDIF.
  ELSE.
    " Handle one-time address
    IF ship_to_addr-country = 'US'.
      PERFORM check_drop_ship USING customer_number_sh ship_to_addr-region lv_errflag.
      IF lv_errflag = 'X'.
        RAISE no_drop_state.
      ENDIF.
    ENDIF.

    " Add ship-to partner with one-time address
    CLEAR ls_partner.
    ls_partner-partn_role = 'WE'.
    ls_partner-partn_numb = customer_number_sh.
    ls_partner-addr_link  = '0000000001'.
    IF name_2 IS NOT INITIAL.
      ls_partner-name_2 = name_2.
    ENDIF.
    APPEND ls_partner TO lt_partners.

    " Add one-time address
    DATA: ls_order_addr TYPE bapiaddr1.
    ls_order_addr-addr_no    = '0000000001'.
    ls_order_addr-name       = ship_to_addr-name1.
    ls_order_addr-name_2     = ship_to_addr-name2.
    ls_order_addr-street     = ship_to_addr-street1.
    ls_order_addr-str_suppl1 = ship_to_addr-street2.
    ls_order_addr-city       = ship_to_addr-city.
    ls_order_addr-postl_cod1 = ship_to_addr-zip_code.
    ls_order_addr-region     = ship_to_addr-region.
    ls_order_addr-country    = ship_to_addr-country.
    ls_order_addr-tel1_numbr = ship_to_addr-tel1_numbr.
    ls_order_addr-e_mail     = ship_to_addr-e_mail.
    ls_order_addr-fax_number = ship_to_addr-fax_number.
    ls_order_addr-transpzone = 'Z000000001'.
    ls_order_addr-langu      = sy-langu.
    APPEND ls_order_addr TO lt_partners-addresses.
  ENDIF.

  " Build order conditions
  LOOP AT promotions INTO DATA(ls_promo).
    DATA: ls_condition TYPE bapicond.
    ls_condition-itm_number = '000000'.
    ls_condition-cond_type  = ls_promo-kschl.
    ls_condition-cond_value = ls_promo-zzamount.
    ls_condition-currency   = 'USD'.
    APPEND ls_condition TO lt_conditions.
  ENDLOOP.

  " Build order texts
  DATA: lv_name TYPE thead-tdname.
  CONCATENATE customer_number_sp '10' '20' INTO lv_name.

  DATA: lt_text TYPE TABLE OF tline.
  CALL FUNCTION 'READ_TEXT'
    EXPORTING
      id       = 'Z008'
      language = 'E'
      name     = lv_name
      object   = 'KNVV'
    TABLES
      lines    = lt_text.

  LOOP AT lt_text INTO DATA(ls_text).
    DATA: ls_order_text TYPE bapisdtext.
    ls_order_text-itm_number = '000000'.
    ls_order_text-text_id    = 'Z008'.
    ls_order_text-langu      = sy-langu.
    ls_order_text-langu_iso  = 'EN'.
    ls_order_text-text_line  = ls_text-tdline.
    APPEND ls_order_text TO lt_texts.
  ENDLOOP.

  LOOP AT order_text INTO DATA(ls_order_text).
    IF sy-tabix = 1.
      ls_order_text-format_col = '/'.
    ENDIF.
    ls_order_text-itm_number = '000000'.
    ls_order_text-text_id    = 'Z008'.
    ls_order_text-langu      = sy-langu.
    ls_order_text-langu_iso  = 'EN'.
    APPEND ls_order_text TO lt_texts.
  ENDLOOP.

  LOOP AT bol_text INTO DATA(ls_bol_text).
    IF sy-tabix = 1.
      ls_bol_text-format_col = '*'.
    ENDIF.
    ls_bol_text-itm_number = '000000'.
    ls_bol_text-text_id    = 'Z005'.
    ls_bol_text-langu      = sy-langu.
    ls_bol_text-langu_iso  = 'EN'.
    APPEND ls_bol_text TO lt_texts.
  ENDLOOP.

  " Create sales order
  CALL FUNCTION 'BAPI_SALESORDER_CREATEFROMDAT2'
    EXPORTING
      order_header_in     = ls_header
    IMPORTING
      salesdocument       = order_number
    TABLES
      return              = results
      order_items_in      = lt_items
      order_partners      = lt_partners
      order_conditions_in = lt_conditions
      order_text          = lt_texts
      extensionin         = lt_extensionin.

  " Check for errors
  READ TABLE results INTO DATA(ls_result)
    WITH KEY type = 'E'.
  IF sy-subrc = 0.
    CLEAR order_number.
    RAISE error.
  ELSE.
    " Commit transaction
    CALL FUNCTION 'BAPI_TRANSACTION_COMMIT'
      EXPORTING
        wait = 'X'.

    " Handle credit release for orders under $100
    IF order_number IS NOT INITIAL.
      DATA: ls_vbak TYPE vbak,
            lv_ctlpc TYPE knkk-ctlpc,
            lv_usr01 TYPE t691f-usrpr0.

      SELECT SINGLE * FROM vbak INTO ls_vbak
        WHERE vbeln = order_number.

      IF sy-subrc = 0 AND ls_vbak-netwr < '100.00' AND ls_vbak-vbtyp = 'C'.
        SELECT SINGLE ctlpc FROM knkk INTO lv_ctlpc
          WHERE kunnr = ls_vbak-kunnr
            AND kkber = ls_company_codes-kkber.

        IF sy-subrc = 0 AND lv_ctlpc IS NOT INITIAL.
          SELECT SINGLE usrpr0 FROM t691f INTO lv_usr01
            WHERE kkber = ls_company_codes-kkber
              AND ctlpc = lv_ctlpc
              AND crmgr = '01'.

          IF sy-subrc = 0 AND lv_usr01 = 'X'.
            SUBMIT zab_sd_release_credit_hold
              WITH p_vbeln = order_number AND RETURN.
            COMMIT WORK.
          ENDIF.
        ENDIF.
      ENDIF.

      " Handle incoterm update
      DATA: ls_vbkd TYPE vbkd,
            ls_xvbpa TYPE vbpa,
            lt_xvbpa TYPE TABLE OF vbpa,
            lv_3rdparty_inco1 TYPE inco1,
            ls_bape_vbak TYPE bape_vbak,
            lv_dropship TYPE c.

      SELECT SINGLE * FROM vbkd INTO ls_vbkd
        WHERE vbeln = order_number.

      SELECT * FROM vbpa INTO TABLE lt_xvbpa
        WHERE vbeln = order_number.

      CALL FUNCTION 'Z_SD_3RDPARTY_FRT_INCO'
        EXPORTING
          kunnr            = customer_number_sh
          vkorg            = ls_vbak-vkorg
          vtweg            = ls_vbak-vtweg
          spart            = ls_vbak-spart
          vsart            = ls_vbak-vsart
        IMPORTING
          ex_zzfreightacct = ls_bape_vbak-zzfreightacct
          ex_inco1         = lv_3rdparty_inco1.

      " Determine incoterm based on business rules
      IF ls_vbak-auart = 'ZPT' OR ls_vbak-auart = 'ZIRS' OR
         ls_vbak-auart = 'ZWFI' OR ls_vbak-auart = 'ZWFP'.
        IF ls_vbkd-bzirk = 'Z00001'.
          IF ls_vbak-auart = 'ZIRS'.
            IF ls_vbkd-inco1 = 'ZTP' OR ls_vbkd-inco1 = 'ZCO'.
              ls_header_in-incoterms1 = ls_vbkd-inco1.
            ELSE.
              ls_header_in-incoterms1 = 'ZPA'.
            ENDIF.
          ELSEIF ls_vbak-auart = 'ZWFI' OR ls_vbak-auart = 'ZWFP'.
            ls_header_in-incoterms1 = 'ZPD'.
          ELSEIF ls_vbak-auart = 'ZPT'.
            IF ( ls_vbak-ernam = 'EXTRANET' OR ls_vbak-ernam = 'XMLEXTRA' ) AND
               ls_vbak-netwr >= '2000.00'.
              ls_header_in-incoterms1 = 'ZPD'.
            ELSEIF ( ls_vbak-ernam = 'EXTRANET' OR ls_vbak-ernam = 'XMLEXTRA' ) AND
                   ls_vbak-netwr < '2000.00'.
              IF ls_vbkd-inco1 = 'ZTP' OR ls_vbkd-inco1 = 'ZCO'.
                ls_header_in-incoterms1 = ls_vbkd-inco1.
              ELSE.
                ls_header_in-incoterms1 = 'ZPA'.
              ENDIF.
            ELSEIF ls_vbak-netwr >= '2500.00'.
              ls_header_in-incoterms1 = 'ZPD'.
            ELSE.
              IF ls_vbkd-inco1 = 'ZTP' OR ls_vbkd-inco1 = 'ZCO'.
                ls_header_in-incoterms1 = ls_vbkd-inco1.
              ELSE.
                ls_header_in-incoterms1 = 'ZPA'.
              ENDIF.
            ENDIF.
          ENDIF.
        ELSE.
          ls_header_in-incoterms1 = ls_vbkd-inco1.
        ENDIF.

        " Check for drop ship
        lv_dropship = 'N'.
        IF ( ls_vbak-auart = 'ZPT' OR ls_vbak-auart = 'ZIRS' ) AND
           ls_vbkd-bzirk = 'Z00001'.
          READ TABLE lt_conditions INTO ls_condition
            WITH KEY cond_type = 'ZDRP'.
          IF sy-subrc = 0 OR ls_xvbpa-adrda = 'E'.
            lv_dropship = 'Y'.
            IF lv_3rdparty_inco1 IS NOT INITIAL.
              ls_header_in-incoterms1 = 'ZTP'.
            ELSE.
              ls_header_in-incoterms1 = 'ZPA'.
            ENDIF.
          ENDIF.
        ENDIF.

        " Validate ZTP vs ZCO
        IF ls_header_in-incoterms1 = 'ZTP' OR ls_header_in-incoterms1 = 'ZCO'.
          IF lv_dropship = 'N'.
            ls_header_in-incoterms1 = 'ZCO'.
          ELSE.
            ls_header_in-incoterms1 = 'ZTP'.
          ENDIF.
        ENDIF.

        " Update incoterms
        IF ls_header_in-incoterms1 IS NOT INITIAL.
          ls_header_inx-updateflag = 'U'.
          ls_header_inx-incoterms1 = 'X'.

          CALL FUNCTION 'BAPI_SALESORDER_CHANGE'
            EXPORTING
              salesdocument    = order_number
              order_header_in  = ls_header_in
              order_header_inx = ls_header_inx
            TABLES
              return           = lt_return.

          IF sy-subrc = 0.
            CALL FUNCTION 'BAPI_TRANSACTION_COMMIT'.
          ENDIF.
        ENDIF.
      ENDIF.
    ENDIF.
  ENDIF.

ENDFUNCTION.

FORM check_drop_ship USING iv_customer_number TYPE kunnr
                          iv_region         TYPE regio
                          ev_errflag        TYPE c.
  " Check if drop ship is allowed for the region
  SELECT SINGLE * FROM zmm_drop_ship_regions
    WHERE region = iv_region
      AND allowed = 'X'.

  IF sy-subrc <> 0.
    ev_errflag = 'X'.
  ENDIF.
ENDFORM.