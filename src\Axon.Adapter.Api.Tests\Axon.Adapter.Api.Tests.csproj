﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsPackable>false</IsPackable>
    <DebugType>portable</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="coverlet.collector" Version="6.0.4" />
    <PackageReference Include="DotNetEnv" Version="3.1.1" />
    <PackageReference Include="MassTransit" Version="8.5.0" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.14.0" />
    <PackageReference Include="Moq" Version="4.20.72" />
    <PackageReference Include="xunit" Version="2.9.3" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.8.2" />
  </ItemGroup>

  <ItemGroup>
    <Using Include="Xunit" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Axon.Adapter.Api\Axon.Adapter.Api.csproj" />
    <Reference Include="sapnco">
      <HintPath>..\Axon.Core\Assemblies\SapNco\v3.1\sapnco.dll</HintPath>
      <CopyLocal>true</CopyLocal>
    </Reference>
    <Reference Include="sapnco_utils">
      <HintPath>..\Axon.Core\Assemblies\SapNco\v3.1\sapnco_utils.dll</HintPath>
      <CopyLocal>true</CopyLocal>
    </Reference>
  </ItemGroup>

</Project>
