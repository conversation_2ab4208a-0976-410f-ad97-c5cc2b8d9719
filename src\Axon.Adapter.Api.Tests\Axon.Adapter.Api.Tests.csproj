﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsPackable>false</IsPackable>
    <DebugType>portable</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="coverlet.collector" Version="6.0.4" />
    <PackageReference Include="DotNetEnv" Version="3.1.1" />
    <PackageReference Include="MassTransit" Version="8.5.0" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.14.0" />
    <PackageReference Include="Moq" Version="4.20.72" />
    <PackageReference Include="xunit" Version="2.9.3" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.8.2" />
  </ItemGroup>

  <ItemGroup>
    <Using Include="Xunit" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Axon.Adapter.Api\Axon.Adapter.Api.csproj" />
    <Reference Include="sapnco">
      <HintPath>..\Axon.Core\Assemblies\SapNco\v3.1\Common\sapnco.dll</HintPath>
      <CopyLocal>true</CopyLocal>
    </Reference>
    <Reference Include="sapnco_utils">
      <HintPath>..\Axon.Core\Assemblies\SapNco\v3.1\Common\sapnco_utils.dll</HintPath>
      <CopyLocal>true</CopyLocal>
    </Reference>
  </ItemGroup>

  <!-- SAP NCo Cross-Platform Configuration -->
  
  <!-- Always copy the managed .NET assemblies from Common -->
  <ItemGroup>
    <Content Include="..\Axon.Core\Assemblies\SapNco\v3.1\Common\sapnco.dll" Link="sapnco.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\Axon.Core\Assemblies\SapNco\v3.1\Common\sapnco_utils.dll" Link="sapnco_utils.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <!-- Windows native libraries -->
  <ItemGroup Condition="'$(OS)' == 'Windows_NT'">
    <Content Include="..\Axon.Core\Assemblies\SapNco\v3.1\Windows\sapnwrfc.dll" Link="sapnwrfc.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <!-- Linux native libraries -->
  <ItemGroup Condition="'$(OS)' != 'Windows_NT' And '$(RuntimeIdentifier)' == 'linux-x64'">
    <Content Include="..\Axon.Core\Assemblies\SapNco\v3.1\Linux\libsapnwrfc.so" Link="libsapnwrfc.so">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\Axon.Core\Assemblies\SapNco\v3.1\Linux\libsapucum.so" Link="libsapucum.so">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <!-- Mac native libraries -->
  <ItemGroup Condition="'$(OS)' != 'Windows_NT' And '$(RuntimeIdentifier)' == 'osx-x64'">
    <Content Include="..\Axon.Core\Assemblies\SapNco\v3.1\Mac\libsapnwrfc.dylib" Link="libsapnwrfc.dylib">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\Axon.Core\Assemblies\SapNco\v3.1\Mac\libsapucum.dylib" Link="libsapucum.dylib">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

</Project>
