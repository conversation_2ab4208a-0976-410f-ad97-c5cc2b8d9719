using Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Authentication;
using Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Options;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using Xunit;

namespace Axon.Adapter.Api.Tests.MagentoIntegrationAdapterAPI.Authentication;

public class MagentoApiKeyProviderTests
{
    private readonly Mock<ILogger<MagentoApiKeyProvider>> _loggerMock;
    private readonly Mock<IOptions<MagentoApiOptions>> _optionsMock;
    
    public MagentoApiKeyProviderTests()
    {
        _loggerMock = new Mock<ILogger<MagentoApiKeyProvider>>();
        _optionsMock = new Mock<IOptions<MagentoApiOptions>>();
    }

    [Fact]
    public async Task ProvideAsync_Should_ReturnApiKey_When_ValidKeyProvided()
    {
        // Arrange
        var validApiKey = "test-api-key-12345";
        var options = new MagentoApiOptions
        {
            RequireInboundAuthentication = true,
            InboundApiKey = validApiKey
        };
        
        _optionsMock.Setup(x => x.Value).Returns(options);
        var provider = new MagentoApiKeyProvider(_optionsMock.Object, _loggerMock.Object);

        // Act
        var result = await provider.ProvideAsync(validApiKey);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(validApiKey, result.Key);
        Assert.Equal("Magento Integration", result.OwnerName);
        Assert.Empty(result.Claims);
    }

    [Fact]
    public async Task ProvideAsync_Should_ReturnNull_When_InvalidKeyProvided()
    {
        // Arrange
        var validApiKey = "test-api-key-12345";
        var invalidApiKey = "invalid-key";
        var options = new MagentoApiOptions
        {
            RequireInboundAuthentication = true,
            InboundApiKey = validApiKey
        };
        
        _optionsMock.Setup(x => x.Value).Returns(options);
        var provider = new MagentoApiKeyProvider(_optionsMock.Object, _loggerMock.Object);

        // Act
        var result = await provider.ProvideAsync(invalidApiKey);

        // Assert
        Assert.Null(result);
        
        // Verify warning was logged
        _loggerMock.Verify(x => x.Log(
            LogLevel.Warning,
            It.IsAny<EventId>(),
            It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Invalid API key provided")),
            It.IsAny<Exception>(),
            It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task ProvideAsync_Should_ReturnNull_When_InboundApiKeyNotConfigured()
    {
        // Arrange
        var options = new MagentoApiOptions
        {
            RequireInboundAuthentication = true,
            InboundApiKey = null // Not configured
        };
        
        _optionsMock.Setup(x => x.Value).Returns(options);
        var provider = new MagentoApiKeyProvider(_optionsMock.Object, _loggerMock.Object);

        // Act
        var result = await provider.ProvideAsync("any-key");

        // Assert
        Assert.Null(result);
        
        // Verify warning was logged
        _loggerMock.Verify(x => x.Log(
            LogLevel.Warning,
            It.IsAny<EventId>(),
            It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Inbound API key is not configured")),
            It.IsAny<Exception>(),
            It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task ProvideAsync_Should_ReturnNull_When_EmptyInboundApiKey()
    {
        // Arrange
        var options = new MagentoApiOptions
        {
            RequireInboundAuthentication = true,
            InboundApiKey = "" // Empty string
        };
        
        _optionsMock.Setup(x => x.Value).Returns(options);
        var provider = new MagentoApiKeyProvider(_optionsMock.Object, _loggerMock.Object);

        // Act
        var result = await provider.ProvideAsync("any-key");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task ProvideAsync_Should_PerformCaseSensitiveComparison()
    {
        // Arrange
        var validApiKey = "Test-API-Key-12345";
        var options = new MagentoApiOptions
        {
            RequireInboundAuthentication = true,
            InboundApiKey = validApiKey
        };
        
        _optionsMock.Setup(x => x.Value).Returns(options);
        var provider = new MagentoApiKeyProvider(_optionsMock.Object, _loggerMock.Object);

        // Act
        var resultLowerCase = await provider.ProvideAsync("test-api-key-12345");
        var resultUpperCase = await provider.ProvideAsync("TEST-API-KEY-12345");
        var resultExactMatch = await provider.ProvideAsync(validApiKey);

        // Assert
        Assert.Null(resultLowerCase); // Should fail due to case mismatch
        Assert.Null(resultUpperCase); // Should fail due to case mismatch
        Assert.NotNull(resultExactMatch); // Should succeed with exact match
    }

    [Fact]
    public async Task ProvideAsync_Should_LogInformation_When_ValidKeyProvided()
    {
        // Arrange
        var validApiKey = "test-api-key-12345";
        var options = new MagentoApiOptions
        {
            RequireInboundAuthentication = true,
            InboundApiKey = validApiKey
        };
        
        _optionsMock.Setup(x => x.Value).Returns(options);
        var provider = new MagentoApiKeyProvider(_optionsMock.Object, _loggerMock.Object);

        // Act
        await provider.ProvideAsync(validApiKey);

        // Assert
        _loggerMock.Verify(x => x.Log(
            LogLevel.Information,
            It.IsAny<EventId>(),
            It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Valid API key provided for Magento Integration")),
            It.IsAny<Exception>(),
            It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public void Constructor_Should_LogInitializationInfo()
    {
        // Arrange
        var options = new MagentoApiOptions
        {
            RequireInboundAuthentication = true,
            InboundApiKey = "test-key"
        };
        
        _optionsMock.Setup(x => x.Value).Returns(options);

        // Act
        _ = new MagentoApiKeyProvider(_optionsMock.Object, _loggerMock.Object);

        // Assert
        _loggerMock.Verify(x => x.Log(
            LogLevel.Information,
            It.IsAny<EventId>(),
            It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("MagentoApiKeyProvider initialized")),
            It.IsAny<Exception>(),
            It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public void InMemoryApiKey_Should_InitializePropertiesCorrectly()
    {
        // Arrange & Act
        var key = "test-key";
        var owner = "Test Owner";
        var apiKey = new InMemoryApiKey(key, owner);

        // Assert
        Assert.Equal(key, apiKey.Key);
        Assert.Equal(owner, apiKey.OwnerName);
        Assert.NotNull(apiKey.Claims);
        Assert.Empty(apiKey.Claims);
    }
}