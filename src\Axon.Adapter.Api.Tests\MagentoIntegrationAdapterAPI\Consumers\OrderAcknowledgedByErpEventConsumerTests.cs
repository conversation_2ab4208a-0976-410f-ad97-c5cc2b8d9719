using System;
using System.Threading;
using System.Threading.Tasks;
using Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Consumers;
using Axon.Contracts.Order.Events;
using MassTransit;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace Axon.Adapter.Api.Tests.MagentoIntegrationAdapterAPI.Consumers;

public class OrderAcknowledgedByErpEventConsumerTests
{
    [Fact]
    public async Task Consume_CallsUpdateOrderStatus_WithCorrectOrderIdAndStatus()
    {
        // Arrange
        var orderId = Guid.NewGuid();
        var erpOrderId = "ERP123";
        var handlerMock = new Mock<IMagentoOrderStatusUpdateHandler>();
        var loggerMock = new Mock<ILogger<OrderAcknowledgedByErpEventConsumer>>();
        var consumer = new OrderAcknowledgedByErpEventConsumer(handlerMock.Object, loggerMock.Object);
        var evt = new OrderAcknowledgedByErpEvent(orderId, new OrderAcknowledgedByErpEvent.OrderRef(erpOrderId));
        var contextMock = new Mock<ConsumeContext<OrderAcknowledgedByErpEvent>>();
        contextMock.SetupGet(x => x.Message).Returns(evt);
        contextMock.SetupGet(x => x.CancellationToken).Returns(CancellationToken.None);

        // Act
        await consumer.Consume(contextMock.Object);

        // Assert
        handlerMock.Verify(x => x.UpdateOrderStatusAsync(orderId, "processing", CancellationToken.None), Times.Once);
    }
} 