using Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Controllers;
using Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Models;
using Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Queries;
using Axon.Adapter.Api.MagentoIntegrationAdapterAPI.RequestHandlers;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using System.Security.Claims;
using Microsoft.AspNetCore.Authorization;

namespace Axon.Adapter.Api.Tests.MagentoIntegrationAdapterAPI.Controllers;

public class CartCreationControllerTests
{
    private readonly Mock<ICartCreatedRequestHandler> _requestHandlerMock;
    private readonly Mock<ILogger<CartCreationController>> _loggerMock;
    private readonly CartCreationController _controller;

    public CartCreationControllerTests()
    {
        _requestHandlerMock = new Mock<ICartCreatedRequestHandler>();
        _loggerMock = new Mock<ILogger<CartCreationController>>();
        _controller = new CartCreationController(_requestHandlerMock.Object, _loggerMock.Object);
    }

    [Fact]
    public async Task CartCreated_Should_ReturnAccepted_When_ValidRequest()
    {
        // Arrange
        var query = new CartCreatedQuery
        {
            CartId = 12345,
            StoreId = 1,
            Currency = "USD",
            Items = new List<CartItemQuery>
            {
                new CartItemQuery
                {
                    Sku = "SKU001",
                    Qty = 2,
                    ProductType = "simple"
                }
            }
        };

        var expectedResponse = new CartCreatedResponse
        {
            CartId = 12345,
            StoreId = 1,
            Currency = "USD",
            CustomerIsGuest = true,
            CreatedAt = "2024-01-01T00:00:00Z",
            UpdatedAt = "2024-01-01T00:00:00Z",
            ItemsCount = 1,
            ItemsQty = 2,
            Items = []
        };

        _requestHandlerMock
            .Setup(x => x.HandleAsync(It.IsAny<CartCreatedQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CartCreated(query);

        // Assert
        var acceptedResult = Assert.IsType<AcceptedResult>(result);
        var response = Assert.IsType<CartCreatedResponse>(acceptedResult.Value);
        Assert.Equal(12345, response.CartId);
        Assert.Equal("USD", response.Currency);
    }

    [Fact]
    public async Task CartCreated_Should_PassCancellationToken_ToHandler()
    {
        // Arrange
        var query = new CartCreatedQuery
        {
            CartId = 11111,
            StoreId = 1,
            Currency = "USD",
            Items = []
        };

        var cts = new CancellationTokenSource();
        var response = new CartCreatedResponse 
        { 
            CartId = 11111, 
            StoreId = 1,
            Currency = "USD",
            CreatedAt = "2024-01-01T00:00:00Z",
            UpdatedAt = "2024-01-01T00:00:00Z"
        };

        _requestHandlerMock
            .Setup(x => x.HandleAsync(It.IsAny<CartCreatedQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        // Act
        await _controller.CartCreated(query, cts.Token);

        // Assert
        _requestHandlerMock.Verify(
            x => x.HandleAsync(
                It.Is<CartCreatedQuery>(q => q.CartId == 11111),
                It.Is<CancellationToken>(ct => ct == cts.Token)),
            Times.Once);
    }

    [Fact]
    public void Controller_Should_Have_Authorize_Attribute()
    {
        // Arrange & Act
        var controllerType = typeof(CartCreationController);
        var authorizeAttribute = controllerType.GetCustomAttributes(typeof(AuthorizeAttribute), true)
            .FirstOrDefault() as AuthorizeAttribute;

        // Assert
        Assert.NotNull(authorizeAttribute);
    }

    [Fact]
    public void Controller_Should_Have_ApiController_Attribute()
    {
        // Arrange & Act
        var controllerType = typeof(CartCreationController);
        var apiControllerAttribute = controllerType.GetCustomAttributes(typeof(ApiControllerAttribute), true)
            .FirstOrDefault() as ApiControllerAttribute;

        // Assert
        Assert.NotNull(apiControllerAttribute);
    }

    [Fact]
    public void CartCreated_Action_Should_Accept_HttpPost()
    {
        // Arrange & Act
        var methodInfo = typeof(CartCreationController).GetMethod(nameof(CartCreationController.CartCreated));
        var httpPostAttribute = methodInfo?.GetCustomAttributes(typeof(HttpPostAttribute), true)
            .FirstOrDefault() as HttpPostAttribute;

        // Assert
        Assert.NotNull(httpPostAttribute);
        Assert.Equal("cart-created", httpPostAttribute.Template);
    }
}