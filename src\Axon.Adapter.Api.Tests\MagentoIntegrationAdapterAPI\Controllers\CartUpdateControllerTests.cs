using Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Controllers;
using Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Models;
using Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Queries;
using Axon.Adapter.Api.MagentoIntegrationAdapterAPI.RequestHandlers;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace Axon.Adapter.Api.Tests.MagentoIntegrationAdapterAPI.Controllers;

public class CartUpdateControllerTests
{
    private readonly Mock<ICartUpdatedRequestHandler> _requestHandlerMock;
    private readonly Mock<ILogger<CartUpdateController>> _loggerMock;
    private readonly CartUpdateController _controller;

    public CartUpdateControllerTests()
    {
        _requestHandlerMock = new Mock<ICartUpdatedRequestHandler>();
        _loggerMock = new Mock<ILogger<CartUpdateController>>();
        _controller = new CartUpdateController(_requestHandlerMock.Object, _loggerMock.Object);
    }

    [Fact]
    public async Task CartUpdate_Should_ReturnAccepted_When_ValidRequest()
    {
        // Arrange
        var query = new CartUpdatedQuery
        {
            CartId = 12345,
            StoreId = 1,
            Items = new List<CartItemQuery>
            {
                new CartItemQuery
                {
                    Sku = "SKU001",
                    Qty = 2,
                    ProductType = "simple"
                }
            }
        };

        var expectedResponse = new CartUpdatedResponse
        {
            CartId = 12345,
            StoreId = 1,
            UpdatedAt = "2024-01-01T00:00:00Z",
            ItemsCount = 1,
            ItemsQty = 2,
            Items = []
        };

        _requestHandlerMock
            .Setup(x => x.HandleAsync(It.IsAny<CartUpdatedQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CartUpdated(query);

        // Assert
        var acceptedResult = Assert.IsType<AcceptedResult>(result);
        var response = Assert.IsType<CartUpdatedResponse>(acceptedResult.Value);
        Assert.Equal(12345, response.CartId);
    }

    [Fact]
    public async Task CartUpdated_Should_PassCancellationToken_ToHandler()
    {
        // Arrange
        var query = new CartUpdatedQuery
        {
            CartId = 11111,
            StoreId = 1,
            Items = []
        };

        var cts = new CancellationTokenSource();
        var response = new CartUpdatedResponse 
        { 
            CartId = 11111, 
            StoreId = 1,
            UpdatedAt = "2024-01-01T00:00:00Z"
        };

        _requestHandlerMock
            .Setup(x => x.HandleAsync(It.IsAny<CartUpdatedQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        // Act
        await _controller.CartUpdated(query, cts.Token);

        // Assert
        _requestHandlerMock.Verify(
            x => x.HandleAsync(
                It.Is<CartUpdatedQuery>(q => q.CartId == 11111),
                It.Is<CancellationToken>(ct => ct == cts.Token)),
            Times.Once);
    }
}