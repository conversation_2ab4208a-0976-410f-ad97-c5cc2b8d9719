using System.ComponentModel.DataAnnotations;
using Xunit;
using Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Queries;

namespace Axon.Adapter.Api.Tests.MagentoIntegrationAdapterAPI.Controllers;

public class OrderCreatedValidationTests
{
    [Fact]
    public void OrderCreatedQuery_WithMissingRequiredFields_ShouldFailValidation()
    {
        // Arrange
        var query = new OrderCreatedQuery();
        var context = new ValidationContext(query);
        var results = new List<ValidationResult>();

        // Act
        var isValid = Validator.TryValidateObject(query, context, results, true);

        // Assert
        Assert.False(isValid);
        Assert.Contains(results, r => r.ErrorMessage!.Contains("Increment ID is required"));
        Assert.Contains(results, r => r.ErrorMessage!.Contains("Order state is required"));
        Assert.Contains(results, r => r.ErrorMessage!.Contains("Order status is required"));
        Assert.Contains(results, r => r.ErrorMessage!.Contains("Customer email is required"));
        Assert.Contains(results, r => r.ErrorMessage!.Contains("Customer first name is required"));
        Assert.Contains(results, r => r.ErrorMessage!.Contains("Customer last name is required"));
        Assert.Contains(results, r => r.ErrorMessage!.Contains("Order must contain at least one item"));
        Assert.Contains(results, r => r.ErrorMessage!.Contains("Total quantity ordered must be greater than 0"));
        Assert.Contains(results, r => r.ErrorMessage!.Contains("Grand total must be greater than 0"));
        Assert.Contains(results, r => r.ErrorMessage!.Contains("Base grand total must be greater than 0"));
        Assert.Contains(results, r => r.ErrorMessage!.Contains("Created at timestamp is required"));
    }

    [Fact]
    public void OrderCreatedQuery_WithEmptyStringFields_ShouldFailValidation()
    {
        // Arrange
        var query = new OrderCreatedQuery
        {
            IncrementId = "",
            State = "",
            Status = "",
            CustomerEmail = "",
            CustomerFirstname = "",
            CustomerLastname = ""
        };
        var context = new ValidationContext(query);
        var results = new List<ValidationResult>();

        // Act
        var isValid = Validator.TryValidateObject(query, context, results, true);

        // Assert
        Assert.False(isValid);
        Assert.Contains(results, r => r.ErrorMessage!.Contains("Increment ID is required"));
        Assert.Contains(results, r => r.ErrorMessage!.Contains("Order state is required"));
        Assert.Contains(results, r => r.ErrorMessage!.Contains("Order status is required"));
        Assert.Contains(results, r => r.ErrorMessage!.Contains("Customer email is required"));
        Assert.Contains(results, r => r.ErrorMessage!.Contains("Customer first name is required"));
        Assert.Contains(results, r => r.ErrorMessage!.Contains("Customer last name is required"));
    }

    [Fact]
    public void OrderCreatedQuery_WithInvalidEmail_ShouldFailValidation()
    {
        // Arrange
        var query = new OrderCreatedQuery
        {
            IncrementId = "12345",
            State = "processing",
            Status = "pending",
            CustomerEmail = "invalid-email",
            CustomerFirstname = "John",
            CustomerLastname = "Doe",
            CreatedAt = DateTime.UtcNow,
            BillingAddress = new Address
            {
                Street = ["123 Main St"],
                City = "Test City",
                Region = "Test Region",
                Postcode = "12345",
                CountryId = "US"
            },
            Items = [new OrderItemQuery { ItemId = 1, Sku = "TEST-SKU", Name = "Test Item", QtyOrdered = 1, Price = 10.00m }],
            Payment = new PaymentQuery { Method = "credit_card", AmountOrdered = 10.00m }
        };
        var context = new ValidationContext(query);
        var results = new List<ValidationResult>();

        // Act
        var isValid = Validator.TryValidateObject(query, context, results, true);

        // Assert
        Assert.False(isValid);
        Assert.Contains(results, r => r.ErrorMessage!.Contains("Invalid email format"));
    }

    [Fact]
    public void OrderCreatedQuery_WithEmptyItems_ShouldFailValidation()
    {
        // Arrange
        var query = new OrderCreatedQuery
        {
            IncrementId = "12345",
            State = "processing",
            Status = "pending",
            CustomerEmail = "<EMAIL>",
            CustomerFirstname = "John",
            CustomerLastname = "Doe",
            CreatedAt = DateTime.UtcNow,
            BillingAddress = new Address
            {
                Street = ["123 Main St"],
                City = "Test City",
                Region = "Test Region",
                Postcode = "12345",
                CountryId = "US"
            },
            Items = [], // Empty list should fail validation
            Payment = new PaymentQuery { Method = "credit_card", AmountOrdered = 10.00m }
        };
        var context = new ValidationContext(query);
        var results = new List<ValidationResult>();

        // Act
        var isValid = Validator.TryValidateObject(query, context, results, true);

        // Assert
        Assert.False(isValid);
        Assert.Contains(results, r => r.ErrorMessage!.Contains("Order must contain at least one item"));
    }

    [Fact]
    public void OrderCreatedQuery_WithValidData_ShouldPassValidation()
    {
        // Arrange
        var query = new OrderCreatedQuery
        {
            IncrementId = "12345",
            State = "processing",
            Status = "pending",
            CustomerEmail = "<EMAIL>",
            CustomerFirstname = "John",
            CustomerLastname = "Doe",
            CustomerId = 1,
            GrandTotal = 10.00m,
            BaseGrandTotal = 10.00m,
            TotalQtyOrdered = 1,
            CreatedAt = DateTime.UtcNow,
            BillingAddress = new Address
            {
                Street = ["123 Main St"],
                City = "Test City",
                Region = "Test Region",
                Postcode = "12345",
                CountryId = "US"
            },
            Items = [new OrderItemQuery 
            { 
                ItemId = 1, 
                Sku = "TEST-SKU", 
                Name = "Test Item", 
                QtyOrdered = 1, 
                Price = 10.00m,
                BasePrice = 10.00m,
                RowTotal = 10.00m,
                BaseRowTotal = 10.00m
            }],
            Payment = new PaymentQuery 
            { 
                Method = "credit_card", 
                AmountOrdered = 10.00m,
                BaseAmountOrdered = 10.00m
            }
        };
        var context = new ValidationContext(query);
        var results = new List<ValidationResult>();

        // Act
        var isValid = Validator.TryValidateObject(query, context, results, true);

        // Assert
        Assert.True(isValid);
        Assert.Empty(results);
    }

    [Fact]
    public void OrderCreatedQuery_WithStringFieldsTooLong_ShouldFailValidation()
    {
        // Arrange
        var query = new OrderCreatedQuery
        {
            IncrementId = new string('A', 51), // Too long (max 50)
            State = new string('B', 21), // Too long (max 20)
            Status = new string('C', 51), // Too long (max 50)
            CustomerEmail = "<EMAIL>",
            CustomerFirstname = new string('D', 256), // Too long (max 255)
            CustomerLastname = new string('E', 256), // Too long (max 255)
            CreatedAt = DateTime.UtcNow, // Add required field
            BillingAddress = new Address
            {
                Street = ["123 Main St"],
                City = "Test City",
                Region = "Test Region",
                Postcode = "12345",
                CountryId = "US"
            },
            Items = [new OrderItemQuery { ItemId = 1, Sku = "TEST-SKU", Name = "Test Item", QtyOrdered = 1, Price = 10.00m }],
            Payment = new PaymentQuery { Method = "credit_card", AmountOrdered = 10.00m }
        };
        var context = new ValidationContext(query);
        var results = new List<ValidationResult>();

        // Act
        var isValid = Validator.TryValidateObject(query, context, results, true);

        // Assert
        Assert.False(isValid);
        Assert.Contains(results, r => r.ErrorMessage!.Contains("Increment ID cannot exceed 50 characters"));
        Assert.Contains(results, r => r.ErrorMessage!.Contains("State cannot exceed 20 characters"));
        Assert.Contains(results, r => r.ErrorMessage!.Contains("Status cannot exceed 50 characters"));
        Assert.Contains(results, r => r.ErrorMessage!.Contains("First name cannot exceed 255 characters"));
        Assert.Contains(results, r => r.ErrorMessage!.Contains("Last name cannot exceed 255 characters"));
    }
} 