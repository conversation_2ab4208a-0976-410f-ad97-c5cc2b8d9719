using Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Controllers;
using Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Models;
using Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Queries;
using Axon.Adapter.Api.MagentoIntegrationAdapterAPI.RequestHandlers;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using System.Security.Claims;
using Microsoft.AspNetCore.Authorization;

namespace Axon.Adapter.Api.Tests.MagentoIntegrationAdapterAPI.Controllers;

public class OrderCreationControllerTests
{
    private readonly Mock<IOrderCreatedRequestHandler> _requestHandlerMock;
    private readonly Mock<ILogger<OrderCreationController>> _loggerMock;
    private readonly OrderCreationController _controller;

    public OrderCreationControllerTests()
    {
        _requestHandlerMock = new Mock<IOrderCreatedRequestHandler>();
        _loggerMock = new Mock<ILogger<OrderCreationController>>();
        _controller = new OrderCreationController(_requestHandlerMock.Object, _loggerMock.Object);
    }

    [Fact]
    public async Task OrderCreated_Should_ReturnAccepted_When_ValidRequest()
    {
        // Arrange
        var query = new OrderCreatedQuery
        {
            IncrementId = "12345",
            State = "processing",
            Status = "pending",
            CustomerEmail = "<EMAIL>",
            CustomerFirstname = "John",
            CustomerLastname = "Doe",
            CustomerId = 1,
            GrandTotal = 100.00m,
            BaseGrandTotal = 100.00m,
            TotalQtyOrdered = 2,
            CreatedAt = DateTime.UtcNow,
            BillingAddress = new Address
            {
                Street = ["123 Main St"],
                City = "Test City",
                Region = "Test Region",
                Postcode = "12345",
                CountryId = "US"
            },
            Items = [new OrderItemQuery 
            { 
                ItemId = 1, 
                Sku = "TEST-SKU", 
                Name = "Test Item", 
                QtyOrdered = 2, 
                Price = 50.00m,
                BasePrice = 50.00m,
                RowTotal = 100.00m,
                BaseRowTotal = 100.00m
            }],
            Payment = new PaymentQuery 
            { 
                Method = "credit_card", 
                AmountOrdered = 100.00m,
                BaseAmountOrdered = 100.00m
            }
        };

        var expectedOrderId = Guid.NewGuid();
        _requestHandlerMock
            .Setup(x => x.HandleAsync(It.IsAny<OrderCreatedQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedOrderId);

        // Act
        var result = await _controller.OrderCreated(query, CancellationToken.None);

        // Assert
        var actionResult = Assert.IsType<ActionResult<OrderCreatedResponse>>(result);
        var acceptedResult = Assert.IsType<AcceptedResult>(actionResult.Result);
        var response = Assert.IsType<OrderCreatedResponse>(acceptedResult.Value);
        Assert.Equal(expectedOrderId, response.OrderId);
    }

    [Fact]
    public async Task OrderCreated_Should_PassCancellationToken_ToHandler()
    {
        // Arrange
        var query = new OrderCreatedQuery
        {
            IncrementId = "11111",
            State = "processing",
            Status = "pending",
            CustomerEmail = "<EMAIL>",
            CustomerFirstname = "Jane",
            CustomerLastname = "Smith",
            CustomerId = 2,
            GrandTotal = 50.00m,
            BaseGrandTotal = 50.00m,
            TotalQtyOrdered = 1,
            CreatedAt = DateTime.UtcNow,
            BillingAddress = new Address
            {
                Street = ["456 Oak Ave"],
                City = "Test City",
                Region = "Test Region",
                Postcode = "12345",
                CountryId = "US"
            },
            Items = [new OrderItemQuery 
            { 
                ItemId = 2, 
                Sku = "TEST-SKU-2", 
                Name = "Test Item 2", 
                QtyOrdered = 1, 
                Price = 50.00m,
                BasePrice = 50.00m,
                RowTotal = 50.00m,
                BaseRowTotal = 50.00m
            }],
            Payment = new PaymentQuery 
            { 
                Method = "paypal", 
                AmountOrdered = 50.00m,
                BaseAmountOrdered = 50.00m
            }
        };

        var cts = new CancellationTokenSource();
        var orderId = Guid.NewGuid();

        _requestHandlerMock
            .Setup(x => x.HandleAsync(It.IsAny<OrderCreatedQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(orderId);

        // Act
        await _controller.OrderCreated(query, cts.Token);

        // Assert
        _requestHandlerMock.Verify(
            x => x.HandleAsync(
                It.Is<OrderCreatedQuery>(q => q.IncrementId == "11111"),
                It.Is<CancellationToken>(ct => ct == cts.Token)),
            Times.Once);
    }

    [Fact]
    public void Controller_Should_Have_Authorize_Attribute()
    {
        // Arrange & Act
        var controllerType = typeof(OrderCreationController);
        var authorizeAttribute = controllerType.GetCustomAttributes(typeof(AuthorizeAttribute), true)
            .FirstOrDefault() as AuthorizeAttribute;

        // Assert
        Assert.NotNull(authorizeAttribute);
    }

    [Fact]
    public void Controller_Should_Have_ApiController_Attribute()
    {
        // Arrange & Act
        var controllerType = typeof(OrderCreationController);
        var apiControllerAttribute = controllerType.GetCustomAttributes(typeof(ApiControllerAttribute), true)
            .FirstOrDefault() as ApiControllerAttribute;

        // Assert
        Assert.NotNull(apiControllerAttribute);
    }

    [Fact]
    public void OrderCreated_Action_Should_Accept_HttpPost()
    {
        // Arrange & Act
        var methodInfo = typeof(OrderCreationController).GetMethod(nameof(OrderCreationController.OrderCreated));
        var httpPostAttribute = methodInfo?.GetCustomAttributes(typeof(HttpPostAttribute), true)
            .FirstOrDefault() as HttpPostAttribute;

        // Assert
        Assert.NotNull(httpPostAttribute);
        Assert.Equal("order-created", httpPostAttribute.Template);
    }
}