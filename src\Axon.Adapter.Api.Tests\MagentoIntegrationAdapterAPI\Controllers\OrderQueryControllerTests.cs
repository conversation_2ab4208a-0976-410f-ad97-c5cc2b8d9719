using Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Controllers;
using Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Models;
using Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Queries;
using Axon.Adapter.Api.MagentoIntegrationAdapterAPI.RequestHandlers;
using Axon.Contracts.Order.Queries;
using Axon.Contracts.Order.Events;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using System.Security.Claims;
using Microsoft.AspNetCore.Authorization;

namespace Axon.Adapter.Api.Tests.MagentoIntegrationAdapterAPI.Controllers;

public class OrderQueryControllerTests
{
    private readonly Mock<IOrderFetchRequestHandler> _requestHandlerMock;
    private readonly Mock<ILogger<OrderQueryController>> _loggerMock;
    private readonly OrderQueryController _controller;

    public OrderQueryControllerTests()
    {
        _requestHandlerMock = new Mock<IOrderFetchRequestHandler>();
        _loggerMock = new Mock<ILogger<OrderQueryController>>();
        _controller = new OrderQueryController(_requestHandlerMock.Object, _loggerMock.Object);
    }

    [Fact]
    public async Task GetOrder_Should_ReturnOk_When_OrderExists()
    {
        // Arrange
        var orderId = Guid.NewGuid();
        var orderResponse = new GetOrderByIdResponse
        {
            OrderId = orderId,
            IncrementId = "12345",
            State = "processing",
            Status = "pending",
            CustomerEmail = "<EMAIL>",
            CustomerFirstname = "John",
            CustomerLastname = "Doe",
            CustomerId = 1,
            GrandTotal = 100.00m,
            BaseGrandTotal = 100.00m,
            TotalQtyOrdered = 2,
            CreatedAt = DateTimeOffset.UtcNow,
            BillingAddress = new Axon.Contracts.Order.Events.Address
            {
                Firstname = "John",
                Lastname = "Doe",
                Street = ["123 Main St"],
                City = "Test City",
                Region = "Test Region",
                Postcode = "12345",
                CountryId = "US",
                Telephone = "555-1234"
            },
            Items = new List<OrderItem>
            {
                new OrderItem
                {
                    ItemId = 1,
                    Sku = "TEST-SKU",
                    Name = "Test Item",
                    Qty = 2,
                    Price = 50.00m,
                    BasePrice = 50.00m,
                    RowTotal = 100.00m,
                    BaseRowTotal = 100.00m
                }
            },
            Payment = new Payment
            {
                Method = "credit_card",
                AmountOrdered = 100.00m,
                BaseAmountOrdered = 100.00m
            }
        };

        _requestHandlerMock
            .Setup(x => x.HandleAsync(It.IsAny<OrderFetchQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(orderResponse);

        // Act
        var result = await _controller.GetOrder(orderId, CancellationToken.None);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var orderData = Assert.IsType<OrderData>(okResult.Value);
        Assert.Equal("12345", orderData.IncrementId);
        Assert.Equal("<EMAIL>", orderData.CustomerEmail);
        Assert.Equal(100.00m, orderData.GrandTotal);
    }

    [Fact]
    public async Task GetOrder_Should_ReturnNotFound_When_OrderDoesNotExist()
    {
        // Arrange
        var orderId = Guid.NewGuid();
        _requestHandlerMock
            .Setup(x => x.HandleAsync(It.IsAny<OrderFetchQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((GetOrderByIdResponse?)null);

        // Act
        var result = await _controller.GetOrder(orderId, CancellationToken.None);

        // Assert
        var notFoundResult = Assert.IsType<NotFoundObjectResult>(result.Result);
        Assert.Equal($"Order {orderId} not found", notFoundResult.Value);
    }

    [Fact]
    public async Task GetOrder_Should_PassCancellationToken_ToHandler()
    {
        // Arrange
        var orderId = Guid.NewGuid();
        var cts = new CancellationTokenSource();

        _requestHandlerMock
            .Setup(x => x.HandleAsync(It.IsAny<OrderFetchQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((GetOrderByIdResponse?)null);

        // Act
        await _controller.GetOrder(orderId, cts.Token);

        // Assert
        _requestHandlerMock.Verify(
            x => x.HandleAsync(
                It.Is<OrderFetchQuery>(q => q.OrderId == orderId),
                It.Is<CancellationToken>(ct => ct == cts.Token)),
            Times.Once);
    }

    [Fact]
    public void Controller_Should_Have_Authorize_Attribute()
    {
        // Arrange & Act
        var controllerType = typeof(OrderQueryController);
        var authorizeAttribute = controllerType.GetCustomAttributes(typeof(AuthorizeAttribute), true)
            .FirstOrDefault() as AuthorizeAttribute;

        // Assert
        Assert.NotNull(authorizeAttribute);
    }

    [Fact]
    public void Controller_Should_Have_ApiController_Attribute()
    {
        // Arrange & Act
        var controllerType = typeof(OrderQueryController);
        var apiControllerAttribute = controllerType.GetCustomAttributes(typeof(ApiControllerAttribute), true)
            .FirstOrDefault() as ApiControllerAttribute;

        // Assert
        Assert.NotNull(apiControllerAttribute);
    }

    [Fact]
    public void GetOrder_Action_Should_Accept_HttpGet()
    {
        // Arrange & Act
        var methodInfo = typeof(OrderQueryController).GetMethod(nameof(OrderQueryController.GetOrder));
        var httpGetAttribute = methodInfo?.GetCustomAttributes(typeof(HttpGetAttribute), true)
            .FirstOrDefault() as HttpGetAttribute;

        // Assert
        Assert.NotNull(httpGetAttribute);
        Assert.Equal("orders/{orderId:guid}", httpGetAttribute.Template);
    }

    [Fact]
    public async Task GetOrder_Should_Map_ShippingAddress_When_Present()
    {
        // Arrange
        var orderId = Guid.NewGuid();
        var orderResponse = new GetOrderByIdResponse
        {
            OrderId = orderId,
            IncrementId = "12345",
            State = "processing",
            Status = "pending",
            CustomerEmail = "<EMAIL>",
            CustomerFirstname = "John",
            CustomerLastname = "Doe",
            CustomerId = 1,
            GrandTotal = 100.00m,
            BaseGrandTotal = 100.00m,
            TotalQtyOrdered = 2,
            CreatedAt = DateTimeOffset.UtcNow,
            BillingAddress = new Axon.Contracts.Order.Events.Address
            {
                Firstname = "John",
                Lastname = "Doe",
                Street = ["123 Main St"],
                City = "Test City",
                Region = "Test Region",
                Postcode = "12345",
                CountryId = "US",
                Telephone = "555-1234"
            },
            ShippingAddress = new Axon.Contracts.Order.Events.Address
            {
                Firstname = "Jane",
                Lastname = "Smith",
                Street = ["456 Oak Ave"],
                City = "Ship City",
                Region = "Ship Region",
                Postcode = "54321",
                CountryId = "CA",
                Telephone = "555-5678"
            },
            Items = new List<OrderItem>
            {
                new OrderItem
                {
                    ItemId = 1,
                    Sku = "TEST-SKU",
                    Name = "Test Item",
                    Qty = 2,
                    Price = 50.00m,
                    BasePrice = 50.00m,
                    RowTotal = 100.00m,
                    BaseRowTotal = 100.00m
                }
            },
            Payment = new Payment
            {
                Method = "credit_card",
                AmountOrdered = 100.00m,
                BaseAmountOrdered = 100.00m
            }
        };

        _requestHandlerMock
            .Setup(x => x.HandleAsync(It.IsAny<OrderFetchQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(orderResponse);

        // Act
        var result = await _controller.GetOrder(orderId, CancellationToken.None);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var orderData = Assert.IsType<OrderData>(okResult.Value);
        Assert.NotNull(orderData.ShippingAddress);
        Assert.Equal("Jane", orderData.ShippingAddress.Firstname);
        Assert.Equal("Ship City", orderData.ShippingAddress.City);
        Assert.Equal("54321", orderData.ShippingAddress.Postcode);
    }
}