using Axon.Adapter.Api.Exceptions;
using Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Queries;
using Axon.Adapter.Api.MagentoIntegrationAdapterAPI.RequestHandlers;
using Axon.Contracts.Cart.Commands;
using Axon.Contracts.Cart.Events;
using MassTransit;
using MassTransit.Testing;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace Axon.Adapter.Api.Tests.MagentoIntegrationAdapterAPI.RequestHandlers;

// Test consumer that returns success
public class TestSuccessConsumer : IConsumer<RecordCartCreatedCommand>
{
    public async Task Consume(ConsumeContext<RecordCartCreatedCommand> context)
    {
        await context.RespondAsync(new CartCreatedEvent
        {
            CartId = context.Message.CartId,
            StoreId = context.Message.StoreId,
            CreatedAt = context.Message.CreatedAt,
            UpdatedAt = context.Message.UpdatedAt,
            Currency = new Contracts.Cart.Events.CartCurrency
            {
                BaseCurrencyCode = context.Message.Currency.BaseCurrencyCode,
                QuoteCurrencyCode = context.Message.Currency.QuoteCurrencyCode
            },
            Totals = new Contracts.Cart.Events.CartTotals
            {
                GrandTotal = context.Message.Totals.GrandTotal,
                Subtotal = context.Message.Totals.Subtotal
            },
            Customer = new Contracts.Cart.Events.CartCustomer
            {
                Email = context.Message.Customer.Email,
                IsGuest = context.Message.Customer.IsGuest
            },
            Items = new List<Contracts.Cart.Events.CartItem>()
        });
    }
}

// Test consumer that returns failure for duplicate carts
public class TestDuplicateConsumer : IConsumer<RecordCartCreatedCommand>
{
    public async Task Consume(ConsumeContext<RecordCartCreatedCommand> context)
    {
        await context.RespondAsync(new CartCreationFailedEvent
        {
            CartId = context.Message.CartId,
            Reason = $"Cart with ID '{context.Message.CartId}' already exists.",
            ErrorCode = "DUPLICATE_CART",
            Timestamp = DateTimeOffset.UtcNow
        });
    }
}

public class CartCreatedRequestHandlerIntegrationTests
{
    [Fact]
    public async Task HandleAsync_Should_ThrowConflictException_When_DuplicateCart()
    {
        // Arrange
        var services = new ServiceCollection();
        services.AddSingleton(Mock.Of<ILogger<CartCreatedRequestHandler>>());
        
        services.AddMassTransitTestHarness(cfg =>
        {
            cfg.AddConsumer<TestDuplicateConsumer>();
        });

        await using var provider = services.BuildServiceProvider(true);
        var harness = provider.GetRequiredService<ITestHarness>();
        await harness.Start();

        try
        {
            var query = new CartCreatedQuery
            {
                CartId = 77777,
                StoreId = 1,
                Currency = "USD",
                Items = []
            };

            var requestClient = harness.GetRequestClient<RecordCartCreatedCommand>();
            var handler = new CartCreatedRequestHandler(requestClient, provider.GetRequiredService<ILogger<CartCreatedRequestHandler>>());

            // Act & Assert
            var exception = await Assert.ThrowsAsync<ConflictException>(() => handler.HandleAsync(query));
            Assert.Equal("Cart with ID '77777' already exists.", exception.Message);
        }
        finally
        {
            await harness.Stop();
        }
    }

    [Fact]
    public async Task HandleAsync_Should_ReturnSuccessResponse_When_CartCreated()
    {
        // Arrange
        var services = new ServiceCollection();
        services.AddSingleton(Mock.Of<ILogger<CartCreatedRequestHandler>>());
        
        services.AddMassTransitTestHarness(cfg =>
        {
            cfg.AddConsumer<TestSuccessConsumer>();
        });

        await using var provider = services.BuildServiceProvider(true);
        var harness = provider.GetRequiredService<ITestHarness>();
        await harness.Start();

        try
        {
            var query = new CartCreatedQuery
            {
                CartId = 12345,
                StoreId = 1,
                Currency = "USD",
                CustomerEmail = "<EMAIL>",
                CustomerIsGuest = false,
                ItemsCount = 2,
                ItemsQty = 3,
                Items = [],
                Totals = new CartTotalsQuery
                {
                    GrandTotal = 150.00m,
                    Subtotal = 140.00m
                }
            };

            var requestClient = harness.GetRequestClient<RecordCartCreatedCommand>();
            var handler = new CartCreatedRequestHandler(requestClient, provider.GetRequiredService<ILogger<CartCreatedRequestHandler>>());

            // Act
            var result = await handler.HandleAsync(query);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(12345, result.CartId);
            Assert.Equal(1, result.StoreId);
            Assert.Equal("USD", result.Currency);
            Assert.Equal("<EMAIL>", result.CustomerEmail);
            Assert.False(result.CustomerIsGuest);
        }
        finally
        {
            await harness.Stop();
        }
    }
}