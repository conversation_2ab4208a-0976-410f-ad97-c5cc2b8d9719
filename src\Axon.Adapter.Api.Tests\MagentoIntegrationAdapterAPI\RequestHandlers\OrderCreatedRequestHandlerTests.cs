﻿using Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Queries;
using Axon.Adapter.Api.MagentoIntegrationAdapterAPI.RequestHandlers;
using Axon.Contracts.Order.Commands;
using Axon.Contracts.Order.Events;
using MassTransit;
using Microsoft.Extensions.Logging;
using Moq;
using Address = Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Queries.Address;

namespace Axon.Adapter.Api.Tests.MagentoIntegrationAdapterAPI.RequestHandlers;

public class OrderCreatedRequestHandlerTests
{
    [Fact]
    public async Task HandleAsync_SendsCommandAndReturnsOrderId()
    {
        // Arrange
        var orderId = Guid.NewGuid();
        var requestClientMock = new Mock<IRequestClient<RecordOrderCreatedCommand>>();
        var loggerMock = new Mock<ILogger<OrderCreatedRequestHandler>>();
        var responseMock = new Mock<Response<OrderCreatedEvent>>();
        responseMock.SetupGet(x => x.Message).Returns(new OrderCreatedEvent { IncrementId = "ORDER123", OrderId = orderId });
        requestClientMock.Setup(x => x.GetResponse<OrderCreatedEvent>(It.IsAny<RecordOrderCreatedCommand>(), It.IsAny<CancellationToken>(), default))
            .ReturnsAsync(responseMock.Object);
        var handler = new OrderCreatedRequestHandler(requestClientMock.Object, loggerMock.Object);
        var query = new OrderCreatedQuery
        {
            IncrementId = "ORDER123",
            State = "new",
            Status = "pending",
            CustomerEmail = "<EMAIL>",
            Items = [],
            BillingAddress = new Address
            {
                Firstname = "John", Lastname = "Doe", Street = ["123 Main St"], City = "Metropolis",
                CountryId = "US", Telephone = "555-1234"
            },
            Payment = new PaymentQuery { Method = "credit_card", AmountOrdered = 100.00m, BaseAmountOrdered = 100.00m },
            TotalQtyOrdered = 0,
            GrandTotal = 100.00m,
            BaseGrandTotal = 100.00m,
            CreatedAt = DateTime.UtcNow
        };
        
        // Act
        var result = await handler.HandleAsync(query, CancellationToken.None);
        
        // Assert
        Assert.Equal(orderId, result);
        requestClientMock.Verify(x => x.GetResponse<OrderCreatedEvent>(It.IsAny<RecordOrderCreatedCommand>(), It.IsAny<CancellationToken>(), default), Times.Once);
    }

    [Fact]
    public async Task HandleAsync_MapsAllFieldsCorrectly()
    {
        var orderId = Guid.NewGuid();
        RecordOrderCreatedCommand? sentCommand = null;
        var requestClientMock = new Mock<IRequestClient<RecordOrderCreatedCommand>>();
        var loggerMock = new Mock<ILogger<OrderCreatedRequestHandler>>();
        var responseMock = new Mock<Response<OrderCreatedEvent>>();
        responseMock.SetupGet(x => x.Message).Returns(new OrderCreatedEvent { IncrementId = "ORDER123", OrderId = orderId });
        requestClientMock.Setup(x => x.GetResponse<OrderCreatedEvent>(It.IsAny<RecordOrderCreatedCommand>(), It.IsAny<CancellationToken>(), default))
            .Callback<RecordOrderCreatedCommand, CancellationToken, RequestTimeout?>((cmd, _, _) => sentCommand = cmd)
            .ReturnsAsync(responseMock.Object);
        var handler = new OrderCreatedRequestHandler(requestClientMock.Object, loggerMock.Object);
        var query = new OrderCreatedQuery
        {
            IncrementId = "ORDER123",
            State = "new",
            Status = "pending",
            CustomerId = 456,
            CustomerEmail = "<EMAIL>",
            CustomerFirstname = "Jane",
            CustomerLastname = "Doe",
            Items =
            [
                new()
                {
                    ItemId = 789,
                    Sku = "SKU1", 
                    QtyOrdered = 2, 
                    Price = 10.00m, 
                    BasePrice = 10.00m,
                    RowTotal = 20.00m,
                    BaseRowTotal = 20.00m,
                    Name = "Item1"
                }
            ],
            BillingAddress = new Address
            {
                Firstname = "Jane", Lastname = "Doe",
                Street = ["123 Main St"], City = "Metropolis",
                CountryId = "US", Telephone = "555-1234"
            },
            ShippingAddress = new Address
            {
                Firstname = "Jane", Lastname = "Doe",
                Street = ["123 Main St"], City = "Metropolis",
                CountryId = "US", Telephone = "555-1234"
            },
            Payment = new PaymentQuery { Method = "credit_card", AmountOrdered = 20.00m, BaseAmountOrdered = 20.00m },
            TotalQtyOrdered = 2,
            GrandTotal = 20.00m,
            BaseGrandTotal = 20.00m,
            CreatedAt = DateTime.UtcNow
        };
        
        var result = await handler.HandleAsync(query, CancellationToken.None);
        
        Assert.NotNull(sentCommand);
        Assert.Equal(query.IncrementId, sentCommand!.IncrementId);
        Assert.Equal(query.CustomerEmail, sentCommand.CustomerEmail);
        Assert.Equal(query.CustomerFirstname, sentCommand.CustomerFirstname);
        Assert.Equal(query.CustomerLastname, sentCommand.CustomerLastname);
        Assert.Equal(1, sentCommand.StoreId); // Default value
        Assert.Single(sentCommand.Items);
        Assert.Equal("SKU1", sentCommand.Items[0].Sku);
        Assert.Equal(2, sentCommand.Items[0].Qty);
        Assert.Equal("Jane", sentCommand.BillingAddress.Firstname);
        Assert.Equal("Jane", sentCommand.ShippingAddress!.Firstname);
        Assert.Equal("credit_card", sentCommand.Payment.Method);
        Assert.Null(sentCommand.ShippingMethod); // Not in v1.0.0 schema
        Assert.Equal(orderId, result);
    }

    [Fact]
    public async Task HandleAsync_ThrowsException_WhenRequestClientThrows()
    {
        var requestClientMock = new Mock<IRequestClient<RecordOrderCreatedCommand>>();
        requestClientMock.Setup(x => x.GetResponse<OrderCreatedEvent>(It.IsAny<RecordOrderCreatedCommand>(), It.IsAny<CancellationToken>(), default))
            .ThrowsAsync(new InvalidOperationException("fail"));
        var loggerMock = new Mock<ILogger<OrderCreatedRequestHandler>>();
        var handler = new OrderCreatedRequestHandler(requestClientMock.Object, loggerMock.Object);
        var query = new OrderCreatedQuery
        {
            IncrementId = "ORDER123",
            State = "new",
            Status = "pending", 
            CustomerEmail = "<EMAIL>",
            Items = [], 
            BillingAddress = new Address(),
            Payment = new PaymentQuery { Method = "credit_card", AmountOrdered = 0, BaseAmountOrdered = 0 },
            TotalQtyOrdered = 0,
            GrandTotal = 0,
            BaseGrandTotal = 0,
            CreatedAt = DateTime.UtcNow
        };
        
        await Assert.ThrowsAsync<InvalidOperationException>(() => handler.HandleAsync(query, CancellationToken.None));
    }
}