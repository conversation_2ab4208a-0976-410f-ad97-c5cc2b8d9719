using Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Queries;
using Axon.Adapter.Api.MagentoIntegrationAdapterAPI.RequestHandlers;
using Axon.Contracts.Order.Queries;
using Axon.Contracts.Order.Events;
using MassTransit;
using Microsoft.Extensions.Logging;
using Moq;

namespace Axon.Adapter.Api.Tests.MagentoIntegrationAdapterAPI.RequestHandlers;

public class OrderFetchRequestHandlerTests
{
    [Fact]
    public async Task HandleAsync_ReturnsOrderResponse_WhenOrderExists()
    {
        // Arrange
        var orderId = Guid.NewGuid();
        var expectedResponse = new GetOrderByIdResponse
        {
            OrderId = orderId,
            IncrementId = "ORDER123",
            CustomerEmail = "<EMAIL>",
            CustomerFirstname = "John",
            CustomerLastname = "Doe",
            StoreId = 1,
            Items = [
                new Axon.Contracts.Order.Events.OrderItem
                {
                    Sku = "SKU1",
                    Qty = 2,
                    Price = 10.00m,
                    Name = "Widget",
                    ProductType = "simple"
                }
            ],
            BillingAddress = new Axon.Contracts.Order.Events.Address
            {
                Firstname = "John",
                Lastname = "Doe",
                Street = ["123 Main St"],
                City = "Metropolis",
                CountryId = "US",
                Telephone = "555-1234"
            },
            Payment = new Axon.Contracts.Order.Events.Payment
            {
                Method = "credit_card"
            },
            GrandTotal = 20.00m,
            CreatedAt = DateTime.UtcNow
        };

        var requestClientMock = new Mock<IRequestClient<GetOrderByIdQuery>>();
        var loggerMock = new Mock<ILogger<OrderFetchRequestHandler>>();
        var responseMock = new Mock<Response<GetOrderByIdResponse>>();
        responseMock.SetupGet(x => x.Message).Returns(expectedResponse);
        requestClientMock.Setup(x => x.GetResponse<GetOrderByIdResponse>(It.IsAny<GetOrderByIdQuery>(), It.IsAny<CancellationToken>(), default))
            .ReturnsAsync(responseMock.Object);

        var handler = new OrderFetchRequestHandler(requestClientMock.Object, loggerMock.Object);
        var query = new OrderFetchQuery { OrderId = orderId };

        // Act
        var result = await handler.HandleAsync(query, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(orderId, result!.OrderId);
        Assert.Equal("ORDER123", result.IncrementId);
        Assert.Equal("<EMAIL>", result.CustomerEmail);
        
        requestClientMock.Verify(x => x.GetResponse<GetOrderByIdResponse>(
            It.Is<GetOrderByIdQuery>(q => q.OrderId == orderId), 
            It.IsAny<CancellationToken>(), 
            default), Times.Once);
        
        loggerMock.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Sending GetOrderByIdQuery with OrderId")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
            
        loggerMock.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Received order response with OrderId") && v.ToString()!.Contains("IncrementId")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task HandleAsync_ReturnsNull_WhenOrderNotFound()
    {
        // Arrange
        var orderId = Guid.NewGuid();
        var requestClientMock = new Mock<IRequestClient<GetOrderByIdQuery>>();
        var loggerMock = new Mock<ILogger<OrderFetchRequestHandler>>();
        
        requestClientMock.Setup(x => x.GetResponse<GetOrderByIdResponse>(It.IsAny<GetOrderByIdQuery>(), It.IsAny<CancellationToken>(), default))
            .ThrowsAsync(new RequestFaultException());

        var handler = new OrderFetchRequestHandler(requestClientMock.Object, loggerMock.Object);
        var query = new OrderFetchQuery { OrderId = orderId };

        // Act
        var result = await handler.HandleAsync(query, CancellationToken.None);

        // Assert
        Assert.Null(result);
        
        requestClientMock.Verify(x => x.GetResponse<GetOrderByIdResponse>(
            It.Is<GetOrderByIdQuery>(q => q.OrderId == orderId), 
            It.IsAny<CancellationToken>(), 
            default), Times.Once);
        
        loggerMock.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Sending GetOrderByIdQuery with OrderId")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
            
        loggerMock.Verify(
            x => x.Log(
                LogLevel.Warning,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Order with OrderId") && v.ToString()!.Contains("not found")),
                It.IsAny<Exception?>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task HandleAsync_MapsAllFieldsCorrectly()
    {
        // Arrange
        var orderId = Guid.NewGuid();
        var expectedResponse = new GetOrderByIdResponse
        {
            OrderId = orderId,
            IncrementId = "ORDER456",
            CustomerEmail = "<EMAIL>",
            CustomerFirstname = "Jane",
            CustomerLastname = "Smith",
            StoreId = 1,
            Items = [
                new Axon.Contracts.Order.Events.OrderItem
                {
                    Sku = "SKU1",
                    Qty = 1,
                    Price = 15.50m,
                    Name = "Premium Widget",
                    ProductType = "configurable"
                },
                new Axon.Contracts.Order.Events.OrderItem
                {
                    Sku = "SKU2",
                    Qty = 2,
                    Price = 8.25m,
                    Name = "Simple Gadget",
                    ProductType = "simple"
                }
            ],
            BillingAddress = new Axon.Contracts.Order.Events.Address
            {
                Firstname = "Jane",
                Lastname = "Smith",
                Street = ["456 Oak Ave", "Apt 2"],
                City = "Gotham",
                Region = "NY",
                Postcode = "54321",
                CountryId = "US",
                Telephone = "555-5678"
            },
            ShippingAddress = new Axon.Contracts.Order.Events.Address
            {
                Firstname = "John",
                Lastname = "Smith",
                Street = ["789 Pine St"],
                City = "Star City",
                Region = "CA",
                Postcode = "98765",
                CountryId = "US",
                Telephone = "555-9999"
            },
            Payment = new Axon.Contracts.Order.Events.Payment
            {
                Method = "paypal"
            },
            ShippingMethod = new Axon.Contracts.Order.Events.ShippingMethod
            {
                MethodCode = "express",
                CarrierCode = "FEDEX"
            },
            State = "processing",
            Status = "confirmed",
            GrandTotal = 32.00m,
            CreatedAt = DateTime.UtcNow
        };

        GetOrderByIdQuery? sentQuery = null;
        var requestClientMock = new Mock<IRequestClient<GetOrderByIdQuery>>();
        var loggerMock = new Mock<ILogger<OrderFetchRequestHandler>>();
        var responseMock = new Mock<Response<GetOrderByIdResponse>>();
        responseMock.SetupGet(x => x.Message).Returns(expectedResponse);
        requestClientMock.Setup(x => x.GetResponse<GetOrderByIdResponse>(It.IsAny<GetOrderByIdQuery>(), It.IsAny<CancellationToken>(), default))
            .Callback<GetOrderByIdQuery, CancellationToken, RequestTimeout?>((query, _, _) => sentQuery = query)
            .ReturnsAsync(responseMock.Object);

        var handler = new OrderFetchRequestHandler(requestClientMock.Object, loggerMock.Object);
        var query = new OrderFetchQuery { OrderId = orderId };

        // Act
        var result = await handler.HandleAsync(query, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.NotNull(sentQuery);
        Assert.Equal(orderId, sentQuery!.OrderId);
        
        // Verify response mapping
        Assert.Equal(expectedResponse.OrderId, result!.OrderId);
        Assert.Equal(expectedResponse.IncrementId, result.IncrementId);
        Assert.Equal(expectedResponse.CustomerEmail, result.CustomerEmail);
        Assert.Equal(expectedResponse.CustomerFirstname, result.CustomerFirstname);
        Assert.Equal(expectedResponse.CustomerLastname, result.CustomerLastname);
        Assert.Equal(expectedResponse.StoreId, result.StoreId);
        
        // Verify items mapping
        Assert.Equal(2, result.Items.Count);
        Assert.Equal("SKU1", result.Items[0].Sku);
        Assert.Equal(1, result.Items[0].Qty);
        Assert.Equal(15.50m, result.Items[0].Price);
        
        // Verify address mapping
        Assert.Equal("Jane", result.BillingAddress.Firstname);
        Assert.Equal(2, result.BillingAddress.Street.Count);
        Assert.NotNull(result.ShippingAddress);
        Assert.Equal("John", result.ShippingAddress.Firstname);
        
        // Verify other fields
        Assert.Equal("paypal", result.Payment.Method);
        Assert.NotNull(result.ShippingMethod);
        Assert.Equal("express", result.ShippingMethod.MethodCode);
        Assert.Equal(expectedResponse.State, result.State);
        Assert.Equal(expectedResponse.Status, result.Status);
        Assert.Equal(expectedResponse.GrandTotal, result.GrandTotal);
        Assert.Equal(expectedResponse.CreatedAt, result.CreatedAt);
    }

    [Fact]
    public async Task HandleAsync_ThrowsException_WhenRequestClientThrows()
    {
        // Arrange
        var orderId = Guid.NewGuid();
        var requestClientMock = new Mock<IRequestClient<GetOrderByIdQuery>>();
        var loggerMock = new Mock<ILogger<OrderFetchRequestHandler>>();
        
        requestClientMock.Setup(x => x.GetResponse<GetOrderByIdResponse>(It.IsAny<GetOrderByIdQuery>(), It.IsAny<CancellationToken>(), default))
            .ThrowsAsync(new InvalidOperationException("Connection failed"));

        var handler = new OrderFetchRequestHandler(requestClientMock.Object, loggerMock.Object);
        var query = new OrderFetchQuery { OrderId = orderId };

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => handler.HandleAsync(query, CancellationToken.None));
        
        requestClientMock.Verify(x => x.GetResponse<GetOrderByIdResponse>(
            It.Is<GetOrderByIdQuery>(q => q.OrderId == orderId), 
            It.IsAny<CancellationToken>(), 
            default), Times.Once);
    }
} 