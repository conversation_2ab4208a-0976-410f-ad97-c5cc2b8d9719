using System.Threading.Tasks;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Consumers.Outbound;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Handlers;
using Axon.Contracts.Order.Events;
using MassTransit;
using MassTransit.Testing;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace Axon.Adapter.Api.Tests.SAPIntegrationAdapterAPI.Handlers
{
    public class OrderCreatedEventConsumerTests
    {
        [Fact]
        public async Task Consumer_Consumes_OrderCreatedEvent_And_Publishes_SapSalesOrderCreatedEvent()
        {
            // Arrange
            var handlerMock = new Mock<IOrderCreatedEventHandler>();
            var sapEvent = new SapSalesOrderCreatedEvent("ORD123", new List<SapSalesOrderCreatedEvent.Result>());
            handlerMock.Setup(h => h.HandleAsync(It.IsAny<OrderCreatedEvent>(), It.IsAny<CancellationToken>())).ReturnsAsync(sapEvent);

            var services = new ServiceCollection();
            services.AddSingleton<IOrderCreatedEventHandler>(handlerMock.Object);
            services.AddSingleton(typeof(ILogger<OrderCreatedEventConsumer>), new Mock<ILogger<OrderCreatedEventConsumer>>().Object);
            services.AddMassTransitTestHarness(cfg =>
            {
                cfg.AddConsumer<OrderCreatedEventConsumer>();
            });

            await using var provider = services.BuildServiceProvider(true);
            var harness = provider.GetRequiredService<ITestHarness>();
            await harness.Start();

            var orderEvent = new OrderCreatedEvent { IncrementId = "CART123", CustomerEmail = "<EMAIL>" };
            await harness.Bus.Publish(orderEvent);

            var consumerHarness = harness.GetConsumerHarness<OrderCreatedEventConsumer>();
            Assert.True(await consumerHarness.Consumed.Any<OrderCreatedEvent>());
            Assert.True(await harness.Published.Any<SapSalesOrderCreatedEvent>());
        }
    }
} 