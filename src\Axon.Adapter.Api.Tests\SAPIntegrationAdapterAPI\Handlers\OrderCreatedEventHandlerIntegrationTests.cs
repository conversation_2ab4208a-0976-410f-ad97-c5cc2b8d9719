using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Clients;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Handlers;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Options;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Services;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Server.Services;
using Axon.Contracts.Order.Events;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Configuration;
using DotNetEnv;

namespace Axon.Adapter.Api.Tests.SAPIntegrationAdapterAPI.Handlers;

public class OrderCreatedEventHandlerIntegrationTests
{
    private readonly ISapApiClient _sapApiClient;
    private readonly ISapSalesOrderCreatedService _sapSalesOrderCreatedService;
    private readonly ILogger<OrderCreatedEventHandler> _logger;

    public OrderCreatedEventHandlerIntegrationTests()
    {
        // Load environment variables from .env file in project root
        // LoadEnvironmentVariables();
        Env.Load(@"C:\src\ALSSoftware\evolution-integration-layer\.env");
        // Build configuration from environment variables
        var configuration = new ConfigurationBuilder()
            .AddEnvironmentVariables()
            .Build();

        var sapOptions = new SapApiOptions();
        configuration.GetSection("SapApi").Bind(sapOptions);

        var options = Options.Create(sapOptions);
        _sapApiClient = new SapApiClient(options);
        _sapSalesOrderCreatedService = new SapSalesOrderCreatedService(_sapApiClient, LoggerFactory.Create(builder => builder.AddConsole()).CreateLogger<SapSalesOrderCreatedService>());
        _logger = LoggerFactory.Create(builder => builder.AddConsole()).CreateLogger<OrderCreatedEventHandler>();
    }

    [Fact]
    public async Task HandleAsync_ReturnsSapSalesOrderCreatedEvent_OnSuccess()
    {
        // Arrange
        var handler = new OrderCreatedEventHandler(_sapSalesOrderCreatedService, _logger);
        var order = new OrderCreatedEvent
        {
            IncrementId = "CART123",
            CustomerEmail = "<EMAIL>",
            ShipToParty = "0002001181",
            SoldToParty = "0000100415",
            PurchaseOrderNumber = $"PO{DateTimeOffset.UtcNow:yyyyMMddHHmmss}",
            ShippingMethod = new ShippingMethod { MethodCode = "10" },
            SapItems = [new SapOrderItem { MaterialNumber = "200927P", Quantity = 1 }]
        };

        // Act
        var result = await handler.HandleAsync(order, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.NotNull(result.OrderNumber);
        Assert.NotEmpty(result.OrderNumber);
        Assert.NotNull(result.Results);
    }

    [Fact(Skip = "Integration test, requires real SAP connection")]
    public async Task HandleAsync_WithMagentoOrderPayload_ReturnsSapSalesOrderCreatedEvent()
    {
        // Arrange - Test with real Magento order payload
        var handler = new OrderCreatedEventHandler(_sapSalesOrderCreatedService, _logger);
        var order = new OrderCreatedEvent
        {
            OrderId = Guid.NewGuid(),
            IncrementId = "000000048",
            CustomerEmail = "<EMAIL>",
            CustomerFirstname = "Vlad updated",
            CustomerLastname = "deyneko",
            StoreId = 1,
            PurchaseOrderNumber = "000000048", // Using increment_id as PO number
            OrderSource = "E", // E-commerce 
            ShipToParty = "0002001181",
            SoldToParty = "0000100415",
            Currency = "USD",
            RequestedDeliveryDate = DateTime.UtcNow.AddDays(7),
            Items = new List<OrderItem>
            {
                new OrderItem
                {
                    ItemId = 43,
                    Sku = "200927P",
                    Qty = 1,
                    Price = 100,
                    BasePrice = 100,
                    RowTotal = 100,
                    BaseRowTotal = 100,
                    Name = "Maytag MGDE300VF2 Gas Dryer",
                    ProductType = "simple"
                }
            },
            SapItems = new List<SapOrderItem>
            {
                new SapOrderItem
                {
                    MaterialNumber = "28808", // Map from SKU
                    Quantity = 1,
                    UnitOfMeasure = "EA",
                    Plant = "1000" // Default plant
                }
            },
            BillingAddress = new Address
            {
                Firstname = "Vlad",
                Lastname = "Deyneko",
                Street = new List<string> { "Miami street" },
                City = "Miami",
                Region = "FL",
                Postcode = "09876",
                CountryId = "US",
                Telephone = "987654321"
            },
            ShippingAddress = new Address
            {
                Firstname = "Vlad",
                Lastname = "Deyneko",
                Street = new List<string> { "Miami street" },
                City = "Miami",
                Region = "FL",
                Postcode = "09876",
                CountryId = "US",
                Telephone = "987654321"
            },
            Payment = new Payment
            {
                Method = "stripe_payments",
                AmountOrdered = 100,
                BaseAmountOrdered = 100
            },
            ShippingMethod = new ShippingMethod
            {
                MethodCode = "10", // Default shipping method
                CarrierCode = "UPS"
            }
        };

        // Act
        var result = await handler.HandleAsync(order, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.NotNull(result.OrderNumber);
        Assert.NotEmpty(result.OrderNumber);
        Assert.NotNull(result.Results);
        Assert.True(result.Results.Count > 0);
        
        // Verify the order number format (SAP typically returns numeric order numbers)
        Assert.Matches(@"^\d+$", result.OrderNumber);
        
        // Verify success results
        var successResults = result.Results.Where(r => r.Type == "S").ToList();
        Assert.True(successResults.Count > 0, "Should have at least one success result");
    }
}