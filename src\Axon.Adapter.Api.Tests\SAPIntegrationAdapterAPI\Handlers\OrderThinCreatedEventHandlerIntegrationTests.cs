using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Clients;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Handlers;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Options;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Services;
using Axon.Contracts.Order.Events;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Configuration;
using DotNetEnv;

namespace Axon.Adapter.Api.Tests.SAPIntegrationAdapterAPI.Handlers;

public class OrderThinCreatedEventHandlerIntegrationTests
{
    private readonly ISapApiClient _sapApiClient;
    private readonly ISapSalesOrderThinCreatedService _sapSalesOrderThinCreatedService;
    private readonly ILogger<OrderThinCreatedEventHandler> _logger;

    public OrderThinCreatedEventHandlerIntegrationTests()
    {
        // Load environment variables from .env file in project root
        Env.Load(@"C:\src\ALSSoftware\evolution-integration-layer\.env");
        // Build configuration from environment variables
        var configuration = new ConfigurationBuilder()
            .AddEnvironmentVariables()
            .Build();

        var sapOptions = new SapApiOptions();
        configuration.GetSection("SapApi").Bind(sapOptions);

        var options = Options.Create(sapOptions);
        _sapApiClient = new SapApiClient(options);
        _sapSalesOrderThinCreatedService = new SapSalesOrderThinCreatedService(_sapApiClient, LoggerFactory.Create(builder => builder.AddConsole()).CreateLogger<SapSalesOrderThinCreatedService>());
        _logger = LoggerFactory.Create(builder => builder.AddConsole()).CreateLogger<OrderThinCreatedEventHandler>();
    }

    [Fact]
    public async Task HandleAsync_ReturnsSapSalesOrderCreatedEvent_OnSuccess()
    {
        // Arrange
        var handler = new OrderThinCreatedEventHandler(_sapSalesOrderThinCreatedService, _logger);
        var order = new OrderCreatedEvent
        {
            IncrementId = "CART123",
            CustomerEmail = "<EMAIL>",
            CustomerFirstname = "John",
            CustomerLastname = "Doe",
            ShipToParty = "0002001181",
            SoldToParty = "0000100415",
            PurchaseOrderNumber = $"PO{DateTimeOffset.UtcNow:yyyyMMddHHmmss}",
            OrderSource = "E",
            ShippingMethod = new ShippingMethod { MethodCode = "01" },
            Items = new List<OrderItem>
            {
                new OrderItem
                {
                    ItemId = 1,
                    Sku = "200927P",
                    Qty = 1,
                    Price = 100,
                    BasePrice = 100,
                    RowTotal = 100,
                    BaseRowTotal = 100,
                    Name = "Test Product",
                    ProductType = "simple"
                }
            },
            ShippingAddress = new Address
            {
                Firstname = "John",
                Lastname = "Doe",
                Street = new List<string> { "123 Test St" },
                City = "Oshkosh",
                Region = "WI",
                Postcode = "54902",
                CountryId = "US",
                Telephone = "************"
            }
        };

        // Act
        var result = await handler.HandleAsync(order, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.NotNull(result.OrderNumber);
        Assert.NotEmpty(result.OrderNumber);
        Assert.NotNull(result.Results);
    }

    [Fact]
    public async Task HandleAsync_WithMagentoOrderPayload_ReturnsSapSalesOrderCreatedEvent()
    {
        // Arrange - Test with real Magento order payload
        var handler = new OrderThinCreatedEventHandler(_sapSalesOrderThinCreatedService, _logger);
        var order = new OrderCreatedEvent
        {
            OrderId = Guid.NewGuid(),
            IncrementId = "000000048",
            CustomerEmail = "<EMAIL>",
            CustomerFirstname = "Vlad updated",
            CustomerLastname = "deyneko",
            StoreId = 1,
            PurchaseOrderNumber = "000000048", // Using increment_id as PO number
            OrderSource = "E", // E-commerce 
            ShipToParty = "0002001181",
            SoldToParty = "0000100415",
            Currency = "USD",
            RequestedDeliveryDate = DateTime.UtcNow.AddDays(7),
            Items = new List<OrderItem>
            {
                new OrderItem
                {
                    ItemId = 43,
                    Sku = "200927P",
                    Qty = 1,
                    Price = 100,
                    BasePrice = 100,
                    RowTotal = 100,
                    BaseRowTotal = 100,
                    Name = "Maytag MGDE300VF2 Gas Dryer",
                    ProductType = "simple"
                }
            },
            BillingAddress = new Address
            {
                Firstname = "Vlad",
                Lastname = "Deyneko",
                Street = new List<string> { "Miami street" },
                City = "Miami",
                Region = "FL",
                Postcode = "09876",
                CountryId = "US",
                Telephone = "987654321"
            },
            ShippingAddress = new Address
            {
                Firstname = "Vlad",
                Lastname = "Deyneko",
                Street = new List<string> { "Miami street" },
                City = "Miami",
                Region = "FL",
                Postcode = "09876",
                CountryId = "US",
                Telephone = "987654321"
            },
            Payment = new Payment
            {
                Method = "stripe_payments",
                AmountOrdered = 100,
                BaseAmountOrdered = 100
            },
            ShippingMethod = new ShippingMethod
            {
                MethodCode = "01", // Default shipping method
                CarrierCode = "UPS"
            }
        };

        // Act
        var result = await handler.HandleAsync(order, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.NotNull(result.OrderNumber);
        Assert.NotEmpty(result.OrderNumber);
        Assert.NotNull(result.Results);
        Assert.True(result.Results.Count > 0);
        
        // Verify the order number format (SAP typically returns numeric order numbers)
        Assert.Matches(@"^\d+$", result.OrderNumber);
        
        // Verify success results
        var successResults = result.Results.Where(r => r.Type == "S").ToList();
        Assert.True(successResults.Count > 0, "Should have at least one success result");
    }
}