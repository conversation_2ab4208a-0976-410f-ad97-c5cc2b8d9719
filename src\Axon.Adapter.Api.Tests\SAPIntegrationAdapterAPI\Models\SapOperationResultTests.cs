using Xunit;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Models;
using SAP.Middleware.Connector;
using Moq;

namespace Axon.Adapter.Api.Tests.SAPIntegrationAdapterAPI.Models;

public class SapOperationResultTests
{
    [Fact]
    public void SapSalesOrderResult_FromSuccess_ShouldInheritBaseProperties()
    {
        // Arrange
        var mockFunction = new Mock<IRfcFunction>();
        var rfcResult = SapRfcResult.Ok(mockFunction.Object, "TEST_FUNCTION", evaluateMessages: false);
        var orderNumber = "SO123456";

        // Act
        var result = SapSalesOrderResult.FromSuccess(rfcResult, orderNumber);

        // Assert
        Assert.True(result.Success);
        Assert.Equal(orderNumber, result.OrderNumber);
        Assert.Equal(SapRfcErrorType.None, result.ErrorType);
        Assert.False(result.HasErrors);
        Assert.False(result.HasWarnings);
        Assert.IsType<SapSalesOrderResult>(result);
        Assert.IsAssignableFrom<SapOperationResultBase>(result);
    }

    [Fact]
    public void SapSalesOrderResult_FromFailure_ShouldInheritBaseProperties()
    {
        // Arrange
        var exception = new InvalidOperationException("Test error");
        var rfcResult = SapRfcResult.FromException(exception, "TEST_FUNCTION");

        // Act
        var result = SapSalesOrderResult.FromFailure(rfcResult);

        // Assert
        Assert.False(result.Success);
        Assert.Equal("Test error", result.ErrorMessage);
        Assert.Equal(SapRfcErrorType.System, result.ErrorType);
        Assert.True(result.HasErrors);
        Assert.Null(result.OrderNumber);
        Assert.IsType<SapSalesOrderResult>(result);
        Assert.IsAssignableFrom<SapOperationResultBase>(result);
    }

    [Fact]
    public void SapSalesOrderThinResult_FromSuccess_ShouldInheritBaseProperties()
    {
        // Arrange
        var mockFunction = new Mock<IRfcFunction>();
        var rfcResult = SapRfcResult.Ok(mockFunction.Object, "TEST_FUNCTION", evaluateMessages: false);
        var orderNumber = "SO789012";

        // Act
        var result = SapSalesOrderThinResult.FromSuccess(rfcResult, orderNumber);

        // Assert
        Assert.True(result.Success);
        Assert.Equal(orderNumber, result.OrderNumber);
        Assert.Equal(SapRfcErrorType.None, result.ErrorType);
        Assert.False(result.HasErrors);
        Assert.False(result.HasWarnings);
        Assert.IsType<SapSalesOrderThinResult>(result);
        Assert.IsAssignableFrom<SapOperationResultBase>(result);
    }

    [Fact]
    public void SapOperationResultBase_GetMessageSummaryByType_ShouldGroupMessagesByType()
    {
        // Arrange
        var mockFunction = new Mock<IRfcFunction>();
        var rfcResult = SapRfcResult.Ok(mockFunction.Object, "TEST_FUNCTION", evaluateMessages: false);
        rfcResult.Messages.AddRange(new List<SapRfcResultItem>
        {
            new SapRfcResultItem { Type = "E", Message = "Error 1" },
            new SapRfcResultItem { Type = "E", Message = "Error 2" },
            new SapRfcResultItem { Type = "W", Message = "Warning 1" },
            new SapRfcResultItem { Type = "S", Message = "Success 1" }
        });

        var result = new SapSalesOrderResult(rfcResult);

        // Act
        var summary = result.GetMessageSummaryByType();

        // Assert
        Assert.Equal(3, summary.Count);
        Assert.Equal("Error 1; Error 2", summary["E"]);
        Assert.Equal("Warning 1", summary["W"]);
        Assert.Equal("Success 1", summary["S"]);
    }

    [Fact]
    public void SapOperationResultBase_GetMostSevereMessageType_ShouldReturnCorrectPriority()
    {
        var mockFunction = new Mock<IRfcFunction>();

        // Test Error priority
        var errorRfcResult = SapRfcResult.Ok(mockFunction.Object, "TEST_FUNCTION", evaluateMessages: false);
        errorRfcResult.Messages.AddRange(new List<SapRfcResultItem>
        {
            new SapRfcResultItem { Type = "S", Message = "Success" },
            new SapRfcResultItem { Type = "E", Message = "Error" },
            new SapRfcResultItem { Type = "W", Message = "Warning" }
        });
        var errorResult = new SapSalesOrderResult(errorRfcResult);
        Assert.Equal("E", errorResult.GetMostSevereMessageType());

        // Test Warning priority (no errors)
        var warningRfcResult = SapRfcResult.Ok(mockFunction.Object, "TEST_FUNCTION", evaluateMessages: false);
        warningRfcResult.Messages.AddRange(new List<SapRfcResultItem>
        {
            new SapRfcResultItem { Type = "S", Message = "Success" },
            new SapRfcResultItem { Type = "W", Message = "Warning" },
            new SapRfcResultItem { Type = "I", Message = "Info" }
        });
        var warningResult = new SapSalesOrderResult(warningRfcResult);
        Assert.Equal("W", warningResult.GetMostSevereMessageType());

        // Test Info priority (no errors or warnings)
        var infoRfcResult = SapRfcResult.Ok(mockFunction.Object, "TEST_FUNCTION", evaluateMessages: false);
        infoRfcResult.Messages.AddRange(new List<SapRfcResultItem>
        {
            new SapRfcResultItem { Type = "S", Message = "Success" },
            new SapRfcResultItem { Type = "I", Message = "Info" }
        });
        var infoResult = new SapSalesOrderResult(infoRfcResult);
        Assert.Equal("I", infoResult.GetMostSevereMessageType());
    }

    [Fact]
    public void SapOperationResultBase_IsOperationSuccessful_ShouldConsiderErrorMessages()
    {
        var mockFunction = new Mock<IRfcFunction>();

        // Test successful operation without errors
        var successRfcResult = SapRfcResult.Ok(mockFunction.Object, "TEST_FUNCTION", evaluateMessages: false);
        successRfcResult.Messages.AddRange(new List<SapRfcResultItem>
        {
            new SapRfcResultItem { Type = "S", Message = "Success" },
            new SapRfcResultItem { Type = "W", Message = "Warning" }
        });
        var successResult = new SapSalesOrderResult(successRfcResult);
        Assert.True(successResult.IsOperationSuccessful());

        // Test operation with errors
        var errorRfcResult = SapRfcResult.Ok(mockFunction.Object, "TEST_FUNCTION", evaluateMessages: false);
        errorRfcResult.Messages.AddRange(new List<SapRfcResultItem>
        {
            new SapRfcResultItem { Type = "E", Message = "Error" },
            new SapRfcResultItem { Type = "S", Message = "Success" }
        });
        var errorResult = new SapSalesOrderResult(errorRfcResult);
        Assert.False(errorResult.IsOperationSuccessful());

        // Test failed operation
        var failedException = new InvalidOperationException("Test failure");
        var failedRfcResult = SapRfcResult.FromException(failedException, "TEST_FUNCTION");
        var failedResult = new SapSalesOrderResult(failedRfcResult);
        Assert.False(failedResult.IsOperationSuccessful());
    }

    [Fact]
    public void SapOperationResultBase_GetInfoSummary_ShouldReturnInfoAndSuccessMessages()
    {
        // Arrange
        var mockFunction = new Mock<IRfcFunction>();
        var rfcResult = SapRfcResult.Ok(mockFunction.Object, "TEST_FUNCTION", evaluateMessages: false);
        rfcResult.Messages.AddRange(new List<SapRfcResultItem>
        {
            new SapRfcResultItem { Type = "I", Message = "Info message" },
            new SapRfcResultItem { Type = "S", Message = "Success message" },
            new SapRfcResultItem { Type = "E", Message = "Error message" },
            new SapRfcResultItem { Type = "W", Message = "Warning message" }
        });

        var result = new SapSalesOrderResult(rfcResult);

        // Act
        var infoSummary = result.GetInfoSummary();

        // Assert
        Assert.Equal("Info message; Success message", infoSummary);
    }

    [Fact]
    public void SapSalesOrderResult_FromError_ShouldCreateFailedResult()
    {
        // Arrange
        var errorMessage = "Custom error message";
        var errorCode = "CUSTOM_ERROR";
        var errorType = SapRfcErrorType.BusinessLogic;

        // Act
        var result = SapSalesOrderResult.FromError(errorMessage, errorCode, errorType);

        // Assert
        Assert.False(result.Success);
        Assert.Equal(errorMessage, result.ErrorMessage);
        Assert.Equal(errorCode, result.ErrorCode);
        Assert.Equal(errorType, result.ErrorType);
        Assert.True(result.HasErrors);
        Assert.Null(result.OrderNumber);
        Assert.Empty(result.Results);
    }
}
