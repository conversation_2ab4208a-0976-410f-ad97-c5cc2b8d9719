using Xunit;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Models.Shared;

namespace Axon.Adapter.Api.Tests.SAPIntegrationAdapterAPI.Models;

public class SapRfcResultTests
{
    [Fact]
    public void Ok_WithFunction_ShouldCreateSuccessfulResult()
    {
        // Arrange
        var functionName = "TEST_FUNCTION";

        // Act
        var result = SapRfcResult.CreateTestSuccess(functionName);

        // Assert
        Assert.True(result.Success);
        Assert.Equal(functionName, result.FunctionName);
        Assert.Null(result.Function); // Test method creates result without SAP function
        Assert.Equal(SapRfcErrorType.None, result.ErrorType);
        Assert.Null(result.ErrorMessage);
        Assert.False(result.HasErrors);
    }

    [Fact]
    public void Error_WithMessage_ShouldCreateFailedResult()
    {
        // Arrange
        var errorMessage = "Test error message";
        var errorCode = "TEST_ERROR";
        var functionName = "TEST_FUNCTION";

        // Act
        var result = SapRfcResult.Error(errorMessage, errorCode, SapRfcErrorType.BusinessLogic, functionName);

        // Assert
        Assert.False(result.Success);
        Assert.Equal(errorMessage, result.ErrorMessage);
        Assert.Equal(errorCode, result.ErrorCode);
        Assert.Equal(SapRfcErrorType.BusinessLogic, result.ErrorType);
        Assert.Equal(functionName, result.FunctionName);
        Assert.True(result.HasErrors);
    }

    [Fact]
    public void FromException_WithSpecificExceptionTypes_ShouldSetCorrectErrorType()
    {
        // Test with generic exception (should map to System error type)
        var genericException = new InvalidOperationException("Generic error");
        var result1 = SapRfcResult.FromException(genericException, "TEST_FUNCTION");

        Assert.False(result1.Success);
        Assert.Equal(SapRfcErrorType.System, result1.ErrorType);
        Assert.Equal("TEST_FUNCTION", result1.FunctionName);
        Assert.Equal(genericException, result1.Exception);
        Assert.True(result1.HasErrors);
    }

    [Fact]
    public void FromException_WithGenericException_ShouldSetSystemErrorType()
    {
        // Arrange
        var exception = new InvalidOperationException("Generic error");
        var functionName = "TEST_FUNCTION";

        // Act
        var result = SapRfcResult.FromException(exception, functionName);

        // Assert
        Assert.False(result.Success);
        Assert.Equal("Generic error", result.ErrorMessage);
        Assert.Equal(SapRfcErrorType.System, result.ErrorType);
        Assert.Equal(functionName, result.FunctionName);
        Assert.Equal(exception, result.Exception);
    }

    [Fact]
    public void HasErrors_WithErrorMessages_ShouldReturnTrue()
    {
        // Arrange
        var result = new SapRfcResult
        {
            Success = true,
            Messages = new List<SapRfcResultItem>
            {
                new SapRfcResultItem { Type = "E", Message = "Error message" },
                new SapRfcResultItem { Type = "S", Message = "Success message" }
            }
        };

        // Act & Assert
        Assert.True(result.HasErrors);
    }

    [Fact]
    public void HasWarnings_WithWarningMessages_ShouldReturnTrue()
    {
        // Arrange
        var result = new SapRfcResult
        {
            Success = true,
            Messages = new List<SapRfcResultItem>
            {
                new SapRfcResultItem { Type = "W", Message = "Warning message" },
                new SapRfcResultItem { Type = "S", Message = "Success message" }
            }
        };

        // Act & Assert
        Assert.True(result.HasWarnings);
    }

    [Fact]
    public void GetErrorSummary_WithErrorMessage_ShouldReturnErrorMessage()
    {
        // Arrange
        var errorMessage = "Primary error message";
        var result = new SapRfcResult
        {
            ErrorMessage = errorMessage,
            Messages = new List<SapRfcResultItem>
            {
                new SapRfcResultItem { Type = "E", Message = "Secondary error" }
            }
        };

        // Act
        var summary = result.GetErrorSummary();

        // Assert
        Assert.Equal(errorMessage, summary);
    }

    [Fact]
    public void GetErrorSummary_WithoutErrorMessage_ShouldReturnConcatenatedErrors()
    {
        // Arrange
        var result = new SapRfcResult
        {
            Messages = new List<SapRfcResultItem>
            {
                new SapRfcResultItem { Type = "E", Message = "Error 1" },
                new SapRfcResultItem { Type = "W", Message = "Warning 1" },
                new SapRfcResultItem { Type = "E", Message = "Error 2" }
            }
        };

        // Act
        var summary = result.GetErrorSummary();

        // Assert
        Assert.Equal("Error 1; Error 2", summary);
    }

    [Fact]
    public void GetWarningSummary_ShouldReturnConcatenatedWarnings()
    {
        // Arrange
        var result = new SapRfcResult
        {
            Messages = new List<SapRfcResultItem>
            {
                new SapRfcResultItem { Type = "W", Message = "Warning 1" },
                new SapRfcResultItem { Type = "E", Message = "Error 1" },
                new SapRfcResultItem { Type = "W", Message = "Warning 2" }
            }
        };

        // Act
        var summary = result.GetWarningSummary();

        // Assert
        Assert.Equal("Warning 1; Warning 2", summary);
    }
}
