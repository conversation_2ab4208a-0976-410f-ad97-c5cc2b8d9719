<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Asp.Versioning.Mvc" Version="8.1.0" />
    <PackageReference Include="AspNetCore.Authentication.ApiKey" Version="9.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Axon.Core.MassTransit\Axon.Core.MassTransit.csproj" />
    <ProjectReference Include="..\Axon.Contracts\Axon.Contracts.csproj" />
    <ProjectReference Include="..\Axon.Core\Axon.Core.csproj" />
    <Reference Include="sapnco">
      <HintPath>..\Axon.Core\Assemblies\SapNco\v3.1\sapnco.dll</HintPath>
      <CopyLocal>true</CopyLocal>
    </Reference>
    <Reference Include="sapnco_utils">
      <HintPath>..\Axon.Core\Assemblies\SapNco\v3.1\sapnco_utils.dll</HintPath>
      <CopyLocal>true</CopyLocal>
    </Reference>
  </ItemGroup>

</Project>
