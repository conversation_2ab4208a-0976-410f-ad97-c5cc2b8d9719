<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <RuntimeIdentifiers>win-x64;linux-x64;linux-arm64;osx-x64</RuntimeIdentifiers>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Asp.Versioning.Mvc" Version="8.1.0" />
    <PackageReference Include="AspNetCore.Authentication.ApiKey" Version="9.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Axon.Core.MassTransit\Axon.Core.MassTransit.csproj" />
    <ProjectReference Include="..\Axon.Contracts\Axon.Contracts.csproj" />
    <ProjectReference Include="..\Axon.Core\Axon.Core.csproj" />
    <Reference Include="sapnco">
      <HintPath>..\Axon.Core\Assemblies\SapNco\v3.1\Common\sapnco.dll</HintPath>
      <CopyLocal>true</CopyLocal>
    </Reference>
    <Reference Include="sapnco_utils">
      <HintPath>..\Axon.Core\Assemblies\SapNco\v3.1\Common\sapnco_utils.dll</HintPath>
      <CopyLocal>true</CopyLocal>
    </Reference>
  </ItemGroup>

  <!-- SAP NCo Cross-Platform Configuration -->
  
  <!-- Always copy the managed .NET assemblies from Common -->
  <ItemGroup>
    <Content Include="..\Axon.Core\Assemblies\SapNco\v3.1\Common\sapnco.dll" Link="sapnco.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\Axon.Core\Assemblies\SapNco\v3.1\Common\sapnco_utils.dll" Link="sapnco_utils.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <!-- Windows native libraries -->
  <ItemGroup Condition="'$(OS)' == 'Windows_NT'">
    <Content Include="..\Axon.Core\Assemblies\SapNco\v3.1\Windows\sapnwrfc.dll" Link="sapnwrfc.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <!-- Linux x64 native libraries -->
  <ItemGroup Condition="'$(OS)' != 'Windows_NT' And '$(RuntimeIdentifier)' == 'linux-x64'">
    <Content Include="..\Axon.Core\Assemblies\SapNco\v3.1\Linux\libsapnwrfc.so" Link="libsapnwrfc.so">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\Axon.Core\Assemblies\SapNco\v3.1\Linux\libsapucum.so" Link="libsapucum.so">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <!-- Linux ARM64 native libraries -->
  <ItemGroup Condition="'$(OS)' != 'Windows_NT' And '$(RuntimeIdentifier)' == 'linux-arm64'">
    <Content Include="..\Axon.Core\Assemblies\SapNco\v3.1\Linux\libsapnwrfc.so" Link="libsapnwrfc.so">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\Axon.Core\Assemblies\SapNco\v3.1\Linux\libsapucum.so" Link="libsapucum.so">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <!-- Mac native libraries -->
  <ItemGroup Condition="'$(OS)' != 'Windows_NT' And '$(RuntimeIdentifier)' == 'osx-x64'">
    <Content Include="..\Axon.Core\Assemblies\SapNco\v3.1\Mac\libsapnwrfc.dylib" Link="libsapnwrfc.dylib">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\Axon.Core\Assemblies\SapNco\v3.1\Mac\libsapucum.dylib" Link="libsapucum.dylib">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

</Project>
