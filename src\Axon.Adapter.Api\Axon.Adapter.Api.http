@AxonAPIBaseAddress = http://localhost:7501

### Create Order (POST)
# @name createOrder
POST {{AxonAPIBaseAddress}}/api/v1.0/magento-integration-adapter/order-created
Content-Type: application/json
Accept: application/json

{
  "increment_id": "000000124",
  "state": "processing",
  "status": "pending",
  "customer_id": 456,
  "customer_email": "<EMAIL>",
  "customer_firstname": "<PERSON>",
  "customer_lastname": "<PERSON><PERSON>",
  "billing_address": {
    "firstname": "<PERSON>",
    "lastname": "<PERSON><PERSON>",
    "street": ["123 Main Street", "Apt 4B"],
    "city": "New York",
    "region": "New York",
    "postcode": "10001",
    "country_id": "US",
    "telephone": "************"
  },
  "shipping_address": {
    "firstname": "<PERSON>",
    "lastname": "<PERSON><PERSON>",
    "street": ["123 Main Street", "Apt 4B"],
    "city": "New York",
    "region": "New York",
    "postcode": "10001",
    "country_id": "US",
    "telephone": "************"
  },
  "items": [
    {
      "item_id": 789,
      "sku": "24-MB01",
      "name": "Joust Duffle Bag",
      "qty_ordered": 2,
      "price": 34.00,
      "base_price": 34.00,
      "row_total": 68.00,
      "base_row_total": 68.00
    }
  ],
  "payment": {
    "method": "checkmo",
    "amount_ordered": 88.95,
    "base_amount_ordered": 88.95
  },
  "total_qty_ordered": 2,
  "grand_total": 88.95,
  "base_grand_total": 88.95,
  "created_at": "2024-03-21T14:30:00Z"
}

### Fetch Order by ID (using order from previous POST)
GET {{AxonAPIBaseAddress}}/api/v1.0/magento-integration-adapter/orders/{{createOrder.response.body.data.order_id}}
Accept: application/json

### Fetch Order by ID - Static Example
GET {{AxonAPIBaseAddress}}/api/v1.0/magento-integration-adapter/orders/123e4567-e89b-12d3-a456-426614174000
Accept: application/json

### Fetch Order by ID - Order Not Found Example (GET)
GET {{AxonAPIBaseAddress}}/api/v1.0/magento-integration-adapter/orders/00000000-0000-0000-0000-000000000000
Accept: application/json

###
