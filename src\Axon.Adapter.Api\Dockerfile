# Use the official .NET 9.0 SDK image for build
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
ARG TARGETPLATFORM
ARG RUNTIME_ID=linux-x64

# Set RUNTIME_ID based on TARGETPLATFORM if not explicitly provided
RUN if [ "$TARGETPLATFORM" = "linux/arm64" ]; then \
        echo "linux-arm64" > /tmp/runtime_id; \
    elif [ "$TARGETPLATFORM" = "linux/amd64" ]; then \
        echo "linux-x64" > /tmp/runtime_id; \
    else \
        echo "$RUNTIME_ID" > /tmp/runtime_id; \
    fi

WORKDIR /app

# Copy solution file first
COPY Axon.sln ./

# Copy all csproj files for the solution
COPY src/Axon.Adapter.Api/*.csproj ./src/Axon.Adapter.Api/
COPY src/Axon.Core/*.csproj ./src/Axon.Core/
COPY src/Axon.Core.MassTransit/*.csproj ./src/Axon.Core.MassTransit/
COPY src/Axon.Contracts/*.csproj ./src/Axon.Contracts/
COPY src/Axon.Domain.Service/*.csproj ./src/Axon.Domain.Service/
COPY src/Axon.Adapter.Api.Tests/*.csproj ./src/Axon.Adapter.Api.Tests/
COPY src/Axon.Domain.Service.Tests/*.csproj ./src/Axon.Domain.Service.Tests/
COPY src/Axon.Integration.Mock.Api/*.csproj ./src/Axon.Integration.Mock.Api/
COPY src/Axon.Core.Tests/*.csproj ./src/Axon.Core.Tests/

# Restore dependencies for the entire solution with detailed verbosity and force restore
RUN dotnet restore Axon.sln --verbosity detailed --force

# Copy the rest of the source code
COPY . .

# Build and publish for the selected runtime
RUN RUNTIME_ID=$(cat /tmp/runtime_id) && \
    echo "Building for runtime: $RUNTIME_ID" && \
    dotnet publish src/Axon.Adapter.Api/Axon.Adapter.Api.csproj -c Release -o out -r $RUNTIME_ID --self-contained false

# Use the official .NET 9.0 ASP.NET runtime image for runtime
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS runtime
ARG TARGETPLATFORM
ARG RUNTIME_ID=linux-x64

# Set RUNTIME_ID based on TARGETPLATFORM if not explicitly provided
RUN if [ "$TARGETPLATFORM" = "linux/arm64" ]; then \
        echo "linux-arm64" > /tmp/runtime_id; \
    elif [ "$TARGETPLATFORM" = "linux/amd64" ]; then \
        echo "linux-x64" > /tmp/runtime_id; \
    else \
        echo "$RUNTIME_ID" > /tmp/runtime_id; \
    fi

WORKDIR /app

# Copy the published application
COPY --from=build /app/out .

# Set up environment for SAP NCo native libraries
ENV LD_LIBRARY_PATH=/app:$LD_LIBRARY_PATH

# Platform-specific SAP NCo library setup
RUN RUNTIME_ID=$(cat /tmp/runtime_id) && \
    if [ "$TARGETPLATFORM" = "linux/amd64" ] || [ "$RUNTIME_ID" = "linux-x64" ]; then \
        echo "Setting up x64 SAP NCo libraries" && \
        chmod +x /app/libsapnwrfc.so /app/libsapucum.so 2>/dev/null || true && \
        echo "SAP NCo x64 libraries configured"; \
    elif [ "$TARGETPLATFORM" = "linux/arm64" ] || [ "$RUNTIME_ID" = "linux-arm64" ]; then \
        echo "Setting up ARM64 SAP NCo libraries" && \
        chmod +x /app/libsapnwrfc.so /app/libsapucum.so 2>/dev/null || true && \
        echo "SAP NCo ARM64 libraries configured"; \
    else \
        echo "Unknown platform: $TARGETPLATFORM, using default library setup" && \
        chmod +x /app/*.so 2>/dev/null || true; \
    fi

# Verify SAP NCo libraries are present and executable
RUN RUNTIME_ID=$(cat /tmp/runtime_id) && \
    echo "=== SAP NCo Library Verification ===" && \
    ls -la /app/*.so 2>/dev/null || echo "No .so files found" && \
    echo "=== Runtime ID: $RUNTIME_ID ===" && \
    echo "=== Target Platform: $TARGETPLATFORM ===" && \
    echo "=== Architecture Info ===" && \
    uname -m && \
    file /app/Axon.Adapter.Api.dll 2>/dev/null || echo "Assembly not found"

# Add health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:7501/health || exit 1

EXPOSE 7501
ENTRYPOINT ["dotnet", "Axon.Adapter.Api.dll"]