using System.Security.Claims;
using AspNetCore.Authentication.ApiKey;
using Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Options;
using Microsoft.Extensions.Options;

namespace Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Authentication;

public class MagentoApiKeyProvider : IApiKeyProvider
{
    private readonly MagentoApiOptions _options;
    private readonly ILogger<MagentoApiKeyProvider> _logger;

    public MagentoApiKeyProvider(IOptions<MagentoApiOptions> options, ILogger<MagentoApiKeyProvider> logger)
    {
        _options = options.Value;
        _logger = logger;
        
        _logger.LogInformation("MagentoApiKeyProvider initialized. RequireInboundAuthentication: {RequireAuth}, HasInboundApiKey: {HasKey}", 
            _options.RequireInboundAuthentication, 
            !string.IsNullOrWhiteSpace(_options.InboundApiKey));
    }

    public Task<IApiKey?> ProvideAsync(string key)
    {
        _logger.LogDebug("ProvideAsync called for authentication validation");
        
        if (string.IsNullOrWhiteSpace(_options.InboundApiKey))
        {
            _logger.LogWarning("Inbound API key is not configured");
            return Task.FromResult<IApiKey?>(null);
        }

        _logger.LogDebug("Comparing provided key with configured key");

        if (key.Equals(_options.InboundApiKey, StringComparison.Ordinal))
        {
            _logger.LogInformation("Valid API key provided for Magento Integration");
            return Task.FromResult<IApiKey?>(new InMemoryApiKey(key, "Magento Integration"));
        }

        _logger.LogWarning("Invalid API key provided for Magento Integration");
        return Task.FromResult<IApiKey?>(null);
    }
}

public class InMemoryApiKey : IApiKey
{
    public InMemoryApiKey(string key, string owner)
    {
        Key = key;
        OwnerName = owner;
        Claims = new List<Claim>();
    }

    public string Key { get; }
    public string OwnerName { get; }
    public IReadOnlyCollection<Claim> Claims { get; }
}