using System.Security.Claims;
using System.Text.Encodings.Web;
using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.Options;

namespace Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Authentication;

public class NoAuthenticationSchemeOptions : AuthenticationSchemeOptions { }

public class NoAuthenticationHandler : AuthenticationHandler<NoAuthenticationSchemeOptions>
{
    public NoAuthenticationHandler(IOptionsMonitor<NoAuthenticationSchemeOptions> options,
        ILoggerFactory logger,
        UrlEncoder encoder)
        : base(options, logger, encoder)
    {
    }

    protected override Task<AuthenticateResult> HandleAuthenticateAsync()
    {
        // Create an anonymous principal that allows all requests
        var claims = new List<Claim>
        {
            new Claim(ClaimTypes.Name, "Anonymous"),
            new Claim(ClaimTypes.NameIdentifier, "anonymous")
        };

        var identity = new ClaimsIdentity(claims, "NoAuth");
        var principal = new ClaimsPrincipal(identity);
        var ticket = new AuthenticationTicket(principal, "NoAuth");

        return Task.FromResult(AuthenticateResult.Success(ticket));
    }
}