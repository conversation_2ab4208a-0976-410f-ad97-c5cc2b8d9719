using Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Options;
using Microsoft.Extensions.Options;

namespace Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Clients;

public class MagentoApiClient
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<MagentoApiClient> _logger;

    public MagentoApiClient(HttpClient httpClient, IOptions<MagentoApiOptions> options, ILogger<MagentoApiClient> logger)
    {
        _logger = logger;
        httpClient.BaseAddress = new Uri(options.Value.BaseAddress);

        if (!string.IsNullOrWhiteSpace(options.Value.OutboundApiKey))
        {
            httpClient.DefaultRequestHeaders.Add("X-API-Key", options.Value.OutboundApiKey);
            _logger.LogDebug("Configured outbound authentication for Magento API client");
        }
        else
        {
            _logger.LogInformation("No outbound API key configured for Magento API client - requests will be sent without authentication");
        }

        _httpClient = httpClient;
    }

    public Task<HttpResponseMessage> UpdateOrderStatusAsync(Guid orderId, string status, bool notify, string comment, bool isCommentVisibleToCustomer,
        CancellationToken cancellationToken)
    {
        var payload = new
        {
            status,
            notify,
            comment = new
            {
                comment,
                is_visible_on_front = isCommentVisibleToCustomer,
            }
        };

        return _httpClient.PostAsJsonAsync($"/rest/default/V1/orders/{orderId.ToString()}/status", payload, cancellationToken);
    }

}