using Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Clients;

namespace Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Consumers;

public interface IMagentoOrderStatusUpdateHandler
{
    Task UpdateOrderStatusAsync(Guid orderId, string newStatus, CancellationToken cancellationToken);
}

public class MagentoOrderStatusUpdateHandler : IMagentoOrderStatusUpdateHandler
{
    private readonly MagentoApiClient _magentoApiClient;
    private readonly ILogger<MagentoOrderStatusUpdateHandler> _logger;

    public MagentoOrderStatusUpdateHandler(MagentoApiClient magentoApiClient, ILogger<MagentoOrderStatusUpdateHandler> logger)
    {
        _magentoApiClient = magentoApiClient;
        _logger = logger;
    }

    public async Task UpdateOrderStatusAsync(Guid orderId, string status, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Updating Magento order {OrderId} to status '{Status}'", orderId, status);
        
        var response = await _magentoApiClient.UpdateOrderStatusAsync(orderId, status, true, "Order acknowledged by ERP", false, cancellationToken);
        response.EnsureSuccessStatusCode();
        
        _logger.LogInformation("Successfully updated Magento order {OrderId} to status '{Status}'", orderId, status);
    }
} 