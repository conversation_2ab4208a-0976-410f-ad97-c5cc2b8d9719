using Axon.Contracts.Order.Events;
using MassTransit;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;

namespace Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Consumers;

public class OrderAcknowledgedByErpEventConsumer : IConsumer<OrderAcknowledgedByErpEvent>
{
    private readonly IMagentoOrderStatusUpdateHandler _orderStatusUpdateHandler;
    private readonly ILogger<OrderAcknowledgedByErpEventConsumer> _logger;

    public OrderAcknowledgedByErpEventConsumer(
        IMagentoOrderStatusUpdateHandler orderStatusUpdateHandler,
        ILogger<OrderAcknowledgedByErpEventConsumer> logger)
    {
        _orderStatusUpdateHandler = orderStatusUpdateHandler;
        _logger = logger;
    }

    public async Task Consume(ConsumeContext<OrderAcknowledgedByErpEvent> context)
    {
        var orderId = context.Message.OrderId;
        var erpOrderId = context.Message.erp.orderId;
        _logger.LogInformation("Received OrderAcknowledgedByErpEvent for OrderId {OrderId} and ErpOrderId {ErpOrderId}", orderId, erpOrderId);

        // Call the service to update the Magento order status
        await _orderStatusUpdateHandler.UpdateOrderStatusAsync(orderId, "processing", context.CancellationToken);
        _logger.LogInformation("Updated Magento order {OrderId} to status 'processing' after ERP acknowledgement", orderId);
    }
}

