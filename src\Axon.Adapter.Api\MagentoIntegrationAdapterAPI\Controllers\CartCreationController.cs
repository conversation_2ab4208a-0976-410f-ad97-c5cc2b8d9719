using Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Models;
using Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Queries;
using Axon.Adapter.Api.MagentoIntegrationAdapterAPI.RequestHandlers;
using Asp.Versioning;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Controllers;

[ApiController]
[ApiVersion("0.1")]
[Route("api/v{version:apiVersion}/magento-integration-adapter")]
[Authorize]
public class CartCreationController : ControllerBase
{
    private readonly ICartCreatedRequestHandler _cartCreatedRequestHandler;
    private readonly ILogger<CartCreationController> _logger;

    public CartCreationController(
        ICartCreatedRequestHandler cartCreatedRequestHandler,
        ILogger<CartCreationController> logger)
    {
        _cartCreatedRequestHandler = cartCreatedRequestHandler;
        _logger = logger;
    }

    [HttpPost("cart-created")]
    public async Task<IActionResult> CartCreated(
        [FromBody] CartCreatedQuery query,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Received cart created query for cart ID: {CartId} from store: {StoreId}", 
            query.CartId, query.StoreId);
    
        var response = await _cartCreatedRequestHandler.HandleAsync(query, cancellationToken);

        _logger.LogInformation("Successfully processed cart created query. Cart ID: {CartId}", 
            response.CartId);

        return Accepted(response);

    }
}