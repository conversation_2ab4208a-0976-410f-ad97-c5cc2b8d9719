using Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Queries;
using Axon.Adapter.Api.MagentoIntegrationAdapterAPI.RequestHandlers;
using Asp.Versioning;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;

namespace Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Controllers;

[ApiController]
[ApiVersion("0.1")]
[Route("api/v{version:apiVersion}/magento-integration-adapter")]
[Authorize]
public class CartUpdateController : ControllerBase
{
    private readonly ICartUpdatedRequestHandler _cartUpdatedRequestHandler;
    private readonly ILogger<CartUpdateController> _logger;

    public CartUpdateController(
        ICartUpdatedRequestHandler cartUpdatedRequestHandler,
        ILogger<CartUpdateController> logger)
    {
        _cartUpdatedRequestHandler = cartUpdatedRequestHandler;
        _logger = logger;
    }

    [HttpPost("cart-updated")]
    public async Task<IActionResult> CartUpdated(
        [FromBody] CartUpdatedQuery query,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Received cart updated query for cart ID: {CartId}", 
            query.CartId);
    
        var response = await _cartUpdatedRequestHandler.HandleAsync(query, cancellationToken);

        _logger.LogInformation("Successfully processed cart updated query. Cart ID: {CartId}", 
            response.CartId);

        return Accepted(response);

    }
}