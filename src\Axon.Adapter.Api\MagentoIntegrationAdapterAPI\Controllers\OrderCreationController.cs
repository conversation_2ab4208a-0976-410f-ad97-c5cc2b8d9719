using Asp.Versioning;
using Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Models;
using Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Queries;
using Axon.Adapter.Api.MagentoIntegrationAdapterAPI.RequestHandlers;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Controllers;

[ApiController]
[ApiVersion("1.0")]
[Route("api/v{version:apiVersion}/magento-integration-adapter")]
[Authorize]
public class OrderCreationController : ControllerBase
{
    private readonly ILogger<OrderCreationController> _logger;
    private readonly IOrderCreatedRequestHandler _orderCreatedRequestHandler;

    public OrderCreationController(
        IOrderCreatedRequestHandler orderCreatedRequestHandler,
        ILogger<OrderCreationController> logger)
    {
        _logger = logger;
        _orderCreatedRequestHandler = orderCreatedRequestHandler;
    }

    [HttpPost("order-created")]
    public async Task<ActionResult<OrderCreatedResponse>> OrderCreated(
        [FromBody] OrderCreatedQuery request,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation(
            "Processing order creation for customer {CustomerEmail} with IncrementId {IncrementId}, Customer: {CustomerFirstname} {CustomerLastname}, State: {State}, Status: {Status}",
            request.CustomerEmail,
            request.IncrementId,
            request.CustomerFirstname,
            request.CustomerLastname,
            request.State,
            request.Status);

        var orderId = await _orderCreatedRequestHandler.HandleAsync(request, cancellationToken);

        _logger.LogInformation(
            "Order creation completed successfully. OrderId: {OrderId}, IncrementId: {IncrementId}, CustomerEmail: {CustomerEmail}, GrandTotal: {GrandTotal}",
            orderId,
            request.IncrementId,
            request.CustomerEmail,
            request.GrandTotal);

        return Accepted(new OrderCreatedResponse(orderId));
    }
}