using Asp.Versioning;
using Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Queries;
using Axon.Adapter.Api.MagentoIntegrationAdapterAPI.RequestHandlers;
using Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Controllers;

[ApiController]
[ApiVersion("1.0")]
[Route("api/v{version:apiVersion}/magento-integration-adapter")]
[Authorize]
public class OrderQueryController : ControllerBase
{
    private readonly ILogger<OrderQueryController> _logger;
    private readonly IOrderFetchRequestHandler _orderFetchRequestHandler;

    public OrderQueryController(
        IOrderFetchRequestHandler orderFetchRequestHandler,
        ILogger<OrderQueryController> logger)
    {
        _logger = logger;
        _orderFetchRequestHandler = orderFetchRequestHandler;
    }

    [HttpGet("orders/{orderId:guid}")]
    public async Task<ActionResult<OrderData>> GetOrder(Guid orderId, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Fetching order with OrderId {OrderId}", orderId);

        var query = new OrderFetchQuery { OrderId = orderId };
        var orderResponse = await _orderFetchRequestHandler.HandleAsync(query, cancellationToken);

        if (orderResponse == null)
        {
            _logger.LogWarning("Order with OrderId {OrderId} not found", orderId);
            return NotFound($"Order {orderId} not found");
        }

        _logger.LogInformation("Successfully fetched order with OrderId {OrderId} and IncrementId {IncrementId}",
            orderId, orderResponse.IncrementId);

        var orderData = new OrderData
        {
            IncrementId = orderResponse.IncrementId,
            State = orderResponse.State,
            Status = orderResponse.Status,
            CustomerId = orderResponse.CustomerId,
            CustomerEmail = orderResponse.CustomerEmail,
            CustomerFirstname = orderResponse.CustomerFirstname,
            CustomerLastname = orderResponse.CustomerLastname,
            BillingAddress = new AddressData
            {
                Firstname = orderResponse.BillingAddress.Firstname,
                Lastname = orderResponse.BillingAddress.Lastname,
                Street = orderResponse.BillingAddress.Street,
                City = orderResponse.BillingAddress.City,
                Region = orderResponse.BillingAddress.Region,
                Postcode = orderResponse.BillingAddress.Postcode,
                CountryId = orderResponse.BillingAddress.CountryId,
                Telephone = orderResponse.BillingAddress.Telephone
            },
            ShippingAddress = orderResponse.ShippingAddress != null
                ? new AddressData
                {
                    Firstname = orderResponse.ShippingAddress.Firstname,
                    Lastname = orderResponse.ShippingAddress.Lastname,
                    Street = orderResponse.ShippingAddress.Street,
                    City = orderResponse.ShippingAddress.City,
                    Region = orderResponse.ShippingAddress.Region,
                    Postcode = orderResponse.ShippingAddress.Postcode,
                    CountryId = orderResponse.ShippingAddress.CountryId,
                    Telephone = orderResponse.ShippingAddress.Telephone
                }
                : null,
            Items = orderResponse.Items.Select(item => new OrderItemData
            {
                ItemId = item.ItemId,
                Sku = item.Sku,
                Name = item.Name,
                QtyOrdered = item.Qty,
                Price = item.Price,
                BasePrice = item.BasePrice,
                RowTotal = item.RowTotal,
                BaseRowTotal = item.BaseRowTotal
            }).ToList(),
            Payment = new PaymentData
            {
                Method = orderResponse.Payment.Method,
                AmountOrdered = orderResponse.Payment.AmountOrdered,
                BaseAmountOrdered = orderResponse.Payment.BaseAmountOrdered
            },
            TotalQtyOrdered = orderResponse.TotalQtyOrdered,
            GrandTotal = orderResponse.GrandTotal,
            BaseGrandTotal = orderResponse.BaseGrandTotal,
            CreatedAt = orderResponse.CreatedAt
        };

        return Ok(orderData);
    }
}