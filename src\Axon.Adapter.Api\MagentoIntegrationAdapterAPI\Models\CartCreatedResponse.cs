using System.Text.Json.Serialization;

namespace Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Models;

/// <summary>
/// Cart Created Response matching OpenAPI specification v1.1.1
/// </summary>
public record CartCreatedResponse
{
    [JsonPropertyName("cart_id")]
    public int CartId { get; init; }

    [JsonPropertyName("store_id")]
    public int StoreId { get; init; }

    [JsonPropertyName("customer_id")]
    public int? CustomerId { get; init; }

    [JsonPropertyName("customer_email")]
    public string? CustomerEmail { get; init; }

    [JsonPropertyName("customer_is_guest")]
    public bool CustomerIsGuest { get; init; }

    [JsonPropertyName("created_at")]
    public string CreatedAt { get; init; } = string.Empty;

    [JsonPropertyName("updated_at")]
    public string UpdatedAt { get; init; } = string.Empty;

    [JsonPropertyName("currency")]
    public string Currency { get; init; } = string.Empty;

    [JsonPropertyName("items_count")]
    public int ItemsCount { get; init; }

    [JsonPropertyName("items_qty")]
    public int ItemsQty { get; init; }

    [JsonPropertyName("items")]
    public List<CartItemResponse> Items { get; init; } = [];

    [JsonPropertyName("totals")]
    public CartTotalsResponse? Totals { get; init; }

    [JsonPropertyName("is_negotiable_quote")]
    public bool IsNegotiableQuote { get; init; }
}

public record CartItemResponse
{
    [JsonPropertyName("item_id")]
    public int? ItemId { get; init; }

    [JsonPropertyName("sku")]
    public string Sku { get; init; } = string.Empty;

    [JsonPropertyName("qty")]
    public int Qty { get; init; }

    [JsonPropertyName("name")]
    public string? Name { get; init; }

    [JsonPropertyName("price")]
    public decimal? Price { get; init; }

    [JsonPropertyName("product_type")]
    public string ProductType { get; init; } = string.Empty;

    [JsonPropertyName("quote_id")]
    public int? QuoteId { get; init; }

    [JsonPropertyName("product_option")]
    public CartProductOptionResponse? ProductOption { get; init; }
}

public record CartProductOptionResponse
{
    [JsonPropertyName("extension_attributes")]
    public CartProductOptionExtensionAttributesResponse? ExtensionAttributes { get; init; }
}

public record CartProductOptionExtensionAttributesResponse
{
    [JsonPropertyName("configurable_item_options")]
    public List<ConfigurableItemOptionResponse>? ConfigurableItemOptions { get; init; }

    [JsonPropertyName("bundle_options")]
    public List<BundleOptionResponse>? BundleOptions { get; init; }
}

public record ConfigurableItemOptionResponse
{
    [JsonPropertyName("option_id")]
    public string? OptionId { get; init; }

    [JsonPropertyName("option_value")]
    public string? OptionValue { get; init; }
}

public record BundleOptionResponse
{
    [JsonPropertyName("option_id")]
    public string? OptionId { get; init; }

    [JsonPropertyName("option_qty")]
    public decimal? OptionQty { get; init; }

    [JsonPropertyName("option_selections")]
    public List<string>? OptionSelections { get; init; }
}

public record CartTotalsResponse
{
    [JsonPropertyName("subtotal")]
    public decimal? Subtotal { get; init; }

    [JsonPropertyName("grand_total")]
    public decimal? GrandTotal { get; init; }

    [JsonPropertyName("discount_amount")]
    public decimal? DiscountAmount { get; init; }
}