using System.Text.Json.Serialization;

namespace Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Models;

/// <summary>
/// Cart Updated Response matching OpenAPI specification v1.1.1
/// </summary>
public record CartUpdatedResponse
{
    [JsonPropertyName("cart_id")]
    public int CartId { get; init; }

    [JsonPropertyName("store_id")]
    public int StoreId { get; init;  }

    [JsonPropertyName("updated_at")]
    public string UpdatedAt { get; init; } = string.Empty;

    [JsonPropertyName("totals")]
    public CartTotalsResponse? Totals { get; init; }

    [JsonPropertyName("items_count")]
    public int ItemsCount { get; init; }

    [JsonPropertyName("items_qty")]
    public int ItemsQty { get; init; }

    [JsonPropertyName("items")]
    public List<CartItemResponse> Items { get; init; } = [];

    [JsonPropertyName("is_negotiable_quote")]
    public bool IsNegotiableQuote { get; init; }
}
