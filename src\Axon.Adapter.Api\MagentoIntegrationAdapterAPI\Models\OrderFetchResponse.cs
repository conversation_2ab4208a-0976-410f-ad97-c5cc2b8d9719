using System.Text.Json.Serialization;
using Axon.Contracts.Order.Queries;

namespace Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Models;

public class OrderFetchResponse
{
    [JsonPropertyName("success")]
    public bool Success { get; set; }
    
    [JsonPropertyName("timestamp")]
    public DateTimeOffset Timestamp { get; set; }
    
    [JsonPropertyName("message")]
    public string Message { get; set; } = string.Empty;
    
    [JsonPropertyName("data")]
    public OrderData? Data { get; set; }
}

public class OrderFetchErrorResponse
{
    [JsonPropertyName("success")]
    public bool Success { get; set; }
    
    [JsonPropertyName("timestamp")]
    public DateTimeOffset Timestamp { get; set; }
    
    [JsonPropertyName("message")]
    public string Message { get; set; } = string.Empty;
    
    [JsonPropertyName("data")]
    public object? Data { get; set; } = null;
}

public class OrderData
{
    [JsonPropertyName("increment_id")]
    public string IncrementId { get; set; } = string.Empty;

    [JsonPropertyName("state")]
    public string? State { get; set; }

    [JsonPropertyName("status")]
    public string? Status { get; set; }

    [JsonPropertyName("customer_id")]
    public int? CustomerId { get; set; }

    [JsonPropertyName("customer_email")]
    public string CustomerEmail { get; set; } = string.Empty;

    [JsonPropertyName("customer_firstname")]
    public string? CustomerFirstname { get; set; }

    [JsonPropertyName("customer_lastname")]
    public string? CustomerLastname { get; set; }

    [JsonPropertyName("billing_address")]
    public AddressData BillingAddress { get; set; } = new();

    [JsonPropertyName("shipping_address")]
    public AddressData? ShippingAddress { get; set; }

    [JsonPropertyName("items")]
    public List<OrderItemData> Items { get; set; } = [];

    [JsonPropertyName("payment")]
    public PaymentData Payment { get; set; } = new();

    [JsonPropertyName("total_qty_ordered")]
    public decimal? TotalQtyOrdered { get; set; }

    [JsonPropertyName("grand_total")]
    public decimal? GrandTotal { get; set; }

    [JsonPropertyName("base_grand_total")]
    public decimal? BaseGrandTotal { get; set; }

    [JsonPropertyName("created_at")]
    public DateTimeOffset? CreatedAt { get; set; }
}

public class AddressData
{
    [JsonPropertyName("firstname")]
    public string? Firstname { get; set; }

    [JsonPropertyName("lastname")]
    public string? Lastname { get; set; }

    [JsonPropertyName("street")]
    public List<string>? Street { get; set; }

    [JsonPropertyName("city")]
    public string? City { get; set; }

    [JsonPropertyName("region")]
    public string? Region { get; set; }

    [JsonPropertyName("postcode")]
    public string? Postcode { get; set; }

    [JsonPropertyName("country_id")]
    public string? CountryId { get; set; }

    [JsonPropertyName("telephone")]
    public string? Telephone { get; set; }
}

public class OrderItemData
{
    [JsonPropertyName("item_id")]
    public int? ItemId { get; set; }

    [JsonPropertyName("sku")]
    public string Sku { get; set; } = string.Empty;

    [JsonPropertyName("name")]
    public string Name { get; set; } = string.Empty;

    [JsonPropertyName("qty_ordered")]
    public decimal QtyOrdered { get; set; }

    [JsonPropertyName("price")]
    public decimal Price { get; set; }

    [JsonPropertyName("base_price")]
    public decimal BasePrice { get; set; }

    [JsonPropertyName("row_total")]
    public decimal? RowTotal { get; set; }

    [JsonPropertyName("base_row_total")]
    public decimal? BaseRowTotal { get; set; }
}

public class PaymentData
{
    [JsonPropertyName("method")]
    public string Method { get; set; } = string.Empty;

    [JsonPropertyName("amount_ordered")]
    public decimal? AmountOrdered { get; set; }

    [JsonPropertyName("base_amount_ordered")]
    public decimal? BaseAmountOrdered { get; set; }
} 