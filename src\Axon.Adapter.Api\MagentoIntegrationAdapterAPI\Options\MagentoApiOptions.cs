// Suppressing these build warnings since we are using validation of IOptions
#pragma warning disable CS8618
using System.ComponentModel.DataAnnotations;

namespace Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Options;

public class MagentoApiOptions
{
    public const string Key = "MagentoApi";

    [Required]
    [Url]
    public string BaseAddress { get; set; }

    public string? InboundApiKey { get; set; }

    public string? OutboundApiKey { get; set; }

    public bool RequireInboundAuthentication { get; set; } = false;
} 