using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Queries;

/// <summary>
/// Cart Created Query matching OpenAPI specification v1.1.1
/// </summary>
public class CartCreatedQuery
{
    [Required]
    [JsonPropertyName("cart_id")]
    public int CartId { get; set; }

    [Required]
    [JsonPropertyName("store_id")]
    public int StoreId { get; set; }

    [JsonPropertyName("customer_id")]
    public int? CustomerId { get; set; }

    [JsonPropertyName("created_at")]
    public string? CreatedAt { get; set; }

    [JsonPropertyName("updated_at")]
    public string? UpdatedAt { get; set; }

    [JsonPropertyName("customer_email")]
    public string? CustomerEmail { get; set; }

    [JsonPropertyName("customer_is_guest")]
    public bool CustomerIsGuest { get; set; } = false;

    [JsonPropertyName("items_count")]
    public int ItemsCount { get; set; }

    [JsonPropertyName("items_qty")]
    public int ItemsQty { get; set; }

    [Required]
    [JsonPropertyName("currency")]
    public string Currency { get; set; } = string.Empty;

    [Required]
    [JsonPropertyName("items")]
    public List<CartItemQuery> Items { get; set; } = [];

    [JsonPropertyName("totals")]
    public CartTotalsQuery? Totals { get; set; }

    [JsonPropertyName("is_negotiable_quote")]
    public bool IsNegotiableQuote { get; set; } = false;
}

public class CartItemQuery
{
    [JsonPropertyName("item_id")]
    public int? ItemId { get; set; }

    [Required]
    [JsonPropertyName("sku")]
    public string Sku { get; set; } = string.Empty;

    [Required]
    [JsonPropertyName("qty")]
    public decimal Qty { get; set; }

    [JsonPropertyName("quote_id")]
    public int? QuoteId { get; set; }

    [JsonPropertyName("name")]
    public string? Name { get; set; }

    [JsonPropertyName("price")]
    public decimal? Price { get; set; }

    [Required]
    [JsonPropertyName("product_type")]
    public string ProductType { get; set; } = string.Empty;

    [JsonPropertyName("product_option")]
    public CartProductOptionQuery? ProductOption { get; set; }
}

public class CartProductOptionQuery
{
    [JsonPropertyName("extension_attributes")]
    public CartProductOptionExtensionAttributesQuery? ExtensionAttributes { get; set; }
}

public class CartProductOptionExtensionAttributesQuery
{
    [JsonPropertyName("configurable_item_options")]
    public List<ConfigurableItemOptionQuery>? ConfigurableItemOptions { get; set; }

    [JsonPropertyName("bundle_options")]
    public List<BundleOptionQuery>? BundleOptions { get; set; }
}

public class ConfigurableItemOptionQuery
{
    [JsonPropertyName("option_id")]
    public string? OptionId { get; set; }

    [JsonPropertyName("option_value")]
    public string? OptionValue { get; set; }
}

public class BundleOptionQuery
{
    [JsonPropertyName("option_id")]
    public string? OptionId { get; set; }

    [JsonPropertyName("option_qty")]
    public decimal? OptionQty { get; set; }

    [JsonPropertyName("option_selections")]
    public List<string>? OptionSelections { get; set; }
}

public class CartTotalsQuery
{
    [JsonPropertyName("subtotal")]
    public decimal? Subtotal { get; set; }

    [JsonPropertyName("grand_total")]
    public decimal? GrandTotal { get; set; }

    [JsonPropertyName("discount_amount")]
    public decimal? DiscountAmount { get; set; }
}