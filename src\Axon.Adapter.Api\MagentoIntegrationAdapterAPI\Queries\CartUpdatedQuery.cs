using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Queries;

/// <summary>
/// Cart Updated Query matching OpenAPI specification v1.1.1
/// </summary>
public class CartUpdatedQuery
{
    [Required]
    [JsonPropertyName("cart_id")]
    public int CartId { get; set; }

    [Required]
    [JsonPropertyName("store_id")]
    public int StoreId { get; set; }

    [JsonPropertyName("updated_at")]
    public string? UpdatedAt { get; set; }

    [JsonPropertyName("customer_email")]
    public string? CustomerEmail { get; set; }

    [Json<PERSON>ropertyName("customer_is_guest")]
    public bool CustomerIsGuest { get; set; } = false;

    [JsonPropertyName("items_count")]
    public int ItemsCount { get; set; }

    [JsonPropertyName("items_qty")]
    public int ItemsQty { get; set; }

    [JsonPropertyName("items")]
    public List<CartItemQuery> Items { get; set; } = [];

    [JsonPropertyName("is_negotiable_quote")]
    public bool IsNegotiableQuote { get; set; } = false;
}
