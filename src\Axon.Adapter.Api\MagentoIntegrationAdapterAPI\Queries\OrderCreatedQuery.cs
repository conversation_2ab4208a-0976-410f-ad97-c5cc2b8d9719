using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Queries;

public class OrderCreatedQuery
{
    [JsonPropertyName("increment_id")]
    [Required(ErrorMessage = "Increment ID is required")]
    [StringLength(50, ErrorMessage = "Increment ID cannot exceed 50 characters")]
    public string IncrementId { get; set; } = string.Empty;

    [JsonPropertyName("state")]
    [Required(ErrorMessage = "Order state is required")]
    [StringLength(20, ErrorMessage = "State cannot exceed 20 characters")]
    public string State { get; set; } = string.Empty;

    [JsonPropertyName("status")]
    [Required(ErrorMessage = "Order status is required")]
    [StringLength(50, ErrorMessage = "Status cannot exceed 50 characters")]
    public string Status { get; set; } = string.Empty;

    [JsonPropertyName("customer_id")]
    public int? CustomerId { get; set; }

    [JsonPropertyName("customer_email")]
    [Required(ErrorMessage = "Customer email is required")]
    [EmailAddress(ErrorMessage = "Invalid email format")]
    [StringLength(255, ErrorMessage = "Customer email cannot exceed 255 characters")]
    public string CustomerEmail { get; set; } = string.Empty;

    [JsonPropertyName("customer_firstname")]
    [Required(ErrorMessage = "Customer first name is required")]
    [StringLength(255, ErrorMessage = "First name cannot exceed 255 characters")]
    public string CustomerFirstname { get; set; } = string.Empty;

    [JsonPropertyName("customer_lastname")]
    [Required(ErrorMessage = "Customer last name is required")]
    [StringLength(255, ErrorMessage = "Last name cannot exceed 255 characters")]
    public string CustomerLastname { get; set; } = string.Empty;

    [JsonPropertyName("billing_address")]
    [Required(ErrorMessage = "Billing address is required")]
    public Address BillingAddress { get; set; } = new();

    [JsonPropertyName("shipping_address")]
    public Address? ShippingAddress { get; set; }

    [JsonPropertyName("items")]
    [Required(ErrorMessage = "Order items are required")]
    [MinLength(1, ErrorMessage = "Order must contain at least one item")]
    public List<OrderItemQuery> Items { get; set; } = [];

    [JsonPropertyName("payment")]
    [Required(ErrorMessage = "Payment information is required")]
    public PaymentQuery Payment { get; set; } = new();

    [JsonPropertyName("total_qty_ordered")]
    [Range(0.01, double.MaxValue, ErrorMessage = "Total quantity ordered must be greater than 0")]
    public decimal TotalQtyOrdered { get; set; }

    [JsonPropertyName("grand_total")]
    [Range(0.01, double.MaxValue, ErrorMessage = "Grand total must be greater than 0")]
    public decimal GrandTotal { get; set; }

    [JsonPropertyName("base_grand_total")]
    [Range(0.01, double.MaxValue, ErrorMessage = "Base grand total must be greater than 0")]
    public decimal BaseGrandTotal { get; set; }

    [JsonPropertyName("created_at")]
    [Required(ErrorMessage = "Created at timestamp is required")]
    public DateTimeOffset? CreatedAt { get; set; }
}

public class OrderItemQuery
{
    [JsonPropertyName("item_id")]
    [Range(1, int.MaxValue, ErrorMessage = "Item ID must be greater than 0")]
    public int ItemId { get; set; }

    [JsonPropertyName("sku")]
    [Required(ErrorMessage = "SKU is required")]
    [StringLength(100, ErrorMessage = "SKU cannot exceed 100 characters")]
    public string Sku { get; set; } = string.Empty;

    [JsonPropertyName("name")]
    [Required(ErrorMessage = "Product name is required")]
    [StringLength(255, ErrorMessage = "Product name cannot exceed 255 characters")]
    public string Name { get; set; } = string.Empty;

    [JsonPropertyName("qty_ordered")]
    [Range(0.01, double.MaxValue, ErrorMessage = "Quantity ordered must be greater than 0")]
    public decimal QtyOrdered { get; set; }

    [JsonPropertyName("price")]
    [Range(0, double.MaxValue, ErrorMessage = "Price must be greater than or equal to 0")]
    public decimal Price { get; set; }

    [JsonPropertyName("base_price")]
    [Range(0, double.MaxValue, ErrorMessage = "Base price must be greater than or equal to 0")]
    public decimal BasePrice { get; set; }

    [JsonPropertyName("row_total")]
    [Range(0, double.MaxValue, ErrorMessage = "Row total must be greater than or equal to 0")]
    public decimal RowTotal { get; set; }

    [JsonPropertyName("base_row_total")]
    [Range(0, double.MaxValue, ErrorMessage = "Base row total must be greater than or equal to 0")]
    public decimal BaseRowTotal { get; set; }
}

public class Address
{
    [JsonPropertyName("firstname")]
    [StringLength(50, ErrorMessage = "First name cannot exceed 50 characters")]
    public string? Firstname { get; set; }

    [JsonPropertyName("lastname")]
    [StringLength(50, ErrorMessage = "Last name cannot exceed 50 characters")]
    public string? Lastname { get; set; }

    [JsonPropertyName("street")]
    public List<string>? Street { get; set; }

    [JsonPropertyName("city")]
    [StringLength(100, ErrorMessage = "City cannot exceed 100 characters")]
    public string? City { get; set; }

    [JsonPropertyName("region")]
    [StringLength(100, ErrorMessage = "Region cannot exceed 100 characters")]
    public string? Region { get; set; }

    [JsonPropertyName("postcode")]
    [StringLength(20, ErrorMessage = "Postcode cannot exceed 20 characters")]
    public string? Postcode { get; set; }

    [JsonPropertyName("country_id")]
    [StringLength(2, ErrorMessage = "Country ID should be 2 characters")]
    public string? CountryId { get; set; }

    [JsonPropertyName("telephone")]
    [StringLength(20, ErrorMessage = "Telephone cannot exceed 20 characters")]
    public string? Telephone { get; set; }
}

public class PaymentQuery
{
    [JsonPropertyName("method")]
    [Required(ErrorMessage = "Payment method is required")]
    [StringLength(50, ErrorMessage = "Payment method cannot exceed 50 characters")]
    public string Method { get; set; } = string.Empty;

    [JsonPropertyName("amount_ordered")]
    [Range(0, double.MaxValue, ErrorMessage = "Amount ordered must be greater than or equal to 0")]
    public decimal AmountOrdered { get; set; }

    [JsonPropertyName("base_amount_ordered")]
    [Range(0, double.MaxValue, ErrorMessage = "Base amount ordered must be greater than or equal to 0")]
    public decimal BaseAmountOrdered { get; set; }
} 