using Axon.Adapter.Api.Exceptions;
using Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Models;
using Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Queries;
using Axon.Contracts.Cart.Commands;
using Axon.Contracts.Cart.Events;
using MassTransit;

namespace Axon.Adapter.Api.MagentoIntegrationAdapterAPI.RequestHandlers;

public interface ICartCreatedRequestHandler
{
    Task<CartCreatedResponse> HandleAsync(CartCreatedQuery query, CancellationToken cancellationToken = default);
}

public class CartCreatedRequestHandler : ICartCreatedRequestHandler
{
    private readonly IRequestClient<RecordCartCreatedCommand> _requestClient;
    private readonly ILogger<CartCreatedRequestHandler> _logger;

    public CartCreatedRequestHandler(
        IRequestClient<RecordCartCreatedCommand> requestClient,
        ILogger<CartCreatedRequestHandler> logger)
    {
        _requestClient = requestClient;
        _logger = logger;
    }

    public async Task<CartCreatedResponse> HandleAsync(CartCreatedQuery query, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Processing cart created query for cart ID: {CartId}", query.CartId);

        try
        {
            var command = MapToCommand(query);

            _logger.LogDebug("Sending RecordCartCreatedCommand for cart: {CartId}", command.CartId);

            var response = await _requestClient.GetResponse<CartCreatedEvent, CartCreationFailedEvent>(command, cancellationToken);

            // Check if we got a failure response
            if (response.Is(out Response<CartCreationFailedEvent>? failureResponse))
            {
                _logger.LogWarning("Cart creation failed for cart ID: {CartId}. Reason: {Reason}",
                    failureResponse.Message.CartId, failureResponse.Message.Reason);

                if (failureResponse.Message.ErrorCode == "DUPLICATE_CART")
                {
                    throw new ConflictException($"Cart with ID '{query.CartId}' already exists.");
                }

                throw new InvalidOperationException($"Cart creation failed: {failureResponse.Message.Reason}");
            }

            // We got a success response
            if (response.Is(out Response<CartCreatedEvent>? successResponse))
            {
                _logger.LogInformation("Successfully processed cart creation for cart ID: {CartId}", successResponse.Message.CartId);

                return new CartCreatedResponse
                {
                    CartId = int.Parse(successResponse.Message.CartId),
                    StoreId = int.Parse(successResponse.Message.StoreId),
                    CustomerId = string.IsNullOrEmpty(successResponse.Message.CustomerId) ? null : int.Parse(successResponse.Message.CustomerId),
                    CustomerEmail = successResponse.Message.Customer.Email,
                    CustomerIsGuest = successResponse.Message.Customer.IsGuest,
                    CreatedAt = successResponse.Message.CreatedAt.ToString("yyyy-MM-dd'T'HH:mm:ss'Z'"),
                    UpdatedAt = successResponse.Message.UpdatedAt.ToString("yyyy-MM-dd'T'HH:mm:ss'Z'"),
                    Currency = successResponse.Message.Currency.BaseCurrencyCode,
                    ItemsCount = successResponse.Message.ItemsCount,
                    ItemsQty = (int)successResponse.Message.ItemsQty,
                    Items = MapItemsToResponse(successResponse.Message.Items),
                    Totals = new CartTotalsResponse
                    {
                        Subtotal = successResponse.Message.Totals.Subtotal,
                        GrandTotal = successResponse.Message.Totals.GrandTotal,
                        DiscountAmount = 0
                    },
                    IsNegotiableQuote = successResponse.Message.IsNegotiableQuote
                };
            }

            throw new InvalidOperationException("Unexpected response type from request");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing cart created query for cart ID: {CartId}", query.CartId);
            throw;
        }
    }

    private RecordCartCreatedCommand MapToCommand(CartCreatedQuery query)
    {
        return new RecordCartCreatedCommand
        {
            CartId = query.CartId.ToString(),
            CustomerId = query.CustomerId?.ToString(),
            StoreId = query.StoreId.ToString(),
            CreatedAt = string.IsNullOrEmpty(query.CreatedAt)
                ? DateTimeOffset.UtcNow
                : DateTimeOffset.Parse(query.CreatedAt),
            UpdatedAt = string.IsNullOrEmpty(query.UpdatedAt)
                ? DateTimeOffset.UtcNow
                : DateTimeOffset.Parse(query.UpdatedAt),
            Currency = new Contracts.Cart.Commands.CartCurrency
            {
                BaseCurrencyCode = query.Currency,
                QuoteCurrencyCode = query.Currency
            },
            Totals = new Contracts.Cart.Commands.CartTotals
            {
                GrandTotal = query.Totals?.GrandTotal ?? 0,
                BaseTaxAmount = 0,
                TaxAmount = 0,
                BaseSubtotal = query.Totals?.Subtotal ?? 0,
                Subtotal = query.Totals?.Subtotal ?? 0
            },
            IsActive = true,
            IsVirtual = false,
            IsNegotiableQuote = query.IsNegotiableQuote,
            IsMultiShipping = false,
            ItemsCount = query.ItemsCount,
            ItemsQty = query.ItemsQty,
            Items = MapItems(query.Items),
            Customer = MapCustomer(query),
            BillingAddress = null,
            ShippingAddress = null,
            PaymentMethod = null,
            ShippingMethod = null,
            IdempotencyKey = $"cart-{query.CartId}-created"
        };
    }

    private List<Contracts.Cart.Commands.CartItem> MapItems(List<CartItemQuery> items)
    {
        return items.Select(item => new Contracts.Cart.Commands.CartItem
        {
            ItemId = item.ItemId?.ToString() ?? string.Empty,
            Sku = item.Sku,
            Name = item.Name ?? string.Empty,
            Qty = item.Qty,
            Price = item.Price ?? 0,
            ProductType = item.ProductType,
            ProductOption = item.ProductOption
        }).ToList();
    }

    private Contracts.Cart.Commands.CartCustomer MapCustomer(CartCreatedQuery query)
    {
        return new Contracts.Cart.Commands.CartCustomer
        {
            Email = query.CustomerEmail,
            GroupId = null,
            IsGuest = query.CustomerIsGuest,
            FirstName = null,
            LastName = null
        };
    }

    private List<CartItemResponse> MapItemsToResponse(List<Contracts.Cart.Events.CartItem> items)
    {
        return items.Select(item => new CartItemResponse
        {
            ItemId = string.IsNullOrEmpty(item.ItemId) ? null : int.Parse(item.ItemId),
            Sku = item.Sku,
            Qty = (int)item.Qty,
            Name = item.Name,
            Price = item.Price,
            ProductType = item.ProductType ?? string.Empty,
            QuoteId = null,
            ProductOption = null
        }).ToList();
    }
}