using Axon.Adapter.Api.Exceptions;
using Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Models;
using Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Queries;
using Axon.Contracts.Cart.Commands;
using Axon.Contracts.Cart.Events;
using MassTransit;

namespace Axon.Adapter.Api.MagentoIntegrationAdapterAPI.RequestHandlers;

public interface ICartUpdatedRequestHandler
{
    Task<CartUpdatedResponse> HandleAsync(CartUpdatedQuery query, CancellationToken cancellationToken = default);
}

public class CartUpdatedRequestHandler : ICartUpdatedRequestHandler
{
    private readonly IRequestClient<RecordCartUpdatedCommand> _requestClient;
    private readonly ILogger<CartUpdatedRequestHandler> _logger;

    public CartUpdatedRequestHandler(
        IRequestClient<RecordCartUpdatedCommand> requestClient,
        ILogger<CartUpdatedRequestHandler> logger)
    {
        _requestClient = requestClient;
        _logger = logger;
    }

    public async Task<CartUpdatedResponse> HandleAsync(CartUpdatedQuery query, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Processing cart updated query for cart ID: {CartId}", query.CartId);

        try
        {
            var command = MapToCommand(query);

            _logger.LogDebug("Sending RecordCartUpdatedCommand for cart: {CartId}", command.CartId);

            var response = await _requestClient.GetResponse<CartUpdatedEvent, CartUpdateFailedEvent>(command, cancellationToken);

            // Check if we got a failure response
            if (response.Is(out Response<CartUpdateFailedEvent>? failureResponse))
            {
                _logger.LogWarning("Cart update failed for cart ID: {CartId}. Reason: {Reason}",
                    failureResponse.Message.CartId, failureResponse.Message.Reason);

                if (failureResponse.Message.ErrorCode == "NONEXISTENT_CART")
                {
                    throw new ConflictException($"Cart with ID '{query.CartId}' doesn't exist.");
                }

                throw new InvalidOperationException($"Cart update failed: {failureResponse.Message.Reason}");
            }

            // We got a success response
            if (response.Is(out Response<CartUpdatedEvent>? successResponse))
            {
                _logger.LogInformation("Successfully processed cart update for cart ID: {CartId}", successResponse.Message.CartId);

                return new CartUpdatedResponse
                {
                    CartId = int.Parse(successResponse.Message.CartId),
                    StoreId = int.Parse(successResponse.Message.StoreId),
                    UpdatedAt = successResponse.Message.UpdatedAt.ToString("yyyy-MM-dd'T'HH:mm:ss'Z'"),
                    Totals = new CartTotalsResponse
                    {
                        Subtotal = successResponse.Message.Totals.Subtotal,
                        GrandTotal = successResponse.Message.Totals.GrandTotal,
                        DiscountAmount = 0
                    },
                    ItemsCount = successResponse.Message.ItemsCount,
                    ItemsQty = (int)successResponse.Message.ItemsQty,
                    Items = MapItemsToResponse(successResponse.Message.Items),
                    IsNegotiableQuote = successResponse.Message.IsNegotiableQuote
                };
            }

            throw new InvalidOperationException("Unexpected response type from request");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing cart updated query for cart ID: {CartId}", query.CartId);
            throw;
        }
    }

    private RecordCartUpdatedCommand MapToCommand(CartUpdatedQuery query)
    {
        return new RecordCartUpdatedCommand
        {
            CartId = query.CartId.ToString(),
            StoreId = query.StoreId.ToString(),
            UpdatedAt = string.IsNullOrEmpty(query.UpdatedAt)
                ? DateTimeOffset.UtcNow
                : DateTimeOffset.Parse(query.UpdatedAt),
            IsNegotiableQuote = query.IsNegotiableQuote,
            IsMultiShipping = false,
            Items = MapItems(query.Items),
            BillingAddress = null,
            ShippingAddress = null,
            PaymentMethod = null,
            ShippingMethod = null,
            IdempotencyKey = $"cart-{query.CartId}-updated"
        };
    }

    private List<Contracts.Cart.Commands.CartItem> MapItems(List<CartItemQuery> items)
    {
        return items.Select(item => new Contracts.Cart.Commands.CartItem
        {
            ItemId = item.ItemId?.ToString() ?? string.Empty,
            Sku = item.Sku,
            Name = item.Name ?? string.Empty,
            Qty = item.Qty,
            Price = item.Price ?? 0,
            ProductType = item.ProductType,
            ProductOption = item.ProductOption
        }).ToList();
    }

    private List<CartItemResponse> MapItemsToResponse(List<Contracts.Cart.Events.CartItem> items)
    {
        return items.Select(item => new CartItemResponse
        {
            ItemId = string.IsNullOrEmpty(item.ItemId) ? null : int.Parse(item.ItemId),
            Sku = item.Sku,
            Qty = (int)item.Qty,
            Name = item.Name,
            Price = item.Price,
            ProductType = item.ProductType ?? string.Empty,
            QuoteId = null,
            ProductOption = null
        }).ToList();
    }
}