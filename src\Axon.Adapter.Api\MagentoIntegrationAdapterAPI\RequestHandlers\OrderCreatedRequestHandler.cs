using Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Queries;
using Axon.Contracts.Order.Commands;
using Axon.Contracts.Order.Events;
using MassTransit;
using Address = Axon.Contracts.Order.Commands.Address;
using OrderItem = Axon.Contracts.Order.Commands.OrderItem;
using Payment = Axon.Contracts.Order.Commands.Payment;
using ProductOption = Axon.Contracts.Order.Commands.ProductOption;
using ShippingMethod = Axon.Contracts.Order.Commands.ShippingMethod;

namespace Axon.Adapter.Api.MagentoIntegrationAdapterAPI.RequestHandlers;

public interface IOrderCreatedRequestHandler
{
    Task<Guid> HandleAsync(OrderCreatedQuery request, CancellationToken cancellationToken);
}

public class OrderCreatedRequestHandler : IOrderCreatedRequestHandler
{
    private readonly IRequestClient<RecordOrderCreatedCommand> _orderCreatedClient;
    private readonly ILogger<OrderCreatedRequestHandler> _logger;

    public OrderCreatedRequestHandler(IRequestClient<RecordOrderCreatedCommand> orderCreatedClient,
        ILogger<OrderCreatedRequestHandler> logger)
    {
        _logger = logger;
        _orderCreatedClient = orderCreatedClient;
    }

    public async Task<Guid> HandleAsync(OrderCreatedQuery request, CancellationToken cancellationToken)
    {
        var command = new RecordOrderCreatedCommand
        {
            IncrementId = request.IncrementId,
            State = request.State,
            Status = request.Status,
            CustomerId = request.CustomerId,
            CustomerEmail = request.CustomerEmail,
            CustomerFirstname = request.CustomerFirstname,
            CustomerLastname = request.CustomerLastname,
            StoreId = 1,
            Items = request.Items.Select(x => new OrderItem
            {
                ItemId = x.ItemId,
                Sku = x.Sku,
                Qty = x.QtyOrdered,
                Price = x.Price,
                BasePrice = x.BasePrice,
                RowTotal = x.RowTotal,
                BaseRowTotal = x.BaseRowTotal,
                Name = x.Name,
                ProductType = "simple",
                ProductOption = null
            }).ToList(),
            BillingAddress = new Address
            {
                Firstname = request.BillingAddress.Firstname ?? string.Empty,
                Lastname = request.BillingAddress.Lastname ?? string.Empty,
                Street = request.BillingAddress.Street ?? [],
                City = request.BillingAddress.City ?? string.Empty,
                Region = request.BillingAddress.Region,
                Postcode = request.BillingAddress.Postcode,
                CountryId = request.BillingAddress.CountryId ?? string.Empty,
                Telephone = request.BillingAddress.Telephone ?? string.Empty
            },
            ShippingAddress = request.ShippingAddress != null
                ? new Address
                {
                    Firstname = request.ShippingAddress.Firstname ?? string.Empty,
                    Lastname = request.ShippingAddress.Lastname ?? string.Empty,
                    Street = request.ShippingAddress.Street ?? [],
                    City = request.ShippingAddress.City ?? string.Empty,
                    Region = request.ShippingAddress.Region,
                    Postcode = request.ShippingAddress.Postcode,
                    CountryId = request.ShippingAddress.CountryId ?? string.Empty,
                    Telephone = request.ShippingAddress.Telephone ?? string.Empty
                }
                : null,
            Payment = new Payment
            {
                Method = request.Payment.Method,
                AmountOrdered = request.Payment.AmountOrdered,
                BaseAmountOrdered = request.Payment.BaseAmountOrdered
            },
            ShippingMethod = null,
            TotalQtyOrdered = request.TotalQtyOrdered,
            GrandTotal = request.GrandTotal,
            BaseGrandTotal = request.BaseGrandTotal,
            CreatedAt = request.CreatedAt
        };

        _logger.LogInformation(
            "Sending RecordOrderCreatedCommand with IncrementId {IncrementId} and CustomerEmail {CustomerEmail}", 
            request.IncrementId,
            command.CustomerEmail);
        
        var requestClientResponse = await _orderCreatedClient.GetResponse<OrderCreatedEvent>(command, cancellationToken);
        var orderCreatedEvent = requestClientResponse.Message;
        
        _logger.LogInformation("Received OrderCreatedEvent with IncrementId {IncrementId} and OrderId {OrderId}",
            request.IncrementId, orderCreatedEvent.OrderId);
        
        return orderCreatedEvent.OrderId;
    }
}