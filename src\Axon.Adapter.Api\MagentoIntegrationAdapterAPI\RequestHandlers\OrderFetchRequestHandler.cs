using Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Queries;
using Axon.Contracts.Order.Queries;
using MassTransit;

namespace Axon.Adapter.Api.MagentoIntegrationAdapterAPI.RequestHandlers;

public interface IOrderFetchRequestHandler
{
    Task<GetOrderByIdResponse?> HandleAsync(OrderFetchQuery request, CancellationToken cancellationToken);
}

public class OrderFetchRequestHandler : IOrderFetchRequestHandler
{
    private readonly IRequestClient<GetOrderByIdQuery> _getOrderClient;
    private readonly ILogger<OrderFetchRequestHandler> _logger;

    public OrderFetchRequestHandler(IRequestClient<GetOrderByIdQuery> getOrderClient,
        ILogger<OrderFetchRequestHandler> logger)
    {
        _logger = logger;
        _getOrderClient = getOrderClient;
    }

    public async Task<GetOrderByIdResponse?> HandleAsync(OrderFetchQuery request, CancellationToken cancellationToken)
    {
        var query = new GetOrderByIdQuery
        {
            OrderId = request.OrderId
        };

        _logger.LogInformation("Sending GetOrderByIdQuery with OrderId {OrderId}", request.OrderId);

        try
        {
            var response = await _getOrderClient.GetResponse<GetOrderByIdResponse>(query, cancellationToken);
            var orderResponse = response.Message;

            _logger.LogInformation("Received order response with OrderId {OrderId} and IncrementId {IncrementId}",
                orderResponse.OrderId, orderResponse.IncrementId);

            return orderResponse;
        }
        catch (RequestFaultException ex)
        {
            _logger.LogWarning(ex, "Order with OrderId {OrderId} not found", request.OrderId);
            return null;
        }
    }
} 