using AspNetCore.Authentication.ApiKey;
using Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Authentication;
using Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Clients;
using Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Consumers;
using Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Options;
using Axon.Adapter.Api.MagentoIntegrationAdapterAPI.RequestHandlers;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Clients;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Handlers;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Options;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Services;
using Axon.Contracts.Cart.Commands;
using Axon.Contracts.Order.Commands;
using Axon.Contracts.Order.Queries;
using Axon.Core.MassTransit.Options;
using Axon.Core.MassTransit.ServiceCollectionExtensions;
using Axon.Core.ServiceCollectionExtensions;
using Prometheus;
using Serilog;
using System.Reflection;
using System.Runtime.Loader;

string[] apiCorsOrigins = ["http://localhost:7500", "http://localhost:7501"];

var host = Host.CreateDefaultBuilder(args)
    .ConfigureAxonDefaults()
    .ConfigureWebHostDefaults(webBuilder =>
    {
        webBuilder.ConfigureServices((context, services) =>
        {
            services.AddAxonCoreServices(apiCorsOrigins);

            var environment = context.HostingEnvironment.EnvironmentName;
            var isAwsEnvironment = environment.StartsWith("AWS-", StringComparison.OrdinalIgnoreCase);

            if (!isAwsEnvironment)
            {
                // Only validate RabbitMQ options in Development environment
                services.AddOptions<RabbitMqOptions>()
                    .Bind(context.Configuration.GetSection(RabbitMqOptions.Key))
                    .ValidateDataAnnotations()
                    .ValidateOnStart();
            }
            else
            {
                // Only validate AWS options in AWS environments
                services.AddOptions<AwsOptions>()
                    .Bind(context.Configuration.GetSection(AwsOptions.Key))
                    .ValidateDataAnnotations()
                    .ValidateOnStart();
            }

            services.AddOptions<SapApiOptions>()
                .Bind(context.Configuration.GetSection(SapApiOptions.Key))
                .ValidateDataAnnotations()
                .ValidateOnStart();

            services.AddOptions<MagentoApiOptions>()
                .Bind(context.Configuration.GetSection(MagentoApiOptions.Key))
                .ValidateDataAnnotations()
                .ValidateOnStart();

            services.AddScoped<ISapApiClient, SapApiClient>();

            services.AddScoped<ISapSalesOrderCreatedService, SapSalesOrderCreatedService>();

            services.AddHttpClient<MagentoApiClient>();

            // Register the order status update handler
            services.AddScoped<IMagentoOrderStatusUpdateHandler, MagentoOrderStatusUpdateHandler>();

            services.AddScoped<OrderCreatedEventHandler>();
            services.AddScoped<IOrderCreatedEventHandler, OrderCreatedEventHandler>();

            services.AddMassTransitWithBroker(x =>
            {
                // Only register SAP consumers if integration is enabled
                var sapOptions = new SapApiOptions();
                context.Configuration.GetSection(SapApiOptions.Key).Bind(sapOptions);

                if (sapOptions.IntegrationEnabled)
                {
                    x.AddConsumer<Axon.Adapter.Api.SAPIntegrationAdapterAPI.Consumers.Outbound.OrderCreatedEventConsumer>();
                }

                x.AddConsumer<OrderAcknowledgedByErpEventConsumer>();
                x.AddRequestClient<RecordOrderCreatedCommand>(TimeSpan.FromSeconds(90));
                x.AddRequestClient<GetOrderByIdQuery>(TimeSpan.FromSeconds(30));
                x.AddRequestClient<RecordCartCreatedCommand>(TimeSpan.FromSeconds(90));
                x.AddRequestClient<RecordCartUpdatedCommand>(TimeSpan.FromSeconds(90));
            });

            services.AddScoped<IOrderCreatedRequestHandler, OrderCreatedRequestHandler>();
            services.AddScoped<IOrderFetchRequestHandler, OrderFetchRequestHandler>();
            services.AddScoped<ICartCreatedRequestHandler, CartCreatedRequestHandler>();
            services.AddScoped<ICartUpdatedRequestHandler, CartUpdatedRequestHandler>();

            // Configure API Key authentication
            var magentoOptions = context.Configuration.GetSection(MagentoApiOptions.Key).Get<MagentoApiOptions>();

            // Log the configuration values
            var logger = LoggerFactory.Create(builder => builder.AddConsole()).CreateLogger<Program>();
            logger.LogInformation("Magento API Configuration - RequireInboundAuthentication: {RequireAuth}, HasInboundApiKey: {HasKey}, BaseAddress: {BaseAddress}",
                magentoOptions?.RequireInboundAuthentication ?? false,
                !string.IsNullOrWhiteSpace(magentoOptions?.InboundApiKey),
                magentoOptions?.BaseAddress ?? "not configured");

            if (magentoOptions?.RequireInboundAuthentication == true)
            {
                logger.LogInformation("Configuring API Key authentication for Magento Integration Adapter");

                services.AddAuthentication(ApiKeyDefaults.AuthenticationScheme)
                    .AddApiKeyInHeaderOrQueryParams<MagentoApiKeyProvider>(options =>
                    {
                        options.Realm = "Magento Integration API";
                        options.KeyName = "X-API-Key";  // Standard API key header
                    });

                services.AddScoped<IApiKeyProvider, MagentoApiKeyProvider>();
            }
            else
            {
                logger.LogWarning("API Key authentication is NOT configured for Magento Integration Adapter");

                // Add a no-op authentication scheme to satisfy [Authorize] attributes
                services.AddAuthentication("NoAuth")
                    .AddScheme<NoAuthenticationSchemeOptions, NoAuthenticationHandler>("NoAuth", null);
            }

            services.AddAuthorization();

            services.AddApiVersioning(options => { options.DefaultApiVersion = new Asp.Versioning.ApiVersion(0, 1); });
        });

        webBuilder.Configure((context, app) =>
        {
            var magentoOptions = context.Configuration.GetSection(MagentoApiOptions.Key).Get<MagentoApiOptions>();

            if (apiCorsOrigins.Length > 0)
            {
                app.UseCors();
            }

            app.UseSerilogRequestLogging();

            // Correlation ID middleware - must be early in the pipeline
            app.Use(async (ctx, next) =>
            {
                const string CorrelationIdHeader = "X-Correlation-ID";
                if (!ctx.Request.Headers.TryGetValue(CorrelationIdHeader, out var correlationId))
                {
                    correlationId = Guid.NewGuid().ToString();
                    ctx.Request.Headers[CorrelationIdHeader] = correlationId;
                }

                ctx.Response.Headers[CorrelationIdHeader] = correlationId;

                using (Serilog.Context.LogContext.PushProperty("CorrelationId", correlationId.ToString()))
                {
                    await next();
                }
            });

            app.UseRouting();
            app.UseHttpMetrics();
            app.UseMiddleware<Axon.Core.Middleware.ResponseEnvelopeMiddleware>();
            app.UseMiddleware<Axon.Core.Middleware.ExceptionHandlingMiddleware>();

            // Always use authentication middleware (even with NoAuth scheme)
            app.UseAuthentication();
            app.UseAuthorization();

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
                endpoints.MapHealthChecks("/healthz");
                endpoints.MapMetrics();
            });
        });
    })
    .Build();

await host.RunAsync();