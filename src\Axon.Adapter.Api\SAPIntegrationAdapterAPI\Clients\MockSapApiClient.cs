using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Models.Shared;

namespace Axon.Adapter.Api.SAPIntegrationAdapterAPI.Clients;

/// <summary>
/// Mock SAP API client for non-Windows platforms where SAP NCo is not available
/// </summary>
public class MockSapApiClient : ISapApiClient
{
    private readonly ILogger<MockSapApiClient> _logger;

    public MockSapApiClient(ILogger<MockSapApiClient> logger)
    {
        _logger = logger;
    }

    public Task<SapRfcResult> CallFunctionAsync(string functionName, object parameters, CancellationToken cancellationToken = default)
    {
        _logger.LogWarning("SAP integration is disabled on this platform. Mock response returned for function: {FunctionName}", functionName);
        
        // Return a mock successful response
        var mockResult = new SapRfcResult(null)
        {
            OrderNumber = $"MOCK{DateTimeOffset.UtcNow:yyyyMMddHHmmss}",
            Messages = new List<SapRfcResultItem>
            {
                new SapRfcResultItem
                {
                    Type = "S",
                    Message = "Mock SAP integration - order would be created in real SAP system",
                    Id = "MOCK",
                    Number = "001"
                }
            }
        };

        return Task.FromResult(mockResult);
    }
}
