using System;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading;
using System.Threading.Tasks;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Options;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Queries;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Models;
using Microsoft.Extensions.Options;
using SAP.Middleware.Connector;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Extensions;

namespace Axon.Adapter.Api.SAPIntegrationAdapterAPI.Clients;

public interface ISapApiClient
{
    Task<RfcResult> CallFunctionAsync(string functionName, object parameters, CancellationToken cancellationToken = default);
}

public class SapApiClient : ISapApiClient
{
    private readonly RfcDestination _sapDestination;
    private readonly RfcRepository _sapRepository;

    public SapApiClient(IOptions<SapApiOptions> options)
    {
        var sapOptions = options.Value;
        
        var destinationConfig = new RfcConfigParameters
        {
            { RfcConfigParameters.Name, "SAP_DESTINATION" },
            { RfcConfigParameters.AppServerHost, sapOptions.BaseAddress.Replace("http://", "").Replace("https://", "") },
            { RfcConfigParameters.SystemNumber, sapOptions.SystemNumber },
            { RfcConfigParameters.Client, sapOptions.Client },
            { RfcConfigParameters.User, sapOptions.User },
            { RfcConfigParameters.Password, sapOptions.Password },
            { RfcConfigParameters.Language, sapOptions.Language },
            { RfcConfigParameters.PoolSize, sapOptions.PoolSize.ToString() }
        };

        _sapDestination = RfcDestinationManager.GetDestination(destinationConfig);
        _sapRepository = _sapDestination.Repository;
    }

    public async Task<RfcResult> CallFunctionAsync(string functionName, object parameters, CancellationToken cancellationToken = default)
    {
        return await Task.Run(() =>
        {
            var function = _sapRepository.CreateFunction(functionName);
            function.SetAllParameters(parameters);
            function.Invoke(_sapDestination);
            return RfcResult.Ok(function);
        }, cancellationToken);
    }
}