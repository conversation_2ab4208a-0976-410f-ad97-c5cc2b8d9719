using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Models.Events;
using MassTransit;

namespace Axon.Adapter.Api.SAPIntegrationAdapterAPI.Consumers.Inbound;

/// <summary>
/// Consumer for SAP customer changed events
/// </summary>
public class SapCustomerChangedEventConsumer : IConsumer<SapCustomerChangedEvent>
{
    private readonly ILogger<SapCustomerChangedEventConsumer> _logger;

    public SapCustomerChangedEventConsumer(ILogger<SapCustomerChangedEventConsumer> logger)
    {
        _logger = logger;
    }

    public async Task Consume(ConsumeContext<SapCustomerChangedEvent> context)
    {
        var sapEvent = context.Message;
        
        _logger.LogInformation("Consuming SAP customer change for customer {CustomerNumber} with change type {ChangeType}", 
            sapEvent.CustomerNumber, sapEvent.ChangeType);

        // TODO: Implement business logic for customer change
        // This could include:
        // - Updating local customer data
        // - Triggering credit checks
        // - Updating contact information
        // - Notifying sales teams

        _logger.LogInformation("Consumed SAP customer change for customer {CustomerNumber} with change type {ChangeType}", 
            sapEvent.CustomerNumber, sapEvent.ChangeType);
        
        await Task.CompletedTask;
    }
}
