using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Models.Events;
using MassTransit;

namespace Axon.Adapter.Api.SAPIntegrationAdapterAPI.Consumers.Inbound;

/// <summary>
/// Consumer for SAP order status updated events
/// </summary>
public class SapOrderStatusUpdatedEventConsumer : IConsumer<SapOrderStatusUpdatedEvent>
{
    private readonly ILogger<SapOrderStatusUpdatedEventConsumer> _logger;

    public SapOrderStatusUpdatedEventConsumer(ILogger<SapOrderStatusUpdatedEventConsumer> logger)
    {
        _logger = logger;
    }

    public async Task Consume(ConsumeContext<SapOrderStatusUpdatedEvent> context)
    {
        var sapEvent = context.Message;
        
        _logger.LogInformation("Consuming SAP order status update for order {OrderNumber} with status {Status}", 
            sapEvent.OrderNumber, sapEvent.Status);

        // TODO: Implement business logic for order status update
        // This could include:
        // - Updating local order status
        // - Triggering downstream processes
        // - Sending notifications
        // - Updating external systems

        _logger.LogInformation("Consumed SAP order status update for order {OrderNumber} with status {Status}", 
            sapEvent.OrderNumber, sapEvent.Status);
    }
}

/// <summary>
/// Consumer for SAP material updated events
/// </summary>
public class SapMaterialUpdatedEventConsumer : IConsumer<SapMaterialUpdatedEvent>
{
    private readonly ILogger<SapMaterialUpdatedEventConsumer> _logger;

    public SapMaterialUpdatedEventConsumer(ILogger<SapMaterialUpdatedEventConsumer> logger)
    {
        _logger = logger;
    }

    public async Task Consume(ConsumeContext<SapMaterialUpdatedEvent> context)
    {
        var sapEvent = context.Message;
        
        _logger.LogInformation("Consuming SAP material update for material {MaterialNumber} with update type {UpdateType}", 
            sapEvent.MaterialNumber, sapEvent.UpdateType);

        // TODO: Implement business logic for material update
        // This could include:
        // - Updating local material data
        // - Triggering inventory updates
        // - Updating pricing information
        // - Notifying relevant systems

        _logger.LogInformation("Consumed SAP material update for material {MaterialNumber} with update type {UpdateType}", 
            sapEvent.MaterialNumber, sapEvent.UpdateType);
    }
}

/// <summary>
/// Consumer for SAP customer changed events
/// </summary>
public class SapCustomerChangedEventConsumer : IConsumer<SapCustomerChangedEvent>
{
    private readonly ILogger<SapCustomerChangedEventConsumer> _logger;

    public SapCustomerChangedEventConsumer(ILogger<SapCustomerChangedEventConsumer> logger)
    {
        _logger = logger;
    }

    public async Task Consume(ConsumeContext<SapCustomerChangedEvent> context)
    {
        var sapEvent = context.Message;
        
        _logger.LogInformation("Consuming SAP customer change for customer {CustomerNumber} with change type {ChangeType}", 
            sapEvent.CustomerNumber, sapEvent.ChangeType);

        // TODO: Implement business logic for customer change
        // This could include:
        // - Updating local customer data
        // - Triggering credit checks
        // - Updating contact information
        // - Notifying sales teams

        _logger.LogInformation("Consumed SAP customer change for customer {CustomerNumber} with change type {ChangeType}", 
            sapEvent.CustomerNumber, sapEvent.ChangeType);
    }
}

/// <summary>
/// Consumer for SAP test events (POC validation)
/// </summary>
public class SapTestEventConsumer : IConsumer<SapTestEvent>
{
    private readonly ILogger<SapTestEventConsumer> _logger;

    public SapTestEventConsumer(ILogger<SapTestEventConsumer> logger)
    {
        _logger = logger;
    }

    public async Task Consume(ConsumeContext<SapTestEvent> context)
    {
        var sapEvent = context.Message;
        
        _logger.LogInformation("Consuming SAP test event: {Message}", sapEvent.Message);

        // TODO: Implement test event processing
        // This could include:
        // - Logging the test event
        // - Validating the RFC connection
        // - Testing the event flow
        // - Sending test notifications

        _logger.LogInformation("Consumed SAP test event: {Message}", sapEvent.Message);
    }
} 