using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Models.Events;
using MassTransit;

namespace Axon.Adapter.Api.SAPIntegrationAdapterAPI.Consumers.Inbound;

/// <summary>
/// Consumer for SAP material updated events
/// </summary>
public class SapMaterialUpdatedEventConsumer : IConsumer<SapMaterialUpdatedEvent>
{
    private readonly ILogger<SapMaterialUpdatedEventConsumer> _logger;

    public SapMaterialUpdatedEventConsumer(ILogger<SapMaterialUpdatedEventConsumer> logger)
    {
        _logger = logger;
    }

    public async Task Consume(ConsumeContext<SapMaterialUpdatedEvent> context)
    {
        var sapEvent = context.Message;
        
        _logger.LogInformation("Consuming SAP material update for material {MaterialNumber} with update type {UpdateType}", 
            sapEvent.MaterialNumber, sapEvent.UpdateType);

        // TODO: Implement business logic for material update
        // This could include:
        // - Updating local material data
        // - Triggering inventory updates
        // - Updating pricing information
        // - Notifying relevant systems

        _logger.LogInformation("Consumed SAP material update for material {MaterialNumber} with update type {UpdateType}", 
            sapEvent.MaterialNumber, sapEvent.UpdateType);
        
        await Task.CompletedTask;
    }
}
