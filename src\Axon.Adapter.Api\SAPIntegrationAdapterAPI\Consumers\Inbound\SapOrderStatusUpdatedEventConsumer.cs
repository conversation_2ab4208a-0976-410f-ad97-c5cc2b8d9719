using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Models.Events;
using MassTransit;

namespace Axon.Adapter.Api.SAPIntegrationAdapterAPI.Consumers.Inbound;

/// <summary>
/// Consumer for SAP order status updated events
/// </summary>
public class SapOrderStatusUpdatedEventConsumer : IConsumer<SapOrderStatusUpdatedEvent>
{
    private readonly ILogger<SapOrderStatusUpdatedEventConsumer> _logger;

    public SapOrderStatusUpdatedEventConsumer(ILogger<SapOrderStatusUpdatedEventConsumer> logger)
    {
        _logger = logger;
    }

    public async Task Consume(ConsumeContext<SapOrderStatusUpdatedEvent> context)
    {
        var sapEvent = context.Message;
        
        _logger.LogInformation("Consuming SAP order status update for order {OrderNumber} with status {Status}", 
            sapEvent.OrderNumber, sapEvent.Status);

        // TODO: Implement business logic for order status update
        // This could include:
        // - Updating local order status
        // - Triggering downstream processes
        // - Sending notifications
        // - Updating external systems

        _logger.LogInformation("Consumed SAP order status update for order {OrderNumber} with status {Status}", 
            sapEvent.OrderNumber, sapEvent.Status);
        
        await Task.CompletedTask;
    }
}
