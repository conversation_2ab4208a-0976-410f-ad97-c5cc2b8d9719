using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Models.Events;
using MassTransit;

namespace Axon.Adapter.Api.SAPIntegrationAdapterAPI.Consumers.Inbound;

/// <summary>
/// Consumer for SAP test events (POC validation)
/// </summary>
public class SapTestEventConsumer : IConsumer<SapTestEvent>
{
    private readonly ILogger<SapTestEventConsumer> _logger;

    public SapTestEventConsumer(ILogger<SapTestEventConsumer> logger)
    {
        _logger = logger;
    }

    public async Task Consume(ConsumeContext<SapTestEvent> context)
    {
        var sapEvent = context.Message;
        
        _logger.LogInformation("Consuming SAP test event: {Message}", sapEvent.Message);

        // TODO: Implement test event processing
        // This could include:
        // - Logging the test event
        // - Validating the RFC connection
        // - Testing the event flow
        // - Sending test notifications

        _logger.LogInformation("Consumed SAP test event: {Message}", sapEvent.Message);
        
        await Task.CompletedTask;
    }
}
