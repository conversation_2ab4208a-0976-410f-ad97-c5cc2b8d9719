using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Handlers;
using Axon.Contracts.Order.Events;
using MassTransit;

namespace Axon.Adapter.Api.SAPIntegrationAdapterAPI.Consumers
{
    public class OrderCreatedEventConsumer(IOrderCreatedEventHandler handler, ILogger<OrderCreatedEventConsumer> logger)
        : IConsumer<OrderCreatedEvent>
    {
        public async Task Consume(ConsumeContext<OrderCreatedEvent> context)
        {
            var incomingOrderCreatedEvent = context.Message;
            logger.LogInformation("Consuming OrderCreatedEvent for IncrementId {IncrementId}, CustomerEmail {CustomerEmail}", incomingOrderCreatedEvent.IncrementId, incomingOrderCreatedEvent.CustomerEmail);

            var sapSalesOrderCreatedEvent = await handler.HandleAsync(incomingOrderCreatedEvent, context.CancellationToken);
            
            logger.LogInformation("Publishing SapSalesOrderCreatedEvent for OrderNumber {OrderNumber}", sapSalesOrderCreatedEvent.OrderNumber);
            await context.Publish(sapSalesOrderCreatedEvent, context.CancellationToken);
            logger.LogInformation("Published SapSalesOrderCreatedEvent for OrderNumber {OrderNumber}", sapSalesOrderCreatedEvent.OrderNumber);
        }
    }
} 