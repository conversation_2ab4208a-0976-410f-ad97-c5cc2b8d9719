using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Native;
using Microsoft.AspNetCore.Mvc;
using System.Runtime.InteropServices;

namespace Axon.Adapter.Api.SAPIntegrationAdapterAPI.Controllers;

/// <summary>
/// Test controller for SAP NW RFC SDK 7.50 native integration
/// </summary>
[ApiController]
[Route("api/v1.0/sap-integration-adapter/native-rfc-test")]
public class NativeRfcTestController : ControllerBase
{
    private readonly ILogger<NativeRfcTestController> _logger;

    public NativeRfcTestController(ILogger<NativeRfcTestController> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Tests SAP NW RFC SDK 7.50 native connection
    /// </summary>
    [HttpGet("connection")]
    public async Task<IActionResult> TestConnection([FromServices] NativeRfcClient nativeRfcClient)
    {
        try
        {
            _logger.LogInformation("Starting SAP NW RFC SDK 7.50 native connection test");

            var result = await nativeRfcClient.TestConnectionAsync();

            var response = new
            {
                success = result.OverallSuccess,
                summary = result.GetSummary(),
                details = new
                {
                    libraryLoad = result.LibraryLoadSuccess,
                    connection = result.ConnectionSuccess,
                    ping = result.PingSuccess,
                    connectionAttributes = result.ConnectionAttributes != null ? new
                    {
                        systemId = result.ConnectionAttributes?.SysId,
                        client = result.ConnectionAttributes?.Client,
                        user = result.ConnectionAttributes?.User,
                        host = result.ConnectionAttributes?.Host,
                        systemNumber = result.ConnectionAttributes?.SysNumber,
                        language = result.ConnectionAttributes?.Language,
                        release = result.ConnectionAttributes?.Rel,
                        kernelRelease = result.ConnectionAttributes?.KernelRel
                    } : null
                },
                error = result.ErrorMessage,
                platform = new
                {
                    os = Environment.OSVersion.Platform.ToString(),
                    architecture = RuntimeInformation.ProcessArchitecture.ToString(),
                    framework = RuntimeInformation.FrameworkDescription,
                    runtimeIdentifier = RuntimeInformation.RuntimeIdentifier
                }
            };

            if (result.OverallSuccess)
            {
                return Ok(response);
            }
            else
            {
                return StatusCode(500, response);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unhandled exception in native RFC test");
            
            return StatusCode(500, new
            {
                success = false,
                summary = "❌ Unhandled exception during test",
                error = ex.Message,
                platform = new
                {
                    os = Environment.OSVersion.Platform.ToString(),
                    architecture = RuntimeInformation.ProcessArchitecture.ToString(),
                    framework = RuntimeInformation.FrameworkDescription,
                    runtimeIdentifier = RuntimeInformation.RuntimeIdentifier
                }
            });
        }
    }

    /// <summary>
    /// Tests if SAP NW RFC SDK library can be loaded (without connecting)
    /// </summary>
    [HttpGet("library-load")]
    public async Task<IActionResult> TestLibraryLoad()
    {
        try
        {
            _logger.LogInformation("Testing SAP NW RFC SDK 7.50 library load");

            var result = await Task.Run(() =>
            {
                try
                {
                    // Try to load the library by calling a simple function
                    var connectionParams = new SapNwRfcSdk.RfcConnectionParameter[]
                    {
                        new() { Name = "ASHOST", Value = "dummy" },
                        new() { Name = "SYSNR", Value = "00" },
                        new() { Name = "CLIENT", Value = "100" },
                        new() { Name = "USER", Value = "dummy" },
                        new() { Name = "PASSWD", Value = "dummy" },
                        new() { Name = "LANG", Value = "EN" }
                    };

                    var rc = SapNwRfcSdk.RfcOpenConnection(connectionParams, (uint)connectionParams.Length, out var handle, out var errorInfo);

                    // Clean up if connection was opened
                    if (handle != IntPtr.Zero)
                    {
                        SapNwRfcSdk.RfcCloseConnection(handle, out _);
                    }

                    return (object)new
                    {
                        success = true,
                        message = "✅ SAP NW RFC SDK library loaded successfully",
                        attemptResult = rc.ToString(),
                        platform = new
                        {
                            os = Environment.OSVersion.Platform.ToString(),
                            architecture = RuntimeInformation.ProcessArchitecture.ToString(),
                            framework = RuntimeInformation.FrameworkDescription,
                            runtimeIdentifier = RuntimeInformation.RuntimeIdentifier
                        }
                    };
                }
                catch (DllNotFoundException ex)
                {
                    return (object)new
                    {
                        success = false,
                        message = "❌ SAP NW RFC SDK library not found",
                        error = ex.Message,
                        platform = new
                        {
                            os = Environment.OSVersion.Platform.ToString(),
                            architecture = RuntimeInformation.ProcessArchitecture.ToString(),
                            framework = RuntimeInformation.FrameworkDescription,
                            runtimeIdentifier = RuntimeInformation.RuntimeIdentifier
                        }
                    };
                }
                catch (Exception ex)
                {
                    return (object)new
                    {
                        success = false,
                        message = "❌ Error loading SAP NW RFC SDK library",
                        error = ex.Message,
                        platform = new
                        {
                            os = Environment.OSVersion.Platform.ToString(),
                            architecture = RuntimeInformation.ProcessArchitecture.ToString(),
                            framework = RuntimeInformation.FrameworkDescription,
                            runtimeIdentifier = RuntimeInformation.RuntimeIdentifier
                        }
                    };
                }
            });

            dynamic dynamicResult = result;
            if (dynamicResult.success)
            {
                return Ok(result);
            }
            else
            {
                return StatusCode(500, result);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unhandled exception in library load test");
            
            return StatusCode(500, new
            {
                success = false,
                message = "❌ Unhandled exception during library load test",
                error = ex.Message
            });
        }
    }
}
