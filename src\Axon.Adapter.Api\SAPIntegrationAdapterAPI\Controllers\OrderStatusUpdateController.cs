using Asp.Versioning;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Models;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Queries;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.RequestHandlers;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Axon.Adapter.Api.SAPIntegrationAdapterAPI.Controllers;

[ApiController]
[ApiVersion("1.0")]
[Route("api/v{version:apiVersion}/sap-integration-adapter")]
[Authorize]
public class OrderStatusUpdateController : ControllerBase
{
    private readonly ILogger<OrderStatusUpdateController> _logger;
    private readonly IOrderStatusUpdatedRequestHandler _orderStatusUpdatedRequestHandler;

    public OrderStatusUpdateController(
        IOrderStatusUpdatedRequestHandler orderStatusUpdatedRequestHandler,
        ILogger<OrderStatusUpdateController> logger)
    {
        _logger = logger;
        _orderStatusUpdatedRequestHandler = orderStatusUpdatedRequestHandler;
    }

    [HttpPost("order-status-updated")]
    public async Task<ActionResult<OrderStatusUpdatedResponse>> OrderStatusUpdated(
        [FromBody] OrderStatusUpdatedQuery request,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation(
            "Processing order status update for order number {OrderNumber}", 
            request.OrderNumber);

        var success = await _orderStatusUpdatedRequestHandler.HandleAsync(request, cancellationToken);

        _logger.LogInformation(
            "Order status update completed successfully for order number {OrderNumber}", 
            request.OrderNumber);

        return Accepted(new OrderStatusUpdatedResponse(success));
    }
}