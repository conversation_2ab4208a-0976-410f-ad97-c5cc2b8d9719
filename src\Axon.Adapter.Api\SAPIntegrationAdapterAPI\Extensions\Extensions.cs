using System.Reflection;
using System.Text.Json.Serialization;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Clients;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Models;
using SAP.Middleware.Connector;

namespace Axon.Adapter.Api.SAPIntegrationAdapterAPI.Extensions
{
    public static class RfcExtensions
    {
        public static void SetAllParameters(this IRfcFunction function, object obj)
        {
            var rfcParams = obj.ToRfcParameters();
            foreach (var param in rfcParams)
            {
                if (param.Value is Action<IRfcFunction> tableSetter)
                {
                    tableSetter(function);
                }
                else if (param.Value is Action<IRfcFunction, string> structureSetter)
                {
                    structureSetter(function, param.Key);
                }
                else
                {
                    function.SetValue(param.Key, param.Value);
                }
            }
        }
        
        public static RfcParameters ToRfcParameters(this object obj)
        {
            var parameters = new RfcParameters();
            if (obj == null) return parameters;

            var type = obj.GetType();
            foreach (var prop in type.GetProperties(BindingFlags.Public | BindingFlags.Instance))
            {
                var jsonAttr = prop.GetCustomAttribute<JsonPropertyNameAttribute>();
                var key = jsonAttr?.Name ?? prop.Name;
                var value = prop.GetValue(obj);

                if (value == null)
                    continue;

                // Handle collections (tables)
                if (value is System.Collections.IEnumerable enumerable && !(value is string))
                {
                    // Add an action to populate the SAP table parameter
                    parameters[key] = (Action<IRfcFunction>)(function =>
                    {
                        var table = function.GetTable(key);
                        foreach (var item in enumerable)
                        {
                            table.Append();
                            foreach (var itemProp in item.GetType().GetProperties(BindingFlags.Public | BindingFlags.Instance))
                            {
                                var itemJsonAttr = itemProp.GetCustomAttribute<JsonPropertyNameAttribute>();
                                var itemKey = itemJsonAttr?.Name ?? itemProp.Name;
                                var itemValue = itemProp.GetValue(item);
                                if (itemValue != null)
                                {
                                    table.SetValue(itemKey, itemValue);
                                }
                            }
                        }
                    });
                }
                // Handle nested objects (structures)
                else if (value.GetType().IsClass && value.GetType() != typeof(string))
                {
                    // Add an action to populate the SAP structure parameter
                    parameters[key] = (Action<IRfcFunction, string>)((function, paramName) =>
                    {
                        var structure = function.GetStructure(paramName);
                        foreach (var itemProp in value.GetType().GetProperties(BindingFlags.Public | BindingFlags.Instance))
                        {
                            var itemJsonAttr = itemProp.GetCustomAttribute<JsonPropertyNameAttribute>();
                            var itemKey = itemJsonAttr?.Name ?? itemProp.Name;
                            var itemValue = itemProp.GetValue(value);
                            if (itemValue != null)
                            {
                                structure.SetValue(itemKey, itemValue);
                            }
                        }
                    });
                }
                // Handle scalars
                else
                {
                    parameters[key] = value;
                }
            }
            return parameters;
        }

        /// <summary>
        /// Converts an IRfcFunction to a list of RfcResultItem objects
        /// </summary>
        /// <param name="function">The RFC function to convert</param>
        /// <param name="tableName">The name of the results table (default: "RESULTS")</param>
        /// <returns>List of RfcResultItem objects</returns>
        public static List<RfcResultItem> ToRfcResults(this IRfcFunction? function, string tableName = "RESULTS")
        {
            var results = new List<RfcResultItem>();
            
            if (function == null) return results;

            try
            {
                var table = function.GetTable(tableName);
                if (table == null) return results;

                foreach (IRfcStructure row in table)
                {
                    results.Add(new RfcResultItem
                    {
                        Type = row.GetValue("TYPE")?.ToString(),
                        Id = row.GetValue("ID")?.ToString(),
                        Number = row.GetValue("NUMBER")?.ToString(),
                        Message = row.GetValue("MESSAGE")?.ToString(),
                        System = row.GetValue("SYSTEM")?.ToString(),
                        LogNo = row.GetValue("LOG_NO")?.ToString(),
                        LogMsgNo = row.GetValue("LOG_MSG_NO")?.ToString(),
                        MessageV1 = row.GetValue("MESSAGE_V1")?.ToString(),
                        MessageV2 = row.GetValue("MESSAGE_V2")?.ToString(),
                        MessageV3 = row.GetValue("MESSAGE_V3")?.ToString(),
                        MessageV4 = row.GetValue("MESSAGE_V4")?.ToString(),
                        Parameter = row.GetValue("PARAMETER")?.ToString(),
                        Row = row.GetValue("ROW")?.ToString() != null ? int.Parse(row.GetValue("ROW").ToString()) : null,
                        Field = row.GetValue("FIELD")?.ToString()
                    });
                }
            }
            catch (Exception ex)
            {
                // Log error but don't throw - return empty results
                Console.WriteLine($"Error converting RFC function to results: {ex.Message}");
            }

            return results;
        }

        /// <summary>
        /// Parses ERROR_CODE and ERROR_MESSAGE export parameters from an RFC function
        /// </summary>
        /// <param name="function">The RFC function to parse</param>
        /// <returns>RfcResultItem with error information if present</returns>
        public static RfcResultItem? ToRfcResult(this IRfcFunction? function)
        {
            if (function == null) return null;

            try
            {
                var errorCode = function.GetValue("ERROR_CODE")?.ToString();
                var errorMessage = function.GetValue("ERROR_MESSAGE")?.ToString();

                // If no error code or message, return null
                if (string.IsNullOrEmpty(errorCode) && string.IsNullOrEmpty(errorMessage))
                {
                    return null;
                }

                return new RfcResultItem
                {
                    Type = !string.IsNullOrEmpty(errorCode) && errorCode != "0" ? "E" : "S",
                    Id = "SAP",
                    Number = errorCode,
                    Message = errorMessage ?? "No error message provided",
                    System = "SAP"
                };
            }
            catch (Exception ex)
            {
                // Log error but don't throw - return null
                Console.WriteLine($"Error parsing RFC function error parameters: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Checks if the RfcResultItem contains an error
        /// </summary>
        /// <param name="result">The RfcResultItem to check</param>
        /// <returns>True if the result contains an error, false otherwise</returns>
        public static bool HasError(this RfcResultItem? result)
        {
            return result?.Type == "E";
        }

        /// <summary>
        /// Checks if any result items contain errors (Type = "E")
        /// </summary>
        /// <param name="results">Collection of result items</param>
        /// <returns>True if any errors are found, false otherwise</returns>
        public static bool HasErrors(this IEnumerable<RfcResultItem> results)
        {
            return results.Any(r => r.Type == "E");
        }
    }
}