using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Services;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Server.Services;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Server.Background;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Handlers;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Clients;

namespace Axon.Adapter.Api.SAPIntegrationAdapterAPI.Extensions;

/// <summary>
/// Extension methods for service collection to register SAP integration services
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Registers all SAP RFC handlers
    /// </summary>
    public static IServiceCollection AddSapRfcHandlers(this IServiceCollection services)
    {
        // Register all RFC handlers
        services.AddTransient<OrderStatusUpdatedRfcHandler>();
        services.AddTransient<MaterialUpdatedRfcHandler>();
        services.AddTransient<CustomerChangedRfcHandler>();
        services.AddTransient<TestEventRfcHandler>();

        return services;
    }

    /// <summary>
    /// Registers SAP integration services
    /// </summary>
    public static IServiceCollection AddSapIntegration(this IServiceCollection services)
    {
        // Register core SAP services
        services.AddScoped<ISapApiClient, SapApiClient>();
        services.AddScoped<ISapSalesOrderCreatedService, SapSalesOrderCreatedService>();
        services.AddScoped<ISapSalesOrderThinCreatedService, SapSalesOrderThinCreatedService>();

        // Register SAP event handlers
        services.AddScoped<OrderCreatedEventHandler>();
        services.AddScoped<IOrderCreatedEventHandler, OrderCreatedEventHandler>();
        services.AddScoped<OrderThinCreatedEventHandler>();
        services.AddScoped<IOrderThinCreatedEventHandler, OrderThinCreatedEventHandler>();

        // Register RFC Server services
        services.AddSingleton<ISapRfcServerService, SapRfcServerService>();
        services.AddHostedService<SapRfcServerBackgroundService>();

        // Register RFC handlers
        services.AddSapRfcHandlers();

        return services;
    }
}
