using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Services;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Server.Services;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Server.Background;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Handlers;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Clients;

namespace Axon.Adapter.Api.SAPIntegrationAdapterAPI.Extensions;

/// <summary>
/// Extension methods for service collection to register SAP integration services
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Registers all SAP RFC handlers
    /// </summary>
    public static IServiceCollection AddSapRfcHandlers(this IServiceCollection services)
    {
        // Register all RFC handlers
        services.AddTransient<OrderStatusUpdatedRfcHandler>();
        services.AddTransient<MaterialUpdatedRfcHandler>();
        services.AddTransient<CustomerChangedRfcHandler>();
        services.AddTransient<TestEventRfcHandler>();

        return services;
    }

    /// <summary>
    /// Registers SAP integration services
    /// </summary>
    public static IServiceCollection AddSapIntegration(this IServiceCollection services)
    {
        // Try to load SAP NCo assemblies and register appropriate services
        if (TrySapNcoAssemblyLoad())
        {
            // SAP NCo assemblies loaded successfully - register real SAP services
            services.AddScoped<ISapApiClient, SapApiClient>();
            services.AddScoped<ISapSalesOrderCreatedService, SapSalesOrderCreatedService>();
            services.AddScoped<ISapSalesOrderThinCreatedService, SapSalesOrderThinCreatedService>();
        }
        else
        {
            // SAP NCo assemblies failed to load - register mock services
            services.AddScoped<ISapApiClient, MockSapApiClient>();
            services.AddScoped<ISapSalesOrderCreatedService, MockSapSalesOrderCreatedService>();
            services.AddScoped<ISapSalesOrderThinCreatedService, MockSapSalesOrderThinCreatedService>();
        }

        // Register SAP event handlers
        services.AddScoped<OrderCreatedEventHandler>();
        services.AddScoped<IOrderCreatedEventHandler, OrderCreatedEventHandler>();
        services.AddScoped<OrderThinCreatedEventHandler>();
        services.AddScoped<IOrderThinCreatedEventHandler, OrderThinCreatedEventHandler>();

        // Register RFC Server services
        services.AddSingleton<ISapRfcServerService, SapRfcServerService>();
        services.AddHostedService<SapRfcServerBackgroundService>();

        // Register RFC handlers
        services.AddSapRfcHandlers();

        return services;
    }

    /// <summary>
    /// Tests if SAP NCo assemblies can be loaded on the current platform
    /// </summary>
    private static bool TrySapNcoAssemblyLoad()
    {
        try
        {
            // Try to load the main SAP NCo assembly
            var assembly = System.Reflection.Assembly.LoadFrom("sapnco.dll");

            // Try to get a basic type to ensure it's functional
            var destinationType = assembly.GetType("SAP.Middleware.Connector.RfcDestination");

            return destinationType != null;
        }
        catch (Exception ex)
        {
            // Log the specific reason why SAP NCo couldn't be loaded
            System.Diagnostics.Debug.WriteLine($"SAP NCo assembly load failed: {ex.Message}");
            return false;
        }
    }
}
