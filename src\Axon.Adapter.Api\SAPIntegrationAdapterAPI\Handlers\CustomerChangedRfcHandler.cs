using SAP.Middleware.Connector;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Server.Handlers;

namespace Axon.Adapter.Api.SAPIntegrationAdapterAPI.Handlers;

/// <summary>
/// Handler for SAP customer change RFC events
/// </summary>
public class CustomerChangedRfcHandler : RfcFunctionHandlerBase
{
    public override string FunctionName => "ZMM_PUSH_CUSTOMER_CHANGE_EVENT";

    public CustomerChangedRfcHandler(ILogger<CustomerChangedRfcHandler> logger) : base(logger)
    {
    }

    public override async Task HandleAsync(IRfcFunction function, CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogInformation("Processing customer change event from SAP");

            var customerNumber = GetStringValue(function, "CUSTOMER_NUMBER");
            var changeType = GetStringValue(function, "CHANGE_TYPE");

            if (string.IsNullOrEmpty(customerNumber) || string.IsNullOrEmpty(changeType))
            {
                SetErrorResponse(function, "Missing required parameters: CUSTOMER_NUMBER or CHANGE_TYPE");
                return;
            }

            // TODO: Implement business logic for customer change
            // This could include:
            // - Updating local customer data in the domain service
            // - Publishing proper domain events (e.g., CustomerUpdatedEvent from Axon.Contracts)
            // - Triggering credit checks
            // - Updating contact information
            // - Notifying sales teams

            Logger.LogInformation("Processed customer change for customer {CustomerNumber} with change type {ChangeType}",
                customerNumber, changeType);

            SetSuccessResponse(function, "Customer change event processed successfully");
        }
        catch (Exception ex)
        {
            SetErrorResponse(function, $"Failed to process customer change: {ex.Message}", ex);
        }

        await Task.CompletedTask;
    }
}
