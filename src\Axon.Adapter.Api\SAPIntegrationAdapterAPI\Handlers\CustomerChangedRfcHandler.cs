using SAP.Middleware.Connector;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Server.Handlers;
using Axon.Contracts.Customer.Events;
using MassTransit;

namespace Axon.Adapter.Api.SAPIntegrationAdapterAPI.Handlers;

/// <summary>
/// Handler for SAP customer change RFC events
/// </summary>
public class CustomerChangedRfcHandler : RfcFunctionHandlerBase
{
    private readonly IPublishEndpoint _publishEndpoint;

    public override string FunctionName => "ZMM_PUSH_CUSTOMER_CHANGE_EVENT";

    public CustomerChangedRfcHandler(ILogger<CustomerChangedRfcHandler> logger, IPublishEndpoint publishEndpoint) : base(logger)
    {
        _publishEndpoint = publishEndpoint;
    }

    public override async Task HandleAsync(IRfcFunction function, CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogInformation("Processing customer change event from SAP");

            var customerNumber = GetStringValue(function, "CUSTOMER_NUMBER");
            var changeType = GetStringValue(function, "CHANGE_TYPE");

            if (string.IsNullOrEmpty(customerNumber) || string.IsNullOrEmpty(changeType))
            {
                SetErrorResponse(function, "Missing required parameters: CUSTOMER_NUMBER or CHANGE_TYPE");
                return;
            }

            // Create and publish proper domain event
            var customerUpdatedEvent = new CustomerUpdatedEvent(
                CustomerNumber: customerNumber,
                ChangeType: changeType,
                UpdatedAt: DateTimeOffset.UtcNow
            );

            await _publishEndpoint.Publish(customerUpdatedEvent, cancellationToken);

            Logger.LogInformation("Published CustomerUpdatedEvent for customer {CustomerNumber} with change type {ChangeType}",
                customerNumber, changeType);

            SetSuccessResponse(function, "Customer change event processed successfully");
        }
        catch (Exception ex)
        {
            SetErrorResponse(function, $"Failed to process customer change: {ex.Message}", ex);
        }

        await Task.CompletedTask;
    }
}
