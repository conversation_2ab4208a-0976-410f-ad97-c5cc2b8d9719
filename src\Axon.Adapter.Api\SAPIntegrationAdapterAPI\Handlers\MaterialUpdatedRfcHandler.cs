using SAP.Middleware.Connector;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Server.Handlers;

namespace Axon.Adapter.Api.SAPIntegrationAdapterAPI.Handlers;

/// <summary>
/// Handler for SAP material update RFC events
/// </summary>
public class MaterialUpdatedRfcHandler : RfcFunctionHandlerBase
{
    public override string FunctionName => "ZMM_PUSH_MATERIAL_UPDATE_EVENT";

    public MaterialUpdatedRfcHandler(ILogger<MaterialUpdatedRfcHandler> logger) : base(logger)
    {
    }

    public override async Task HandleAsync(IRfcFunction function, CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogInformation("Processing material update event from SAP");

            var materialNumber = GetStringValue(function, "MATERIAL_NUMBER");
            var updateType = GetStringValue(function, "UPDATE_TYPE");

            if (string.IsNullOrEmpty(materialNumber) || string.IsNullOrEmpty(updateType))
            {
                SetErrorResponse(function, "Missing required parameters: MATERIAL_NUMBER or UPDATE_TYPE");
                return;
            }

            // TODO: Implement business logic for material update
            // This could include:
            // - Updating local material data in the domain service
            // - Publishing proper domain events (e.g., MaterialUpdatedEvent from Axon.Contracts)
            // - Triggering inventory updates
            // - Updating pricing information
            // - Notifying relevant systems

            Logger.LogInformation("Processed material update for material {MaterialNumber} with update type {UpdateType}",
                materialNumber, updateType);

            SetSuccessResponse(function, "Material update event processed successfully");
        }
        catch (Exception ex)
        {
            SetErrorResponse(function, $"Failed to process material update: {ex.Message}", ex);
        }

        await Task.CompletedTask;
    }
}
