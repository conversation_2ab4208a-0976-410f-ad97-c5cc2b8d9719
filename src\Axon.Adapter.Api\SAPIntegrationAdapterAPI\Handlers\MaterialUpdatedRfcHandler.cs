using SAP.Middleware.Connector;
using MassTransit;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Models.Events;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Server.Handlers;

namespace Axon.Adapter.Api.SAPIntegrationAdapterAPI.Handlers;

/// <summary>
/// Handler for SAP material update RFC events
/// </summary>
public class MaterialUpdatedRfcHandler : RfcFunctionHandlerBase
{
    private readonly IPublishEndpoint _publishEndpoint;

    public override string FunctionName => "ZMM_PUSH_MATERIAL_UPDATE_EVENT";

    public MaterialUpdatedRfcHandler(
        ILogger<MaterialUpdatedRfcHandler> logger,
        IPublishEndpoint publishEndpoint) : base(logger)
    {
        _publishEndpoint = publishEndpoint;
    }

    public override async Task HandleAsync(IRfcFunction function, CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogInformation("Processing material update event from SAP");

            var materialNumber = GetStringValue(function, "MATERIAL_NUMBER");
            var updateType = GetStringValue(function, "UPDATE_TYPE");

            if (string.IsNullOrEmpty(materialNumber) || string.IsNullOrEmpty(updateType))
            {
                SetErrorResponse(function, "Missing required parameters: MATERIAL_NUMBER or UPDATE_TYPE");
                return;
            }

            var sapEvent = new SapMaterialUpdatedEvent
            {
                MaterialNumber = materialNumber,
                UpdateType = updateType,
                Timestamp = DateTimeOffset.UtcNow
            };

            await _publishEndpoint.Publish(sapEvent, cancellationToken);
            
            SetSuccessResponse(function, "Material update event processed successfully");
            
            Logger.LogInformation("Published material update event for material {MaterialNumber} with update type {UpdateType}", 
                materialNumber, updateType);
        }
        catch (Exception ex)
        {
            SetErrorResponse(function, $"Failed to process material update: {ex.Message}", ex);
        }
    }
}
