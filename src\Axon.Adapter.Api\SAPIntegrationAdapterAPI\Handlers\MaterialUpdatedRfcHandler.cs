using SAP.Middleware.Connector;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Server.Handlers;
using Axon.Contracts.Material.Events;
using MassTransit;

namespace Axon.Adapter.Api.SAPIntegrationAdapterAPI.Handlers;

/// <summary>
/// Handler for SAP material update RFC events
/// </summary>
public class MaterialUpdatedRfcHandler : RfcFunctionHandlerBase
{
    private readonly IPublishEndpoint _publishEndpoint;

    public override string FunctionName => "ZMM_PUSH_MATERIAL_UPDATE_EVENT";

    public MaterialUpdatedRfcHandler(ILogger<MaterialUpdatedRfcHandler> logger, IPublishEndpoint publishEndpoint) : base(logger)
    {
        _publishEndpoint = publishEndpoint;
    }

    public override async Task HandleAsync(IRfcFunction function, CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogInformation("Processing material update event from SAP");

            var materialNumber = GetStringValue(function, "MATERIAL_NUMBER");
            var updateType = GetStringValue(function, "UPDATE_TYPE");

            if (string.IsNullOrEmpty(materialNumber) || string.IsNullOrEmpty(updateType))
            {
                SetErrorResponse(function, "Missing required parameters: MATERIAL_NUMBER or UPDATE_TYPE");
                return;
            }

            // Create and publish proper domain event
            var materialUpdatedEvent = new MaterialUpdatedEvent(
                MaterialNumber: materialNumber,
                UpdateType: updateType,
                UpdatedAt: DateTimeOffset.UtcNow
            );

            await _publishEndpoint.Publish(materialUpdatedEvent, cancellationToken);

            Logger.LogInformation("Published MaterialUpdatedEvent for material {MaterialNumber} with update type {UpdateType}",
                materialNumber, updateType);

            SetSuccessResponse(function, "Material update event processed successfully");
        }
        catch (Exception ex)
        {
            SetErrorResponse(function, $"Failed to process material update: {ex.Message}", ex);
        }

        await Task.CompletedTask;
    }
}
