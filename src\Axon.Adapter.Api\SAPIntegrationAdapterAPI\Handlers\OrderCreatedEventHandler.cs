using System.Text.Json;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Clients;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Models.Operations;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Queries;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Services;
using Axon.Contracts.Order.Events;
using System.Text.Json.Serialization;

namespace Axon.Adapter.Api.SAPIntegrationAdapterAPI.Handlers;

public interface IOrderCreatedEventHandler
{
    Task<SapSalesOrderCreatedEvent> HandleAsync(OrderCreatedEvent order, CancellationToken cancellationToken);
}

    public class OrderCreatedEventHandler : IOrderCreatedEventHandler
    {
        private readonly ISapSalesOrderCreatedService _sapSalesOrderCreatedService;
        private readonly ILogger<OrderCreatedEventHandler> _logger;

        public OrderCreatedEventHandler(ISapSalesOrderCreatedService sapSalesOrderCreatedService, ILogger<OrderCreatedEventHandler> logger)
        {
            _sapSalesOrderCreatedService = sapSalesOrderCreatedService;
            _logger = logger;
        }

    public async Task<SapSalesOrderCreatedEvent> HandleAsync(OrderCreatedEvent order, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing OrderCreatedEvent for IncrementId: {IncrementId}", order.IncrementId);
        
        var result = await _sapSalesOrderCreatedService.CreateSalesOrderFromEventAsync(order, cancellationToken);

        if (!result.Success)
        {
            throw new InvalidOperationException($"SAP sales order creation failed: {result.ErrorMessage}");
        }

        if (string.IsNullOrEmpty(result.OrderNumber))
        {
            throw new InvalidOperationException("SAP sales order creation succeeded but no order number was returned");
        }

        _logger.LogInformation("Successfully created SAP sales order for IncrementId {IncrementId}", order.IncrementId);

        var sapSalesOrderCreatedEvent = new SapSalesOrderCreatedEvent(
            result.OrderNumber,
            result.Results?.Select(r => new SapSalesOrderCreatedEvent.Result(
                r.Type ?? string.Empty,
                r.Id ?? string.Empty,
                r.Number ?? string.Empty,
                r.Message ?? string.Empty,
                r.System ?? string.Empty,
                r.LogNo,
                r.LogMsgNo,
                r.MessageV1,
                r.MessageV2,
                r.MessageV3,
                r.MessageV4,
                r.Parameter,
                r.Row,
                r.Field
            )).ToList() ?? new List<SapSalesOrderCreatedEvent.Result>()
        );
        return sapSalesOrderCreatedEvent;
    }
} 