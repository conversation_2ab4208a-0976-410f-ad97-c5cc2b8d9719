using SAP.Middleware.Connector;
using MassTransit;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Models.Events;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Server.Handlers;

namespace Axon.Adapter.Api.SAPIntegrationAdapterAPI.Handlers;

/// <summary>
/// Handler for SAP order status update RFC events
/// </summary>
public class OrderStatusUpdatedRfcHandler : RfcFunctionHandlerBase
{
    private readonly IPublishEndpoint _publishEndpoint;

    public override string FunctionName => "ZMM_PUSH_ORDER_STATUS_EVENT";

    public OrderStatusUpdatedRfcHandler(
        ILogger<OrderStatusUpdatedRfcHandler> logger,
        IPublishEndpoint publishEndpoint) : base(logger)
    {
        _publishEndpoint = publishEndpoint;
    }

    public override async Task HandleAsync(IRfcFunction function, CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogInformation("Processing order status update event from SAP");

            var orderNumber = GetStringValue(function, "ORDER_NUMBER");
            var status = GetStringValue(function, "STATUS");

            if (string.IsNullOrEmpty(orderNumber) || string.IsNullOrEmpty(status))
            {
                SetErrorResponse(function, "Missing required parameters: ORDER_NUMBER or STATUS");
                return;
            }

            var sapEvent = new SapOrderStatusUpdatedEvent
            {
                OrderNumber = orderNumber,
                Status = status,
                Timestamp = DateTimeOffset.UtcNow
            };

            await _publishEndpoint.Publish(sapEvent, cancellationToken);
            
            SetSuccessResponse(function, "Order status update event processed successfully");
            
            Logger.LogInformation("Published order status update event for order {OrderNumber} with status {Status}", 
                orderNumber, status);
        }
        catch (Exception ex)
        {
            SetErrorResponse(function, $"Failed to process order status update: {ex.Message}", ex);
        }
    }
}
