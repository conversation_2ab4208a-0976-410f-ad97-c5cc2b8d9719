using SAP.Middleware.Connector;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Server.Handlers;

namespace Axon.Adapter.Api.SAPIntegrationAdapterAPI.Handlers;

/// <summary>
/// Handler for SAP order status update RFC events
/// </summary>
public class OrderStatusUpdatedRfcHandler : RfcFunctionHandlerBase
{
    public override string FunctionName => "ZMM_PUSH_ORDER_STATUS_EVENT";

    public OrderStatusUpdatedRfcHandler(ILogger<OrderStatusUpdatedRfcHandler> logger) : base(logger)
    {
    }

    public override async Task HandleAsync(IRfcFunction function, CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogInformation("Processing order status update event from SAP");

            var orderNumber = GetStringValue(function, "ORDER_NUMBER");
            var status = GetStringValue(function, "STATUS");

            if (string.IsNullOrEmpty(orderNumber) || string.IsNullOrEmpty(status))
            {
                SetErrorResponse(function, "Missing required parameters: ORDER_NUMBER or STATUS");
                return;
            }

            // TODO: Implement business logic for order status update
            // This could include:
            // - Updating local order status in the domain service
            // - Publishing proper domain events (e.g., OrderStatusUpdatedEvent from Axon.Contracts)
            // - Triggering downstream processes
            // - Sending notifications

            Logger.LogInformation("Processed order status update for order {OrderNumber} with status {Status}",
                orderNumber, status);

            SetSuccessResponse(function, "Order status update event processed successfully");
        }
        catch (Exception ex)
        {
            SetErrorResponse(function, $"Failed to process order status update: {ex.Message}", ex);
        }

        await Task.CompletedTask;
    }
}
