using SAP.Middleware.Connector;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Server.Handlers;
using Axon.Contracts.Order.Events;
using MassTransit;

namespace Axon.Adapter.Api.SAPIntegrationAdapterAPI.Handlers;

/// <summary>
/// Handler for SAP order status update RFC events
/// </summary>
public class OrderStatusUpdatedRfcHandler : RfcFunctionHandlerBase
{
    private readonly IPublishEndpoint _publishEndpoint;

    public override string FunctionName => "ZMM_PUSH_ORDER_STATUS_EVENT";

    public OrderStatusUpdatedRfcHandler(ILogger<OrderStatusUpdatedRfcHandler> logger, IPublishEndpoint publishEndpoint) : base(logger)
    {
        _publishEndpoint = publishEndpoint;
    }

    public override async Task HandleAsync(IRfcFunction function, CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogInformation("Processing order status update event from SAP");

            // Validate required parameters using the improved base class method
            if (!ValidateRequiredParameters(function, "ORDER_NUMBER", "STATUS"))
            {
                return; // Error response already set by validation method
            }

            // Get validated parameters
            var orderNumber = GetRequiredStringValue(function, "ORDER_NUMBER");
            var status = GetRequiredStringValue(function, "STATUS");

            // Create and publish proper domain event
            var orderStatusUpdatedEvent = new OrderStatusUpdatedEvent(
                OrderNumber: orderNumber,
                Status: status,
                UpdatedAt: DateTimeOffset.UtcNow,
                UpdatedBy: "SAP"
            );

            await _publishEndpoint.Publish(orderStatusUpdatedEvent, cancellationToken);

            Logger.LogInformation("Published OrderStatusUpdatedEvent for order {OrderNumber} with status {Status}",
                orderNumber, status);

            SetSuccessResponse(function, "Order status update event processed successfully");
        }
        catch (Exception ex)
        {
            SetErrorResponse(function, $"Failed to process order status update: {ex.Message}", ex);
        }

        await Task.CompletedTask;
    }
}
