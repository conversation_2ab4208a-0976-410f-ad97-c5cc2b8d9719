using SAP.Middleware.Connector;
using MassTransit;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Models.Events;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Server.Handlers;

namespace Axon.Adapter.Api.SAPIntegrationAdapterAPI.Handlers;

/// <summary>
/// Handler for SAP test RFC events (for POC validation)
/// </summary>
public class TestEventRfcHandler : RfcFunctionHandlerBase
{
    private readonly IPublishEndpoint _publishEndpoint;

    public override string FunctionName => "ZMM_PUSH_TEST_EVENT";

    public TestEventRfcHandler(
        ILogger<TestEventRfcHandler> logger,
        IPublishEndpoint publishEndpoint) : base(logger)
    {
        _publishEndpoint = publishEndpoint;
    }

    public override async Task HandleAsync(IRfcFunction function, CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogInformation("Processing test event from SAP");

            var testMessage = GetStringValue(function, "TEST_MESSAGE") ?? "Default test message";

            var sapEvent = new SapTestEvent
            {
                Message = testMessage,
                Timestamp = DateTimeOffset.UtcNow
            };

            await _publishEndpoint.Publish(sapEvent, cancellationToken);
            
            SetSuccessResponse(function, "Test event processed successfully");
            
            Logger.LogInformation("Published test event with message: {Message}", testMessage);
        }
        catch (Exception ex)
        {
            SetErrorResponse(function, $"Failed to process test event: {ex.Message}", ex);
        }
    }
}
