using SAP.Middleware.Connector;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Server.Handlers;

namespace Axon.Adapter.Api.SAPIntegrationAdapterAPI.Handlers;

/// <summary>
/// Handler for SAP test RFC events (for POC validation)
/// </summary>
public class TestEventRfcHandler : RfcFunctionHandlerBase
{
    public override string FunctionName => "ZMM_PUSH_TEST_EVENT";

    public TestEventRfcHandler(ILogger<TestEventRfcHandler> logger) : base(logger)
    {
    }

    public override async Task HandleAsync(IRfcFunction function, CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogInformation("Processing test event from SAP");

            var testMessage = GetStringValue(function, "TEST_MESSAGE") ?? "Default test message";

            // TODO: Implement test event processing
            // This could include:
            // - Logging the test event
            // - Validating the RFC connection
            // - Testing the event flow
            // - Sending test notifications

            Logger.LogInformation("Processed test event with message: {Message}", testMessage);

            SetSuccessResponse(function, "Test event processed successfully");
        }
        catch (Exception ex)
        {
            SetErrorResponse(function, $"Failed to process test event: {ex.Message}", ex);
        }

        await Task.CompletedTask;
    }
}
