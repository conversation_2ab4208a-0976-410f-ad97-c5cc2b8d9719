namespace Axon.Adapter.Api.SAPIntegrationAdapterAPI.Models.Events;

/// <summary>
/// Event emitted when SAP order status is updated
/// </summary>
public class SapOrderStatusUpdatedEvent
{
    public string OrderNumber { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTimeOffset Timestamp { get; set; }
}

/// <summary>
/// Event emitted when SAP material is updated
/// </summary>
public class SapMaterialUpdatedEvent
{
    public string MaterialNumber { get; set; } = string.Empty;
    public string UpdateType { get; set; } = string.Empty;
    public DateTimeOffset Timestamp { get; set; }
}

/// <summary>
/// Event emitted when SAP customer is changed
/// </summary>
public class SapCustomerChangedEvent
{
    public string CustomerNumber { get; set; } = string.Empty;
    public string ChangeType { get; set; } = string.Empty;
    public DateTimeOffset Timestamp { get; set; }
}

/// <summary>
/// Test event for POC validation
/// </summary>
public class SapTestEvent
{
    public string Message { get; set; } = string.Empty;
    public DateTimeOffset Timestamp { get; set; }
} 