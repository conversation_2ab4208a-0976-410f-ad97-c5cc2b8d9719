namespace Axon.Adapter.Api.SAPIntegrationAdapterAPI.Models;

/// <summary>
/// Represents parameters for SAP RFC function calls.
/// Inherits from Dictionary to provide key-value parameter storage.
/// </summary>
public class RfcParameters : Dictionary<string, object>
{
    /// <summary>
    /// Adds a parameter with null checking for the value.
    /// </summary>
    /// <param name="key">Parameter name</param>
    /// <param name="value">Parameter value (null values are converted to empty string)</param>
    public new void Add(string key, object? value)
    {
        base.Add(key, value ?? string.Empty);
    }

    /// <summary>
    /// Sets a parameter value, overwriting if the key already exists.
    /// </summary>
    /// <param name="key">Parameter name</param>
    /// <param name="value">Parameter value (null values are converted to empty string)</param>
    public void Set(string key, object? value)
    {
        this[key] = value ?? string.Empty;
    }

    /// <summary>
    /// Gets a parameter value as a string, returning empty string if not found.
    /// </summary>
    /// <param name="key">Parameter name</param>
    /// <returns>Parameter value as string</returns>
    public string GetString(string key)
    {
        return TryGetValue(key, out var value) ? value?.ToString() ?? string.Empty : string.Empty;
    }
} 