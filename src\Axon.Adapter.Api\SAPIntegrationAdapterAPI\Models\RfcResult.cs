using SAP.Middleware.Connector;

namespace Axon.Adapter.Api.SAPIntegrationAdapterAPI.Models;

/// <summary>
/// Represents the result of an SAP RFC function call.
/// </summary>
public class RfcResult
{
    /// <summary>
    /// Indicates whether the RFC function call was successful.
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Error message if the RFC function call failed.
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// The RFC function result containing the returned data.
    /// </summary>
    public IRfcFunction? Function { get; set; }

    /// <summary>
    /// Creates a successful result with the given function.
    /// </summary>
    /// <param name="function">The RFC function result</param>
    /// <returns>A successful RfcResult</returns>
    public static RfcResult Ok(IRfcFunction function)
    {
        return new RfcResult
        {
            Success = true,
            Function = function
        };
    }

    /// <summary>
    /// Creates a failed result with the given error message.
    /// </summary>
    /// <param name="errorMessage">The error message</param>
    /// <returns>A failed RfcResult</returns>
    public static RfcResult Error(string errorMessage)
    {
        return new RfcResult
        {
            Success = false,
            ErrorMessage = errorMessage
        };
    }
} 