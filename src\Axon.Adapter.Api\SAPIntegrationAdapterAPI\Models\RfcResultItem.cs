namespace Axon.Adapter.Api.SAPIntegrationAdapterAPI.Models;

/// <summary>
/// Represents a SAP BAPIRET2 result structure used across all SAP RFC calls
/// </summary>
public class SapRfcResultItem
{
    /// <summary>
    /// Message type (S=Success, E=Error, W=Warning, I=Info, A=Abort)
    /// </summary>
    public string? Type { get; set; }

    /// <summary>
    /// Message ID
    /// </summary>
    public string? Id { get; set; }

    /// <summary>
    /// Message number
    /// </summary>
    public string? Number { get; set; }

    /// <summary>
    /// Message text
    /// </summary>
    public string? Message { get; set; }

    /// <summary>
    /// System name
    /// </summary>
    public string? System { get; set; }

    /// <summary>
    /// Log number
    /// </summary>
    public string? LogNo { get; set; }

    /// <summary>
    /// Log message number
    /// </summary>
    public string? LogMsgNo { get; set; }

    /// <summary>
    /// Message variable 1
    /// </summary>
    public string? MessageV1 { get; set; }

    /// <summary>
    /// Message variable 2
    /// </summary>
    public string? MessageV2 { get; set; }

    /// <summary>
    /// Message variable 3
    /// </summary>
    public string? MessageV3 { get; set; }

    /// <summary>
    /// Message variable 4
    /// </summary>
    public string? MessageV4 { get; set; }

    /// <summary>
    /// Parameter name
    /// </summary>
    public string? Parameter { get; set; }

    /// <summary>
    /// Row number
    /// </summary>
    public int? Row { get; set; }

    /// <summary>
    /// Field name
    /// </summary>
    public string? Field { get; set; }
} 