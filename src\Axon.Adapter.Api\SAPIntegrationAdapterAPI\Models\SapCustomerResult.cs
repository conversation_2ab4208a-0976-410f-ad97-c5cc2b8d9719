namespace Axon.Adapter.Api.SAPIntegrationAdapterAPI.Models;

/// <summary>
/// Example of how to extend SapOperationResultBase for customer operations.
/// This demonstrates the pattern for creating new SAP operation result types.
/// </summary>
public class SapCustomerResult : SapOperationResultBase
{
    /// <summary>
    /// The SAP customer number that was created or retrieved.
    /// </summary>
    public string? CustomerNumber { get; set; }

    /// <summary>
    /// The customer name.
    /// </summary>
    public string? CustomerName { get; set; }

    /// <summary>
    /// The customer's credit limit.
    /// </summary>
    public decimal? CreditLimit { get; set; }

    /// <summary>
    /// Initializes a new instance of SapCustomerResult.
    /// </summary>
    /// <param name="rfcResult">The RFC result to wrap</param>
    public SapCustomerResult(SapRfcResult rfcResult) : base(rfcResult)
    {
    }

    /// <summary>
    /// Creates a successful customer result from an RfcResult.
    /// </summary>
    /// <param name="rfcResult">The RFC result</param>
    /// <param name="customerNumber">The customer number</param>
    /// <param name="customerName">The customer name</param>
    /// <param name="creditLimit">The credit limit</param>
    /// <returns>A successful SapCustomerResult</returns>
    public static SapCustomerResult FromSuccess(SapRfcResult rfcResult, string? customerNumber = null,
        string? customerName = null, decimal? creditLimit = null)
    {
        var result = CreateSuccess<SapCustomerResult>(rfcResult);
        result.CustomerNumber = customerNumber;
        result.CustomerName = customerName;
        result.CreditLimit = creditLimit;
        return result;
    }

    /// <summary>
    /// Creates a failed customer result from an RfcResult.
    /// </summary>
    /// <param name="rfcResult">The RFC result</param>
    /// <returns>A failed SapCustomerResult</returns>
    public static SapCustomerResult FromFailure(SapRfcResult rfcResult)
    {
        return CreateFailure<SapCustomerResult>(rfcResult);
    }

    /// <summary>
    /// Creates a failed customer result with custom error information.
    /// </summary>
    /// <param name="errorMessage">The error message</param>
    /// <param name="errorCode">Optional error code</param>
    /// <param name="errorType">The type of error</param>
    /// <returns>A failed SapCustomerResult</returns>
    public static SapCustomerResult FromError(string errorMessage, string? errorCode = null,
        SapRfcErrorType errorType = SapRfcErrorType.BusinessLogic)
    {
        return CreateFailure<SapCustomerResult>(errorMessage, errorCode, errorType);
    }

    /// <summary>
    /// Gets a summary of the customer information.
    /// </summary>
    /// <returns>A formatted string with customer details</returns>
    public string GetCustomerSummary()
    {
        if (!Success || string.IsNullOrEmpty(CustomerNumber))
            return "No customer information available";

        var parts = new List<string> { $"Customer: {CustomerNumber}" };
        
        if (!string.IsNullOrEmpty(CustomerName))
            parts.Add($"Name: {CustomerName}");
            
        if (CreditLimit.HasValue)
            parts.Add($"Credit Limit: {CreditLimit:C}");

        return string.Join(", ", parts);
    }
}
