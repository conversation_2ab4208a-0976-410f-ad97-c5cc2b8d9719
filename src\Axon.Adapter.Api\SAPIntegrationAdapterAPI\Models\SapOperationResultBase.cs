namespace Axon.Adapter.Api.SAPIntegrationAdapterAPI.Models;

/// <summary>
/// Base class for all SAP operation results, providing common error handling and message evaluation functionality.
/// Uses composition with RfcResult to avoid duplication.
/// </summary>
public abstract class SapOperationResultBase
{
    /// <summary>
    /// The underlying RFC result that contains the detailed SAP response information.
    /// </summary>
    public SapRfcResult RfcResult { get; protected set; }

    /// <summary>
    /// Indicates whether the SAP operation was successful.
    /// </summary>
    public bool Success => RfcResult.Success;

    /// <summary>
    /// Primary error message if the operation failed.
    /// </summary>
    public string? ErrorMessage => RfcResult.ErrorMessage;

    /// <summary>
    /// Detailed error code for programmatic error handling.
    /// </summary>
    public string? ErrorCode => RfcResult.ErrorCode;

    /// <summary>
    /// The type of error that occurred.
    /// </summary>
    public SapRfcErrorType ErrorType => RfcResult.ErrorType;

    /// <summary>
    /// Collection of detailed result messages from SAP (BAPIRET2 structures).
    /// </summary>
    public List<SapRfcResultItem> Results => RfcResult.Messages;

    /// <summary>
    /// Timestamp when the operation was performed.
    /// </summary>
    public DateTimeOffset Timestamp => RfcResult.Timestamp;

    /// <summary>
    /// The name of the RFC function that was called.
    /// </summary>
    public string? FunctionName => RfcResult.FunctionName;

    /// <summary>
    /// The original exception that caused the failure, if any.
    /// </summary>
    public Exception? Exception => RfcResult.Exception;

    /// <summary>
    /// Indicates whether the result contains any error messages.
    /// </summary>
    public bool HasErrors => RfcResult.HasErrors;

    /// <summary>
    /// Indicates whether the result contains any warning messages.
    /// </summary>
    public bool HasWarnings => RfcResult.HasWarnings;

    /// <summary>
    /// Gets all error messages as a concatenated string.
    /// </summary>
    public string GetErrorSummary() => RfcResult.GetErrorSummary();

    /// <summary>
    /// Gets all warning messages as a concatenated string.
    /// </summary>
    public string GetWarningSummary() => RfcResult.GetWarningSummary();

    /// <summary>
    /// Initializes the base class with an RfcResult.
    /// </summary>
    /// <param name="rfcResult">The RFC result to wrap</param>
    protected SapOperationResultBase(SapRfcResult rfcResult)
    {
        RfcResult = rfcResult ?? throw new ArgumentNullException(nameof(rfcResult));
    }

    /// <summary>
    /// Creates a successful result from an RfcResult.
    /// </summary>
    /// <typeparam name="T">The specific result type to create</typeparam>
    /// <param name="rfcResult">The RFC result to convert</param>
    /// <returns>A successful operation result</returns>
    protected static T CreateSuccess<T>(SapRfcResult rfcResult) where T : SapOperationResultBase
    {
        return (T)Activator.CreateInstance(typeof(T), rfcResult)!;
    }

    /// <summary>
    /// Creates a failed result from an RfcResult.
    /// </summary>
    /// <typeparam name="T">The specific result type to create</typeparam>
    /// <param name="rfcResult">The RFC result to convert</param>
    /// <returns>A failed operation result</returns>
    protected static T CreateFailure<T>(SapRfcResult rfcResult) where T : SapOperationResultBase
    {
        return (T)Activator.CreateInstance(typeof(T), rfcResult)!;
    }

    /// <summary>
    /// Creates a failed result with custom error information.
    /// </summary>
    /// <typeparam name="T">The specific result type to create</typeparam>
    /// <param name="errorMessage">The error message</param>
    /// <param name="errorCode">Optional error code</param>
    /// <param name="errorType">The type of error</param>
    /// <returns>A failed operation result</returns>
    protected static T CreateFailure<T>(string errorMessage, string? errorCode = null, SapRfcErrorType errorType = SapRfcErrorType.BusinessLogic) where T : SapOperationResultBase
    {
        var rfcResult = SapRfcResult.Error(errorMessage, errorCode, errorType);
        return (T)Activator.CreateInstance(typeof(T), rfcResult)!;
    }

    /// <summary>
    /// Gets all informational messages as a concatenated string.
    /// </summary>
    public string GetInfoSummary()
    {
        var infoMessages = Results.Where(m => m.Type == "I" || m.Type == "S").Select(m => m.Message).Where(m => !string.IsNullOrEmpty(m));
        return string.Join("; ", infoMessages);
    }

    /// <summary>
    /// Gets a summary of all messages grouped by type.
    /// </summary>
    /// <returns>A dictionary with message types as keys and concatenated messages as values</returns>
    public Dictionary<string, string> GetMessageSummaryByType()
    {
        return Results
            .Where(r => !string.IsNullOrEmpty(r.Message))
            .GroupBy(r => r.Type ?? "Unknown")
            .ToDictionary(
                g => g.Key,
                g => string.Join("; ", g.Select(r => r.Message))
            );
    }

    /// <summary>
    /// Gets the most severe message type present in the results.
    /// Priority: E (Error) > W (Warning) > I (Info) > S (Success)
    /// </summary>
    /// <returns>The most severe message type, or null if no messages</returns>
    public string? GetMostSevereMessageType()
    {
        if (Results.Any(r => r.Type == "E")) return "E";
        if (Results.Any(r => r.Type == "W")) return "W";
        if (Results.Any(r => r.Type == "I")) return "I";
        if (Results.Any(r => r.Type == "S")) return "S";
        return null;
    }

    /// <summary>
    /// Checks if the operation should be considered successful based on message severity.
    /// An operation is considered successful if there are no error messages, regardless of warnings.
    /// </summary>
    /// <returns>True if the operation should be considered successful</returns>
    public bool IsOperationSuccessful()
    {
        return RfcResult.Success && !RfcResult.HasErrors;
    }
}
