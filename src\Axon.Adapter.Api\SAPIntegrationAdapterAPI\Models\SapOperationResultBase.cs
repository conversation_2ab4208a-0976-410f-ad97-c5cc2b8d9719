using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Extensions;

namespace Axon.Adapter.Api.SAPIntegrationAdapterAPI.Models;

/// <summary>
/// Base class for all SAP operation results, providing common error handling and message evaluation functionality.
/// </summary>
public abstract class SapOperationResultBase
{
    /// <summary>
    /// Indicates whether the SAP operation was successful.
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Primary error message if the operation failed.
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Detailed error code for programmatic error handling.
    /// </summary>
    public string? ErrorCode { get; set; }

    /// <summary>
    /// The type of error that occurred.
    /// </summary>
    public RfcErrorType ErrorType { get; set; } = RfcErrorType.None;

    /// <summary>
    /// Collection of detailed result messages from SAP (BAPIRET2 structures).
    /// </summary>
    public List<RfcResultItem> Results { get; set; } = new();

    /// <summary>
    /// Timestamp when the operation was performed.
    /// </summary>
    public DateTimeOffset Timestamp { get; set; } = DateTimeOffset.UtcNow;

    /// <summary>
    /// Indicates whether the result contains any error messages.
    /// </summary>
    public bool HasErrors => !Success || Results.HasErrors();

    /// <summary>
    /// Indicates whether the result contains any warning messages.
    /// </summary>
    public bool HasWarnings => Results.Any(m => m.Type == "W");

    /// <summary>
    /// Gets all error messages as a concatenated string.
    /// </summary>
    public string GetErrorSummary()
    {
        if (!string.IsNullOrEmpty(ErrorMessage))
            return ErrorMessage;

        var errorMessages = Results.Where(m => m.Type == "E").Select(m => m.Message).Where(m => !string.IsNullOrEmpty(m));
        return string.Join("; ", errorMessages);
    }

    /// <summary>
    /// Gets all warning messages as a concatenated string.
    /// </summary>
    public string GetWarningSummary()
    {
        var warningMessages = Results.Where(m => m.Type == "W").Select(m => m.Message).Where(m => !string.IsNullOrEmpty(m));
        return string.Join("; ", warningMessages);
    }

    /// <summary>
    /// Gets all informational messages as a concatenated string.
    /// </summary>
    public string GetInfoSummary()
    {
        var infoMessages = Results.Where(m => m.Type == "I" || m.Type == "S").Select(m => m.Message).Where(m => !string.IsNullOrEmpty(m));
        return string.Join("; ", infoMessages);
    }

    /// <summary>
    /// Creates a successful result from an RfcResult.
    /// </summary>
    /// <typeparam name="T">The specific result type to create</typeparam>
    /// <param name="rfcResult">The RFC result to convert</param>
    /// <returns>A successful operation result</returns>
    protected static T CreateSuccess<T>(RfcResult rfcResult) where T : SapOperationResultBase, new()
    {
        return new T
        {
            Success = rfcResult.Success,
            ErrorMessage = rfcResult.ErrorMessage,
            ErrorCode = rfcResult.ErrorCode,
            ErrorType = rfcResult.ErrorType,
            Results = rfcResult.Messages,
            Timestamp = rfcResult.Timestamp
        };
    }

    /// <summary>
    /// Creates a failed result from an RfcResult.
    /// </summary>
    /// <typeparam name="T">The specific result type to create</typeparam>
    /// <param name="rfcResult">The RFC result to convert</param>
    /// <returns>A failed operation result</returns>
    protected static T CreateFailure<T>(RfcResult rfcResult) where T : SapOperationResultBase, new()
    {
        return new T
        {
            Success = false,
            ErrorMessage = rfcResult.ErrorMessage ?? "Unknown error occurred",
            ErrorCode = rfcResult.ErrorCode,
            ErrorType = rfcResult.ErrorType,
            Results = rfcResult.Messages,
            Timestamp = rfcResult.Timestamp
        };
    }

    /// <summary>
    /// Creates a failed result with custom error information.
    /// </summary>
    /// <typeparam name="T">The specific result type to create</typeparam>
    /// <param name="errorMessage">The error message</param>
    /// <param name="errorCode">Optional error code</param>
    /// <param name="errorType">The type of error</param>
    /// <returns>A failed operation result</returns>
    protected static T CreateFailure<T>(string errorMessage, string? errorCode = null, RfcErrorType errorType = RfcErrorType.BusinessLogic) where T : SapOperationResultBase, new()
    {
        return new T
        {
            Success = false,
            ErrorMessage = errorMessage,
            ErrorCode = errorCode,
            ErrorType = errorType,
            Results = new List<RfcResultItem>(),
            Timestamp = DateTimeOffset.UtcNow
        };
    }

    /// <summary>
    /// Gets a summary of all messages grouped by type.
    /// </summary>
    /// <returns>A dictionary with message types as keys and concatenated messages as values</returns>
    public Dictionary<string, string> GetMessageSummaryByType()
    {
        return Results
            .Where(r => !string.IsNullOrEmpty(r.Message))
            .GroupBy(r => r.Type ?? "Unknown")
            .ToDictionary(
                g => g.Key,
                g => string.Join("; ", g.Select(r => r.Message))
            );
    }

    /// <summary>
    /// Gets the most severe message type present in the results.
    /// Priority: E (Error) > W (Warning) > I (Info) > S (Success)
    /// </summary>
    /// <returns>The most severe message type, or null if no messages</returns>
    public string? GetMostSevereMessageType()
    {
        if (Results.Any(r => r.Type == "E")) return "E";
        if (Results.Any(r => r.Type == "W")) return "W";
        if (Results.Any(r => r.Type == "I")) return "I";
        if (Results.Any(r => r.Type == "S")) return "S";
        return null;
    }

    /// <summary>
    /// Checks if the operation should be considered successful based on message severity.
    /// An operation is considered successful if there are no error messages, regardless of warnings.
    /// </summary>
    /// <returns>True if the operation should be considered successful</returns>
    public bool IsOperationSuccessful()
    {
        return Success && !Results.HasErrors();
    }
}
