using SAP.Middleware.Connector;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Extensions;

namespace Axon.Adapter.Api.SAPIntegrationAdapterAPI.Models;

/// <summary>
/// Represents the result of an SAP RFC function call with comprehensive error handling.
/// </summary>
public class SapRfcResult
{
    /// <summary>
    /// Indicates whether the RFC function call was successful at both transport and business logic levels.
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Primary error message if the RFC function call failed.
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Detailed error code for programmatic error handling.
    /// </summary>
    public string? ErrorCode { get; set; }

    /// <summary>
    /// The type of error that occurred.
    /// </summary>
    public SapRfcErrorType ErrorType { get; set; } = SapRfcErrorType.None;

    /// <summary>
    /// The RFC function result containing the returned data.
    /// </summary>
    public IRfcFunction? Function { get; set; }

    /// <summary>
    /// The name of the RFC function that was called.
    /// </summary>
    public string? FunctionName { get; set; }

    /// <summary>
    /// Collection of detailed result messages from SAP (BAPIRET2 structures).
    /// </summary>
    public List<SapRfcResultItem> Messages { get; set; } = new();

    /// <summary>
    /// The original exception that caused the failure, if any.
    /// </summary>
    public Exception? Exception { get; set; }

    /// <summary>
    /// Timestamp when the RFC call was made.
    /// </summary>
    public DateTimeOffset Timestamp { get; set; } = DateTimeOffset.UtcNow;

    /// <summary>
    /// Indicates whether the result contains any error messages.
    /// </summary>
    public bool HasErrors => !Success || Messages.HasErrors();

    /// <summary>
    /// Indicates whether the result contains any warning messages.
    /// </summary>
    public bool HasWarnings => Messages.Any(m => m.Type == "W");

    /// <summary>
    /// Gets all error messages as a concatenated string.
    /// </summary>
    public string GetErrorSummary()
    {
        if (!string.IsNullOrEmpty(ErrorMessage))
            return ErrorMessage;

        var errorMessages = Messages.Where(m => m.Type == "E").Select(m => m.Message).Where(m => !string.IsNullOrEmpty(m));
        return string.Join("; ", errorMessages);
    }

    /// <summary>
    /// Gets all warning messages as a concatenated string.
    /// </summary>
    public string GetWarningSummary()
    {
        var warningMessages = Messages.Where(m => m.Type == "W").Select(m => m.Message).Where(m => !string.IsNullOrEmpty(m));
        return string.Join("; ", warningMessages);
    }

    /// <summary>
    /// Creates a successful result with the given function and automatically evaluates SAP messages.
    /// </summary>
    /// <param name="function">The RFC function result</param>
    /// <param name="functionName">The name of the function that was called</param>
    /// <param name="evaluateMessages">Whether to automatically evaluate BAPIRET2 messages for errors</param>
    /// <returns>A SapRfcResult with success/failure determined by SAP message evaluation</returns>
    public static SapRfcResult Ok(IRfcFunction function, string? functionName = null, bool evaluateMessages = true)
    {
        var result = new SapRfcResult
        {
            Success = true,
            Function = function,
            FunctionName = functionName,
            ErrorType = SapRfcErrorType.None
        };

        if (evaluateMessages)
        {
            result.EvaluateMessages();
        }

        return result;
    }

    /// <summary>
    /// Creates a failed result with the given error information.
    /// </summary>
    /// <param name="errorMessage">The error message</param>
    /// <param name="errorCode">Optional error code</param>
    /// <param name="errorType">The type of error</param>
    /// <param name="functionName">The name of the function that failed</param>
    /// <param name="exception">The original exception, if any</param>
    /// <returns>A failed SapRfcResult</returns>
    public static SapRfcResult Error(string errorMessage, string? errorCode = null,
        SapRfcErrorType errorType = SapRfcErrorType.BusinessLogic, string? functionName = null, Exception? exception = null)
    {
        return new SapRfcResult
        {
            Success = false,
            ErrorMessage = errorMessage,
            ErrorCode = errorCode,
            ErrorType = errorType,
            FunctionName = functionName,
            Exception = exception
        };
    }

    /// <summary>
    /// Creates a failed result from an exception.
    /// </summary>
    /// <param name="exception">The exception that occurred</param>
    /// <param name="functionName">The name of the function that failed</param>
    /// <returns>A failed SapRfcResult</returns>
    public static SapRfcResult FromException(Exception exception, string? functionName = null)
    {
        var errorType = exception switch
        {
            RfcCommunicationException => SapRfcErrorType.Communication,
            RfcLogonException => SapRfcErrorType.Authentication,
            RfcAbapException => SapRfcErrorType.AbapException,
            RfcInvalidStateException => SapRfcErrorType.InvalidState,
            _ => SapRfcErrorType.System
        };

        return new SapRfcResult
        {
            Success = false,
            ErrorMessage = exception.Message,
            ErrorCode = exception.GetType().Name,
            ErrorType = errorType,
            FunctionName = functionName,
            Exception = exception
        };
    }

    /// <summary>
    /// Evaluates the RFC function result for SAP-specific error messages and updates the Success flag accordingly.
    /// </summary>
    private void EvaluateMessages()
    {
        if (Function == null) return;

        try
        {
            // Try to get messages from standard BAPIRET2 table
            Messages = Function.ToRfcResults("RETURN");

            // If no messages in RETURN table, try other common table names
            if (!Messages.Any())
            {
                Messages = Function.ToRfcResults("RESULTS");
            }

            // If still no messages, try to get error from export parameters
            if (!Messages.Any())
            {
                var errorResult = Function.ToRfcResult();
                if (errorResult != null)
                {
                    Messages.Add(errorResult);
                }
            }

            // Update success status based on messages
            if (Messages.HasErrors())
            {
                Success = false;
                ErrorType = SapRfcErrorType.BusinessLogic;
                if (string.IsNullOrEmpty(ErrorMessage))
                {
                    ErrorMessage = GetErrorSummary();
                }
            }
        }
        catch (Exception ex)
        {
            // If we can't evaluate messages, log it but don't fail the entire result
            // This preserves the original success status
            Messages.Add(new SapRfcResultItem
            {
                Type = "W",
                Id = "SYSTEM",
                Message = $"Could not evaluate SAP messages: {ex.Message}"
            });
        }
    }
}

/// <summary>
/// Represents the type of error that occurred during an RFC call.
/// </summary>
public enum SapRfcErrorType
{
    /// <summary>
    /// No error occurred.
    /// </summary>
    None,

    /// <summary>
    /// Communication error (network, connection issues).
    /// </summary>
    Communication,

    /// <summary>
    /// Authentication or authorization error.
    /// </summary>
    Authentication,

    /// <summary>
    /// Business logic error returned by SAP.
    /// </summary>
    BusinessLogic,

    /// <summary>
    /// ABAP exception thrown by the RFC function.
    /// </summary>
    AbapException,

    /// <summary>
    /// Invalid state or configuration error.
    /// </summary>
    InvalidState,

    /// <summary>
    /// System or unexpected error.
    /// </summary>
    System
}