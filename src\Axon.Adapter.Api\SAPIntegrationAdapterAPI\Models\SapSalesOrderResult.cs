namespace Axon.Adapter.Api.SAPIntegrationAdapterAPI.Models;

/// <summary>
/// Represents the result of a SAP sales order creation operation.
/// </summary>
public class SapSalesOrderResult : SapOperationResultBase
{
    /// <summary>
    /// The SAP sales order number that was created.
    /// </summary>
    public string? OrderNumber { get; set; }

    /// <summary>
    /// Initializes a new instance of SapSalesOrderResult.
    /// </summary>
    /// <param name="rfcResult">The RFC result to wrap</param>
    public SapSalesOrderResult(SapRfcResult rfcResult) : base(rfcResult)
    {
    }

    /// <summary>
    /// Creates a successful sales order result from an RfcResult.
    /// </summary>
    /// <param name="rfcResult">The RFC result</param>
    /// <param name="orderNumber">The created order number</param>
    /// <returns>A successful SapSalesOrderResult</returns>
    public static SapSalesOrderResult FromSuccess(SapRfcResult rfcResult, string? orderNumber = null)
    {
        var result = CreateSuccess<SapSalesOrderResult>(rfcResult);
        result.OrderNumber = orderNumber;
        return result;
    }

    /// <summary>
    /// Creates a failed sales order result from an RfcResult.
    /// </summary>
    /// <param name="rfcResult">The RFC result</param>
    /// <returns>A failed SapSalesOrderResult</returns>
    public static SapSalesOrderResult FromFailure(SapRfcResult rfcResult)
    {
        return CreateFailure<SapSalesOrderResult>(rfcResult);
    }

    /// <summary>
    /// Creates a failed sales order result with custom error information.
    /// </summary>
    /// <param name="errorMessage">The error message</param>
    /// <param name="errorCode">Optional error code</param>
    /// <param name="errorType">The type of error</param>
    /// <returns>A failed SapSalesOrderResult</returns>
    public static SapSalesOrderResult FromError(string errorMessage, string? errorCode = null, SapRfcErrorType errorType = SapRfcErrorType.BusinessLogic)
    {
        return CreateFailure<SapSalesOrderResult>(errorMessage, errorCode, errorType);
    }

    /// <summary>
    /// Gets a summary of the sales order information.
    /// </summary>
    /// <returns>A formatted string with order details</returns>
    public string GetOrderSummary()
    {
        if (!Success || string.IsNullOrEmpty(OrderNumber))
            return "No order information available";

        var parts = new List<string> { $"Order: {OrderNumber}" };
        
        if (!string.IsNullOrEmpty(FunctionName))
            parts.Add($"Function: {FunctionName}");
            
        if (HasWarnings)
            parts.Add($"Warnings: {Results.Count(r => r.Type == "W")}");

        return string.Join(", ", parts);
    }
}
