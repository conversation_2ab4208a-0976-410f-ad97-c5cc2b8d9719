namespace Axon.Adapter.Api.SAPIntegrationAdapterAPI.Models;

/// <summary>
/// Represents the result of a SAP thin sales order creation operation.
/// </summary>
public class SapSalesOrderThinResult : SapOperationResultBase
{
    /// <summary>
    /// The SAP sales order number that was created.
    /// </summary>
    public string? OrderNumber { get; set; }

    /// <summary>
    /// Initializes a new instance of SapSalesOrderThinResult.
    /// </summary>
    /// <param name="rfcResult">The RFC result to wrap</param>
    public SapSalesOrderThinResult(SapRfcResult rfcResult) : base(rfcResult)
    {
    }

    /// <summary>
    /// Creates a successful thin sales order result from an RfcResult.
    /// </summary>
    /// <param name="rfcResult">The RFC result</param>
    /// <param name="orderNumber">The created order number</param>
    /// <returns>A successful SapSalesOrderThinResult</returns>
    public static SapSalesOrderThinResult FromSuccess(SapRfcResult rfcResult, string? orderNumber = null)
    {
        var result = CreateSuccess<SapSalesOrderThinResult>(rfcResult);
        result.OrderNumber = orderNumber;
        return result;
    }

    /// <summary>
    /// Creates a failed thin sales order result from an RfcResult.
    /// </summary>
    /// <param name="rfcResult">The RFC result</param>
    /// <returns>A failed SapSalesOrderThinResult</returns>
    public static SapSalesOrderThinResult FromFailure(SapRfcResult rfcResult)
    {
        return CreateFailure<SapSalesOrderThinResult>(rfcResult);
    }

    /// <summary>
    /// Creates a failed thin sales order result with custom error information.
    /// </summary>
    /// <param name="errorMessage">The error message</param>
    /// <param name="errorCode">Optional error code</param>
    /// <param name="errorType">The type of error</param>
    /// <returns>A failed SapSalesOrderThinResult</returns>
    public static SapSalesOrderThinResult FromError(string errorMessage, string? errorCode = null, SapRfcErrorType errorType = SapRfcErrorType.BusinessLogic)
    {
        return CreateFailure<SapSalesOrderThinResult>(errorMessage, errorCode, errorType);
    }

    /// <summary>
    /// Gets a summary of the thin sales order information.
    /// </summary>
    /// <returns>A formatted string with order details</returns>
    public string GetOrderSummary()
    {
        if (!Success || string.IsNullOrEmpty(OrderNumber))
            return "No thin order information available";

        var parts = new List<string> { $"Thin Order: {OrderNumber}" };
        
        if (!string.IsNullOrEmpty(FunctionName))
            parts.Add($"Function: {FunctionName}");
            
        if (HasWarnings)
            parts.Add($"Warnings: {Results.Count(r => r.Type == "W")}");

        return string.Join(", ", parts);
    }
}
