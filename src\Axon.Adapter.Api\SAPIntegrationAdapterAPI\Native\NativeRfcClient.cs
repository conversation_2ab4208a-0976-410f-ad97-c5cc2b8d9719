using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Options;
using Microsoft.Extensions.Options;
using static Axon.Adapter.Api.SAPIntegrationAdapterAPI.Native.SapNwRfcSdk;

namespace Axon.Adapter.Api.SAPIntegrationAdapterAPI.Native;

/// <summary>
/// Native RFC client using SAP NW RFC SDK 7.50 via P/Invoke
/// </summary>
public class NativeRfcClient : IDisposable
{
    private readonly ILogger<NativeRfcClient> _logger;
    private readonly SapApiOptions _options;
    private IntPtr _connectionHandle = IntPtr.Zero;
    private bool _disposed = false;

    public NativeRfcClient(ILogger<NativeRfcClient> logger, IOptions<SapApiOptions> options)
    {
        _logger = logger;
        _options = options.Value;
    }

    /// <summary>
    /// Tests if the native SAP NW RFC SDK can be loaded and used
    /// </summary>
    public async Task<NativeRfcTestResult> TestConnectionAsync()
    {
        var result = new NativeRfcTestResult();
        
        try
        {
            _logger.LogInformation("Testing SAP NW RFC SDK 7.50 native connection...");

            // Step 1: Test library loading
            result.LibraryLoadSuccess = await TestLibraryLoadAsync();
            if (!result.LibraryLoadSuccess)
            {
                result.ErrorMessage = "Failed to load SAP NW RFC SDK library";
                return result;
            }

            // Step 2: Test connection
            result.ConnectionSuccess = await TestSapConnectionAsync();
            if (!result.ConnectionSuccess)
            {
                result.ErrorMessage = "Failed to connect to SAP system";
                return result;
            }

            // Step 3: Test ping
            result.PingSuccess = await TestSapPingAsync();
            if (!result.PingSuccess)
            {
                result.ErrorMessage = "Failed to ping SAP system";
                return result;
            }

            // Step 4: Get connection attributes
            result.ConnectionAttributes = await GetConnectionAttributesAsync();

            result.OverallSuccess = true;
            _logger.LogInformation("SAP NW RFC SDK 7.50 native connection test completed successfully");

        }
        catch (Exception ex)
        {
            result.ErrorMessage = ex.Message;
            result.Exception = ex;
            _logger.LogError(ex, "SAP NW RFC SDK 7.50 native connection test failed");
        }

        return result;
    }

    private async Task<bool> TestLibraryLoadAsync()
    {
        return await Task.Run(() =>
        {
            try
            {
                // Try to call a simple function to test if library loads
                var connectionParams = CreateConnectionParameters();
                var rc = RfcOpenConnection(connectionParams, (uint)connectionParams.Length, out var handle, out var errorInfo);
                
                // Even if connection fails, if we get here, the library loaded successfully
                if (handle != IntPtr.Zero)
                {
                    RfcCloseConnection(handle, out _);
                }

                _logger.LogInformation("SAP NW RFC SDK library loaded successfully. Connection attempt result: {Result}", rc);
                return true;
            }
            catch (DllNotFoundException ex)
            {
                _logger.LogError(ex, "SAP NW RFC SDK library not found. Make sure sapnwrfc library is available.");
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error testing SAP NW RFC SDK library load");
                return false;
            }
        });
    }

    private async Task<bool> TestSapConnectionAsync()
    {
        return await Task.Run(() =>
        {
            try
            {
                var connectionParams = CreateConnectionParameters();
                var rc = RfcOpenConnection(connectionParams, (uint)connectionParams.Length, out _connectionHandle, out var errorInfo);

                if (rc == RfcRc.RFC_OK)
                {
                    _logger.LogInformation("Successfully connected to SAP system");
                    return true;
                }
                else
                {
                    _logger.LogError("Failed to connect to SAP system. RC: {ReturnCode}, Error: {ErrorMessage}", 
                        rc, errorInfo.Message);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception during SAP connection test");
                return false;
            }
        });
    }

    private async Task<bool> TestSapPingAsync()
    {
        if (_connectionHandle == IntPtr.Zero)
            return false;

        return await Task.Run(() =>
        {
            try
            {
                var rc = RfcPing(_connectionHandle, out var errorInfo);

                if (rc == RfcRc.RFC_OK)
                {
                    _logger.LogInformation("Successfully pinged SAP system");
                    return true;
                }
                else
                {
                    _logger.LogError("Failed to ping SAP system. RC: {ReturnCode}, Error: {ErrorMessage}", 
                        rc, errorInfo.Message);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception during SAP ping test");
                return false;
            }
        });
    }

    private async Task<RfcConnectionAttributes?> GetConnectionAttributesAsync()
    {
        if (_connectionHandle == IntPtr.Zero)
            return null;

        return await Task.Run(() =>
        {
            try
            {
                var rc = RfcGetConnectionAttributes(_connectionHandle, out var attributes, out var errorInfo);

                if (rc == RfcRc.RFC_OK)
                {
                    _logger.LogInformation("Retrieved SAP connection attributes: System={SysId}, Client={Client}, User={User}",
                        attributes.SysId, attributes.Client, attributes.User);
                    return (RfcConnectionAttributes?)attributes;
                }
                else
                {
                    _logger.LogError("Failed to get SAP connection attributes. RC: {ReturnCode}, Error: {ErrorMessage}",
                        rc, errorInfo.Message);
                    return (RfcConnectionAttributes?)null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception getting SAP connection attributes");
                return (RfcConnectionAttributes?)null;
            }
        });
    }

    private RfcConnectionParameter[] CreateConnectionParameters()
    {
        return new[]
        {
            new RfcConnectionParameter { Name = "ASHOST", Value = _options.BaseAddress.Replace("http://", "").Replace("https://", "") },
            new RfcConnectionParameter { Name = "SYSNR", Value = _options.SystemNumber },
            new RfcConnectionParameter { Name = "CLIENT", Value = _options.Client },
            new RfcConnectionParameter { Name = "USER", Value = _options.User },
            new RfcConnectionParameter { Name = "PASSWD", Value = _options.Password },
            new RfcConnectionParameter { Name = "LANG", Value = _options.Language }
        };
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            if (_connectionHandle != IntPtr.Zero)
            {
                RfcCloseConnection(_connectionHandle, out _);
                _connectionHandle = IntPtr.Zero;
            }
            _disposed = true;
        }
    }
}

/// <summary>
/// Result of native RFC connection test
/// </summary>
public class NativeRfcTestResult
{
    public bool OverallSuccess { get; set; }
    public bool LibraryLoadSuccess { get; set; }
    public bool ConnectionSuccess { get; set; }
    public bool PingSuccess { get; set; }
    public RfcConnectionAttributes? ConnectionAttributes { get; set; }
    public string? ErrorMessage { get; set; }
    public Exception? Exception { get; set; }

    public string GetSummary()
    {
        if (OverallSuccess)
        {
            return $"✅ SAP NW RFC SDK 7.50 test successful - Connected to {ConnectionAttributes?.SysId} Client {ConnectionAttributes?.Client}";
        }
        else
        {
            return $"❌ SAP NW RFC SDK 7.50 test failed: {ErrorMessage}";
        }
    }
}
