using System.Runtime.InteropServices;

namespace Axon.Adapter.Api.SAPIntegrationAdapterAPI.Native;

/// <summary>
/// P/Invoke wrapper for SAP NW RFC SDK 7.50
/// </summary>
public static class SapNwRfcSdk
{
    // Library name varies by platform
    private const string LibraryName = "sapnwrfc";

    #region Enums and Structures

    public enum RfcRc
    {
        RFC_OK = 0,
        RFC_COMMUNICATION_FAILURE = 1,
        RFC_LOGON_FAILURE = 2,
        RFC_ABAP_RUNTIME_FAILURE = 3,
        RFC_ABAP_MESSAGE = 4,
        RFC_ABAP_EXCEPTION = 5,
        RFC_CLOSED = 6,
        RFC_CANCELED = 7,
        RFC_TIMEOUT = 8,
        RFC_MEMORY_INSUFFICIENT = 9,
        RFC_VERSION_MISMATCH = 10,
        RFC_INVALID_PROTOCOL = 11,
        RFC_SERIALIZATION_FAILURE = 12,
        RFC_INVALID_HANDLE = 13,
        RFC_RETRY = 14,
        RFC_EXTERNAL_FAILURE = 15,
        RFC_EXECUTED = 16,
        RFC_NOT_FOUND = 17,
        RFC_NOT_SUPPORTED = 18,
        RFC_ILLEGAL_STATE = 19,
        RFC_INVALID_PARAMETER = 20,
        RFC_CODEPAGE_CONVERSION_FAILURE = 21,
        RFC_CONVERSION_FAILURE = 22,
        RFC_BUFFER_TOO_SMALL = 23,
        RFC_TABLE_MOVE_BOF = 24,
        RFC_TABLE_MOVE_EOF = 25,
        RFC_START_SAPGUI_FAILURE = 26,
        RFC_ABAP_CLASS_EXCEPTION = 27,
        RFC_UNKNOWN_ERROR = 28,
        RFC_AUTHORIZATION_FAILURE = 29
    }

    [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Unicode)]
    public struct RfcConnectionParameter
    {
        [MarshalAs(UnmanagedType.LPWStr)]
        public string Name;
        
        [MarshalAs(UnmanagedType.LPWStr)]
        public string Value;
    }

    [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Unicode)]
    public struct RfcErrorInfo
    {
        public RfcRc Code;
        public RfcRc Group;
        
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 128)]
        public string Key;
        
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 512)]
        public string Message;
        
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 20)]
        public string AbapMsgClass;
        
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 3)]
        public string AbapMsgType;
        
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 3)]
        public string AbapMsgNumber;
        
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 50)]
        public string AbapMsgV1;
        
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 50)]
        public string AbapMsgV2;
        
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 50)]
        public string AbapMsgV3;
        
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 50)]
        public string AbapMsgV4;
    }

    #endregion

    #region Core RFC Functions

    /// <summary>
    /// Opens a connection to an SAP system
    /// </summary>
    [DllImport(LibraryName, CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Unicode)]
    public static extern RfcRc RfcOpenConnection(
        [In] RfcConnectionParameter[] connectionParams,
        uint paramCount,
        out IntPtr rfcHandle,
        out RfcErrorInfo errorInfo);

    /// <summary>
    /// Closes an RFC connection
    /// </summary>
    [DllImport(LibraryName, CallingConvention = CallingConvention.Cdecl)]
    public static extern RfcRc RfcCloseConnection(
        IntPtr rfcHandle,
        out RfcErrorInfo errorInfo);

    /// <summary>
    /// Pings the SAP system to test connectivity
    /// </summary>
    [DllImport(LibraryName, CallingConvention = CallingConvention.Cdecl)]
    public static extern RfcRc RfcPing(
        IntPtr rfcHandle,
        out RfcErrorInfo errorInfo);

    /// <summary>
    /// Gets connection attributes
    /// </summary>
    [DllImport(LibraryName, CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Unicode)]
    public static extern RfcRc RfcGetConnectionAttributes(
        IntPtr rfcHandle,
        out RfcConnectionAttributes attributes,
        out RfcErrorInfo errorInfo);

    #endregion

    #region Helper Structures

    [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Unicode)]
    public struct RfcConnectionAttributes
    {
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 64)]
        public string Dest;
        
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 100)]
        public string Host;
        
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 100)]
        public string PartnerHost;
        
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 10)]
        public string SysNumber;
        
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 8)]
        public string SysId;
        
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 3)]
        public string Client;
        
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 12)]
        public string User;
        
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 2)]
        public string Language;
        
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 4)]
        public string Trace;
        
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 8)]
        public string IsoLanguage;
        
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 4)]
        public string Codepage;
        
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 8)]
        public string PartnerCodepage;
        
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 1)]
        public string RfcRole;
        
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 1)]
        public string Type;
        
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 4)]
        public string PartnerType;
        
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 8)]
        public string Rel;
        
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 8)]
        public string PartnerRel;
        
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 8)]
        public string KernelRel;
        
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 40)]
        public string CpicConvId;
        
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 8)]
        public string ProgName;
        
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 128)]
        public string PartnerBytesPerChar;
        
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 1)]
        public string Reserved;
    }

    #endregion
}
