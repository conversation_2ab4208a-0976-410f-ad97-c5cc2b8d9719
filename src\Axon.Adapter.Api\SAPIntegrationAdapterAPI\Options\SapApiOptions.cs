// Suppressing these build warnings since we are using validation of IOptions
#pragma warning disable CS8618

using System.ComponentModel.DataAnnotations;
using System.ComponentModel;

namespace Axon.Adapter.Api.SAPIntegrationAdapterAPI.Options;

public class SapApiOptions
{
    public const string Key = "SapApi";

    [Display(Name = "Integration Enabled")]
    [Description("Master switch to enable/disable all SAP integration")]
    [DefaultValue(true)]
    public bool IntegrationEnabled { get; set; } = true;

    [Required]
    [Url]
    [Display(Name = "SAP Application Server")]
    [Description("Hostname or IP address of SAP application server")]
    [RegularExpression(@"^https?://[^\s/$.?#].[^\s]*$", ErrorMessage = "Invalid URL format")]
    public string BaseAddress { get; set; }

    [Required]
    [RegularExpression(@"^\d{2}$", ErrorMessage = "System number must be 2 digits (00-99)")]
    [Display(Name = "System Number")]
    [Description("SAP system number (e.g., 00, 01)")]
    public string SystemNumber { get; set; }

    [Required]
    [RegularExpression(@"^\d{3}$", ErrorMessage = "Client must be 3 digits")]
    [Display(Name = "Client")]
    [Description("SAP client number")]
    public string Client { get; set; }

    [Required]
    [Display(Name = "Username")]
    [Description("SAP RFC user account")]
    public string User { get; set; }
    
    [Required]
    [DataType(DataType.Password)]
    [Display(Name = "Password")]
    [Description("SAP user password")]
    public string Password { get; set; }

    [Display(Name = "Language")]
    [Description("SAP logon language")]
    [DefaultValue("EN")]
    [RegularExpression(@"^[A-Z]{2}$", ErrorMessage = "Language must be 2 uppercase letters")]
    public string Language { get; set; } = "EN";

    [Display(Name = "System ID")]
    [Description("SAP system identifier")]
    [RegularExpression(@"^[A-Z0-9]{3}$", ErrorMessage = "System ID must be 3 alphanumeric characters")]
    public string SystemId { get; set; }
    
    // Connection Pooling
    [Range(1, 50)]
    [DefaultValue(5)]
    [Display(Name = "Pool Size")]
    [Description("Initial connection pool size")]
    public int PoolSize { get; set; } = 5;

    [Range(1, 200)]
    [DefaultValue(50)]
    [Display(Name = "Max Pool Size")]
    [Description("Maximum connection pool size")]
    public int MaxPoolSize { get; set; } = 50;

    [Range(30, 3600)]
    [DefaultValue(600)]
    [Display(Name = "Idle Timeout (seconds)")]
    [Description("Connection idle timeout in seconds")]    
    public int IdleTimeout { get; set; } = 600;

    [Range(5, 300)]
    [DefaultValue(30)]
    [Display(Name = "Connection Timeout (seconds)")]
    [Description("Connection establishment timeout")]
    public int ConnectionTimeout { get; set; } = 30;

    // Security
    [Display(Name = "Use SNC")]
    [Description("Use Secure Network Communication")]
    public bool UseSnc { get; set; } = false;

    [Display(Name = "SNC Quality of Protection")]
    [Description("1=Auth, 2=Integrity, 3=Privacy, 8=Default, 9=Maximum")]
    [RegularExpression(@"^[1-3,8-9]$")]
    public string SncQop { get; set; }

    [Display(Name = "SNC Identity")]
    [Description("Your SNC identity")]
    public string SncIdentity { get; set; }

    [Display(Name = "SNC Partner Name")]
    [Description("SAP system SNC identity")]
    public string SncPartnerName { get; set; }

    [Display(Name = "SNC Library Path")]
    [Description("Path to SNC library (optional)")]
    public string SncLib { get; set; }

    // Advanced Options
    [Display(Name = "Use SAP Router")]
    [Description("Connect through SAP Router")]
    public bool UseRouter { get; set; } = false;

    [Display(Name = "SAP Router Connection String")]
    [Description("SAP Router connection string (e.g., /H/router.host.com/S/3299/H/)")]
    public string RouterConnection { get; set; }

    [Display(Name = "Use Load Balancing")]
    [Description("Use SAP message server for load balancing")]
    public bool UseLoadBalancing { get; set; } = false;

    [Display(Name = "Message Server")]
    [Description("SAP message server hostname")]
    public string MessageServer { get; set; }

    [Display(Name = "Logon Group")]
    [Description("SAP logon group name")]
    public string Group { get; set; }

    [Display(Name = "R/3 Name")]
    [Description("SAP system R/3 name")]
    public string R3Name { get; set; }

    [Display(Name = "Trace Level")]
    [Description("RFC trace level (0=off, 1=brief, 2=verbose, 3=full)")]
    [Range(0, 3)]
    [DefaultValue(0)]
    public int TraceLevel { get; set; } = 0;
    
    [Display(Name = "Trace Directory")]
    [Description("Directory for RFC trace files")]
    public string TraceDirectory { get; set; }
    
    [Display(Name = "Code Page")]
    [Description("Character encoding (leave empty for default)")]
    public string Codepage { get; set; }
    
    [Display(Name = "PCS")]
    [Description("Partner Character Set")]
    public string Pcs { get; set; }
    
    [Display(Name = "Check")]
    [Description("Enable/disable version check")]
    [DefaultValue("1")]
    public string Check { get; set; } = "1";
} 
