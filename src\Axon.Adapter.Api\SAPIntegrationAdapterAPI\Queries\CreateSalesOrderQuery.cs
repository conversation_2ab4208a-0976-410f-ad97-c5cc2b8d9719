using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Axon.Adapter.Api.SAPIntegrationAdapterAPI.Queries
{
    public class CreateSalesOrderQuery
    {
        [JsonPropertyName("CUSTOMER_NUMBER_SH")]
        public string? CustomerNumberSh { get; set; } = string.Empty;

        [JsonPropertyName("CUSTOMER_NUMBER_SP")]
        public string CustomerNumberSp { get; set; } = string.Empty;

        [JsonPropertyName("CUSTOMER_PO_NUM")]
        public string CustomerPoNum { get; set; } = string.Empty;

        [JsonPropertyName("SHIP_TYPE")]
        public string ShipType { get; set; } = string.Empty;

        [JsonPropertyName("NAME_2")]
        public string? Name2 { get; set; }

        [JsonPropertyName("ORDER_BY_NAME")]
        public string? OrderByName { get; set; }

        [JsonPropertyName("ORDER_BY_PHONE")]
        public string? OrderByPhone { get; set; }

        [JsonPropertyName("RUSH_ORDER")]
        public string? RushOrder { get; set; }

        [JsonPropertyName("ORDER_SRC")]
        public string? OrderSrc { get; set; }

        [JsonPropertyName("SHIP_TO_ADDR")]
        public ShipToAddr? ShipToAddr { get; set; }

        [JsonPropertyName("MATERIAL_ITEMS")]
        public List<MaterialItem> MaterialItems { get; set; } = new();

        [JsonPropertyName("ORDER_TEXT")]
        public List<OrderText>? OrderText { get; set; }
    }

    public class ShipToAddr
    {
        [JsonPropertyName("NAME1")]
        public string Name1 { get; set; } = string.Empty;

        [JsonPropertyName("NAME2")]
        public string? Name2 { get; set; }

        [JsonPropertyName("STREET1")]
        public string Street1 { get; set; } = string.Empty;

        [JsonPropertyName("STREET2")]
        public string? Street2 { get; set; }

        [JsonPropertyName("CITY")]
        public string City { get; set; } = string.Empty;

        [JsonPropertyName("REGION")]
        public string Region { get; set; } = string.Empty;

        [JsonPropertyName("ZIP_CODE")]
        public string ZipCode { get; set; } = string.Empty;

        [JsonPropertyName("COUNTRY")]
        public string Country { get; set; } = string.Empty;

        [JsonPropertyName("TEL1_NUMBR")]
        public string? Tel1Numbr { get; set; }

        [JsonPropertyName("E_MAIL")]
        public string? Email { get; set; }

        [JsonPropertyName("FAX_NUMBER")]
        public string? FaxNumber { get; set; }

        [JsonPropertyName("PLTYP")]
        public string? PLTYP { get; set; }

        [JsonPropertyName("RISK_CAT")]
        public string? RiskCategory { get; set; }

        [JsonPropertyName("SALES_DISTRICT")]
        public string? SalesDistrict { get; set; }
    }

    public class MaterialItem
    {
        [JsonPropertyName("MATNR")]
        public string Matnr { get; set; } = string.Empty;

        [JsonPropertyName("TARGET_QTY")]
        public string TargetQty { get; set; } = string.Empty;

        // [JsonPropertyName("PRICE_PER")]
        // public decimal? PricePer { get; set; }
    }

    public class ItCondition
    {
        [JsonPropertyName("KSCHL")]
        public string? Kschl { get; set; }

        [JsonPropertyName("ZZAMOUNT")]
        public string? ZzAmount { get; set; }
    }

    public class OrderText
    {
        [JsonPropertyName("TEXT_LINE")]
        public string TextLine { get; set; } = string.Empty;
    }
} 