using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Axon.Adapter.Api.SAPIntegrationAdapterAPI.Queries
{
    public class CreateSalesOrderThinQuery
    {
        [JsonPropertyName("SOLD_TO_NUM")]
        public string? SoldToNumber { get; set; } = string.Empty;

        [JsonPropertyName("SHIP_TO_NUM")]
        public string ShipToNumber { get; set; } = string.Empty;

        [JsonPropertyName("ORDER_BY_NAME")]
        public string? OrderByName { get; set; } = string.Empty;

        [JsonPropertyName("ORDER_BY_PHONE")]
        public string? OrderByPhone { get; set; }

        [JsonPropertyName("CUST_PO_NUM")]
        public string? CustomerPoNumber { get; set; }

        [JsonPropertyName("SHIPPING_TYPE")]
        public string? ShippingType { get; set; } = string.Empty;

        // [JsonPropertyName("ORDER_SRC")]
        // public string? OrderSource { get; set; }

        [JsonPropertyName("SHIP_TO_ADDR")]
        public ShipToAddress? ShipToAddress { get; set; }

        [JsonPropertyName("ORDER_ITEMS")]
        public List<OrderItem> OrderItems { get; set; } = new();
    }

    public class ShipToAddress
    {
        [JsonPropertyName("NAME1")]
        public string Name1 { get; set; } = string.Empty;

        [JsonPropertyName("NAME2")]
        public string? Name2 { get; set; }

        [JsonPropertyName("STREET1")]
        public string Street1 { get; set; } = string.Empty;

        [JsonPropertyName("STREET2")]
        public string? Street2 { get; set; }

        [JsonPropertyName("CITY")]
        public string City { get; set; } = string.Empty;

        [JsonPropertyName("STATE")]
        public string State { get; set; } = string.Empty;

        [JsonPropertyName("ZIP_CODE")]
        public string ZipCode { get; set; } = string.Empty;

        [JsonPropertyName("COUNTRY")]
        public string Country { get; set; } = string.Empty;

        [JsonPropertyName("PHONE_NUM")]
        public string? PhoneNumber { get; set; }

        [JsonPropertyName("EMAIL_ADDR")]
        public string? EmailAddress { get; set; }
    }

    public class OrderItem
    {
        [JsonPropertyName("MATERIAL_NUM")]
        public string MaterialNumber { get; set; } = string.Empty;

        [JsonPropertyName("ORDER_QTY")]
        public string OrderQuantity { get; set; } = string.Empty;

        [JsonPropertyName("ITEM_AMT")]
        public decimal ItemAmount { get; set; }
    }
} 