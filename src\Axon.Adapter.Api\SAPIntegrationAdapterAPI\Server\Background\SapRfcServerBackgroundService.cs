using Microsoft.Extensions.Options;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Options;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Server.Services;

namespace Axon.Adapter.Api.SAPIntegrationAdapterAPI.Server.Background;

/// <summary>
/// Background service to manage the SAP RFC Server lifecycle
/// </summary>
public class SapRfcServerBackgroundService : BackgroundService
{
    private readonly ILogger<SapRfcServerBackgroundService> _logger;
    private readonly ISapRfcServerService _rfcServerService;
    private readonly SapApiOptions _sapOptions;

    public SapRfcServerBackgroundService(
        ILogger<SapRfcServerBackgroundService> logger,
        ISapRfcServerService rfcServerService,
        IOptions<SapApiOptions> sapOptions)
    {
        _logger = logger;
        _rfcServerService = rfcServerService;
        _sapOptions = sapOptions.Value;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        // Only start RFC server if SAP integration is enabled
        if (!_sapOptions.IntegrationEnabled)
        {
            _logger.LogInformation("SAP integration is disabled. RFC Server will not start.");
            return;
        }

        try
        {
            _logger.LogInformation("Starting SAP RFC Server background service");
            
            // Start the RFC server
            await _rfcServerService.StartAsync(stoppingToken);
            
            _logger.LogInformation("SAP RFC Server background service started successfully");

            // Keep the service running until cancellation is requested
            while (!stoppingToken.IsCancellationRequested)
            {
                await Task.Delay(TimeSpan.FromSeconds(30), stoppingToken);
                
                // Log server status periodically
                if (_rfcServerService.IsRunning)
                {
                    _logger.LogDebug("SAP RFC Server is running");
                }
                else
                {
                    _logger.LogWarning("SAP RFC Server is not running");
                }
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("SAP RFC Server background service is stopping");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in SAP RFC Server background service");
            throw;
        }
        finally
        {
            // Stop the RFC server when the service is stopping
            if (_rfcServerService.IsRunning)
            {
                _logger.LogInformation("Stopping SAP RFC Server");
                await _rfcServerService.StopAsync(stoppingToken);
                _logger.LogInformation("SAP RFC Server stopped");
            }
        }
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("SAP RFC Server background service is stopping");
        
        // Stop the RFC server
        if (_rfcServerService.IsRunning)
        {
            await _rfcServerService.StopAsync(cancellationToken);
        }
        
        await base.StopAsync(cancellationToken);
    }
} 