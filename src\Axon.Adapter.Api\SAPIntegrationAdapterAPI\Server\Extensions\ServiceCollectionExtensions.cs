using System.Reflection;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Server.Handlers;

namespace Axon.Adapter.Api.SAPIntegrationAdapterAPI.Server.Extensions;

/// <summary>
/// Extension methods for service collection to register RFC handlers automatically
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Registers all RFC function handlers automatically using reflection
    /// </summary>
    public static IServiceCollection AddRfcHandlers(this IServiceCollection services, Assembly? assembly = null)
    {
        assembly ??= Assembly.GetExecutingAssembly();
        
        var handlerTypes = assembly.GetTypes()
            .Where(type => type.IsClass && !type.IsAbstract && 
                          typeof(IRfcFunctionHandler).IsAssignableFrom(type))
            .ToList();

        foreach (var handlerType in handlerTypes)
        {
            services.AddTransient(typeof(IRfcFunctionHandler), handlerType);
            services.AddTransient(handlerType);
        }

        return services;
    }
}
