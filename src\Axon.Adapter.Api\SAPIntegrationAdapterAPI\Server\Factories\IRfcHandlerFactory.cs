using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Server.Handlers;

namespace Axon.Adapter.Api.SAPIntegrationAdapterAPI.Server.Factories;

/// <summary>
/// Factory for creating RFC function handlers
/// </summary>
public interface IRfcHandlerFactory
{
    /// <summary>
    /// Gets all registered RFC function handlers
    /// </summary>
    IEnumerable<IRfcFunctionHandler> GetAllHandlers();
    
    /// <summary>
    /// Gets a handler by function name
    /// </summary>
    IRfcFunctionHandler? GetHandler(string functionName);
    
    /// <summary>
    /// Gets all function names that have registered handlers
    /// </summary>
    IEnumerable<string> GetRegisteredFunctionNames();
}

/// <summary>
/// Implementation of RFC handler factory using dependency injection
/// </summary>
public class RfcHandlerFactory : IRfcHandlerFactory
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<RfcHandlerFactory> _logger;
    private readonly Dictionary<string, Type> _handlerTypes;

    public RfcHandlerFactory(IServiceProvider serviceProvider, ILogger<RfcHandlerFactory> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
        _handlerTypes = DiscoverHandlers();
    }

    public IEnumerable<IRfcFunctionHandler> GetAllHandlers()
    {
        var handlers = new List<IRfcFunctionHandler>();
        
        foreach (var handlerType in _handlerTypes.Values)
        {
            try
            {
                var handler = (IRfcFunctionHandler)_serviceProvider.GetRequiredService(handlerType);
                handlers.Add(handler);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create handler of type {HandlerType}", handlerType.Name);
            }
        }
        
        return handlers;
    }

    public IRfcFunctionHandler? GetHandler(string functionName)
    {
        if (!_handlerTypes.TryGetValue(functionName, out var handlerType))
        {
            return null;
        }

        try
        {
            return (IRfcFunctionHandler)_serviceProvider.GetRequiredService(handlerType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create handler for function {FunctionName}", functionName);
            return null;
        }
    }

    public IEnumerable<string> GetRegisteredFunctionNames()
    {
        return _handlerTypes.Keys;
    }

    private Dictionary<string, Type> DiscoverHandlers()
    {
        var handlerTypes = new Dictionary<string, Type>();
        
        // Get all registered IRfcFunctionHandler implementations
        var handlers = _serviceProvider.GetServices<IRfcFunctionHandler>();
        
        foreach (var handler in handlers)
        {
            var handlerType = handler.GetType();
            var functionName = handler.FunctionName;
            
            if (handlerTypes.ContainsKey(functionName))
            {
                _logger.LogWarning("Duplicate handler found for function {FunctionName}. Using {HandlerType}", 
                    functionName, handlerType.Name);
            }
            
            handlerTypes[functionName] = handlerType;
            _logger.LogDebug("Discovered handler {HandlerType} for function {FunctionName}", 
                handlerType.Name, functionName);
        }
        
        return handlerTypes;
    }
}
