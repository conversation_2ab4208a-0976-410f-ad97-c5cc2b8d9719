using SAP.Middleware.Connector;
using System.Diagnostics;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Server.Telemetry;

namespace Axon.Adapter.Api.SAPIntegrationAdapterAPI.Server.Handlers;

/// <summary>
/// Interface for handling RFC function calls from SAP
/// </summary>
public interface IRfcFunctionHandler
{
    /// <summary>
    /// The name of the RFC function this handler processes
    /// </summary>
    string FunctionName { get; }
    
    /// <summary>
    /// Handles the RFC function call
    /// </summary>
    /// <param name="function">The RFC function with input parameters</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    Task HandleAsync(IRfcFunction function, CancellationToken cancellationToken = default);
}

/// <summary>
/// Base class for RFC function handlers providing common functionality
/// </summary>
public abstract class RfcFunctionHandlerBase : IRfcFunctionHandler
{
    protected readonly ILogger Logger;
    protected readonly RfcTelemetry? Telemetry;

    protected RfcFunctionHandlerBase(ILogger logger, RfcTelemetry? telemetry = null)
    {
        Logger = logger;
        Telemetry = telemetry;
    }

    public abstract string FunctionName { get; }

    public virtual async Task HandleAsync(IRfcFunction function, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        using var activity = Telemetry?.StartActivity(FunctionName, GetType().Name);

        try
        {
            Logger.LogInformation("Processing RFC function {FunctionName}", FunctionName);

            await HandleAsyncCore(function, cancellationToken);

            stopwatch.Stop();
            Telemetry?.RecordRequest(FunctionName, stopwatch.Elapsed.TotalSeconds, GetType().Name);

            Logger.LogInformation("Successfully processed RFC function {FunctionName} in {Duration}ms",
                FunctionName, stopwatch.ElapsedMilliseconds);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            Telemetry?.RecordError(FunctionName, ex.GetType().Name, stopwatch.Elapsed.TotalSeconds, GetType().Name);

            Logger.LogError(ex, "Failed to process RFC function {FunctionName} after {Duration}ms",
                FunctionName, stopwatch.ElapsedMilliseconds);

            activity?.SetStatus(ActivityStatusCode.Error, ex.Message);
            throw;
        }
    }

    /// <summary>
    /// Core implementation of the handler logic
    /// </summary>
    protected abstract Task HandleAsyncCore(IRfcFunction function, CancellationToken cancellationToken = default);

    /// <summary>
    /// Sets a success response in the RFC function
    /// </summary>
    protected void SetSuccessResponse(IRfcFunction function, string message = "Success")
    {
        try
        {
            function.SetValue("SUCCESS", "X");
            function.SetValue("MESSAGE", message);
        }
        catch (Exception ex)
        {
            Logger.LogWarning(ex, "Failed to set success response parameters for function {FunctionName}", FunctionName);
        }
    }

    /// <summary>
    /// Sets an error response in the RFC function
    /// </summary>
    protected void SetErrorResponse(IRfcFunction function, string message, Exception? exception = null)
    {
        try
        {
            function.SetValue("SUCCESS", "");
            function.SetValue("MESSAGE", message);
            
            if (exception != null)
            {
                Logger.LogError(exception, "Error in RFC function {FunctionName}: {Message}", FunctionName, message);
            }
            else
            {
                Logger.LogWarning("RFC function {FunctionName} returned error: {Message}", FunctionName, message);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to set error response parameters for function {FunctionName}", FunctionName);
        }
    }

    /// <summary>
    /// Safely gets a string value from the RFC function
    /// </summary>
    protected string? GetStringValue(IRfcFunction function, string parameterName)
    {
        try
        {
            return function.GetValue(parameterName)?.ToString();
        }
        catch (Exception ex)
        {
            Logger.LogWarning(ex, "Failed to get parameter {ParameterName} from function {FunctionName}", 
                parameterName, FunctionName);
            return null;
        }
    }
}
