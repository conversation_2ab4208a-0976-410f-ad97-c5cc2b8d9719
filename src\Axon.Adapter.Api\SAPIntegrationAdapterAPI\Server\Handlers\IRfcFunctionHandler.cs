using SAP.Middleware.Connector;

namespace Axon.Adapter.Api.SAPIntegrationAdapterAPI.Server.Handlers;

/// <summary>
/// Interface for handling RFC function calls from SAP
/// </summary>
public interface IRfcFunctionHandler
{
    /// <summary>
    /// The name of the RFC function this handler processes
    /// </summary>
    string FunctionName { get; }
    
    /// <summary>
    /// Handles the RFC function call
    /// </summary>
    /// <param name="function">The RFC function with input parameters</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    Task HandleAsync(IRfcFunction function, CancellationToken cancellationToken = default);
}

/// <summary>
/// Base class for RFC function handlers providing common functionality
/// </summary>
public abstract class RfcFunctionHandlerBase : IRfcFunctionHandler
{
    protected readonly ILogger Logger;

    protected RfcFunctionHandlerBase(ILogger logger)
    {
        Logger = logger;
    }

    public abstract string FunctionName { get; }

    public abstract Task HandleAsync(IRfcFunction function, CancellationToken cancellationToken = default);

    /// <summary>
    /// Sets a success response in the RFC function
    /// </summary>
    protected void SetSuccessResponse(IRfcFunction function, string message = "Success")
    {
        try
        {
            function.SetValue("SUCCESS", "X");
            function.SetValue("MESSAGE", message);
        }
        catch (Exception ex)
        {
            Logger.LogWarning(ex, "Failed to set success response parameters for function {FunctionName}", FunctionName);
        }
    }

    /// <summary>
    /// Sets an error response in the RFC function
    /// </summary>
    protected void SetErrorResponse(IRfcFunction function, string message, Exception? exception = null)
    {
        try
        {
            function.SetValue("SUCCESS", "");
            function.SetValue("MESSAGE", message);
            
            if (exception != null)
            {
                Logger.LogError(exception, "Error in RFC function {FunctionName}: {Message}", FunctionName, message);
            }
            else
            {
                Logger.LogWarning("RFC function {FunctionName} returned error: {Message}", FunctionName, message);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to set error response parameters for function {FunctionName}", FunctionName);
        }
    }

    /// <summary>
    /// Safely gets a string value from the RFC function
    /// </summary>
    protected string? GetStringValue(IRfcFunction function, string parameterName)
    {
        try
        {
            return function.GetValue(parameterName)?.ToString();
        }
        catch (Exception ex)
        {
            Logger.LogWarning(ex, "Failed to get parameter {ParameterName} from function {FunctionName}",
                parameterName, FunctionName);
            return null;
        }
    }

    /// <summary>
    /// Gets a required string value from RFC function parameters
    /// </summary>
    protected string GetRequiredStringValue(IRfcFunction function, string parameterName)
    {
        var value = GetStringValue(function, parameterName);
        if (string.IsNullOrWhiteSpace(value))
        {
            throw new ArgumentException($"Required parameter '{parameterName}' is missing or empty");
        }
        return value;
    }

    /// <summary>
    /// Validates that all required parameters are present
    /// </summary>
    protected bool ValidateRequiredParameters(IRfcFunction function, params string[] parameterNames)
    {
        var missingParameters = new List<string>();

        foreach (var parameterName in parameterNames)
        {
            var value = GetStringValue(function, parameterName);
            if (string.IsNullOrWhiteSpace(value))
            {
                missingParameters.Add(parameterName);
            }
        }

        if (missingParameters.Any())
        {
            var errorMessage = $"Missing required parameters: {string.Join(", ", missingParameters)}";
            SetErrorResponse(function, errorMessage);
            Logger.LogWarning("RFC function {FunctionName} validation failed: {Error}",
                FunctionName, errorMessage);
            return false;
        }

        return true;
    }
}
