using SAP.Middleware.Connector;
using MassTransit;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Models.Events;

namespace Axon.Adapter.Api.SAPIntegrationAdapterAPI.Server.Handlers;

/// <summary>
/// Handler for SAP order status update events
/// </summary>
public class OrderStatusUpdatedHandler : RfcFunctionHandlerBase
{
    private readonly IPublishEndpoint _publishEndpoint;

    public override string FunctionName => "ZMM_PUSH_ORDER_STATUS_EVENT";

    public OrderStatusUpdatedHandler(
        ILogger<OrderStatusUpdatedHandler> logger,
        IPublishEndpoint publishEndpoint) : base(logger)
    {
        _publishEndpoint = publishEndpoint;
    }

    public override async Task HandleAsync(IRfcFunction function, CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogInformation("Processing order status update event from SAP");

            var orderNumber = GetStringValue(function, "ORDER_NUMBER");
            var status = GetStringValue(function, "STATUS");

            if (string.IsNullOrEmpty(orderNumber) || string.IsNullOrEmpty(status))
            {
                SetErrorResponse(function, "Missing required parameters: ORDER_NUMBER or STATUS");
                return;
            }

            var sapEvent = new SapOrderStatusUpdatedEvent
            {
                OrderNumber = orderNumber,
                Status = status,
                Timestamp = DateTimeOffset.UtcNow
            };

            await _publishEndpoint.Publish(sapEvent, cancellationToken);
            
            SetSuccessResponse(function, "Order status update event processed successfully");
            
            Logger.LogInformation("Published order status update event for order {OrderNumber} with status {Status}", 
                orderNumber, status);
        }
        catch (Exception ex)
        {
            SetErrorResponse(function, $"Failed to process order status update: {ex.Message}", ex);
        }
    }
}

/// <summary>
/// Handler for SAP material update events
/// </summary>
public class MaterialUpdatedHandler : RfcFunctionHandlerBase
{
    private readonly IPublishEndpoint _publishEndpoint;

    public override string FunctionName => "ZMM_PUSH_MATERIAL_UPDATE_EVENT";

    public MaterialUpdatedHandler(
        ILogger<MaterialUpdatedHandler> logger,
        IPublishEndpoint publishEndpoint) : base(logger)
    {
        _publishEndpoint = publishEndpoint;
    }

    public override async Task HandleAsync(IRfcFunction function, CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogInformation("Processing material update event from SAP");

            var materialNumber = GetStringValue(function, "MATERIAL_NUMBER");
            var updateType = GetStringValue(function, "UPDATE_TYPE");

            if (string.IsNullOrEmpty(materialNumber) || string.IsNullOrEmpty(updateType))
            {
                SetErrorResponse(function, "Missing required parameters: MATERIAL_NUMBER or UPDATE_TYPE");
                return;
            }

            var sapEvent = new SapMaterialUpdatedEvent
            {
                MaterialNumber = materialNumber,
                UpdateType = updateType,
                Timestamp = DateTimeOffset.UtcNow
            };

            await _publishEndpoint.Publish(sapEvent, cancellationToken);
            
            SetSuccessResponse(function, "Material update event processed successfully");
            
            Logger.LogInformation("Published material update event for material {MaterialNumber} with update type {UpdateType}", 
                materialNumber, updateType);
        }
        catch (Exception ex)
        {
            SetErrorResponse(function, $"Failed to process material update: {ex.Message}", ex);
        }
    }
}

/// <summary>
/// Handler for SAP customer change events
/// </summary>
public class CustomerChangedHandler : RfcFunctionHandlerBase
{
    private readonly IPublishEndpoint _publishEndpoint;

    public override string FunctionName => "ZMM_PUSH_CUSTOMER_CHANGE_EVENT";

    public CustomerChangedHandler(
        ILogger<CustomerChangedHandler> logger,
        IPublishEndpoint publishEndpoint) : base(logger)
    {
        _publishEndpoint = publishEndpoint;
    }

    public override async Task HandleAsync(IRfcFunction function, CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogInformation("Processing customer change event from SAP");

            var customerNumber = GetStringValue(function, "CUSTOMER_NUMBER");
            var changeType = GetStringValue(function, "CHANGE_TYPE");

            if (string.IsNullOrEmpty(customerNumber) || string.IsNullOrEmpty(changeType))
            {
                SetErrorResponse(function, "Missing required parameters: CUSTOMER_NUMBER or CHANGE_TYPE");
                return;
            }

            var sapEvent = new SapCustomerChangedEvent
            {
                CustomerNumber = customerNumber,
                ChangeType = changeType,
                Timestamp = DateTimeOffset.UtcNow
            };

            await _publishEndpoint.Publish(sapEvent, cancellationToken);
            
            SetSuccessResponse(function, "Customer change event processed successfully");
            
            Logger.LogInformation("Published customer change event for customer {CustomerNumber} with change type {ChangeType}", 
                customerNumber, changeType);
        }
        catch (Exception ex)
        {
            SetErrorResponse(function, $"Failed to process customer change: {ex.Message}", ex);
        }
    }
}

/// <summary>
/// Handler for SAP test events (for POC validation)
/// </summary>
public class TestEventHandler : RfcFunctionHandlerBase
{
    private readonly IPublishEndpoint _publishEndpoint;

    public override string FunctionName => "ZMM_PUSH_TEST_EVENT";

    public TestEventHandler(
        ILogger<TestEventHandler> logger,
        IPublishEndpoint publishEndpoint) : base(logger)
    {
        _publishEndpoint = publishEndpoint;
    }

    public override async Task HandleAsync(IRfcFunction function, CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogInformation("Processing test event from SAP");

            var testMessage = GetStringValue(function, "TEST_MESSAGE") ?? "Default test message";

            var sapEvent = new SapTestEvent
            {
                Message = testMessage,
                Timestamp = DateTimeOffset.UtcNow
            };

            await _publishEndpoint.Publish(sapEvent, cancellationToken);
            
            SetSuccessResponse(function, "Test event processed successfully");
            
            Logger.LogInformation("Published test event with message: {Message}", testMessage);
        }
        catch (Exception ex)
        {
            SetErrorResponse(function, $"Failed to process test event: {ex.Message}", ex);
        }
    }
}
