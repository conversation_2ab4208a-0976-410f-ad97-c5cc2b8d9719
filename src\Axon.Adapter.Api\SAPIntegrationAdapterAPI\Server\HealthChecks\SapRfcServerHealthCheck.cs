using Microsoft.Extensions.Diagnostics.HealthChecks;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Server.Services;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Server.Options;
using Microsoft.Extensions.Options;

namespace Axon.Adapter.Api.SAPIntegrationAdapterAPI.Server.HealthChecks;

/// <summary>
/// Health check for SAP RFC Server
/// </summary>
public class SapRfcServerHealthCheck : IHealthCheck
{
    private readonly ISapRfcServerService _rfcServerService;
    private readonly SapRfcServerOptions _options;
    private readonly ILogger<SapRfcServerHealthCheck> _logger;

    public SapRfcServerHealthCheck(
        ISapRfcServerService rfcServerService,
        IOptions<SapRfcServerOptions> options,
        ILogger<SapRfcServerHealthCheck> logger)
    {
        _rfcServerService = rfcServerService;
        _options = options.Value;
        _logger = logger;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(
        HealthCheckContext context, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            // If RFC server is disabled, it's healthy by definition
            if (!_options.Enabled)
            {
                return HealthCheckResult.Healthy("RFC Server is disabled");
            }

            // Check if server is running
            if (!_rfcServerService.IsRunning)
            {
                return HealthCheckResult.Unhealthy("RFC Server is not running");
            }

            // Perform detailed health check
            var isHealthy = await _rfcServerService.IsHealthyAsync();
            
            if (!isHealthy)
            {
                return HealthCheckResult.Degraded("RFC Server is running but not healthy");
            }

            var data = new Dictionary<string, object>
            {
                ["server_name"] = _options.ServerName,
                ["gateway_host"] = _options.GatewayHost,
                ["gateway_service"] = _options.GatewayService,
                ["program_id"] = _options.ProgramId,
                ["max_connections"] = _options.MaxConnections,
                ["is_running"] = _rfcServerService.IsRunning,
                ["last_check"] = DateTimeOffset.UtcNow
            };

            return HealthCheckResult.Healthy("RFC Server is running and healthy", data);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Health check failed for RFC Server");
            
            return HealthCheckResult.Unhealthy(
                "RFC Server health check failed", 
                ex, 
                new Dictionary<string, object>
                {
                    ["error"] = ex.Message,
                    ["last_check"] = DateTimeOffset.UtcNow
                });
        }
    }
}
