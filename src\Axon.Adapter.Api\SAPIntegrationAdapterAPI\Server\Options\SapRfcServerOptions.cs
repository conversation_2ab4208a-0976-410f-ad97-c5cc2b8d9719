using System.ComponentModel.DataAnnotations;

namespace Axon.Adapter.Api.SAPIntegrationAdapterAPI.Server.Options;

/// <summary>
/// Configuration options for SAP RFC Server
/// </summary>
public class SapRfcServerOptions
{
    public const string SectionName = "SapRfcServer";

    /// <summary>
    /// Whether the RFC server is enabled
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// RFC server name
    /// </summary>
    [Required]
    [MinLength(1)]
    public string ServerName { get; set; } = "AXON_RFC_SERVER";

    /// <summary>
    /// Gateway host for RFC server
    /// </summary>
    [Required]
    [MinLength(1)]
    public string GatewayHost { get; set; } = string.Empty;

    /// <summary>
    /// Gateway service for RFC server
    /// </summary>
    [Required]
    [MinLength(1)]
    public string GatewayService { get; set; } = "sapgw00";

    /// <summary>
    /// Program ID for RFC server registration
    /// </summary>
    [Required]
    [MinLength(1)]
    public string ProgramId { get; set; } = string.Empty;

    /// <summary>
    /// Repository destination for RFC server
    /// </summary>
    [Required]
    [MinLength(1)]
    public string RepositoryDestination { get; set; } = string.Empty;

    /// <summary>
    /// Maximum number of connections
    /// </summary>
    [Range(1, 100)]
    public int MaxConnections { get; set; } = 10;

    /// <summary>
    /// Connection timeout in seconds
    /// </summary>
    [Range(1, 300)]
    public int ConnectionTimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// Health check interval in seconds
    /// </summary>
    [Range(10, 3600)]
    public int HealthCheckIntervalSeconds { get; set; } = 30;

    /// <summary>
    /// Whether to enable detailed logging
    /// </summary>
    public bool EnableDetailedLogging { get; set; } = false;

    /// <summary>
    /// Validates the configuration
    /// </summary>
    public IEnumerable<ValidationResult> Validate()
    {
        var results = new List<ValidationResult>();
        var context = new ValidationContext(this);
        
        Validator.TryValidateObject(this, context, results, true);
        
        // Additional custom validations
        if (string.IsNullOrWhiteSpace(GatewayHost))
        {
            results.Add(new ValidationResult("GatewayHost is required", new[] { nameof(GatewayHost) }));
        }
        
        if (string.IsNullOrWhiteSpace(ProgramId))
        {
            results.Add(new ValidationResult("ProgramId is required", new[] { nameof(ProgramId) }));
        }
        
        return results;
    }

    /// <summary>
    /// Gets a summary of the configuration for logging
    /// </summary>
    public string GetConfigurationSummary()
    {
        return $"RFC Server: {ServerName}, Gateway: {GatewayHost}:{GatewayService}, " +
               $"ProgramId: {ProgramId}, MaxConnections: {MaxConnections}";
    }
}
