using Polly;
using Polly.Extensions.Http;
using SAP.Middleware.Connector;

namespace Axon.Adapter.Api.SAPIntegrationAdapterAPI.Server.Resilience;

/// <summary>
/// Resilience policies for RFC operations
/// </summary>
public static class RfcResiliencePolicy
{
    /// <summary>
    /// Creates a retry policy for RFC operations
    /// </summary>
    public static IAsyncPolicy CreateRetryPolicy(ILogger logger, int maxRetries = 3)
    {
        return Policy
            .Handle<RfcCommunicationException>()
            .Or<RfcAbapException>()
            .Or<TimeoutException>()
            .WaitAndRetryAsync(
                retryCount: maxRetries,
                sleepDurationProvider: retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)),
                onRetry: (outcome, timespan, retryCount, context) =>
                {
                    logger.LogWarning("RFC operation failed, retrying in {Delay}ms. Attempt {RetryCount}/{MaxRetries}. Error: {Error}",
                        timespan.TotalMilliseconds, retryCount, maxRetries, outcome.Exception?.Message);
                });
    }

    /// <summary>
    /// Creates a circuit breaker policy for RFC operations
    /// </summary>
    public static IAsyncPolicy CreateCircuitBreakerPolicy(ILogger logger, int failureThreshold = 5, TimeSpan? durationOfBreak = null)
    {
        durationOfBreak ??= TimeSpan.FromMinutes(1);
        
        return Policy
            .Handle<RfcCommunicationException>()
            .Or<RfcAbapException>()
            .CircuitBreakerAsync(
                handledEventsAllowedBeforeBreaking: failureThreshold,
                durationOfBreak: durationOfBreak.Value,
                onBreak: (exception, duration) =>
                {
                    logger.LogError("RFC circuit breaker opened for {Duration}ms due to: {Error}",
                        duration.TotalMilliseconds, exception.Message);
                },
                onReset: () =>
                {
                    logger.LogInformation("RFC circuit breaker reset");
                },
                onHalfOpen: () =>
                {
                    logger.LogInformation("RFC circuit breaker half-open");
                });
    }

    /// <summary>
    /// Creates a combined policy with retry and circuit breaker
    /// </summary>
    public static IAsyncPolicy CreateCombinedPolicy(ILogger logger)
    {
        var retryPolicy = CreateRetryPolicy(logger);
        var circuitBreakerPolicy = CreateCircuitBreakerPolicy(logger);
        
        return Policy.WrapAsync(retryPolicy, circuitBreakerPolicy);
    }

    /// <summary>
    /// Creates a timeout policy for RFC operations
    /// </summary>
    public static IAsyncPolicy CreateTimeoutPolicy(TimeSpan timeout)
    {
        return Policy.TimeoutAsync(timeout);
    }

    /// <summary>
    /// Creates a comprehensive resilience policy
    /// </summary>
    public static IAsyncPolicy CreateComprehensivePolicy(ILogger logger, TimeSpan? timeout = null)
    {
        timeout ??= TimeSpan.FromSeconds(30);
        
        var timeoutPolicy = CreateTimeoutPolicy(timeout.Value);
        var combinedPolicy = CreateCombinedPolicy(logger);
        
        return Policy.WrapAsync(timeoutPolicy, combinedPolicy);
    }
}
