using System.Diagnostics;
using System.Diagnostics.Metrics;

namespace Axon.Adapter.Api.SAPIntegrationAdapterAPI.Server.Telemetry;

/// <summary>
/// Telemetry and metrics for RFC operations
/// </summary>
public class RfcTelemetry : IDisposable
{
    private readonly Meter _meter;
    private readonly Counter<long> _requestCounter;
    private readonly Counter<long> _errorCounter;
    private readonly Histogram<double> _requestDuration;
    private readonly UpDownCounter<long> _activeConnections;
    private readonly Gauge<long> _serverStatus;

    public static readonly ActivitySource ActivitySource = new("Axon.SapRfcServer");

    public RfcTelemetry()
    {
        _meter = new Meter("Axon.SapRfcServer", "1.0.0");
        
        _requestCounter = _meter.CreateCounter<long>(
            "sap_rfc_requests_total",
            description: "Total number of RFC requests processed");
            
        _errorCounter = _meter.CreateCounter<long>(
            "sap_rfc_errors_total", 
            description: "Total number of RFC errors");
            
        _requestDuration = _meter.CreateHistogram<double>(
            "sap_rfc_request_duration_seconds",
            unit: "s",
            description: "Duration of RFC request processing");
            
        _activeConnections = _meter.CreateUpDownCounter<long>(
            "sap_rfc_active_connections",
            description: "Number of active RFC connections");
            
        _serverStatus = _meter.CreateGauge<long>(
            "sap_rfc_server_status",
            description: "RFC server status (1=running, 0=stopped)");
    }

    /// <summary>
    /// Records a successful RFC request
    /// </summary>
    public void RecordRequest(string functionName, double durationSeconds, string? handlerType = null)
    {
        var tags = new TagList
        {
            ["function_name"] = functionName,
            ["status"] = "success"
        };
        
        if (!string.IsNullOrEmpty(handlerType))
        {
            tags.Add("handler_type", handlerType);
        }

        _requestCounter.Add(1, tags);
        _requestDuration.Record(durationSeconds, tags);
    }

    /// <summary>
    /// Records a failed RFC request
    /// </summary>
    public void RecordError(string functionName, string errorType, double durationSeconds, string? handlerType = null)
    {
        var tags = new TagList
        {
            ["function_name"] = functionName,
            ["error_type"] = errorType,
            ["status"] = "error"
        };
        
        if (!string.IsNullOrEmpty(handlerType))
        {
            tags.Add("handler_type", handlerType);
        }

        _requestCounter.Add(1, tags);
        _errorCounter.Add(1, tags);
        _requestDuration.Record(durationSeconds, tags);
    }

    /// <summary>
    /// Updates the number of active connections
    /// </summary>
    public void UpdateActiveConnections(long count)
    {
        _activeConnections.Add(count);
    }

    /// <summary>
    /// Updates the server status
    /// </summary>
    public void UpdateServerStatus(bool isRunning)
    {
        _serverStatus.Record(isRunning ? 1 : 0);
    }

    /// <summary>
    /// Creates an activity for RFC request processing
    /// </summary>
    public Activity? StartActivity(string functionName, string? handlerType = null)
    {
        var activity = ActivitySource.StartActivity($"rfc.{functionName}");
        
        activity?.SetTag("rfc.function_name", functionName);
        activity?.SetTag("rfc.handler_type", handlerType);
        activity?.SetTag("rfc.server", "axon");
        
        return activity;
    }

    public void Dispose()
    {
        _meter?.Dispose();
        ActivitySource?.Dispose();
    }
}
