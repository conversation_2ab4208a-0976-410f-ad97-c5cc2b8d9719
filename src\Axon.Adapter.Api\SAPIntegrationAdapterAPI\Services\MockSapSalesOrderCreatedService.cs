using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Models.Shared;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Models.Operations;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Queries;
using Axon.Contracts.Order.Events;

namespace Axon.Adapter.Api.SAPIntegrationAdapterAPI.Services;

/// <summary>
/// Mock SAP sales order service for non-Windows platforms
/// </summary>
public class MockSapSalesOrderCreatedService : ISapSalesOrderCreatedService
{
    private readonly ILogger<MockSapSalesOrderCreatedService> _logger;

    public MockSapSalesOrderCreatedService(ILogger<MockSapSalesOrderCreatedService> logger)
    {
        _logger = logger;
    }

    public Task<SapSalesOrderResult> CreateSalesOrderAsync(CreateSalesOrderQuery salesOrder, CancellationToken cancellationToken = default)
    {
        _logger.LogWarning("SAP integration is disabled on this platform. Mock sales order created for PO: {PurchaseOrderNumber}", salesOrder.CustomerPoNum);

        var mockRfcResult = new SapRfcResult(null)
        {
            OrderNumber = $"MOCK{DateTimeOffset.UtcNow:yyyyMMddHHmmss}",
            Messages = new List<SapRfcResultItem>
            {
                new SapRfcResultItem
                {
                    Type = "S",
                    Message = "Mock SAP integration - sales order would be created in real SAP system",
                    Id = "MOCK",
                    Number = "001"
                }
            }
        };

        var mockResult = SapSalesOrderResult.FromSuccess(mockRfcResult, mockRfcResult.OrderNumber);
        return Task.FromResult(mockResult);
    }

    public Task<SapSalesOrderResult> CreateSalesOrderFromEventAsync(OrderCreatedEvent order, CancellationToken cancellationToken = default)
    {
        _logger.LogWarning("SAP integration is disabled on this platform. Mock sales order created for IncrementId: {IncrementId}", order.IncrementId);

        var mockRfcResult = new SapRfcResult(null)
        {
            OrderNumber = $"MOCK{DateTimeOffset.UtcNow:yyyyMMddHHmmss}",
            Messages = new List<SapRfcResultItem>
            {
                new SapRfcResultItem
                {
                    Type = "S",
                    Message = "Mock SAP integration - sales order would be created in real SAP system",
                    Id = "MOCK",
                    Number = "001"
                }
            }
        };

        var mockResult = SapSalesOrderResult.FromSuccess(mockRfcResult, mockRfcResult.OrderNumber);
        return Task.FromResult(mockResult);
    }
}
