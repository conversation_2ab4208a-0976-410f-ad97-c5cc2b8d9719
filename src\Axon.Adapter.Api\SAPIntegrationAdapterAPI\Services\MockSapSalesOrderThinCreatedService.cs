using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Models.Shared;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Models.Operations;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Queries;
using Axon.Contracts.Order.Events;

namespace Axon.Adapter.Api.SAPIntegrationAdapterAPI.Services;

/// <summary>
/// Mock SAP thin sales order service for non-Windows platforms
/// </summary>
public class MockSapSalesOrderThinCreatedService : ISapSalesOrderThinCreatedService
{
    private readonly ILogger<MockSapSalesOrderThinCreatedService> _logger;

    public MockSapSalesOrderThinCreatedService(ILogger<MockSapSalesOrderThinCreatedService> logger)
    {
        _logger = logger;
    }

    public Task<SapSalesOrderThinResult> CreateSalesOrderThinAsync(CreateSalesOrderThinQuery salesOrder, CancellationToken cancellationToken = default)
    {
        _logger.LogWarning("SAP integration is disabled on this platform. Mock thin sales order created for PO: {CustomerPoNumber}", salesOrder.CustomerPoNumber);

        var mockRfcResult = new SapRfcResult(null)
        {
            OrderNumber = $"THIN{DateTimeOffset.UtcNow:yyyyMMddHHmmss}",
            Messages = new List<SapRfcResultItem>
            {
                new SapRfcResultItem
                {
                    Type = "S",
                    Message = "Mock SAP integration - thin sales order would be created in real SAP system",
                    Id = "MOCK",
                    Number = "002"
                }
            }
        };

        var mockResult = SapSalesOrderThinResult.FromSuccess(mockRfcResult, mockRfcResult.OrderNumber);
        return Task.FromResult(mockResult);
    }

    public Task<SapSalesOrderThinResult> CreateSalesOrderThinFromEventAsync(OrderCreatedEvent order, CancellationToken cancellationToken = default)
    {
        logger.LogWarning("SAP integration is disabled on this platform. Mock thin sales order created for IncrementId: {IncrementId}", order.IncrementId);
        
        var mockResult = new SapSalesOrderResult
        {
            OrderNumber = $"THIN{DateTimeOffset.UtcNow:yyyyMMddHHmmss}",
            Results = new List<SapRfcResultItem>
            {
                new SapRfcResultItem
                {
                    Type = "S",
                    Message = "Mock SAP integration - thin sales order would be created in real SAP system",
                    Id = "MOCK",
                    Number = "002"
                }
            }
        };

        return Task.FromResult(mockResult);
    }
}
