using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Clients;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Extensions;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Models;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Options;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Queries;
using Axon.Contracts.Order.Events;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Axon.Adapter.Api.SAPIntegrationAdapterAPI.Services;

public interface ISapSalesOrderCreatedService
{
    Task<SapSalesOrderResult> CreateSalesOrderAsync(CreateSalesOrderQuery salesOrder, CancellationToken cancellationToken = default);
    Task<SapSalesOrderResult> CreateSalesOrderFromEventAsync(OrderCreatedEvent order, CancellationToken cancellationToken = default);
}

public class SapSalesOrderCreatedService : ISapSalesOrderCreatedService
{
    private readonly ISapApiClient _sapApiClient;
    private readonly ILogger<SapSalesOrderCreatedService> _logger;

    public SapSalesOrderCreatedService(ISapApiClient sapApiClient, ILogger<SapSalesOrderCreatedService> logger)
    {
        _sapApiClient = sapApiClient;
        _logger = logger;
    }

    public async Task<SapSalesOrderResult> CreateSalesOrderFromEventAsync(OrderCreatedEvent order, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Creating SAP sales order from OrderCreatedEvent for IncrementId: {IncrementId}", order.IncrementId);

        var createSalesOrderQuery = new CreateSalesOrderQuery
        {
            // Required fields
            CustomerNumberSh = order.ShipToParty,
            CustomerNumberSp = order.SoldToParty ?? string.Empty,
            CustomerPoNum = order.PurchaseOrderNumber,
            ShipType = order.ShippingMethod?.MethodCode ?? string.Empty,
            
            // Optional customer information
            OrderByName = $"{order.CustomerFirstname} {order.CustomerLastname}".Trim(),
            OrderByPhone = null, // Could be added to OrderCreatedEvent if needed
            
            // Additional order details
            Name2 = null, // Could be extracted from billing address or added to OrderCreatedEvent

            // Rush order handling
            RushOrder = order.ShippingMethod?.MethodCode?.Contains("RUSH", StringComparison.OrdinalIgnoreCase) == true ? "X" : null,
            
            // Order source and delivery
            OrderSrc = "E", // E-commerce source
            
            // Shipping address mapping
            ShipToAddr = order.ShippingAddress != null ? new ShipToAddr
            {
                Name1 = $"{order.ShippingAddress.Firstname} {order.ShippingAddress.Lastname}".Trim(),
                Name2 = null, // Could be company name if available
                Street1 = order.ShippingAddress.Street.FirstOrDefault() ?? string.Empty,
                Street2 = order.ShippingAddress.Street.Count > 1 ? order.ShippingAddress.Street[1] : null,
                City = order.ShippingAddress.City,
                Region = order.ShippingAddress.Region ?? string.Empty,
                ZipCode = order.ShippingAddress.Postcode ?? string.Empty,
                Country = order.ShippingAddress.CountryId,
                Tel1Numbr = order.ShippingAddress.Telephone,
                Email = order.CustomerEmail,
                FaxNumber = null
            } : null,
            
            // Material items mapping with enhanced details
            MaterialItems = order.SapItems?.Select(item => new MaterialItem
            {
                Matnr = item.MaterialNumber,
                TargetQty = item.Quantity.ToString().PadLeft(10, '0') // SAP format
            }).ToList() ?? new List<MaterialItem>(),
            
            // Order text/comments - not available in OrderCreatedEvent
            OrderText = null
        };

        return await CreateSalesOrderAsync(createSalesOrderQuery, cancellationToken);
    }

    public async Task<SapSalesOrderResult> CreateSalesOrderAsync(CreateSalesOrderQuery salesOrder, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Creating SAP sales order for PO: {PurchaseOrderNumber}", salesOrder.CustomerPoNum);

        var result = await _sapApiClient.CallFunctionAsync("ZMM_POST_SALESORDER", salesOrder, cancellationToken);

        if (!result.Success)
        {
            return new SapSalesOrderResult
            {
                Success = false,
                ErrorMessage = result.ErrorMessage
            };
        }

        var results = result.Function.ToRfcResults();

        if (results.HasErrors())
        {
            return new SapSalesOrderResult
            {
                Success = false,
                ErrorMessage = "SAP returned one or more errors",
                Results = results
            };
        }

        var orderNumber = result.Function?.GetValue("ORDER_NUMBER")?.ToString();

        _logger.LogInformation("SAP sales order created successfully. Order Number: {OrderNumber}", orderNumber);

        return new SapSalesOrderResult
        {
            Success = true,
            OrderNumber = orderNumber,
            Results = results
        };
    }
}

public class SapSalesOrderResult
{
    public bool Success { get; set; }
    public string? OrderNumber { get; set; }
    public string? ErrorMessage { get; set; }
    public List<RfcResultItem> Results { get; set; } = new();
}