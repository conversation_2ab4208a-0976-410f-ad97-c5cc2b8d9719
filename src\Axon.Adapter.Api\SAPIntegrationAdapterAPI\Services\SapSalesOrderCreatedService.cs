using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Clients;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Extensions;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Models;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Options;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Queries;
using Axon.Contracts.Order.Events;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Axon.Adapter.Api.SAPIntegrationAdapterAPI.Services;

public interface ISapSalesOrderCreatedService
{
    Task<SapSalesOrderResult> CreateSalesOrderAsync(CreateSalesOrderQuery salesOrder, CancellationToken cancellationToken = default);
    Task<SapSalesOrderResult> CreateSalesOrderFromEventAsync(OrderCreatedEvent order, CancellationToken cancellationToken = default);
}

public class SapSalesOrderCreatedService : ISapSalesOrderCreatedService
{
    private readonly ISapApiClient _sapApiClient;
    private readonly ILogger<SapSalesOrderCreatedService> _logger;

    public SapSalesOrderCreatedService(ISapApiClient sapApiClient, ILogger<SapSalesOrderCreatedService> logger)
    {
        _sapApiClient = sapApiClient;
        _logger = logger;
    }

    public async Task<SapSalesOrderResult> CreateSalesOrderFromEventAsync(OrderCreatedEvent order, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Creating SAP sales order from OrderCreatedEvent for IncrementId: {IncrementId}", order.IncrementId);

        var createSalesOrderQuery = new CreateSalesOrderQuery
        {
            // Required fields
            CustomerNumberSh = order.ShipToParty,
            CustomerNumberSp = order.SoldToParty ?? string.Empty,
            CustomerPoNum = order.PurchaseOrderNumber,
            ShipType = order.ShippingMethod?.MethodCode ?? string.Empty,
            
            // Optional customer information
            OrderByName = $"{order.CustomerFirstname} {order.CustomerLastname}".Trim(),
            OrderByPhone = null, // Could be added to OrderCreatedEvent if needed
            
            // Additional order details
            Name2 = null, // Could be extracted from billing address or added to OrderCreatedEvent

            // Rush order handling
            RushOrder = order.ShippingMethod?.MethodCode?.Contains("RUSH", StringComparison.OrdinalIgnoreCase) == true ? "X" : null,
            
            // Order source and delivery
            OrderSrc = "E", // E-commerce source
            
            // Shipping address mapping
            ShipToAddr = order.ShippingAddress != null ? new ShipToAddr
            {
                Name1 = $"{order.ShippingAddress.Firstname} {order.ShippingAddress.Lastname}".Trim(),
                Name2 = null, // Could be company name if available
                Street1 = order.ShippingAddress.Street.FirstOrDefault() ?? string.Empty,
                Street2 = order.ShippingAddress.Street.Count > 1 ? order.ShippingAddress.Street[1] : null,
                City = order.ShippingAddress.City,
                Region = order.ShippingAddress.Region ?? string.Empty,
                ZipCode = order.ShippingAddress.Postcode ?? string.Empty,
                Country = order.ShippingAddress.CountryId,
                Tel1Numbr = order.ShippingAddress.Telephone,
                Email = order.CustomerEmail,
                FaxNumber = null
            } : null,
            
            // Material items mapping with enhanced details
            MaterialItems = order.SapItems?.Select(item => new MaterialItem
            {
                Matnr = item.MaterialNumber,
                TargetQty = item.Quantity.ToString().PadLeft(10, '0') // SAP format
            }).ToList() ?? new List<MaterialItem>(),
            
            // Order text/comments - not available in OrderCreatedEvent
            OrderText = null
        };

        return await CreateSalesOrderAsync(createSalesOrderQuery, cancellationToken);
    }

    public async Task<SapSalesOrderResult> CreateSalesOrderAsync(CreateSalesOrderQuery salesOrder, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Creating SAP sales order for PO: {PurchaseOrderNumber}", salesOrder.CustomerPoNum);

        var result = await _sapApiClient.CallFunctionAsync("ZMM_POST_SALESORDER", salesOrder, cancellationToken);

        if (!result.Success)
        {
            _logger.LogError("SAP sales order creation failed for PO: {PurchaseOrderNumber}. Error: {ErrorMessage}. ErrorType: {ErrorType}",
                salesOrder.CustomerPoNum, result.ErrorMessage, result.ErrorType);

            return new SapSalesOrderResult
            {
                Success = false,
                ErrorMessage = result.ErrorMessage ?? "Unknown error occurred",
                ErrorCode = result.ErrorCode,
                ErrorType = result.ErrorType,
                Results = result.Messages
            };
        }

        // Check for warnings
        if (result.HasWarnings)
        {
            _logger.LogWarning("SAP sales order creation completed with warnings for PO: {PurchaseOrderNumber}. Warnings: {Warnings}",
                salesOrder.CustomerPoNum, result.GetWarningSummary());
        }

        var orderNumber = result.Function?.GetValue("ORDER_NUMBER")?.ToString();

        _logger.LogInformation("SAP sales order created successfully. Order Number: {OrderNumber}, PO: {PurchaseOrderNumber}",
            orderNumber, salesOrder.CustomerPoNum);

        return new SapSalesOrderResult
        {
            Success = true,
            OrderNumber = orderNumber,
            Results = result.Messages
        };
    }
}

public class SapSalesOrderResult
{
    public bool Success { get; set; }
    public string? OrderNumber { get; set; }
    public string? ErrorMessage { get; set; }
    public string? ErrorCode { get; set; }
    public RfcErrorType ErrorType { get; set; } = RfcErrorType.None;
    public List<RfcResultItem> Results { get; set; } = new();

    /// <summary>
    /// Indicates whether the result contains any error messages.
    /// </summary>
    public bool HasErrors => !Success || Results.HasErrors();

    /// <summary>
    /// Indicates whether the result contains any warning messages.
    /// </summary>
    public bool HasWarnings => Results.Any(m => m.Type == "W");

    /// <summary>
    /// Gets all error messages as a concatenated string.
    /// </summary>
    public string GetErrorSummary()
    {
        if (!string.IsNullOrEmpty(ErrorMessage))
            return ErrorMessage;

        var errorMessages = Results.Where(m => m.Type == "E").Select(m => m.Message).Where(m => !string.IsNullOrEmpty(m));
        return string.Join("; ", errorMessages);
    }

    /// <summary>
    /// Gets all warning messages as a concatenated string.
    /// </summary>
    public string GetWarningSummary()
    {
        var warningMessages = Results.Where(m => m.Type == "W").Select(m => m.Message).Where(m => !string.IsNullOrEmpty(m));
        return string.Join("; ", warningMessages);
    }
}