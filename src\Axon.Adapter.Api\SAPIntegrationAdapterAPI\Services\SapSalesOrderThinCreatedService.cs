using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Clients;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Extensions;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Models;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Options;
using Axon.Adapter.Api.SAPIntegrationAdapterAPI.Queries;
using Axon.Contracts.Order.Events;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Axon.Adapter.Api.SAPIntegrationAdapterAPI.Services;

public interface ISapSalesOrderThinCreatedService
{
    Task<SapSalesOrderThinResult> CreateSalesOrderThinAsync(CreateSalesOrderThinQuery salesOrder, CancellationToken cancellationToken = default);
    Task<SapSalesOrderThinResult> CreateSalesOrderThinFromEventAsync(OrderCreatedEvent order, CancellationToken cancellationToken = default);
}

public class SapSalesOrderThinCreatedService : ISapSalesOrderThinCreatedService
{
    private readonly ISapApiClient _sapApiClient;
    private readonly ILogger<SapSalesOrderThinCreatedService> _logger;

    public SapSalesOrderThinCreatedService(ISapApiClient sapApiClient, ILogger<SapSalesOrderThinCreatedService> logger)
    {
        _sapApiClient = sapApiClient;
        _logger = logger;
    }

    public async Task<SapSalesOrderThinResult> CreateSalesOrderThinFromEventAsync(OrderCreatedEvent order, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Creating SAP sales order from OrderCreatedEvent for IncrementId: {IncrementId}", order.IncrementId);

        var createSalesOrderThinQuery = new CreateSalesOrderThinQuery
        {
            CustomerPoNumber = order.IncrementId,
            SoldToNumber = order.SoldToParty,
            ShipToNumber = order.ShipToParty ?? string.Empty,
            OrderByName = $"{order.CustomerFirstname} {order.CustomerLastname}".Trim(),
            ShippingType = order.ShippingMethod?.MethodCode ?? string.Empty,
            // OrderSource = order.OrderSource,
            ShipToAddress = order.ShippingAddress != null ? new ShipToAddress
            {
                Name1 = $"{order.ShippingAddress.Firstname} {order.ShippingAddress.Lastname}".Trim(),
                Street1 = order.ShippingAddress.Street.FirstOrDefault() ?? string.Empty,
                Street2 = order.ShippingAddress.Street.Count > 1 ? order.ShippingAddress.Street[1] : null,
                City = order.ShippingAddress.City,
                State = order.ShippingAddress.Region ?? string.Empty,
                ZipCode = order.ShippingAddress.Postcode ?? string.Empty,
                Country = order.ShippingAddress.CountryId,
                PhoneNumber = order.ShippingAddress.Telephone,
                EmailAddress = order.CustomerEmail
            } : null,
            OrderItems = order.Items.Select(item => new Axon.Adapter.Api.SAPIntegrationAdapterAPI.Queries.OrderItem
            {
                MaterialNumber = item.Sku,
                OrderQuantity = item.Qty.ToString(),
                ItemAmount = item.BaseRowTotal
            }).ToList()
        };

        return await CreateSalesOrderThinAsync(createSalesOrderThinQuery, cancellationToken);
    }

    public async Task<SapSalesOrderThinResult> CreateSalesOrderThinAsync(CreateSalesOrderThinQuery salesOrder, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Creating SAP thin sales order for PO: {CustomerPoNumber}", salesOrder.CustomerPoNumber);

        var result = await _sapApiClient.CallFunctionAsync("ZSD_CRT_WEB_SALES_ORDER", salesOrder, cancellationToken);

        if (!result.Success)
        {
            _logger.LogError("SAP thin sales order creation failed for PO: {CustomerPoNumber}. Error: {ErrorMessage}. ErrorType: {ErrorType}",
                salesOrder.CustomerPoNumber, result.ErrorMessage, result.ErrorType);

            return SapSalesOrderThinResult.FromFailure(result);
        }

        // Check for warnings
        if (result.HasWarnings)
        {
            _logger.LogWarning("SAP thin sales order creation completed with warnings for PO: {CustomerPoNumber}. Warnings: {Warnings}",
                salesOrder.CustomerPoNumber, result.GetWarningSummary());
        }

        var orderNumber = result.Function?.GetValue("ORDER_NUMBER")?.ToString();

        _logger.LogInformation("SAP thin sales order created successfully. Order Number: {OrderNumber}, PO: {CustomerPoNumber}",
            orderNumber, salesOrder.CustomerPoNumber);

        return SapSalesOrderThinResult.FromSuccess(result, orderNumber);
    }
}

