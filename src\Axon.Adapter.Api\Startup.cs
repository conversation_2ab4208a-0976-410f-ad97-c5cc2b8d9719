using Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Clients;
using Axon.Adapter.Api.MagentoIntegrationAdapterAPI.Consumers;

public class Startup
{
    public void ConfigureServices(IServiceCollection services)
    {
        // Register the typed HttpClient for Magento
        services.AddHttpClient<MagentoApiClient>(client =>
        {
            client.BaseAddress = new Uri("https://your-magento-url"); // TODO: Replace with actual base URL or config
            // Optionally add default headers, authentication, etc.
        });

        // Register the order status update handler
        services.AddScoped<IMagentoOrderStatusUpdateHandler, MagentoOrderStatusUpdateHandler>();
    }
} 