{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "Serilog": {"MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Information", "Microsoft.Hosting.Lifetime": "Information", "System": "Information", "Microsoft.AspNetCore": "Information"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"theme": "Serilog.Sinks.SystemConsole.Themes.AnsiConsoleTheme::Code, Serilog.Sinks.Console", "outputTemplate": "🚀 [{Timestamp:HH:mm:ss} {Level:u3}] 📦 {SourceContext:l} {Message:lj}{NewLine}{Exception}"}}]}, "SapApi": {"BaseAddress": "http://localhost:7600", "SystemNumber": "00", "Client": "100", "User": "RFC_USER", "Password": "RFC_PASSWORD", "Language": "EN", "SystemId": "DEV", "PoolSize": 5, "MaxPoolSize": 50, "IdleTimeout": 600, "ConnectionTimeout": 30, "UseSnc": false, "UseRouter": false, "UseLoadBalancing": false, "TraceLevel": 2, "Check": "1"}}