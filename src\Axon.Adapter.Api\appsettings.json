{"Serilog": {"Using": ["Serilog.Sinks.Console"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "System": "Warning", "Microsoft.AspNetCore": "Warning", "MassTransit": "Debug", "MassTransit.AmazonSqsTransport": "Debug", "MassTransit.Monitoring": "Debug"}}, "Enrich": ["FromLogContext", "WithEnvironmentName", "WithProcessId", "WithThreadId"]}, "AllowedHosts": "*"}