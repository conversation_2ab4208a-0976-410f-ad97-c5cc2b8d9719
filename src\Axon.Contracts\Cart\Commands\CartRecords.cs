namespace Axon.Contracts.Cart.Commands;

public record CartCurrency
{
    public string BaseCurrencyCode { get; init; } = "USD";
    public string QuoteCurrencyCode { get; init; } = "USD";
}

public record CartTotals
{
    public decimal GrandTotal { get; init; }
    public decimal BaseTaxAmount { get; init; }
    public decimal TaxAmount { get; init; }
    public decimal BaseSubtotal { get; init; }
    public decimal Subtotal { get; init; }
}

public record CartItem
{
    public string ItemId { get; init; } = string.Empty;
    public string Sku { get; init; } = string.Empty;
    public string Name { get; init; } = string.Empty;
    public decimal Qty { get; init; }
    public decimal Price { get; init; }
    public string? ProductType { get; init; }
    public object? ProductOption { get; init; }
}

public record CartCustomer
{
    public string? Email { get; init; }
    public int? GroupId { get; init; }
    public bool IsGuest { get; init; } = true;
    public string? FirstName { get; init; }
    public string? LastName { get; init; }
}

public record CartAddress
{
    public string? Id { get; init; }
    public string? Region { get; init; }
    public string Country { get; init; } = string.Empty;
    public List<string> Street { get; init; } = new();
    public string City { get; init; } = string.Empty;
    public string Postcode { get; init; } = string.Empty;
    public string Firstname { get; init; } = string.Empty;
    public string Lastname { get; init; } = string.Empty;
    public string? Telephone { get; init; }
}

public record CartPaymentMethod
{
    public string Method { get; init; } = string.Empty;
    public string? PoNumber { get; init; }
}

public record CartShippingMethod
{
    public string CarrierCode { get; init; } = string.Empty;
    public string MethodCode { get; init; } = string.Empty;
    public string MethodTitle { get; init; } = string.Empty;
    public decimal Amount { get; init; }
}