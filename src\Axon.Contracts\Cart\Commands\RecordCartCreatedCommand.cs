namespace Axon.Contracts.Cart.Commands;

public class RecordCartCreatedCommand
{
    public string CartId { get; init; } = string.Empty;
    public string? CustomerId { get; init; }
    public string StoreId { get; init; } = string.Empty;
    public DateTimeOffset CreatedAt { get; init; }
    public DateTimeOffset UpdatedAt { get; init; }
    public CartCurrency Currency { get; init; } = new();
    public CartTotals Totals { get; init; } = new();
    public bool IsActive { get; init; }
    public bool IsVirtual { get; init; }
    public bool IsNegotiableQuote { get; init; }
    public bool IsMultiShipping { get; init; }
    public int ItemsCount { get; init; }
    public decimal ItemsQty { get; init; }
    public List<CartItem> Items { get; init; } = new();
    public CartCustomer Customer { get; init; } = new();
    public CartAddress? BillingAddress { get; init; }
    public CartAddress? ShippingAddress { get; init; }
    public CartPaymentMethod? PaymentMethod { get; init; }
    public CartShippingMethod? ShippingMethod { get; init; }
    public string IdempotencyKey { get; init; } = string.Empty;
}
