namespace Axon.Contracts.Cart.Commands;

public class RecordCartUpdatedCommand
{
    public string CartId { get; init; } = string.Empty;
    public string StoreId { get; init; } = string.Empty;
    public DateTimeOffset UpdatedAt { get; init; }
    public CartTotals Totals { get; init; } = new();
    public bool IsNegotiableQuote { get; init; }
    public bool IsMultiShipping { get; init; }
    public List<CartItem> Items { get; init; } = new();
    public CartAddress? BillingAddress { get; init; }
    public CartAddress? ShippingAddress { get; init; }
    public CartPaymentMethod? PaymentMethod { get; init; }
    public CartShippingMethod? ShippingMethod { get; init; }
    public string IdempotencyKey { get; init; } = string.Empty;
}