namespace Axon.Contracts.Customer.Events;

/// <summary>
/// Domain event representing a customer update
/// </summary>
public record CustomerUpdatedEvent(
    string CustomerNumber,
    string ChangeType,
    DateTimeOffset UpdatedAt,
    CustomerUpdateDetails? Details = null
)
{
    public record CustomerUpdateDetails(
        string? Name = null,
        string? Email = null,
        string? Phone = null,
        CustomerAddress? Address = null,
        string? Status = null,
        Dictionary<string, object>? AdditionalProperties = null
    );

    public record CustomerAddress(
        string Street,
        string City,
        string? Region,
        string? PostalCode,
        string CountryCode
    );
}
