namespace Axon.Contracts.Material.Events;

/// <summary>
/// Domain event representing a material update
/// </summary>
public record MaterialUpdatedEvent(
    string MaterialNumber,
    string UpdateType,
    DateTimeOffset UpdatedAt,
    MaterialUpdatedEvent.MaterialUpdateDetails? Details = null
)
{
    public record MaterialUpdateDetails(
        string? Description = null,
        string? Category = null,
        decimal? Price = null,
        string? UnitOfMeasure = null,
        bool? IsActive = null,
        Dictionary<string, object>? AdditionalProperties = null
    );
}
