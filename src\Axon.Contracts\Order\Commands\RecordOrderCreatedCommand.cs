namespace Axon.Contracts.Order.Commands;

public class RecordOrderCreatedCommand
{
    public string IncrementId { get; set; } = string.Empty;
    public string State { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public int? CustomerId { get; set; }
    public string CustomerEmail { get; set; } = string.Empty;
    public string? CustomerFirstname { get; set; }
    public string? CustomerLastname { get; set; }
    public int StoreId { get; set; }
    public List<OrderItem> Items { get; set; } = [];
    public Address BillingAddress { get; set; } = new();
    public Address? ShippingAddress { get; set; }
    public Payment Payment { get; set; } = new();
    public ShippingMethod? ShippingMethod { get; set; }
    public decimal TotalQtyOrdered { get; set; }
    public decimal GrandTotal { get; set; }
    public decimal BaseGrandTotal { get; set; }
    public DateTimeOffset? CreatedAt { get; set; }
}

public record OrderItem
{
    public int ItemId { get; init; }
    public string Sku { get; init; } = string.Empty;
    public decimal Qty { get; init; }
    public decimal Price { get; init; }
    public decimal BasePrice { get; init; }
    public decimal RowTotal { get; init; }
    public decimal BaseRowTotal { get; init; }
    public string Name { get; init; } = string.Empty;
    public string ProductType { get; init; } = string.Empty;
    public ProductOption? ProductOption { get; init; }
}

public record ProductOption
{
    public object? ExtensionAttributes { get; init; }
}

public record Address
{
    public string Firstname { get; init; } = string.Empty;
    public string Lastname { get; init; } = string.Empty;
    public List<string> Street { get; init; } = [];
    public string City { get; init; } = string.Empty;
    public string? Region { get; init; }
    public string? Postcode { get; init; }
    public string CountryId { get; init; } = string.Empty;
    public string Telephone { get; init; } = string.Empty;
}

public record Payment
{
    public string Method { get; init; } = string.Empty;
    public decimal AmountOrdered { get; init; }
    public decimal BaseAmountOrdered { get; init; }
}

public record ShippingMethod
{
    public string MethodCode { get; init; } = string.Empty;
    public string CarrierCode { get; init; } = string.Empty;
} 