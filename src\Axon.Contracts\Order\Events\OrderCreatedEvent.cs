namespace Axon.Contracts.Order.Events;

public class OrderCreatedEvent
{
    public Guid OrderId { get; init; }
    
    // From RecordOrderCreatedCommand
    public string IncrementId { get; set; } = string.Empty;
    public string CustomerEmail { get; set; } = string.Empty;
    public string? CustomerFirstname { get; set; }
    public string? CustomerLastname { get; set; }
    public int StoreId { get; set; }
    public List<OrderItem> Items { get; set; } = [];
    public Address BillingAddress { get; set; } = new();
    public Address? ShippingAddress { get; set; }
    public Payment Payment { get; set; } = new();
    public ShippingMethod? ShippingMethod { get; set; }

    // SAP-specific fields
    public string PurchaseOrderNumber { get; set; } = string.Empty; // Map from IncrementId or external order number
    public string OrderSource{ get; set; } = "E"; // Default to "OR" (Standard Order)
    public string? SoldToParty { get; set; }  // Map from CustomerEmail or customer number
    public string? ShipToParty { get; set; } // Map from ShippingAddress or customer number
    public string Currency { get; set; } = "USD"; // Default, or map from order context
    public DateTime? RequestedDeliveryDate { get; set; } // Optional, can be null

    public List<SapOrderItem> SapItems { get; set; } = [];
}

public record OrderItem
{
    public int ItemId { get; init; }
    public string Sku { get; init; } = string.Empty;
    public decimal Qty { get; init; }
    public decimal Price { get; init; }
    public decimal BasePrice { get; init; }
    public decimal RowTotal { get; init; }
    public decimal BaseRowTotal { get; init; }
    public string Name { get; init; } = string.Empty;
    public string ProductType { get; init; } = string.Empty;
    public ProductOption? ProductOption { get; init; }
}

public record SapOrderItem
{
    public string MaterialNumber { get; init; } = string.Empty; // Map from Sku
    public double Quantity { get; init; } // Map from Qty
    public string UnitOfMeasure { get; init; } = "EA"; // Default to "EA" (Each)
    public string? Plant { get; init; } // Optional, can be null
}

public record ProductOption
{
    public object? ExtensionAttributes { get; init; }
}

public record Address
{
    public string Firstname { get; init; } = string.Empty;
    public string Lastname { get; init; } = string.Empty;
    public List<string> Street { get; init; } = [];
    public string City { get; init; } = string.Empty;
    public string? Region { get; init; }
    public string? Postcode { get; init; }
    public string CountryId { get; init; } = string.Empty;
    public string Telephone { get; init; } = string.Empty;
}

public record Payment
{
    public string Method { get; init; } = string.Empty;
    public decimal AmountOrdered { get; init; }
    public decimal BaseAmountOrdered { get; init; }
}

public record ShippingMethod
{
    public string MethodCode { get; init; } = string.Empty;
    public string CarrierCode { get; init; } = string.Empty;
} 