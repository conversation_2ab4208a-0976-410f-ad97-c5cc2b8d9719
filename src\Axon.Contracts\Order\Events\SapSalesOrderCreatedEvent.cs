namespace Axon.Contracts.Order.Events;

public record SapSalesOrderCreatedEvent(
    string OrderN<PERSON>ber,
    IReadOnlyList<SapSalesOrderCreatedEvent.Result> Results
)
{
    public record Result(
        string Type,
        string Id,
        string Number,
        string Message,
        string System,
        string? LogNo = null,
        string? LogMsgNo = null,
        string? MessageV1 = null,
        string? MessageV2 = null,
        string? MessageV3 = null,
        string? MessageV4 = null,
        string? Parameter = null,
        int? Row = null,
        string? Field = null
    );
} 