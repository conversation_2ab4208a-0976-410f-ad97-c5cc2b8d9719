using Axon.Contracts.Order.Events;

namespace Axon.Contracts.Order.Queries;

public class GetOrderByIdResponse
{
    public Guid OrderId { get; init; }
    public string IncrementId { get; set; } = string.Empty;
    public string CustomerEmail { get; set; } = string.Empty;
    public string? CustomerFirstname { get; set; }
    public string? CustomerLastname { get; set; }
    public int? CustomerId { get; set; }
    public int StoreId { get; set; }
    public List<OrderItem> Items { get; set; } = [];
    public Address BillingAddress { get; set; } = new();
    public Address? ShippingAddress { get; set; }
    public Payment Payment { get; set; } = new();
    public ShippingMethod? ShippingMethod { get; set; }
    public string? State { get; set; }
    public string? Status { get; set; }
    public decimal? GrandTotal { get; set; }
    public decimal? BaseGrandTotal { get; set; }
    public decimal? TotalQtyOrdered { get; set; }
    public DateTimeOffset? CreatedAt { get; set; }
} 