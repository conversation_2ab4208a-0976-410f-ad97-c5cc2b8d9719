using Amazon.SimpleNotificationService;
using Amazon.SQS;
using Axon.Core.MassTransit.Options;
using MassTransit;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Amazon;

namespace Axon.Core.MassTransit.ServiceCollectionExtensions;

public static class MassTransitRegistration
{

    /// <summary>
    /// Registers MassTransit with automatic broker selection based on environment.
    /// Uses RabbitMQ for local development and AWS SNS/SQS for AWS environments.
    /// </summary>
    /// <param name="services">The IServiceCollection to add MassTransit to.</param>
    /// <param name="configureConsumers">Action to configure consumers</param>
    public static IServiceCollection AddMassTransitWithBroker(this IServiceCollection services,
        Action<IBusRegistrationConfigurator> configureConsumers)
    {
        var serviceProvider = services.BuildServiceProvider();
        var configuration = serviceProvider.GetRequiredService<IConfiguration>();
        var hostEnvironment = serviceProvider.GetRequiredService<IHostEnvironment>();

        var useAws = hostEnvironment.EnvironmentName.StartsWith("AWS-", StringComparison.OrdinalIgnoreCase);

        if (useAws)
        {
            // Use AWS SNS/SQS when deployed to AWS environments
            services.AddMassTransitWithAws(configuration, configureConsumers);
        }
        else
        {
            // Use RabbitMQ for local development
            services.AddMassTransitWithRabbitMq(configureConsumers);
        }

        return services;
    }

    private static IServiceCollection AddMassTransitWithAws(this IServiceCollection services, IConfiguration configuration,
        Action<IBusRegistrationConfigurator> configureConsumers)
    {
        services.AddMassTransit(x =>
        {
            x.AddMetrics();
            configureConsumers(x);

            x.UsingAmazonSqs((context, cfg) =>
            {
                var configuration = context.GetRequiredService<IConfiguration>();
                var awsOptions = new AwsOptions();
                configuration.GetSection(AwsOptions.Key).Bind(awsOptions);

                cfg.Host(awsOptions.Region, h =>
                {

                });

                cfg.ConfigureEndpoints(context);
            });
        });

        return services;
    }

    private static IServiceCollection AddMassTransitWithRabbitMq(this IServiceCollection services,
        Action<IBusRegistrationConfigurator> configureConsumers)
    {
        services.AddMassTransit(x =>
        {
            x.AddMetrics();
            configureConsumers(x);
            x.UsingRabbitMq((context, cfg) =>
            {
                var configuration = context.GetRequiredService<IConfiguration>();
                var rabbitOptions = new RabbitMqOptions();
                configuration.GetSection(RabbitMqOptions.Key).Bind(rabbitOptions);

                cfg.Host(rabbitOptions.Host, rabbitOptions.VirtualHost, h =>
                {
                    h.Username(rabbitOptions.Username);
                    h.Password(rabbitOptions.Password);
                });
                cfg.ConfigureEndpoints(context);
            });
        });
        return services;
    }

}
