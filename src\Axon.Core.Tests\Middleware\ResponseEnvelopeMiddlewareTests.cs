using Axon.Core.Middleware;
using Axon.Core.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Moq;
using System.Text;
using System.Text.Json;

namespace Axon.Core.Tests.Middleware;

public class ResponseEnvelopeMiddlewareTests
{
    private readonly Mock<ILogger<ResponseEnvelopeMiddleware>> _loggerMock;
    private readonly ResponseEnvelopeMiddleware _middleware;
    private readonly DefaultHttpContext _httpContext;
    private readonly Mock<RequestDelegate> _nextMock;

    public ResponseEnvelopeMiddlewareTests()
    {
        _loggerMock = new Mock<ILogger<ResponseEnvelopeMiddleware>>();
        _nextMock = new Mock<RequestDelegate>();
        _middleware = new ResponseEnvelopeMiddleware(_nextMock.Object, _loggerMock.Object);
        _httpContext = new DefaultHttpContext();
    }

    [Theory]
    [InlineData("/healthz")]
    [InlineData("/health")]
    [InlineData("/metrics")]
    [InlineData("/swagger")]
    [InlineData("/favicon.ico")]
    [InlineData("/_framework/something")]
    [InlineData("/_content/something")]
    public async Task Invoke_Should_Skip_Infrastructure_Endpoints(string path)
    {
        // Arrange
        _httpContext.Request.Path = path;
        var originalStream = new MemoryStream();
        _httpContext.Response.Body = originalStream;

        // Act
        await _middleware.Invoke(_httpContext);

        // Assert
        _nextMock.Verify(next => next(_httpContext), Times.Once);
        Assert.Same(originalStream, _httpContext.Response.Body);
    }

    [Fact]
    public async Task Invoke_Should_Wrap_Successful_JSON_Response_With_Data()
    {
        // Arrange
        var testData = new { id = 1, name = "Test" };
        var jsonData = JsonSerializer.Serialize(testData);

        _httpContext.Request.Path = "/api/test";
        _httpContext.Response.StatusCode = 200;
        _httpContext.Response.ContentType = "application/json";

        _nextMock.Setup(next => next(_httpContext))
            .Callback(async () =>
            {
                await _httpContext.Response.WriteAsync(jsonData);
            })
            .Returns(Task.CompletedTask);

        var originalStream = new MemoryStream();
        _httpContext.Response.Body = originalStream;

        // Act
        await _middleware.Invoke(_httpContext);

        // Assert
        originalStream.Seek(0, SeekOrigin.Begin);
        var responseContent = await new StreamReader(originalStream).ReadToEndAsync();
        var apiResponse = JsonSerializer.Deserialize<ApiResponse<object>>(responseContent, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });

        Assert.NotNull(apiResponse);
        Assert.NotNull(apiResponse.Data);
        Assert.Null(apiResponse.Error);
    }

    [Fact]
    public async Task Invoke_Should_Not_Wrap_Non_JSON_Success_Response()
    {
        // Arrange
        var textResponse = "Plain text response";

        _httpContext.Request.Path = "/api/test";
        _httpContext.Response.StatusCode = 200;
        _httpContext.Response.ContentType = "text/plain";

        _nextMock.Setup(next => next(_httpContext))
            .Callback(async () =>
            {
                await _httpContext.Response.WriteAsync(textResponse);
            })
            .Returns(Task.CompletedTask);

        var originalStream = new MemoryStream();
        _httpContext.Response.Body = originalStream;

        // Act
        await _middleware.Invoke(_httpContext);

        // Assert
        originalStream.Seek(0, SeekOrigin.Begin);
        var responseContent = await new StreamReader(originalStream).ReadToEndAsync();

        Assert.Equal(textResponse, responseContent);
    }

    [Theory]
    [InlineData(404, "Order not found", "NOT_FOUND")]
    [InlineData(400, "Invalid request", "BAD_REQUEST")]
    [InlineData(401, "Unauthorized access", "UNAUTHORIZED")]
    [InlineData(403, "Access forbidden", "FORBIDDEN")]
    [InlineData(500, "Internal error", "INTERNAL_SERVER_ERROR")]
    public async Task Invoke_Should_Wrap_Error_Response_With_Custom_Message(int statusCode, string customMessage, string expectedErrorType)
    {
        // Arrange
        _httpContext.Request.Path = "/api/test";
        _httpContext.Response.StatusCode = statusCode;
        _httpContext.Response.ContentType = "text/plain";

        _nextMock.Setup(next => next(_httpContext))
            .Callback(async () =>
            {
                await _httpContext.Response.WriteAsync(customMessage);
            })
            .Returns(Task.CompletedTask);

        var originalStream = new MemoryStream();
        _httpContext.Response.Body = originalStream;

        // Act
        await _middleware.Invoke(_httpContext);

        // Assert
        originalStream.Seek(0, SeekOrigin.Begin);
        var responseContent = await new StreamReader(originalStream).ReadToEndAsync();
        var apiResponse = JsonSerializer.Deserialize<ApiResponse>(responseContent, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });

        Assert.NotNull(apiResponse);
        Assert.Null(apiResponse.Data);
        Assert.NotNull(apiResponse.Error);
        Assert.Equal(customMessage, apiResponse.Error.Message);
        Assert.Equal(expectedErrorType, apiResponse.Error.Type);
        Assert.Equal("application/json", _httpContext.Response.ContentType);
    }

    [Theory]
    [InlineData(404, "NOT_FOUND", "The requested resource was not found")]
    [InlineData(400, "BAD_REQUEST", "Bad Request")]
    [InlineData(401, "UNAUTHORIZED", "Authentication is required for this resource")]
    [InlineData(403, "FORBIDDEN", "Forbidden")]
    [InlineData(500, "INTERNAL_SERVER_ERROR", "An unexpected error occurred while processing the request")]
    public async Task Invoke_Should_Use_Default_Error_Message_When_No_Custom_Message(int statusCode, string expectedErrorType, string expectedMessage)
    {
        // Arrange
        _httpContext.Request.Path = "/api/test";
        _httpContext.Response.StatusCode = statusCode;
        _httpContext.Response.ContentType = "application/json";

        _nextMock.Setup(next => next(_httpContext))
            .Returns(Task.CompletedTask);

        var originalStream = new MemoryStream();
        _httpContext.Response.Body = originalStream;

        // Act
        await _middleware.Invoke(_httpContext);

        // Assert
        originalStream.Seek(0, SeekOrigin.Begin);
        var responseContent = await new StreamReader(originalStream).ReadToEndAsync();
        var apiResponse = JsonSerializer.Deserialize<ApiResponse>(responseContent, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });

        Assert.NotNull(apiResponse);
        Assert.Null(apiResponse.Data);
        Assert.NotNull(apiResponse.Error);
        Assert.Equal(expectedMessage, apiResponse.Error.Message);
        Assert.Equal(expectedErrorType, apiResponse.Error.Type);
    }

    [Fact]
    public async Task Invoke_Should_Not_Double_Wrap_Already_Wrapped_Response()
    {
        // Arrange
        var apiResponse = new ApiResponse<object>
        {
            Data = new { id = 1 },
            Error = null
        };
        var jsonResponse = JsonSerializer.Serialize(apiResponse, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });

        _httpContext.Request.Path = "/api/test";
        _httpContext.Response.StatusCode = 200;
        _httpContext.Response.ContentType = "application/json";

        _nextMock.Setup(next => next(_httpContext))
            .Callback(async () =>
            {
                await _httpContext.Response.WriteAsync(jsonResponse);
            })
            .Returns(Task.CompletedTask);

        var originalStream = new MemoryStream();
        _httpContext.Response.Body = originalStream;

        // Act
        await _middleware.Invoke(_httpContext);

        // Assert
        originalStream.Seek(0, SeekOrigin.Begin);
        var responseContent = await new StreamReader(originalStream).ReadToEndAsync();

        // Should be the same as the original (not double-wrapped)
        Assert.Equal(jsonResponse, responseContent);
    }

    [Fact]
    public async Task Invoke_Should_Wrap_JSON_Error_Response()
    {
        // Arrange
        var errorData = new { error = "Something went wrong", code = "ERR001" };
        var jsonData = JsonSerializer.Serialize(errorData);

        _httpContext.Request.Path = "/api/test";
        _httpContext.Response.StatusCode = 400;
        _httpContext.Response.ContentType = "application/json";

        _nextMock.Setup(next => next(_httpContext))
            .Callback(async () =>
            {
                await _httpContext.Response.WriteAsync(jsonData);
            })
            .Returns(Task.CompletedTask);

        var originalStream = new MemoryStream();
        _httpContext.Response.Body = originalStream;

        // Act
        await _middleware.Invoke(_httpContext);

        // Assert
        originalStream.Seek(0, SeekOrigin.Begin);
        var responseContent = await new StreamReader(originalStream).ReadToEndAsync();
        var apiResponse = JsonSerializer.Deserialize<ApiResponse>(responseContent, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });

        Assert.NotNull(apiResponse);
        Assert.Null(apiResponse.Data);
        Assert.NotNull(apiResponse.Error);
        Assert.Equal("BAD_REQUEST", apiResponse.Error.Type);
    }

    [Fact]
    public async Task Invoke_Should_Handle_Empty_Response_Body()
    {
        // Arrange
        _httpContext.Request.Path = "/api/test";
        _httpContext.Response.StatusCode = 204; // No Content
        _httpContext.Response.ContentType = "application/json";

        _nextMock.Setup(next => next(_httpContext))
            .Returns(Task.CompletedTask);

        var originalStream = new MemoryStream();
        _httpContext.Response.Body = originalStream;

        // Act
        await _middleware.Invoke(_httpContext);

        // Assert
        originalStream.Seek(0, SeekOrigin.Begin);
        var responseContent = await new StreamReader(originalStream).ReadToEndAsync();
        var apiResponse = JsonSerializer.Deserialize<ApiResponse>(responseContent, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });

        Assert.NotNull(apiResponse);
        Assert.Null(apiResponse.Data);
        Assert.Null(apiResponse.Error);
    }

    [Fact]
    public async Task Invoke_Should_Handle_Exception_During_Wrapping()
    {
        // Arrange
        _httpContext.Request.Path = "/api/test";
        _httpContext.Response.StatusCode = 200;
        _httpContext.Response.ContentType = "application/json";

        // Setup the service provider to return our logger mock
        var serviceProviderMock = new Mock<IServiceProvider>();
        serviceProviderMock.Setup(sp => sp.GetService(typeof(ILogger<ResponseEnvelopeMiddleware>)))
            .Returns(_loggerMock.Object);
        _httpContext.RequestServices = serviceProviderMock.Object;

        _nextMock.Setup(next => next(_httpContext))
            .Callback(async () =>
            {
                // Write invalid JSON that will cause deserialization to fail
                await _httpContext.Response.WriteAsync("{invalid json");
            })
            .Returns(Task.CompletedTask);

        var originalStream = new MemoryStream();
        _httpContext.Response.Body = originalStream;

        // Act
        await _middleware.Invoke(_httpContext);

        // Assert
        originalStream.Seek(0, SeekOrigin.Begin);
        var responseContent = await new StreamReader(originalStream).ReadToEndAsync();

        // Even with invalid JSON in a success response, it should still wrap it as a string
        var apiResponse = JsonSerializer.Deserialize<ApiResponse>(responseContent, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });

        Assert.NotNull(apiResponse);
        Assert.NotNull(apiResponse.Data);
        Assert.Equal("{invalid json", apiResponse.Data.ToString());
    }

    [Theory]
    [InlineData(301)] // Moved Permanently
    [InlineData(302)] // Found
    [InlineData(304)] // Not Modified
    public async Task Invoke_Should_Not_Wrap_Redirect_Responses(int statusCode)
    {
        // Arrange
        _httpContext.Request.Path = "/api/test";
        _httpContext.Response.StatusCode = statusCode;
        _httpContext.Response.ContentType = "text/html";
        _httpContext.Response.Headers["Location"] = "/new-location";

        _nextMock.Setup(next => next(_httpContext))
            .Returns(Task.CompletedTask);

        var originalStream = new MemoryStream();
        _httpContext.Response.Body = originalStream;

        // Act
        await _middleware.Invoke(_httpContext);

        // Assert
        originalStream.Seek(0, SeekOrigin.Begin);
        var responseContent = await new StreamReader(originalStream).ReadToEndAsync();

        // Should be empty (no wrapping for redirects)
        Assert.Empty(responseContent);
    }

    [Fact]
    public async Task Invoke_Should_Always_Return_Empty_Details_Array_For_Errors()
    {
        // Arrange
        _httpContext.Request.Path = "/api/test";
        _httpContext.Response.StatusCode = 404;
        _httpContext.Response.ContentType = "text/plain";

        _nextMock.Setup(next => next(_httpContext))
            .Callback(async () =>
            {
                await _httpContext.Response.WriteAsync("Not found");
            })
            .Returns(Task.CompletedTask);

        var originalStream = new MemoryStream();
        _httpContext.Response.Body = originalStream;

        // Act
        await _middleware.Invoke(_httpContext);

        // Assert
        originalStream.Seek(0, SeekOrigin.Begin);
        var responseContent = await new StreamReader(originalStream).ReadToEndAsync();
        var apiResponse = JsonSerializer.Deserialize<ApiResponse>(responseContent, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });

        Assert.NotNull(apiResponse);
        Assert.NotNull(apiResponse.Error);
        Assert.NotNull(apiResponse.Error.Details);
        Assert.Empty(apiResponse.Error.Details);
    }
}