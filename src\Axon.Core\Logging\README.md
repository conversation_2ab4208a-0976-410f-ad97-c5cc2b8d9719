# Serilog with Automatic Log Sanitization

## Overview

This solution combines **Serilog's powerful structured logging** with **automatic CWE-117 sanitization** to provide secure, feature-rich logging across the entire application. Developers get the best of both worlds: advanced logging capabilities with built-in security.

## Architecture

### **Serilog + SanitizedLogger Wrapper**
```
Application Code
       ↓
   ILogger<T> (Dependency Injection)
       ↓
SanitizedLogger<T> (Security Layer)
       ↓
   Serilog ILogger (Structured Logging)
       ↓
Multiple Sinks (Console, File, etc.)
```

## Key Features

### **🔒 Security (CWE-117 Prevention)**
- **Log Injection Prevention**: Removes `\r`, `\n`, `\t`, control characters
- **PII Protection**: Automatically masks emails, phones, personal info
- **Smart Detection**: Recognizes sensitive data by property names and patterns
- **Length Limiting**: Prevents log spam attacks

### **📊 Structured Logging (Serilog)**
- **Rich Structured Data**: Capture complex objects as structured properties
- **Multiple Sinks**: Console, potentially Elasticsearch, Seq, etc.
- **Contextual Logging**: Correlation IDs, scoped properties
- **Performance**: Efficient message templates and structured capture
- **Configuration**: Environment-specific logging via appsettings.json

## Configuration

### **appsettings.json**
```json
{
  "Serilog": {
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Warning",
        "System": "Warning"
      }
    },
    "WriteTo": [
      {
        "Name": "Console",
        "Args": {
          "outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"
        }
      }
    ]
  }
}
```

### **appsettings.Development.json**
```json
{
  "Serilog": {
    "MinimumLevel": {
      "Default": "Debug"
    },
    "WriteTo": [
      {
        "Name": "Console",
        "Args": {
          "outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {SourceContext} {Message:lj} {Properties:j}{NewLine}{Exception}"
        }
      }
    ]
  }
}
```

## Usage Examples

### **Basic Structured Logging with Automatic Sanitization**
```csharp
// Input (potentially dangerous)
_logger.LogInformation("User {CustomerEmail} created order {IncrementId}", 
    "<EMAIL>\r\nMALICIOUS LOG", 
    "1234567890");

// Output (automatically sanitized)
// [14:32:15 INF] User use***@example.com_MALICIOUS LOG created order 1234567890 
// {"CustomerEmail": "use***@example.com", "IncrementId": "1234567890_MALICIOUS LOG"}
```

### **Complex Object Logging**
```csharp
var orderData = new {
    CustomerEmail = "<EMAIL>",
    CustomerFirstname = "Jane",
    IncrementId = "cart456\nMalicious\tEntry",
    BillingAddress = new {
        Street = "123 Main St",
        Telephone = "(*************"
    }
};

// Automatically sanitizes nested properties
_logger.LogInformation("Processing order {@OrderData}", orderData);
```

### **Contextual Logging with Correlation IDs**
```csharp
using (_logger.BeginScope(new Dictionary<string, object>
{
    ["CorrelationId"] = correlationId,
    ["UserId"] = userId
}))
{
    _logger.LogInformation("Processing request for {CustomerEmail}", email);
    // Scoped properties automatically included in all logs
}
```

## Sanitization Rules

### **Automatic Email Masking**
- `<EMAIL>` → `use***@example.com`
- Preserves domain for debugging while protecting privacy

### **PII Property Detection**
Properties automatically sanitized: `CustomerEmail`, `Firstname`, `Lastname`, `Name`, `Address`, `Telephone`, `Phone`

### **Phone Number Masking**
- `(*************` → `55***4567`
- `******-123-4567` → `+1***4567`

### **Identifier Protection**
- Long IDs truncated: `very-long-id...` (prevents spam)
- Properties ending in `Id` or `Number` get length-limited

## Serilog Features

### **HTTP Request Logging**
Automatic HTTP request/response logging with sanitized user data:
```
[14:32:15 INF] HTTP POST /api/orders responded 200 in 45.2 ms
{"RequestHost": "localhost:5000", "CorrelationId": "abc123"}
```

### **Multiple Sinks**
- **Console**: Development debugging
- **File**: Production logging with rolling
- **Future**: Elasticsearch, Seq, Application Insights

### **Environment-Specific Configuration**
- **Development**: Debug level, verbose output
- **Production**: Information level, structured JSON
- **Staging**: Custom configurations per environment

## Benefits

✅ **Security First**: CWE-117 prevention built-in  
✅ **Rich Logging**: Structured data capture  
✅ **Performance**: Efficient message templates  
✅ **Flexibility**: Multiple output formats and destinations  
✅ **Debugging**: Correlation IDs and contextual data  
✅ **Compliance**: Automatic PII masking  
✅ **Zero Overhead**: No code changes needed for security  


## File Outputs

### **Console Output (Development)**
```
[14:32:15 INF] Axon.Controllers.OrderController User use***@example.com created order 1234567890 
{"CustomerEmail": "use***@example.com", "IncrementId": "1234567890", "CorrelationId": "abc-123"}
```

### **File Output (Production)**
```
2024-01-15 14:32:15.123 +00:00 [INF] User use***@example.com created order 1234567890 
{"CustomerEmail": "use***@example.com", "IncrementId": "1234567890", "Environment": "Production"}
```

## Architecture Benefits

1. **Security by Default**: Every log statement automatically protected
2. **Rich Context**: Structured properties for better analysis
3. **Performance**: Compiled message templates
4. **Flexibility**: Easy to add new sinks or change formats
5. **Maintainability**: Single configuration point for all logging behavior 