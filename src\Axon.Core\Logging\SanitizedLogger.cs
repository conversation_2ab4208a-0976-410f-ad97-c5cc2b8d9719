using System.Text.RegularExpressions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;

namespace Axon.Core.Logging;

/// <summary>
/// ILogger wrapper that automatically sanitizes all log statements to prevent CWE-117 log injection attacks
/// This wraps the standard ILogger and sanitizes all parameters before logging
/// </summary>
// codeql[cs/log-forging] - This class IS the CWE-117 mitigation - it sanitizes logs to prevent injection
public class SanitizedLogger<T> : ILogger<T>
{
    private readonly ILogger _innerLogger;

    // Regex patterns for detecting different types of sensitive data
    private static readonly Regex LogInjectionPattern = new Regex(
        @"[\r\n\t\x00-\x1F\x7F-\x9F]",
        RegexOptions.Compiled
    );

    private static readonly Regex EmailPattern = new Regex(
        @"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b",
        RegexOptions.Compiled | RegexOptions.IgnoreCase
    );

    private static readonly Regex PhonePattern = new Regex(
        @"(\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})",
        RegexOptions.Compiled
    );

    // Known PII property names (case-insensitive)
    private static readonly HashSet<string> PiiPropertyNames = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
    {
        "CustomerEmail", "Email", "CustomerFirstname", "CustomerLastname",
        "Firstname", "Lastname", "Name", "Address", "Street", "City",
        "Telephone", "Phone", "PhoneNumber", "CustomerName"
    };

    public SanitizedLogger(ILoggerFactory loggerFactory)
    {
        _innerLogger = loggerFactory.CreateLogger<T>();
    }

    public IDisposable? BeginScope<TState>(TState state) where TState : notnull => _innerLogger.BeginScope(state);
    public bool IsEnabled(LogLevel logLevel) => _innerLogger.IsEnabled(logLevel);

    // codeql[cs/log-forging] - This method sanitizes logs to prevent CWE-117 injection attacks
    public void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception? exception,
        Func<TState, Exception?, string> formatter)
    {
        if (!IsEnabled(logLevel))
            return;

        // Create a sanitized formatter that sanitizes the final message
        var sanitizedFormatter = new Func<TState, Exception?, string>((s, ex) =>
        {
            var originalMessage = formatter(s, ex);
            return SanitizeLogMessage(originalMessage);
        });

        _innerLogger.Log(logLevel, eventId, state, exception, sanitizedFormatter);
    }

    // codeql[cs/log-forging] - This method implements CWE-117 sanitization logic
    private string SanitizeLogMessage(string message)
    {
        if (string.IsNullOrEmpty(message))
            return message;

        // Remove log injection characters from the final message
        var sanitized = LogInjectionPattern.Replace(message, "_");

        // Also sanitize any emails or phones that might appear in the message
        sanitized = EmailPattern.Replace(sanitized, match =>
        {
            var emailValue = match.Value;
            var parts = emailValue.Split('@');
            if (parts.Length == 2 && parts[0].Length > 3)
            {
                return $"{parts[0].Substring(0, 3)}***@{parts[1]}";
            }

            return "***@***.***";
        });

        sanitized = PhonePattern.Replace(sanitized, "***-***-****");

        return sanitized;
    }
}

/// <summary>
/// Service collection extensions for registered sanitized logging
/// </summary>
public static class SanitizedLoggingExtensions
{
    /// <summary>
    /// Adds sanitized logging by replacing ILogger<T> instances with SanitizedLogger<T> to automatically prevent CWE-117 vulnerabilities
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <returns>The service collection for chaining</returns>
    public static IServiceCollection AddSanitizedLogging(this IServiceCollection services)
    {
        // Replace ILogger<T> with SanitizedLogger<T> using the factory pattern
        services.AddSingleton(typeof(ILogger<>), typeof(SanitizedLogger<>));

        return services;
    }
}