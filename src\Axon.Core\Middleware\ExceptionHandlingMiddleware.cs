using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System.Net;
using System.Text.Json;
using Axon.Core.Models;

namespace Axon.Core.Middleware;

public class ExceptionHandlingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<ExceptionHandlingMiddleware> _logger;

    public ExceptionHandlingMiddleware(RequestDelegate next, ILogger<ExceptionHandlingMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task Invoke(HttpContext context)
    {
        try
        {
            await _next(context);
        }
        catch (Exception ex)
        {
            await HandleExceptionAsync(context, ex);
        }
    }

    private async Task HandleExceptionAsync(HttpContext context, Exception exception)
    {
        context.Response.ContentType = "application/json";
        
        ApiResponse<object> errorResponse;
        
        // Check for ConflictException by type name since we can't reference it directly
        if (exception.GetType().Name == "ConflictException")
        {
            _logger.LogWarning(exception, "Conflict occurred");
            context.Response.StatusCode = (int)HttpStatusCode.Conflict;
            errorResponse = new ApiResponse<object>
            {
                Data = null,
                Error = new ApiError
                {
                    Message = exception.Message,
                    Type = "CONFLICT_ERROR"
                }
            };
        }
        else
        {
            _logger.LogError(exception, "Unhandled exception occurred");
            context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
            errorResponse = new ApiResponse<object>
            {
                Data = null,
                Error = ApiError.InternalServerError()
            };
        }

        await context.Response.WriteAsync(JsonSerializer.Serialize(errorResponse, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        }));
    }
}