using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using System.Text;
using System.Text.Json;
using Axon.Core.Models;

namespace Axon.Core.Middleware;

/// <summary>
/// Middleware that wraps all API responses in a standard envelope format
/// </summary>
public class ResponseEnvelopeMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<ResponseEnvelopeMiddleware> _logger;

    public ResponseEnvelopeMiddleware(RequestDelegate next, ILogger<ResponseEnvelopeMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task Invoke(HttpContext context)
    {
        // Skip wrapping for certain endpoints (health checks, metrics, swagger, etc.)
        if (ShouldSkipWrapping(context.Request.Path))
        {
            await _next(context);
            return;
        }

        // Store the original response body stream
        var originalBodyStream = context.Response.Body;

        try
        {
            // Create a new memory stream for the response body
            using var responseBody = new MemoryStream();
            context.Response.Body = responseBody;

            // Call the next middleware in the pipeline
            await _next(context);

            // Only wrap the response if it's a JSON response and not already wrapped
            if (ShouldWrapResponse(context))
            {
                await WrapResponse(context, responseBody, originalBodyStream);
            }
            else
            {
                // Copy the response body to the original stream without wrapping
                responseBody.Seek(0, SeekOrigin.Begin);
                await responseBody.CopyToAsync(originalBodyStream);
            }
        }
        finally
        {
            // Restore the original response body stream
            context.Response.Body = originalBodyStream;
        }
    }

    private static bool ShouldSkipWrapping(PathString requestPath)
    {
        var path = requestPath.Value?.ToLowerInvariant() ?? string.Empty;

        // Skip wrapping for infrastructure endpoints
        return path.StartsWith("/healthz") ||
               path.StartsWith("/health") ||
               path.StartsWith("/metrics") ||
               path.StartsWith("/swagger") ||
               path.StartsWith("/favicon.ico") ||
               path.Contains("_framework") ||
               path.Contains("_content");
    }

    private static bool ShouldWrapResponse(HttpContext context)
    {
        var contentType = context.Response.ContentType?.ToLowerInvariant();
        var statusCode = context.Response.StatusCode;

        // Always wrap error responses (4xx, 5xx) regardless of content type
        if (statusCode >= 400 && statusCode < 600)
        {
            return true;
        }

        // For success responses, only wrap JSON
        if (contentType == null || !contentType.Contains("application/json"))
        {
            return false;
        }

        // Wrap 2xx JSON responses
        return statusCode >= 200 && statusCode < 300;
    }

    private async Task WrapResponse(HttpContext context, MemoryStream responseBody, Stream originalBodyStream)
    {
        try
        {
            responseBody.Seek(0, SeekOrigin.Begin);
            var responseContent = await new StreamReader(responseBody).ReadToEndAsync();

            object? data = null;
            ApiError? error = null;

            // Check if response already has data (not empty)
            if (!string.IsNullOrWhiteSpace(responseContent))
            {
                try
                {
                    // Check if the response is already an ApiResponse (e.g., from validation errors)
                    if (responseContent.Contains("\"data\"") && responseContent.Contains("\"error\""))
                    {
                        // Response is already in ApiResponse format, don't wrap it
                        await WriteResponse(context, responseContent, originalBodyStream);
                        return;
                    }

                    // Try to deserialize the existing response
                    data = JsonSerializer.Deserialize<object>(responseContent);
                }
                catch
                {
                    // If deserialization fails, treat as plain text
                    data = responseContent;
                }
            }

            // Handle different status codes
            if (context.Response.StatusCode >= 400)
            {
                // Use custom message if provided, otherwise use default
                var errorMessage = !string.IsNullOrWhiteSpace(responseContent) && data is string customMessage
                    ? customMessage
                    : GetErrorMessageForStatus(context.Response.StatusCode);

                error = new ApiError
                {
                    Message = errorMessage,
                    Type = GetErrorCodeForStatus(context.Response.StatusCode)
                };
                data = null;
            }

            var apiResponse = new ApiResponse<object>
            {
                Data = data,
                Error = error
            };

            var jsonResponse = JsonSerializer.Serialize(apiResponse, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            await WriteResponse(context, jsonResponse, originalBodyStream);
        }
        catch (Exception ex)
        {
            // Log error and return a generic error response
            var logger = context.RequestServices.GetService(typeof(ILogger<ResponseEnvelopeMiddleware>)) as ILogger<ResponseEnvelopeMiddleware>;
            logger?.LogError(ex, "Error wrapping response");

            var errorResponse = new ApiResponse<object>
            {
                Data = null,
                Error = new ApiError
                {
                    Message = "An unexpected error occurred while processing the response",
                    Type = "INTERNAL_SERVER_ERROR"
                }
            };

            var errorJson = JsonSerializer.Serialize(errorResponse, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            context.Response.StatusCode = 500;
            await WriteResponse(context, errorJson, originalBodyStream);
        }
    }

    private static async Task WriteResponse(HttpContext context, string content, Stream originalBodyStream)
    {
        context.Response.ContentType = "application/json";
        context.Response.ContentLength = Encoding.UTF8.GetByteCount(content);

        await originalBodyStream.WriteAsync(Encoding.UTF8.GetBytes(content));
    }

    private string GetErrorCodeForStatus(int statusCode)
    {
        return statusCode switch
        {
            400 => "BAD_REQUEST",
            401 => "UNAUTHORIZED",
            403 => "FORBIDDEN",
            404 => "NOT_FOUND",
            >= 500 => "INTERNAL_SERVER_ERROR",
            _ => "UNKNOWN_ERROR"
        };
    }

    private string GetErrorMessageForStatus(int statusCode)
    {
        return statusCode switch
        {
            400 => "Bad Request",
            401 => "Authentication is required for this resource",
            403 => "Forbidden",
            404 => "The requested resource was not found",
            >= 500 => "An unexpected error occurred while processing the request",
            _ => "An unknown error occurred"
        };
    }
}