using System.Text.Json.Serialization;

namespace Axon.Core.Models;

/// <summary>
/// Represents an error detail for a specific field
/// </summary>
public class ApiErrorDetail
{
    [JsonPropertyName("field")]
    public string Field { get; set; } = string.Empty;

    [JsonPropertyName("message")]
    public string Message { get; set; } = string.Empty;

    public ApiErrorDetail()
    {
    }

    public ApiErrorDetail(string field, string message)
    {
        Field = field;
        Message = message;
    }
}

/// <summary>
/// Represents an API error with message, type, and optional field-specific details
/// </summary>
public class ApiError
{
    [JsonPropertyName("message")]
    public string Message { get; set; } = string.Empty;

    [JsonPropertyName("type")]
    public string Type { get; set; } = string.Empty;

    [JsonPropertyName("details")]
    public List<ApiErrorDetail> Details { get; set; } = new List<ApiErrorDetail>();

    public ApiError()
    {
    }

    public ApiError(string message, string type, List<ApiErrorDetail>? details = null)
    {
        Message = message;
        Type = type;
        Details = details ?? new List<ApiErrorDetail>();
    }

    /// <summary>
    /// Creates a validation error with field-specific details
    /// </summary>
    public static ApiError ValidationError(string message, List<ApiErrorDetail> details)
    {
        return new ApiError(message, "ValidationError", details);
    }

    /// <summary>
    /// Creates a validation error with a single field detail
    /// </summary>
    public static ApiError ValidationError(string message, string field, string fieldMessage)
    {
        return new ApiError(message, "ValidationError", [new ApiErrorDetail(field, fieldMessage)]);
    }

    /// <summary>
    /// Creates a generic error without field details
    /// </summary>
    public static ApiError GenericError(string message, string type)
    {
        return new ApiError(message, type);
    }

    /// <summary>
    /// Creates an internal server error
    /// </summary>
    public static ApiError InternalServerError(string message = "An unexpected error occurred")
    {
        return new ApiError(message, "InternalServerError");
    }

    /// <summary>
    /// Creates a not found error
    /// </summary>
    public static ApiError NotFoundError(string message)
    {
        return new ApiError(message, "NotFoundError");
    }

    /// <summary>
    /// Creates an unauthorized error
    /// </summary>
    public static ApiError UnauthorizedError(string message = "Authentication required")
    {
        return new ApiError(message, "UnauthorizedError");
    }

    /// <summary>
    /// Creates a forbidden error
    /// </summary>
    public static ApiError ForbiddenError(string message = "Access forbidden")
    {
        return new ApiError(message, "ForbiddenError");
    }
}

/// <summary>
/// Standard envelope wrapper for all API responses
/// </summary>
/// <typeparam name="T">The type of data being returned</typeparam>
public class ApiResponse<T>
{
    [JsonPropertyName("data")]
    public T? Data { get; set; }

    [JsonPropertyName("error")]
    public ApiError? Error { get; set; }

    public ApiResponse()
    {
    }

    public ApiResponse(T? data, ApiError? error = null)
    {
        Data = data;
        Error = error;
    }

    /// <summary>
    /// Creates a successful response with data
    /// </summary>
    public static ApiResponse<T> SuccessResponse(T data)
    {
        return new ApiResponse<T>(data);
    }

    /// <summary>
    /// Creates an error response
    /// </summary>
    public static ApiResponse<T> ErrorResponse(ApiError error)
    {
        return new ApiResponse<T>(default(T), error);
    }

    /// <summary>
    /// Creates an error response with a simple message and type
    /// </summary>
    public static ApiResponse<T> ErrorResponse(string message, string type)
    {
        return new ApiResponse<T>(default(T), ApiError.GenericError(message, type));
    }

    /// <summary>
    /// Creates a validation error response
    /// </summary>
    public static ApiResponse<T> ValidationErrorResponse(string message, List<ApiErrorDetail> details)
    {
        return new ApiResponse<T>(default(T), ApiError.ValidationError(message, details));
    }

    /// <summary>
    /// Creates a validation error response with a single field
    /// </summary>
    public static ApiResponse<T> ValidationErrorResponse(string message, string field, string fieldMessage)
    {
        return new ApiResponse<T>(default(T), ApiError.ValidationError(message, field, fieldMessage));
    }
}

/// <summary>
/// Non-generic version for responses without typed data
/// </summary>
public class ApiResponse
{
    [JsonPropertyName("data")]
    public object? Data { get; set; }

    [JsonPropertyName("error")]
    public ApiError? Error { get; set; }

    public ApiResponse()
    {
    }

    public ApiResponse(object? data, ApiError? error = null)
    {
        Data = data;
        Error = error;
    }

    /// <summary>
    /// Creates a successful response with data
    /// </summary>
    public static ApiResponse SuccessResponse(object? data = null)
    {
        return new ApiResponse(data);
    }

    /// <summary>
    /// Creates an error response
    /// </summary>
    public static ApiResponse ErrorResponse(ApiError error)
    {
        return new ApiResponse(null, error);
    }

    /// <summary>
    /// Creates an error response with a simple message and type
    /// </summary>
    public static ApiResponse ErrorResponse(string message, string type)
    {
        return new ApiResponse(null, ApiError.GenericError(message, type));
    }

    /// <summary>
    /// Creates a validation error response
    /// </summary>
    public static ApiResponse ValidationErrorResponse(string message, List<ApiErrorDetail> details)
    {
        return new ApiResponse(null, ApiError.ValidationError(message, details));
    }

    /// <summary>
    /// Creates a validation error response with a single field
    /// </summary>
    public static ApiResponse ValidationErrorResponse(string message, string field, string fieldMessage)
    {
        return new ApiResponse(null, ApiError.ValidationError(message, field, fieldMessage));
    }
}