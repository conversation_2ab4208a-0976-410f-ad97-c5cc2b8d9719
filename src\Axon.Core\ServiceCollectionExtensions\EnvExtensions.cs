using DotNetEnv;
using Microsoft.Extensions.Hosting;

namespace Axon.Core.ServiceCollectionExtensions;

public static class EnvExtensions
{
    /// <summary>
    /// Loads environment variables from a .env file (default: .env.local) only in Development environment.
    /// Call this at the start of your Program.cs.
    /// </summary>
    /// <param name="hostBuilder">The IHostBuilder instance.</param>
    /// <param name="envFile">The .env file to load (default: .env.local).</param>
    /// <returns>The IHostBuilder for chaining.</returns>
    public static IHostBuilder LoadEnvFile(this IHostBuilder hostBuilder, string envFile = ".env.local")
    {
        hostBuilder.ConfigureAppConfiguration((hostingContext, config) =>
        {
            // Env.Load(envFile);
        });
        return hostBuilder;
    }
} 