using Axon.Core.Middleware;
using Axon.Core.Models;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Prometheus;
using Axon.Core.Logging;
using Serilog;
using Serilog.Events;
using Serilog.Sinks.SystemConsole.Themes;
using System.Linq;
using Microsoft.Extensions.Configuration;

namespace Axon.Core.ServiceCollectionExtensions;

public static class HostingExtensions
{
    public static IHostBuilder ConfigureAxonDefaults(this IHostBuilder hostBuilder)
    {
        return hostBuilder.ConfigureAppConfiguration((hostingContext, config) => { })
            .UseSerilog((context, configuration) =>
            {
                var environment = context.HostingEnvironment.EnvironmentName;
                var isLocalDevelopment = IsLocalDevelopmentEnvironment(environment);

                configuration
                    .MinimumLevel.Information()
                    .MinimumLevel.Override("Microsoft", LogEventLevel.Warning)
                    .MinimumLevel.Override("Microsoft.Hosting.Lifetime", LogEventLevel.Information)
                    .MinimumLevel.Override("System", LogEventLevel.Warning)
                    .Enrich.FromLogContext()
                    .Enrich.WithEnvironmentName()
                    .Enrich.WithMachineName()
                    .Enrich.WithProcessId()
                    .Enrich.WithThreadId();

                // Configure console output based on environment
                if (isLocalDevelopment)
                {
                    // Local Development: Colorized, human-readable output perfect for docker-compose
                    configuration.WriteTo.Console(
                        theme: AnsiConsoleTheme.Code,
                        outputTemplate:
                        "[{Timestamp:HH:mm:ss} {Level:u3}] {SourceContext:l}: {Message:lj}{NewLine}{Exception}"
                    );
                }
                else
                {
                    // Production/Staging/Testing: Structured JSON for log aggregation tools
                    configuration.WriteTo.Console(
                        formatter: new Serilog.Formatting.Compact.RenderedCompactJsonFormatter()
                    );
                }

                configuration.ReadFrom.Configuration(context.Configuration);
            })
            .ConfigureWebHostDefaults(webBuilder =>
            {
                // Common app configuration can be added here if it's truly universal
                // or broken into smaller, more granular extensions.
            });
    }

    /// <summary>
    /// Determines if the current environment should use local development logging (colorized, human-readable)
    /// vs production logging (structured JSON)
    /// </summary>
    /// <param name="environmentName">The environment name (e.g., Development, Production, Staging)</param>
    /// <returns>True if local development logging should be used, false for structured JSON</returns>
    private static bool IsLocalDevelopmentEnvironment(string environmentName)
    {
        // Only use colorized logging for these specific local development environments
        var localEnvironments = new[]
        {
            "Development",
            "Local",
            "Dev",
            "Debug"
        };

        return localEnvironments.Contains(environmentName, StringComparer.OrdinalIgnoreCase);
    }

    public static IServiceCollection AddAxonCoreServices(this IServiceCollection services, string[]? corsOrigins = null)
    {
        // Add sanitized logging to prevent CWE-117 log injection vulnerabilities
        // This wraps Serilog's ILogger<T> with automatic sanitization
        services.AddSanitizedLogging();

        // Configure controllers with built-in model validation
        services.AddControllers()
        .ConfigureApiBehaviorOptions(options =>
        {
            // Configure automatic model validation to return our ApiResponse format
            options.InvalidModelStateResponseFactory = context =>
            {
                var validationErrors = context.ModelState
                    .Where(x => x.Value?.Errors.Count > 0)
                    .SelectMany(x => x.Value!.Errors.Select(e => new ApiErrorDetail(x.Key, e.ErrorMessage)))
                    .ToList();

                var apiResponse = new ApiResponse<object>
                {
                    Data = null,
                    Error = new ApiError
                    {
                        Message = "One or more validation errors occurred.",
                        Type = "VALIDATION_ERROR",
                        Details = validationErrors
                    }
                };

                return new BadRequestObjectResult(apiResponse);
            };
        });

        services.AddHealthChecks();

        // Conditionally add CORS policy definition
        if (corsOrigins != null && corsOrigins.Length > 0)
        {
            services.AddCors(options =>
            {
                options.AddDefaultPolicy(builder =>
                {
                    builder.WithOrigins(corsOrigins)
                        .AllowAnyHeader()
                        .AllowAnyMethod();
                });
            });
        }

        return services;
    }

    public static IApplicationBuilder UseAxonCoreMiddleware(this IApplicationBuilder app, bool useCors)
    {
        if (useCors)
        {
            app.UseCors();
        }

        // Add Serilog request logging for HTTP requests
        app.UseSerilogRequestLogging(options =>
        {
            // Customize the message template for better readability
            options.MessageTemplate = "🌐 HTTP {RequestMethod} {RequestPath} → {StatusCode} in {Elapsed:0.0000}ms";

            // Emit debug-level events instead of the defaults
            options.GetLevel = (httpContext, elapsed, ex) => ex != null
                ? LogEventLevel.Error
                : httpContext.Response.StatusCode > 499
                    ? LogEventLevel.Error
                    : LogEventLevel.Information;

            // Attach additional properties to the request completion event
            options.EnrichDiagnosticContext = (diagnosticContext, httpContext) =>
            {
                if (httpContext.Request.Host.HasValue)
                {
                    diagnosticContext.Set("RequestHost", httpContext.Request.Host.Value);
                }
                diagnosticContext.Set("RequestScheme", httpContext.Request.Scheme);

                var userAgent = httpContext.Request.Headers["User-Agent"].FirstOrDefault();
                if (!string.IsNullOrEmpty(userAgent))
                {
                    diagnosticContext.Set("UserAgent", userAgent);
                }

                if (httpContext.Request.Headers.TryGetValue("X-Correlation-ID", out var correlationId))
                {
                    diagnosticContext.Set("CorrelationId", correlationId.ToString());
                }
            };
        });

        app.UseRouting();
        app.UseHttpMetrics();
        app.UseMiddleware<ResponseEnvelopeMiddleware>();
        app.UseMiddleware<ExceptionHandlingMiddleware>();
        // Note: UseAuthentication() should be called here if needed, before UseAuthorization()
        app.UseAuthorization();
        app.UseEndpoints(endpoints =>
        {
            endpoints.MapControllers();
            endpoints.MapHealthChecks("/healthz");
            endpoints.MapMetrics();
        });
        app.Use(async (ctx, next) =>
        {
            const string CorrelationIdHeader = "X-Correlation-ID";
            if (!ctx.Request.Headers.TryGetValue(CorrelationIdHeader, out var correlationId))
            {
                correlationId = Guid.NewGuid().ToString();
                ctx.Request.Headers[CorrelationIdHeader] = correlationId;
            }

            ctx.Response.Headers[CorrelationIdHeader] = correlationId;

            // Add correlation ID to Serilog's log context
            using (Serilog.Context.LogContext.PushProperty("CorrelationId", correlationId.ToString()))
            {
                await next();
            }
        });
        return app;
    }
}