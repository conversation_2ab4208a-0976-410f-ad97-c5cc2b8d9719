using Axon.Contracts.Cart.Commands;
using Axon.Domain.Service.Cart.Application;
using Axon.Domain.Service.Cart.Domain;
using Axon.Domain.Service.Cart.Domain.Exceptions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace Axon.Domain.Service.Tests.Cart.Application;

public class RecordCartCreatedHandlerTests
{
    private readonly Mock<ICartRepository> _cartRepositoryMock;
    private readonly Mock<ILogger<RecordCartCreatedHandler>> _loggerMock;
    private readonly RecordCartCreatedHandler _handler;

    public RecordCartCreatedHandlerTests()
    {
        _cartRepositoryMock = new Mock<ICartRepository>();
        _loggerMock = new Mock<ILogger<RecordCartCreatedHandler>>();
        _handler = new RecordCartCreatedHandler(_cartRepositoryMock.Object, _loggerMock.Object);
    }

    [Fact]
    public async Task Handle_Should_CreateNewCart_When_CartDoesNotExist()
    {
        // Arrange
        var command = new RecordCartCreatedCommand
        {
            CartId = "new-cart-123",
            CustomerId = "customer-456",
            StoreId = "1",
            CreatedAt = DateTimeOffset.UtcNow.AddMinutes(-5),
            UpdatedAt = DateTimeOffset.UtcNow.AddMinutes(-5),
            Currency = new Contracts.Cart.Commands.CartCurrency
            {
                BaseCurrencyCode = "USD",
                QuoteCurrencyCode = "USD"
            },
            Totals = new Contracts.Cart.Commands.CartTotals
            {
                GrandTotal = 100m,
                Subtotal = 90m,
                BaseSubtotal = 90m,
                TaxAmount = 10m,
                BaseTaxAmount = 10m
            },
            IsActive = true,
            IsVirtual = false,
            IsNegotiableQuote = false,
            IsMultiShipping = false,
            ItemsCount = 1,
            ItemsQty = 2,
            Items = new List<Contracts.Cart.Commands.CartItem>
            {
                new Contracts.Cart.Commands.CartItem
                {
                    ItemId = "item-1",
                    Sku = "SKU001",
                    Name = "Test Product",
                    Qty = 2,
                    Price = 45m,
                    ProductType = "simple"
                }
            },
            Customer = new Contracts.Cart.Commands.CartCustomer
            {
                Email = "<EMAIL>",
                GroupId = 1,
                IsGuest = false,
                FirstName = "John",
                LastName = "Doe"
            },
            IdempotencyKey = "cart-new-cart-123-created"
        };

        // No need to check ExistsAsync anymore - repository handles duplicate checking

        _cartRepositoryMock
            .Setup(x => x.AddAsync(It.IsAny<Axon.Domain.Service.Cart.Domain.Cart>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((Axon.Domain.Service.Cart.Domain.Cart cart, CancellationToken ct) => cart);

        // Act
        var result = await _handler.Handle(command);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("new-cart-123", result.CartId);
        Assert.Equal("customer-456", result.CustomerId);
        Assert.Equal("1", result.StoreId);
        Assert.Equal(90m, result.Totals.GrandTotal); // Recalculated: 2*45 = 90
        Assert.Single(result.Items);
        Assert.Equal("<EMAIL>", result.Customer.Email);
        Assert.Equal("John", result.Customer.FirstName);
        Assert.Equal("Doe", result.Customer.LastName);
        Assert.False(result.Customer.IsGuest);

        _cartRepositoryMock.Verify(x => x.AddAsync(
            It.Is<Axon.Domain.Service.Cart.Domain.Cart>(c => 
                c.CartId == "new-cart-123" &&
                c.CustomerId == "customer-456" &&
                c.StoreId == "1" &&
                c.Items.Count == 1 &&
                c.Customer.Email == "<EMAIL>"
            ),
            It.IsAny<CancellationToken>()
        ), Times.Once);
    }

    [Fact]
    public async Task Handle_Should_ThrowDuplicateCartException_When_CartAlreadyExists()
    {
        // Arrange
        var command = new RecordCartCreatedCommand
        {
            CartId = "existing-cart-123",
            CustomerId = "customer-789",
            StoreId = "2",
            Currency = new Contracts.Cart.Commands.CartCurrency { BaseCurrencyCode = "USD", QuoteCurrencyCode = "USD" },
            Totals = new Contracts.Cart.Commands.CartTotals(),
            Customer = new Contracts.Cart.Commands.CartCustomer { Email = "<EMAIL>" },
            IdempotencyKey = "cart-existing-cart-123-created"
        };

        _cartRepositoryMock
            .Setup(x => x.AddAsync(It.IsAny<Axon.Domain.Service.Cart.Domain.Cart>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new DuplicateCartException("existing-cart-123"));

        // Act & Assert
        var exception = await Assert.ThrowsAsync<DuplicateCartException>(() => _handler.Handle(command));
        Assert.Equal("Cart with ID 'existing-cart-123' already exists.", exception.Message);
        Assert.Equal("existing-cart-123", exception.CartId);

        _cartRepositoryMock.Verify(x => x.AddAsync(It.IsAny<Axon.Domain.Service.Cart.Domain.Cart>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Should_HandleGuestCart_When_CustomerIsGuest()
    {
        // Arrange
        var command = new RecordCartCreatedCommand
        {
            CartId = "guest-cart-123",
            CustomerId = null,
            StoreId = "1",
            Currency = new Contracts.Cart.Commands.CartCurrency { BaseCurrencyCode = "EUR", QuoteCurrencyCode = "EUR" },
            Totals = new Contracts.Cart.Commands.CartTotals(),
            Customer = new Contracts.Cart.Commands.CartCustomer
            {
                Email = null,
                GroupId = 0,
                IsGuest = true,
                FirstName = null,
                LastName = null
            },
            IsActive = true,
            IdempotencyKey = "cart-guest-cart-123-created"
        };

        // Repository handles duplicate checking
        _cartRepositoryMock
            .Setup(x => x.AddAsync(It.IsAny<Axon.Domain.Service.Cart.Domain.Cart>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((Axon.Domain.Service.Cart.Domain.Cart cart, CancellationToken ct) => cart);

        // Act
        var result = await _handler.Handle(command);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("guest-cart-123", result.CartId);
        Assert.Null(result.CustomerId);
        Assert.True(result.Customer.IsGuest);
        Assert.Null(result.Customer.Email);
        Assert.Equal("EUR", result.Currency.BaseCurrencyCode);
    }

    [Fact]
    public async Task Handle_Should_MapAddressesCorrectly_When_AddressesProvided()
    {
        // Arrange
        var command = new RecordCartCreatedCommand
        {
            CartId = "address-cart-123",
            StoreId = "1",
            Currency = new Contracts.Cart.Commands.CartCurrency { BaseCurrencyCode = "USD", QuoteCurrencyCode = "USD" },
            Totals = new Contracts.Cart.Commands.CartTotals(),
            Customer = new Contracts.Cart.Commands.CartCustomer { IsGuest = true },
            BillingAddress = new Contracts.Cart.Commands.CartAddress
            {
                Id = "billing-1",
                Firstname = "Bill",
                Lastname = "Smith",
                Street = new List<string> { "123 Billing St" },
                City = "Billing City",
                Region = "BC",
                Country = "US",
                Postcode = "12345",
                Telephone = "555-1111"
            },
            ShippingAddress = new Contracts.Cart.Commands.CartAddress
            {
                Id = "shipping-1",
                Firstname = "Ship",
                Lastname = "Jones",
                Street = new List<string> { "456 Shipping Ave", "Suite 100" },
                City = "Shipping City",
                Region = "SC",
                Country = "US",
                Postcode = "54321",
                Telephone = "555-2222"
            },
            IdempotencyKey = "cart-address-cart-123-created"
        };

        // Repository handles duplicate checking
        _cartRepositoryMock
            .Setup(x => x.AddAsync(It.IsAny<Axon.Domain.Service.Cart.Domain.Cart>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((Axon.Domain.Service.Cart.Domain.Cart cart, CancellationToken ct) => cart);

        // Act
        var result = await _handler.Handle(command);

        // Assert
        Assert.NotNull(result.BillingAddress);
        Assert.Equal("billing-1", result.BillingAddress.Id);
        Assert.Equal("Bill", result.BillingAddress.Firstname);
        Assert.Equal("Smith", result.BillingAddress.Lastname);
        Assert.Equal("123 Billing St", result.BillingAddress.Street[0]);
        Assert.Equal("Billing City", result.BillingAddress.City);

        Assert.NotNull(result.ShippingAddress);
        Assert.Equal("shipping-1", result.ShippingAddress.Id);
        Assert.Equal("Ship", result.ShippingAddress.Firstname);
        Assert.Equal("Jones", result.ShippingAddress.Lastname);
        Assert.Equal(2, result.ShippingAddress.Street.Count);
        Assert.Equal("456 Shipping Ave", result.ShippingAddress.Street[0]);
        Assert.Equal("Suite 100", result.ShippingAddress.Street[1]);
    }

    [Fact]
    public async Task Handle_Should_MapPaymentAndShippingMethods_When_Provided()
    {
        // Arrange
        var command = new RecordCartCreatedCommand
        {
            CartId = "methods-cart-123",
            StoreId = "1",
            Currency = new Contracts.Cart.Commands.CartCurrency { BaseCurrencyCode = "USD", QuoteCurrencyCode = "USD" },
            Totals = new Contracts.Cart.Commands.CartTotals(),
            Customer = new Contracts.Cart.Commands.CartCustomer { IsGuest = true },
            PaymentMethod = new Contracts.Cart.Commands.CartPaymentMethod
            {
                Method = "purchase_order",
                PoNumber = "PO-98765"
            },
            ShippingMethod = new Contracts.Cart.Commands.CartShippingMethod
            {
                CarrierCode = "ups",
                MethodCode = "ground",
                MethodTitle = "UPS Ground",
                Amount = 12.50m
            },
            IdempotencyKey = "cart-methods-cart-123-created"
        };

        // Repository handles duplicate checking
        _cartRepositoryMock
            .Setup(x => x.AddAsync(It.IsAny<Axon.Domain.Service.Cart.Domain.Cart>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((Axon.Domain.Service.Cart.Domain.Cart cart, CancellationToken ct) => cart);

        // Act
        var result = await _handler.Handle(command);

        // Assert
        Assert.NotNull(result.PaymentMethod);
        Assert.Equal("purchase_order", result.PaymentMethod.Method);
        Assert.Equal("PO-98765", result.PaymentMethod.PoNumber);

        Assert.NotNull(result.ShippingMethod);
        Assert.Equal("ups", result.ShippingMethod.CarrierCode);
        Assert.Equal("ground", result.ShippingMethod.MethodCode);
        Assert.Equal("UPS Ground", result.ShippingMethod.MethodTitle);
        Assert.Equal(12.50m, result.ShippingMethod.Amount);
    }

    [Fact]
    public async Task Handle_Should_SetNegotiableAndMultiShippingFlags()
    {
        // Arrange
        var command = new RecordCartCreatedCommand
        {
            CartId = "flags-cart-123",
            StoreId = "1",
            Currency = new Contracts.Cart.Commands.CartCurrency { BaseCurrencyCode = "USD", QuoteCurrencyCode = "USD" },
            Totals = new Contracts.Cart.Commands.CartTotals(),
            Customer = new Contracts.Cart.Commands.CartCustomer { IsGuest = false },
            IsNegotiableQuote = true,
            IsMultiShipping = true,
            IdempotencyKey = "cart-flags-cart-123-created"
        };

        // Repository handles duplicate checking
        _cartRepositoryMock
            .Setup(x => x.AddAsync(It.IsAny<Axon.Domain.Service.Cart.Domain.Cart>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((Axon.Domain.Service.Cart.Domain.Cart cart, CancellationToken ct) => cart);

        // Act
        var result = await _handler.Handle(command);

        // Assert
        Assert.True(result.IsNegotiableQuote);
        Assert.True(result.IsMultiShipping);
    }

    [Fact]
    public async Task Handle_Should_ThrowException_When_RepositoryFails()
    {
        // Arrange
        var command = new RecordCartCreatedCommand
        {
            CartId = "error-cart-123",
            StoreId = "1",
            Currency = new Contracts.Cart.Commands.CartCurrency { BaseCurrencyCode = "USD", QuoteCurrencyCode = "USD" },
            Totals = new Contracts.Cart.Commands.CartTotals(),
            Customer = new Contracts.Cart.Commands.CartCustomer { IsGuest = true },
            IdempotencyKey = "cart-error-cart-123-created"
        };

        // Repository handles duplicate checking
        _cartRepositoryMock
            .Setup(x => x.AddAsync(It.IsAny<Axon.Domain.Service.Cart.Domain.Cart>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Database error"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _handler.Handle(command));
    }
}