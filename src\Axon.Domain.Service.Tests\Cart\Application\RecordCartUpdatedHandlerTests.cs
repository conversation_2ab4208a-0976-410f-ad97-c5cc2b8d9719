using Axon.Contracts.Cart.Commands;
using Axon.Contracts.Cart.Events;
using Axon.Domain.Service.Cart.Application;
using Axon.Domain.Service.Cart.Domain;
using Axon.Domain.Service.Cart.Domain.Exceptions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace Axon.Domain.Service.Tests.Cart.Application;

public class RecordCartUpdatedHandlerTests
{
    private readonly Mock<ICartRepository> _cartRepositoryMock;
    private readonly Mock<ILogger<RecordCartUpdatedHandler>> _loggerMock;
    private readonly RecordCartUpdatedHandler _handler;

    public RecordCartUpdatedHandlerTests()
    {
        _cartRepositoryMock = new Mock<ICartRepository>();
        _loggerMock = new Mock<ILogger<RecordCartUpdatedHandler>>();
        _handler = new RecordCartUpdatedHandler(_cartRepositoryMock.Object, _loggerMock.Object);
    }

    [Fact]
    public void Handle_Should_Error_When_CartDoesNotExist()
    {
        // Arrange
        var command = new RecordCartUpdatedCommand
        {
            CartId = "not-real",
            UpdatedAt = DateTimeOffset.UtcNow,
            Totals = new Contracts.Cart.Commands.CartTotals
            {
                GrandTotal = 100m,
                Subtotal = 90m,
                BaseSubtotal = 90m,
                TaxAmount = 10m,
                BaseTaxAmount = 10m
            },
            IsNegotiableQuote = false,
            IsMultiShipping = false,
            Items = new List<Contracts.Cart.Commands.CartItem>
            {
                new Contracts.Cart.Commands.CartItem
                {
                    ItemId = "item-1",
                    Sku = "SKU001",
                    Name = "Test Product",
                    Qty = 2,
                    Price = 45m,
                    ProductType = "simple"
                }
            },
            IdempotencyKey = "cart-nonexistent-cart-123-updated"
        };

        _cartRepositoryMock
            .Setup(x => x.AddAsync(It.IsAny<Axon.Domain.Service.Cart.Domain.Cart>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((Axon.Domain.Service.Cart.Domain.Cart cart, CancellationToken ct) => cart);

        // Act & Assert
        var exception = Assert.ThrowsAsync<NonexistentCartException>(async () =>
            await _handler.Handle(command));
    }

    [Fact]
    public async Task Handle_Should_Successfully_Update_Cart_With_Minimal_Data()
    {
        // Arrange
        var cartId = "cart-123";
        var storeId = "1";
        var existingCart = Axon.Domain.Service.Cart.Domain.Cart.Create(
            cartId: cartId,
            customerId: "customer-123",
            storeId: storeId,
            createdAt: DateTimeOffset.UtcNow.AddHours(-1),
            currency: new Axon.Domain.Service.Cart.Domain.CartCurrency("USD", "USD"),
            customer: new Axon.Domain.Service.Cart.Domain.CartCustomer(
                Email: "<EMAIL>",
                GroupId: 1,
                IsGuest: false,
                FirstName: "John",
                LastName: "Doe"
            )
        );

        var command = new RecordCartUpdatedCommand
        {
            CartId = cartId,
            StoreId = storeId,
            UpdatedAt = DateTimeOffset.UtcNow,
            Totals = new Contracts.Cart.Commands.CartTotals
            {
                GrandTotal = 150m,
                Subtotal = 140m,
                BaseSubtotal = 140m,
                TaxAmount = 10m,
                BaseTaxAmount = 10m
            },
            IsNegotiableQuote = false,
            IsMultiShipping = false,
            Items = new List<Contracts.Cart.Commands.CartItem>
            {
                new Contracts.Cart.Commands.CartItem
                {
                    ItemId = "item-1",
                    Sku = "SKU001",
                    Name = "Product 1",
                    Qty = 2,
                    Price = 50m,
                    ProductType = "simple"
                },
                new Contracts.Cart.Commands.CartItem
                {
                    ItemId = "item-2",
                    Sku = "SKU002",
                    Name = "Product 2",
                    Qty = 1,
                    Price = 40m,
                    ProductType = "configurable"
                }
            },
            IdempotencyKey = "test-update-123"
        };

        _cartRepositoryMock
            .Setup(x => x.GetByIdAsync(cartId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingCart);

        _cartRepositoryMock
            .Setup(x => x.SaveNewVersionAsync(It.IsAny<Axon.Domain.Service.Cart.Domain.Cart>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((Axon.Domain.Service.Cart.Domain.Cart cart, CancellationToken ct) => cart);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(storeId, result.StoreId);
        Assert.Equal(cartId, result.CartId);
        Assert.Equal(2, result.ItemsCount);
        Assert.Equal(3, result.ItemsQty);
        Assert.Equal(command.IsNegotiableQuote, result.IsNegotiableQuote);
        Assert.Equal(command.IsMultiShipping, result.IsMultiShipping);
        
        // Verify items are mapped correctly
        Assert.Equal(2, result.Items.Count);
        Assert.Contains(result.Items, i => i.Sku == "SKU001" && i.Qty == 2);
        Assert.Contains(result.Items, i => i.Sku == "SKU002" && i.Qty == 1);
        
        // Verify repository calls
        _cartRepositoryMock.Verify(x => x.SaveNewVersionAsync(It.IsAny<Axon.Domain.Service.Cart.Domain.Cart>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Should_Successfully_Update_Cart_With_Full_Data()
    {
        // Arrange
        var cartId = "cart-456";
        var storeId = "2";
        var existingCart = Axon.Domain.Service.Cart.Domain.Cart.Create(
            cartId: cartId,
            customerId: "customer-456",
            storeId: storeId,
            createdAt: DateTimeOffset.UtcNow.AddHours(-2),
            currency: new Axon.Domain.Service.Cart.Domain.CartCurrency("USD", "USD"),
            customer: new Axon.Domain.Service.Cart.Domain.CartCustomer(
                Email: "<EMAIL>",
                GroupId: 2,
                IsGuest: false,
                FirstName: "Jane",
                LastName: "Smith"
            )
        );

        var command = new RecordCartUpdatedCommand
        {
            CartId = cartId,
            StoreId = storeId,
            UpdatedAt = DateTimeOffset.UtcNow,
            Totals = new Contracts.Cart.Commands.CartTotals
            {
                GrandTotal = 215m,
                Subtotal = 200m,
                BaseSubtotal = 200m,
                TaxAmount = 15m,
                BaseTaxAmount = 15m
            },
            IsNegotiableQuote = true,
            IsMultiShipping = true,
            Items = new List<Contracts.Cart.Commands.CartItem>
            {
                new Contracts.Cart.Commands.CartItem
                {
                    ItemId = "item-3",
                    Sku = "SKU003",
                    Name = "Product 3",
                    Qty = 1,
                    Price = 200m,
                    ProductType = "bundle",
                    ProductOption = new { color = "red", size = "L" }
                }
            },
            BillingAddress = new Contracts.Cart.Commands.CartAddress
            {
                Id = "billing-1",
                Region = "CA",
                Country = "US",
                Street = new List<string> { "123 Main St", "Apt 4B" },
                City = "Los Angeles",
                Postcode = "90001",
                Firstname = "Jane",
                Lastname = "Smith",
                Telephone = "555-1234"
            },
            ShippingAddress = new Contracts.Cart.Commands.CartAddress
            {
                Id = "shipping-1",
                Region = "NY",
                Country = "US",
                Street = new List<string> { "456 Park Ave" },
                City = "New York",
                Postcode = "10001",
                Firstname = "Jane",
                Lastname = "Smith",
                Telephone = "555-5678"
            },
            PaymentMethod = new Contracts.Cart.Commands.CartPaymentMethod
            {
                Method = "purchase_order",
                PoNumber = "PO-12345"
            },
            ShippingMethod = new Contracts.Cart.Commands.CartShippingMethod
            {
                CarrierCode = "fedex",
                MethodCode = "fedex_2day",
                MethodTitle = "FedEx 2Day",
                Amount = 15m
            },
            IdempotencyKey = "test-full-update-456"
        };

        _cartRepositoryMock
            .Setup(x => x.GetByIdAsync(cartId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingCart);

        _cartRepositoryMock
            .Setup(x => x.SaveNewVersionAsync(It.IsAny<Axon.Domain.Service.Cart.Domain.Cart>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((Axon.Domain.Service.Cart.Domain.Cart cart, CancellationToken ct) => cart);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(cartId, result.CartId);
        Assert.Equal(storeId, result.StoreId);
        Assert.True(result.IsNegotiableQuote);
        Assert.True(result.IsMultiShipping);
        
        // Verify addresses
        Assert.NotNull(result.BillingAddress);
        Assert.Equal("billing-1", result.BillingAddress.Id);
        Assert.Equal("Los Angeles", result.BillingAddress.City);
        
        Assert.NotNull(result.ShippingAddress);
        Assert.Equal("shipping-1", result.ShippingAddress.Id);
        Assert.Equal("New York", result.ShippingAddress.City);
        
        // Verify payment method
        Assert.NotNull(result.PaymentMethod);
        Assert.Equal("purchase_order", result.PaymentMethod.Method);
        Assert.Equal("PO-12345", result.PaymentMethod.PoNumber);
        
        // Verify shipping method
        Assert.NotNull(result.ShippingMethod);
        Assert.Equal("fedex", result.ShippingMethod.CarrierCode);
        Assert.Equal(15m, result.ShippingMethod.Amount);
        
        // Verify repository calls
        _cartRepositoryMock.Verify(x => x.SaveNewVersionAsync(It.IsAny<Axon.Domain.Service.Cart.Domain.Cart>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Should_Update_Cart_With_Virtual_Items_Only()
    {
        // Arrange
        var cartId = "cart-virtual";
        var existingCart = Axon.Domain.Service.Cart.Domain.Cart.Create(
            cartId: cartId,
            customerId: "customer-virtual",
            storeId: "store-1",
            createdAt: DateTimeOffset.UtcNow.AddHours(-1),
            currency: new Axon.Domain.Service.Cart.Domain.CartCurrency("USD", "USD"),
            customer: new Axon.Domain.Service.Cart.Domain.CartCustomer(
                Email: "<EMAIL>",
                GroupId: 1,
                IsGuest: false,
                FirstName: "Virtual",
                LastName: "Customer"
            )
        );

        var command = new RecordCartUpdatedCommand
        {
            CartId = cartId,
            UpdatedAt = DateTimeOffset.UtcNow,
            Totals = new Contracts.Cart.Commands.CartTotals
            {
                GrandTotal = 50m,
                Subtotal = 50m,
                BaseSubtotal = 50m,
                TaxAmount = 0m,
                BaseTaxAmount = 0m
            },
            IsNegotiableQuote = false,
            IsMultiShipping = false,
            Items = new List<Contracts.Cart.Commands.CartItem>
            {
                new Contracts.Cart.Commands.CartItem
                {
                    ItemId = "virtual-1",
                    Sku = "VIRTUAL001",
                    Name = "Virtual Product",
                    Qty = 1,
                    Price = 25m,
                    ProductType = "virtual"
                },
                new Contracts.Cart.Commands.CartItem
                {
                    ItemId = "download-1",
                    Sku = "DOWNLOAD001",
                    Name = "Downloadable Product",
                    Qty = 1,
                    Price = 25m,
                    ProductType = "downloadable"
                }
            },
            IdempotencyKey = "test-virtual-update"
        };

        _cartRepositoryMock
            .Setup(x => x.GetByIdAsync(cartId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingCart);

        _cartRepositoryMock
            .Setup(x => x.SaveNewVersionAsync(It.IsAny<Axon.Domain.Service.Cart.Domain.Cart>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((Axon.Domain.Service.Cart.Domain.Cart cart, CancellationToken ct) => cart);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(cartId, result.CartId);
        Assert.Equal(2, result.ItemsCount);
        Assert.All(result.Items, item => 
            Assert.True(item.ProductType == "virtual" || item.ProductType == "downloadable"));
    }

    [Fact]
    public async Task Handle_Should_Throw_And_Log_When_Repository_Save_Fails()
    {
        // Arrange
        var cartId = "cart-error";
        var existingCart = Axon.Domain.Service.Cart.Domain.Cart.Create(
            cartId: cartId,
            customerId: "customer-error",
            storeId: "store-1",
            createdAt: DateTimeOffset.UtcNow.AddHours(-1),
            currency: new Axon.Domain.Service.Cart.Domain.CartCurrency("USD", "USD"),
            customer: new Axon.Domain.Service.Cart.Domain.CartCustomer(
                Email: "<EMAIL>",
                GroupId: 1,
                IsGuest: false,
                FirstName: "Error",
                LastName: "Test"
            )
        );

        var command = new RecordCartUpdatedCommand
        {
            CartId = cartId,
            UpdatedAt = DateTimeOffset.UtcNow,
            Totals = new Contracts.Cart.Commands.CartTotals
            {
                GrandTotal = 100m,
                Subtotal = 100m,
                BaseSubtotal = 100m,
                TaxAmount = 0m,
                BaseTaxAmount = 0m
            },
            IsNegotiableQuote = false,
            IsMultiShipping = false,
            Items = new List<Contracts.Cart.Commands.CartItem>
            {
                new Contracts.Cart.Commands.CartItem
                {
                    ItemId = "item-1",
                    Sku = "SKU001",
                    Name = "Product 1",
                    Qty = 1,
                    Price = 100m,
                    ProductType = "simple"
                }
            },
            IdempotencyKey = "test-error"
        };

        var expectedException = new InvalidOperationException("Database connection failed");

        _cartRepositoryMock
            .Setup(x => x.GetByIdAsync(cartId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingCart);

        _cartRepositoryMock
            .Setup(x => x.SaveNewVersionAsync(It.IsAny<Axon.Domain.Service.Cart.Domain.Cart>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(expectedException);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(
            async () => await _handler.Handle(command, CancellationToken.None));
        
        Assert.Equal("Database connection failed", exception.Message);
        
        // Verify error was logged
        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains($"Error processing RecordCartUpdatedCommand for cart: {cartId}")),
                It.Is<Exception>(ex => ex == expectedException),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task Handle_Should_Update_Cart_With_Empty_Items_List()
    {
        // Arrange
        var cartId = "cart-empty";
        var existingCart = Axon.Domain.Service.Cart.Domain.Cart.Create(
            cartId: cartId,
            customerId: "customer-empty",
            storeId: "store-1",
            createdAt: DateTimeOffset.UtcNow.AddHours(-1),
            currency: new Axon.Domain.Service.Cart.Domain.CartCurrency("USD", "USD"),
            customer: new Axon.Domain.Service.Cart.Domain.CartCustomer(
                Email: "<EMAIL>",
                GroupId: 1,
                IsGuest: false,
                FirstName: "Empty",
                LastName: "Cart"
            )
        );

        var command = new RecordCartUpdatedCommand
        {
            CartId = cartId,
            UpdatedAt = DateTimeOffset.UtcNow,
            Totals = new Contracts.Cart.Commands.CartTotals
            {
                GrandTotal = 0m,
                Subtotal = 0m,
                BaseSubtotal = 0m,
                TaxAmount = 0m,
                BaseTaxAmount = 0m
            },
            IsNegotiableQuote = false,
            IsMultiShipping = false,
            Items = new List<Contracts.Cart.Commands.CartItem>(), // Empty items list
            IdempotencyKey = "test-empty-cart"
        };

        _cartRepositoryMock
            .Setup(x => x.GetByIdAsync(cartId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingCart);

        _cartRepositoryMock
            .Setup(x => x.SaveNewVersionAsync(It.IsAny<Axon.Domain.Service.Cart.Domain.Cart>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((Axon.Domain.Service.Cart.Domain.Cart cart, CancellationToken ct) => cart);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(cartId, result.CartId);
        Assert.Equal(0, result.ItemsCount);
        Assert.Equal(0, result.ItemsQty);
        Assert.Empty(result.Items);
    }

    [Fact]
    public async Task Handle_Should_Log_Information_Messages_During_Processing()
    {
        // Arrange
        var cartId = "cart-logging";
        var existingCart = Axon.Domain.Service.Cart.Domain.Cart.Create(
            cartId: cartId,
            customerId: "customer-logging",
            storeId: "store-1",
            createdAt: DateTimeOffset.UtcNow.AddHours(-1),
            currency: new Axon.Domain.Service.Cart.Domain.CartCurrency("USD", "USD"),
            customer: new Axon.Domain.Service.Cart.Domain.CartCustomer(
                Email: "<EMAIL>",
                GroupId: 1,
                IsGuest: false,
                FirstName: "Log",
                LastName: "Test"
            )
        );

        var command = new RecordCartUpdatedCommand
        {
            CartId = cartId,
            UpdatedAt = DateTimeOffset.UtcNow,
            Totals = new Contracts.Cart.Commands.CartTotals
            {
                GrandTotal = 50m,
                Subtotal = 50m,
                BaseSubtotal = 50m,
                TaxAmount = 0m,
                BaseTaxAmount = 0m
            },
            IsNegotiableQuote = false,
            IsMultiShipping = false,
            Items = new List<Contracts.Cart.Commands.CartItem>
            {
                new Contracts.Cart.Commands.CartItem
                {
                    ItemId = "item-1",
                    Sku = "SKU001",
                    Name = "Product 1",
                    Qty = 1,
                    Price = 50m,
                    ProductType = "simple"
                }
            },
            IdempotencyKey = "test-logging"
        };

        _cartRepositoryMock
            .Setup(x => x.GetByIdAsync(cartId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingCart);

        _cartRepositoryMock
            .Setup(x => x.SaveNewVersionAsync(It.IsAny<Axon.Domain.Service.Cart.Domain.Cart>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((Axon.Domain.Service.Cart.Domain.Cart cart, CancellationToken ct) => cart);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert - Verify logging
        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains($"Processing RecordCartUpdatedCommand for cart: {cartId}")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }
}