using Axon.Contracts.Cart.Commands;
using Axon.Contracts.Cart.Events;
using Axon.Domain.Service.Cart.Application;
using Axon.Domain.Service.Cart.Consumers;
using Axon.Domain.Service.Cart.Domain.Exceptions;
using MassTransit;
using MassTransit.Testing;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace Axon.Domain.Service.Tests.Cart.Consumers;

public class RecordCartCreatedCommandConsumerTests
{
    private readonly Mock<IRecordCartCreatedHandler> _handlerMock;
    private readonly Mock<ILogger<RecordCartCreatedCommandConsumer>> _loggerMock;

    public RecordCartCreatedCommandConsumerTests()
    {
        _handlerMock = new Mock<IRecordCartCreatedHandler>();
        _loggerMock = new Mock<ILogger<RecordCartCreatedCommandConsumer>>();
    }

    [Fact]
    public async Task Consume_Should_ProcessCommand_PublishEvent_AndRespond()
    {
        // Arrange
        var command = new RecordCartCreatedCommand
        {
            CartId = "test-cart-123",
            CustomerId = "customer-456",
            StoreId = "1",
            CreatedAt = DateTimeOffset.UtcNow,
            UpdatedAt = DateTimeOffset.UtcNow,
            Currency = new Contracts.Cart.Commands.CartCurrency { BaseCurrencyCode = "USD", QuoteCurrencyCode = "USD" },
            Totals = new Contracts.Cart.Commands.CartTotals { GrandTotal = 100m },
            Customer = new Contracts.Cart.Commands.CartCustomer { Email = "<EMAIL>", IsGuest = false },
            IsActive = true,
            ItemsCount = 2,
            ItemsQty = 3,
            IdempotencyKey = "cart-test-cart-123-created"
        };

        var expectedEvent = new CartCreatedEvent
        {
            CartId = "test-cart-123",
            CustomerId = "customer-456",
            StoreId = "1",
            CreatedAt = command.CreatedAt,
            UpdatedAt = command.UpdatedAt,
            Currency = new Contracts.Cart.Events.CartCurrency { BaseCurrencyCode = "USD", QuoteCurrencyCode = "USD" },
            Totals = new Contracts.Cart.Events.CartTotals { GrandTotal = 100m },
            Customer = new Contracts.Cart.Events.CartCustomer { Email = "<EMAIL>", IsGuest = false },
        };

        _handlerMock
            .Setup(x => x.Handle(It.IsAny<RecordCartCreatedCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedEvent);

        // Arrange services
        var services = new ServiceCollection();
        services.AddSingleton(_handlerMock.Object);
        services.AddSingleton(_loggerMock.Object);
        services.AddMassTransitTestHarness(cfg =>
        {
            cfg.AddConsumer<RecordCartCreatedCommandConsumer>();
        });

        await using var provider = services.BuildServiceProvider(true);
        var harness = provider.GetRequiredService<ITestHarness>();
        await harness.Start();

        try
        {
            // Act
            var client = harness.GetRequestClient<RecordCartCreatedCommand>();
            var response = await client.GetResponse<CartCreatedEvent>(command);

            // Assert
            Assert.True(await harness.Consumed.Any<RecordCartCreatedCommand>());
            Assert.True(await harness.Published.Any<CartCreatedEvent>());
            Assert.NotNull(response.Message);
            Assert.Equal("test-cart-123", response.Message.CartId);

            _handlerMock.Verify(x => x.Handle(
                It.Is<RecordCartCreatedCommand>(cmd => cmd.CartId == "test-cart-123"),
                It.IsAny<CancellationToken>()
            ), Times.Once);
        }
        finally
        {
            await harness.Stop();
        }
    }

    [Fact]
    public async Task Consume_Should_LogInformation_When_ProcessingCommand()
    {
        // Arrange
        var command = new RecordCartCreatedCommand
        {
            CartId = "log-cart-123",
            StoreId = "2",
            Currency = new Contracts.Cart.Commands.CartCurrency { BaseCurrencyCode = "EUR", QuoteCurrencyCode = "EUR" },
            Totals = new Contracts.Cart.Commands.CartTotals(),
            Customer = new Contracts.Cart.Commands.CartCustomer { IsGuest = true },
            IdempotencyKey = "cart-log-cart-123-created"
        };

        var expectedEvent = new CartCreatedEvent
        {
            CartId = "log-cart-123",
            StoreId = "2",
        };

        _handlerMock
            .Setup(x => x.Handle(It.IsAny<RecordCartCreatedCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedEvent);

        // Arrange services
        var services = new ServiceCollection();
        services.AddSingleton(_handlerMock.Object);
        services.AddSingleton(_loggerMock.Object);
        services.AddMassTransitTestHarness(cfg => 
        { 
            cfg.AddConsumer<RecordCartCreatedCommandConsumer>(); 
        });

        await using var provider = services.BuildServiceProvider(true);
        var harness = provider.GetRequiredService<ITestHarness>();
        await harness.Start();

        try
        {
            // Act
            await harness.Bus.Publish(command);
            
            // Wait for message to be consumed
            Assert.True(await harness.Consumed.Any<RecordCartCreatedCommand>());

            // Assert
            _loggerMock.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Received RecordCartCreatedCommand for cart: log-cart-123 with idempotency key: cart-log-cart-123-created")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);

            _loggerMock.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Published CartCreatedEvent for cart: log-cart-123")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);

            _loggerMock.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Successfully processed RecordCartCreatedCommand for cart: log-cart-123")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }
        finally
        {
            await harness.Stop();
        }
    }

    [Fact]
    public async Task Consume_Should_LogError_And_Throw_When_HandlerFails()
    {
        // Arrange
        var command = new RecordCartCreatedCommand
        {
            CartId = "error-cart-123",
            StoreId = "1",
            Currency = new Contracts.Cart.Commands.CartCurrency { BaseCurrencyCode = "USD", QuoteCurrencyCode = "USD" },
            Totals = new Contracts.Cart.Commands.CartTotals(),
            Customer = new Contracts.Cart.Commands.CartCustomer { IsGuest = true },
            IdempotencyKey = "cart-error-cart-123-created"
        };

        var exception = new InvalidOperationException("Handler failed");
        _handlerMock
            .Setup(x => x.Handle(It.IsAny<RecordCartCreatedCommand>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(exception);

        // Arrange services
        var services = new ServiceCollection();
        services.AddSingleton(_handlerMock.Object);
        services.AddSingleton(_loggerMock.Object);
        services.AddMassTransitTestHarness(cfg => 
        { 
            cfg.AddConsumer<RecordCartCreatedCommandConsumer>(); 
        });

        await using var provider = services.BuildServiceProvider(true);
        var harness = provider.GetRequiredService<ITestHarness>();
        await harness.Start();

        try
        {
            // Act
            await harness.Bus.Publish(command);
            
            // Wait for the message to be consumed (but faulted)
            await Task.Delay(500);

            // Assert
            Assert.True(await harness.Consumed.Any<RecordCartCreatedCommand>());
            Assert.False(await harness.Published.Any<CartCreatedEvent>());

            _loggerMock.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Error processing RecordCartCreatedCommand for cart: error-cart-123")),
                    It.Is<Exception>(e => e == exception),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }
        finally
        {
            await harness.Stop();
        }
    }

    [Fact]
    public async Task Consume_Should_RespondWithFailureEvent_When_DuplicateCartException()
    {
        // Arrange
        var command = new RecordCartCreatedCommand
        {
            CartId = "duplicate-cart-123",
            StoreId = "1",
            Currency = new Contracts.Cart.Commands.CartCurrency { BaseCurrencyCode = "USD", QuoteCurrencyCode = "USD" },
            Totals = new Contracts.Cart.Commands.CartTotals(),
            Customer = new Contracts.Cart.Commands.CartCustomer { IsGuest = true },
            IdempotencyKey = "cart-duplicate-cart-123-created"
        };

        _handlerMock
            .Setup(x => x.Handle(It.IsAny<RecordCartCreatedCommand>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new DuplicateCartException("duplicate-cart-123"));

        // Arrange services
        var services = new ServiceCollection();
        services.AddSingleton(_handlerMock.Object);
        services.AddSingleton(_loggerMock.Object);
        services.AddMassTransitTestHarness(cfg => 
        { 
            cfg.AddConsumer<RecordCartCreatedCommandConsumer>(); 
        });

        await using var provider = services.BuildServiceProvider(true);
        var harness = provider.GetRequiredService<ITestHarness>();
        await harness.Start();

        try
        {
            // Act
            var client = harness.GetRequestClient<RecordCartCreatedCommand>();
            var response = await client.GetResponse<CartCreatedEvent, CartCreationFailedEvent>(command);

            // Assert
            Assert.True(response.Is(out Response<CartCreationFailedEvent>? failureResponse));
            Assert.NotNull(failureResponse);
            Assert.Equal("duplicate-cart-123", failureResponse.Message.CartId);
            Assert.Equal("DUPLICATE_CART", failureResponse.Message.ErrorCode);
            Assert.Contains("already exists", failureResponse.Message.Reason);

            // Verify that no success event was published
            Assert.False(await harness.Published.Any<CartCreatedEvent>());

            // Verify that the handler was called and threw the exception
            _handlerMock.Verify(x => x.Handle(
                It.Is<RecordCartCreatedCommand>(cmd => cmd.CartId == "duplicate-cart-123"),
                It.IsAny<CancellationToken>()
            ), Times.Once);

            // Verify warning logging
            _loggerMock.Verify(
                x => x.Log(
                    LogLevel.Warning,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Cart already exists: duplicate-cart-123")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }
        finally
        {
            await harness.Stop();
        }
    }

    [Fact]
    public async Task Consume_Should_PublishEventWithCorrectData()
    {
        // Arrange
        var command = new RecordCartCreatedCommand
        {
            CartId = "publish-cart-123",
            CustomerId = "customer-789",
            StoreId = "3",
            CreatedAt = DateTimeOffset.UtcNow.AddMinutes(-10),
            UpdatedAt = DateTimeOffset.UtcNow.AddMinutes(-5),
            Currency = new Contracts.Cart.Commands.CartCurrency { BaseCurrencyCode = "GBP", QuoteCurrencyCode = "GBP" },
            Totals = new Contracts.Cart.Commands.CartTotals 
            { 
                GrandTotal = 250.50m,
                Subtotal = 230.00m,
                TaxAmount = 20.50m
            },
            Customer = new Contracts.Cart.Commands.CartCustomer 
            { 
                Email = "<EMAIL>",
                FirstName = "Jane",
                LastName = "Smith",
                IsGuest = false,
                GroupId = 2
            },
            IsActive = true,
            IsVirtual = true,
            IsNegotiableQuote = true,
            IsMultiShipping = false,
            ItemsCount = 3,
            ItemsQty = 5,
            Items = new List<Contracts.Cart.Commands.CartItem>
            {
                new Contracts.Cart.Commands.CartItem 
                { 
                    ItemId = "item-1",
                    Sku = "DIGITAL-001",
                    Name = "Digital Product",
                    Qty = 5,
                    Price = 46.00m,
                    ProductType = "virtual"
                }
            },
            IdempotencyKey = "cart-publish-cart-123-created"
        };

        var expectedEvent = new CartCreatedEvent
        {
            CartId = command.CartId,
            CustomerId = command.CustomerId,
            StoreId = command.StoreId,
            CreatedAt = command.CreatedAt,
            UpdatedAt = command.UpdatedAt,
            Currency = new Contracts.Cart.Events.CartCurrency 
            { 
                BaseCurrencyCode = command.Currency.BaseCurrencyCode,
                QuoteCurrencyCode = command.Currency.QuoteCurrencyCode
            },
            Totals = new Contracts.Cart.Events.CartTotals
            {
                GrandTotal = command.Totals.GrandTotal,
                Subtotal = command.Totals.Subtotal,
                TaxAmount = command.Totals.TaxAmount,
                BaseSubtotal = command.Totals.BaseSubtotal,
                BaseTaxAmount = command.Totals.BaseTaxAmount
            },
            Customer = new Contracts.Cart.Events.CartCustomer
            {
                Email = command.Customer.Email,
                FirstName = command.Customer.FirstName,
                LastName = command.Customer.LastName,
                IsGuest = command.Customer.IsGuest,
                GroupId = command.Customer.GroupId
            },
            IsActive = command.IsActive,
            IsVirtual = command.IsVirtual,
            IsNegotiableQuote = command.IsNegotiableQuote,
            IsMultiShipping = command.IsMultiShipping,
            ItemsCount = command.ItemsCount,
            ItemsQty = command.ItemsQty,
            Items = command.Items.Select(i => new Contracts.Cart.Events.CartItem
            {
                ItemId = i.ItemId,
                Sku = i.Sku,
                Name = i.Name,
                Qty = i.Qty,
                Price = i.Price,
                ProductType = i.ProductType
            }).ToList(),
        };

        _handlerMock
            .Setup(x => x.Handle(It.IsAny<RecordCartCreatedCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedEvent);

        // Arrange services
        var services = new ServiceCollection();
        services.AddSingleton(_handlerMock.Object);
        services.AddSingleton(_loggerMock.Object);
        services.AddMassTransitTestHarness(cfg => 
        { 
            cfg.AddConsumer<RecordCartCreatedCommandConsumer>(); 
        });

        await using var provider = services.BuildServiceProvider(true);
        var harness = provider.GetRequiredService<ITestHarness>();
        await harness.Start();

        try
        {
            // Act
            var client = harness.GetRequestClient<RecordCartCreatedCommand>();
            var response = await client.GetResponse<CartCreatedEvent>(command);

            // Assert
            Assert.True(await harness.Published.Any<CartCreatedEvent>());
            var publishedMessages = harness.Published.Select<CartCreatedEvent>().Where(x => x.Context.SourceAddress != null).ToList();
            Assert.Single(publishedMessages);
            
            var publishedEvent = publishedMessages.First().Context.Message;
            Assert.Equal("publish-cart-123", publishedEvent.CartId);
            Assert.Equal("customer-789", publishedEvent.CustomerId);
            Assert.Equal("3", publishedEvent.StoreId);
            Assert.Equal(250.50m, publishedEvent.Totals.GrandTotal);
            Assert.Equal("<EMAIL>", publishedEvent.Customer.Email);
            Assert.Equal("Jane", publishedEvent.Customer.FirstName);
            Assert.Equal("Smith", publishedEvent.Customer.LastName);
            Assert.False(publishedEvent.Customer.IsGuest);
            Assert.Equal(2, publishedEvent.Customer.GroupId);
            Assert.True(publishedEvent.IsVirtual);
            Assert.True(publishedEvent.IsNegotiableQuote);
            Assert.Equal(3, publishedEvent.ItemsCount);
            Assert.Equal(5, publishedEvent.ItemsQty);
            Assert.Single(publishedEvent.Items);
            Assert.Equal("DIGITAL-001", publishedEvent.Items[0].Sku);
        }
        finally
        {
            await harness.Stop();
        }
    }
}