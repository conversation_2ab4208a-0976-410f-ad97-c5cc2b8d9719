using Axon.Domain.Service.Cart.Domain;
using Xunit;

namespace Axon.Domain.Service.Tests.Cart.Domain;

public class CartTests
{
    [Fact]
    public void Create_Should_CreateCart_When_ValidParameters()
    {
        // Arrange
        var cartId = "test-cart-123";
        var customerId = "customer-456";
        var storeId = "1";
        var createdAt = DateTimeOffset.UtcNow;
        var currency = new CartCurrency("USD", "USD");
        var customer = new CartCustomer("<EMAIL>", 1, false, "John", "Doe");

        // Act
        var cart = Axon.Domain.Service.Cart.Domain.Cart.Create(cartId, customerId, storeId, createdAt, currency, customer);

        // Assert
        Assert.NotNull(cart);
        Assert.Equal(cartId, cart.CartId);
        Assert.Equal(1, cart.Version);
        Assert.Equal(customerId, cart.CustomerId);
        Assert.Equal(storeId, cart.StoreId);
        Assert.Equal(createdAt, cart.CreatedAt);
        Assert.Equal(createdAt, cart.UpdatedAt);
        Assert.Equal(currency, cart.Currency);
        Assert.Equal(customer, cart.Customer);
        Assert.True(cart.IsActive);
        Assert.False(cart.IsVirtual);
        Assert.False(cart.IsNegotiableQuote);
        Assert.False(cart.IsMultiShipping);
        Assert.Empty(cart.Items);
        Assert.Equal(0, cart.Totals.GrandTotal);
    }

    [Fact]
    public void Create_Should_ThrowException_When_CartIdIsEmpty()
    {
        // Arrange
        var currency = new CartCurrency("USD", "USD");
        var customer = new CartCustomer("<EMAIL>", 1, false, "John", "Doe");

        // Act & Assert
        var exception = Assert.Throws<ArgumentException>(() => 
            Axon.Domain.Service.Cart.Domain.Cart.Create("", "customer-123", "1", DateTimeOffset.UtcNow, currency, customer));
        Assert.Contains("Cart ID cannot be empty", exception.Message);
    }

    [Fact]
    public void Create_Should_ThrowException_When_StoreIdIsEmpty()
    {
        // Arrange
        var currency = new CartCurrency("USD", "USD");
        var customer = new CartCustomer("<EMAIL>", 1, false, "John", "Doe");

        // Act & Assert
        var exception = Assert.Throws<ArgumentException>(() => 
            Axon.Domain.Service.Cart.Domain.Cart.Create("cart-123", "customer-123", "", DateTimeOffset.UtcNow, currency, customer));
        Assert.Contains("Store ID cannot be empty", exception.Message);
    }

    [Fact]
    public void Create_Should_ThrowException_When_CurrencyIsNull()
    {
        // Arrange
        var customer = new CartCustomer("<EMAIL>", 1, false, "John", "Doe");

        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => 
            Axon.Domain.Service.Cart.Domain.Cart.Create("cart-123", "customer-123", "1", DateTimeOffset.UtcNow, null!, customer));
    }

    [Fact]
    public void Create_Should_ThrowException_When_CustomerIsNull()
    {
        // Arrange
        var currency = new CartCurrency("USD", "USD");

        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => 
            Axon.Domain.Service.Cart.Domain.Cart.Create("cart-123", "customer-123", "1", DateTimeOffset.UtcNow, currency, null!));
    }

    [Fact]
    public void WithItem_Should_AddItemToCart_And_UpdateTotals()
    {
        // Arrange
        var cart = CreateTestCart();
        var item = new CartItem("item-1", "SKU001", "Product 1", 2, 50.00m, "simple");

        // Act
        var updatedCart = Axon.Domain.Service.Cart.Domain.Cart.WithItem(cart, item);

        // Assert
        Assert.Single(updatedCart.Items);
        Assert.Equal(item, updatedCart.Items[0]);
        Assert.Equal(100.00m, updatedCart.Totals.Subtotal);
        Assert.Equal(100.00m, updatedCart.Totals.GrandTotal);
        Assert.True(updatedCart.UpdatedAt >= updatedCart.CreatedAt);
        Assert.Equal(2, updatedCart.Version);
        // Original cart should remain unchanged
        Assert.Empty(cart.Items);
        Assert.Equal(1, cart.Version);
    }

    [Fact]
    public void WithItem_Should_ThrowException_When_ItemIsNull()
    {
        // Arrange
        var cart = CreateTestCart();

        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => Axon.Domain.Service.Cart.Domain.Cart.WithItem(cart, null!));
    }

    [Fact]
    public void WithoutItem_Should_RemoveItemFromCart_And_UpdateTotals()
    {
        // Arrange
        var cart = CreateTestCart();
        var item1 = new CartItem("item-1", "SKU001", "Product 1", 2, 50.00m);
        var item2 = new CartItem("item-2", "SKU002", "Product 2", 1, 30.00m);
        var cartWithItems = Axon.Domain.Service.Cart.Domain.Cart.WithItems(cart, new[] { item1, item2 });

        // Act
        var updatedCart = Axon.Domain.Service.Cart.Domain.Cart.WithoutItem(cartWithItems, "item-1");

        // Assert
        Assert.Single(updatedCart.Items);
        Assert.Equal("item-2", updatedCart.Items[0].ItemId);
        Assert.Equal(30.00m, updatedCart.Totals.Subtotal);
        Assert.Equal(30.00m, updatedCart.Totals.GrandTotal);
        Assert.Equal(3, updatedCart.Version);
    }

    [Fact]
    public void WithoutItem_Should_ReturnNewVersion_When_ItemNotFound()
    {
        // Arrange
        var cart = CreateTestCart();
        var item = new CartItem("item-1", "SKU001", "Product 1", 2, 50.00m);
        var cartWithItem = Axon.Domain.Service.Cart.Domain.Cart.WithItem(cart, item);

        // Act
        var updatedCart = Axon.Domain.Service.Cart.Domain.Cart.WithoutItem(cartWithItem, "item-999");

        // Assert
        Assert.Single(updatedCart.Items);
        Assert.Equal(100.00m, updatedCart.Totals.GrandTotal);
        Assert.Equal(3, updatedCart.Version);
    }

    [Fact]
    public void WithBillingAddress_Should_UpdateBillingAddress()
    {
        // Arrange
        var cart = CreateTestCart();
        var address = new CartAddress(
            "addr-1", "CA", "US", 
            new List<string> { "123 Main St" }, 
            "Los Angeles", "90001", "John", "Doe", "555-1234");

        // Act
        var updatedCart = Axon.Domain.Service.Cart.Domain.Cart.WithBillingAddress(cart, address);

        // Assert
        Assert.NotNull(updatedCart.BillingAddress);
        Assert.Equal(address, updatedCart.BillingAddress);
        Assert.True(updatedCart.UpdatedAt >= updatedCart.CreatedAt);
        Assert.Equal(2, updatedCart.Version);
        // Original cart should remain unchanged
        Assert.Null(cart.BillingAddress);
        Assert.Equal(1, cart.Version);
    }

    [Fact]
    public void WithBillingAddress_Should_ThrowException_When_AddressIsNull()
    {
        // Arrange
        var cart = CreateTestCart();

        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => Axon.Domain.Service.Cart.Domain.Cart.WithBillingAddress(cart, null!));
    }

    [Fact]
    public void WithShippingAddress_Should_UpdateShippingAddress()
    {
        // Arrange
        var cart = CreateTestCart();
        var address = new CartAddress(
            "addr-2", "NY", "US", 
            new List<string> { "456 Oak Ave" }, 
            "New York", "10001", "Jane", "Smith", "555-5678");

        // Act
        var updatedCart = Axon.Domain.Service.Cart.Domain.Cart.WithShippingAddress(cart, address);

        // Assert
        Assert.NotNull(updatedCart.ShippingAddress);
        Assert.Equal(address, updatedCart.ShippingAddress);
        Assert.True(updatedCart.UpdatedAt >= updatedCart.CreatedAt);
        Assert.Equal(2, updatedCart.Version);
    }

    [Fact]
    public void WithPaymentMethod_Should_UpdatePaymentMethod()
    {
        // Arrange
        var cart = CreateTestCart();
        var payment = new CartPaymentMethod("credit_card", null);

        // Act
        var updatedCart = Axon.Domain.Service.Cart.Domain.Cart.WithPaymentMethod(cart, payment);

        // Assert
        Assert.NotNull(updatedCart.PaymentMethod);
        Assert.Equal(payment, updatedCart.PaymentMethod);
        Assert.True(updatedCart.UpdatedAt >= updatedCart.CreatedAt);
        Assert.Equal(2, updatedCart.Version);
    }

    [Fact]
    public void WithShippingMethod_Should_UpdateShippingMethod_And_RecalculateTotals()
    {
        // Arrange
        var cart = CreateTestCart();
        var cartWithItem = Axon.Domain.Service.Cart.Domain.Cart.WithItem(cart, new CartItem("item-1", "SKU001", "Product 1", 2, 50.00m));
        var shipping = new CartShippingMethod("fedex", "ground", "FedEx Ground", 15.99m);

        // Act
        var updatedCart = Axon.Domain.Service.Cart.Domain.Cart.WithShippingMethod(cartWithItem, shipping);

        // Assert
        Assert.NotNull(updatedCart.ShippingMethod);
        Assert.Equal(shipping, updatedCart.ShippingMethod);
        Assert.Equal(100.00m, updatedCart.Totals.Subtotal);
        Assert.Equal(115.99m, updatedCart.Totals.GrandTotal);
        Assert.Equal(3, updatedCart.Version);
    }

    [Fact]
    public void WithNegotiableQuoteStatus_Should_UpdateFlag()
    {
        // Arrange
        var cart = CreateTestCart();

        // Act
        var updatedCart = Axon.Domain.Service.Cart.Domain.Cart.WithNegotiableQuoteStatus(cart, true);

        // Assert
        Assert.True(updatedCart.IsNegotiableQuote);
        Assert.True(updatedCart.UpdatedAt >= updatedCart.CreatedAt);
        Assert.Equal(2, updatedCart.Version);
        // Original cart should remain unchanged
        Assert.False(cart.IsNegotiableQuote);
    }

    [Fact]
    public void WithMultiShippingStatus_Should_UpdateFlag()
    {
        // Arrange
        var cart = CreateTestCart();

        // Act
        var updatedCart = Axon.Domain.Service.Cart.Domain.Cart.WithMultiShippingStatus(cart, true);

        // Assert
        Assert.True(updatedCart.IsMultiShipping);
        Assert.True(updatedCart.UpdatedAt >= updatedCart.CreatedAt);
        Assert.Equal(2, updatedCart.Version);
    }

    [Fact]
    public void AsDeactivated_Should_SetIsActiveFalse()
    {
        // Arrange
        var cart = CreateTestCart();

        // Act
        var updatedCart = Axon.Domain.Service.Cart.Domain.Cart.AsDeactivated(cart);

        // Assert
        Assert.False(updatedCart.IsActive);
        Assert.True(updatedCart.UpdatedAt >= updatedCart.CreatedAt);
        Assert.Equal(2, updatedCart.Version);
        // Original cart should remain active
        Assert.True(cart.IsActive);
    }

    [Fact]
    public void IsVirtual_Should_BeTrue_When_AllItemsAreVirtual()
    {
        // Arrange
        var cart = CreateTestCart();
        var item1 = new CartItem("item-1", "DIGITAL-001", "Digital Product", 1, 20.00m, "virtual");
        var item2 = new CartItem("item-2", "DOWNLOAD-001", "Downloadable", 1, 15.00m, "downloadable");
        var updatedCart = Axon.Domain.Service.Cart.Domain.Cart.WithItems(cart, new[] { item1, item2 });

        // Assert
        Assert.True(updatedCart.IsVirtual);
    }

    [Fact]
    public void IsVirtual_Should_BeFalse_When_HasPhysicalItems()
    {
        // Arrange
        var cart = CreateTestCart();
        var item1 = new CartItem("item-1", "DIGITAL-001", "Digital Product", 1, 20.00m, "virtual");
        var item2 = new CartItem("item-2", "PHYSICAL-001", "Physical Product", 1, 50.00m, "simple");
        var updatedCart = Axon.Domain.Service.Cart.Domain.Cart.WithItems(cart, new[] { item1, item2 });

        // Assert
        Assert.False(updatedCart.IsVirtual);
    }

    [Fact]
    public void MultipleOperations_Should_IncrementVersion()
    {
        // Arrange
        var cart = CreateTestCart();
        var item = new CartItem("item-1", "SKU001", "Test Product", 1, 50m);
        var billingAddress = new CartAddress("bill-1", "TX", "US", new List<string> { "789 Elm St" }, 
            "Dallas", "75001", "Bill", "Jones", "555-9999");
        var shippingAddress = new CartAddress("ship-1", "TX", "US", new List<string> { "789 Elm St" }, 
            "Dallas", "75001", "Ship", "Jones", "555-9999");
        var paymentMethod = new CartPaymentMethod("purchase_order", "PO-123");
        var shippingMethod = new CartShippingMethod("ups", "ground", "UPS Ground", 20.00m);

        // Act - Chain multiple operations
        var cart1 = Axon.Domain.Service.Cart.Domain.Cart.WithItem(cart, item);
        var cart2 = Axon.Domain.Service.Cart.Domain.Cart.WithBillingAddress(cart1, billingAddress);
        var cart3 = Axon.Domain.Service.Cart.Domain.Cart.WithShippingAddress(cart2, shippingAddress);
        var cart4 = Axon.Domain.Service.Cart.Domain.Cart.WithPaymentMethod(cart3, paymentMethod);
        var cart5 = Axon.Domain.Service.Cart.Domain.Cart.WithShippingMethod(cart4, shippingMethod);
        var cart6 = Axon.Domain.Service.Cart.Domain.Cart.WithNegotiableQuoteStatus(cart5, true);
        var cart7 = Axon.Domain.Service.Cart.Domain.Cart.WithMultiShippingStatus(cart6, true);
        var finalCart = Axon.Domain.Service.Cart.Domain.Cart.AsDeactivated(cart7);

        // Assert
        Assert.Equal(9, finalCart.Version); // Started at 1, 8 operations
        Assert.False(finalCart.IsActive);
        Assert.True(finalCart.IsNegotiableQuote);
        Assert.True(finalCart.IsMultiShipping);
        Assert.Single(finalCart.Items);
        Assert.Equal(billingAddress, finalCart.BillingAddress);
        Assert.Equal(shippingAddress, finalCart.ShippingAddress);
        Assert.Equal(paymentMethod, finalCart.PaymentMethod);
        Assert.Equal(shippingMethod, finalCart.ShippingMethod);
        
        // Totals should be recalculated
        Assert.Equal(50.00m, finalCart.Totals.Subtotal);
        Assert.Equal(70.00m, finalCart.Totals.GrandTotal); // 50 + 20 (shipping)
    }

    [Fact]
    public void Create_With_InitialData_Should_CreateCompleteCart()
    {
        // Arrange
        var cartId = "test-cart-123";
        var customerId = "customer-456";
        var storeId = "1";
        var createdAt = DateTimeOffset.UtcNow;
        var currency = new CartCurrency("USD", "USD");
        var customer = new CartCustomer("<EMAIL>", 1, false, "John", "Doe");
        var items = new List<CartItem>
        {
            new CartItem("item-1", "SKU001", "Product 1", 2, 50.00m),
            new CartItem("item-2", "SKU002", "Product 2", 1, 30.00m)
        };
        var billingAddress = new CartAddress("bill-1", "TX", "US", new List<string> { "789 Elm St" }, 
            "Dallas", "75001", "Bill", "Jones", "555-9999");
        var shippingAddress = new CartAddress("ship-1", "TX", "US", new List<string> { "789 Elm St" }, 
            "Dallas", "75001", "Ship", "Jones", "555-9999");
        var paymentMethod = new CartPaymentMethod("credit_card", null);
        var shippingMethod = new CartShippingMethod("fedex", "ground", "FedEx Ground", 15.00m);

        // Act
        var cart = Axon.Domain.Service.Cart.Domain.Cart.Create(
            cartId, customerId, storeId, createdAt, currency, customer,
            items, billingAddress, shippingAddress, paymentMethod, shippingMethod,
            isNegotiableQuote: true, isMultiShipping: true, isActive: false);

        // Assert
        Assert.Equal(1, cart.Version);
        Assert.Equal(2, cart.Items.Count);
        Assert.Equal(billingAddress, cart.BillingAddress);
        Assert.Equal(shippingAddress, cart.ShippingAddress);
        Assert.Equal(paymentMethod, cart.PaymentMethod);
        Assert.Equal(shippingMethod, cart.ShippingMethod);
        Assert.True(cart.IsNegotiableQuote);
        Assert.True(cart.IsMultiShipping);
        Assert.False(cart.IsActive);
        Assert.Equal(130.00m, cart.Totals.Subtotal); // 2*50 + 1*30
        Assert.Equal(145.00m, cart.Totals.GrandTotal); // 130 + 15
    }

    [Fact]
    public void CartItem_Should_CreateValidItem_When_ValidParameters()
    {
        // Act
        var item = new CartItem("item-123", "SKU-TEST", "Test Product", 5, 25.50m, "configurable", new { color = "red" });

        // Assert
        Assert.Equal("item-123", item.ItemId);
        Assert.Equal("SKU-TEST", item.Sku);
        Assert.Equal("Test Product", item.Name);
        Assert.Equal(5, item.Qty);
        Assert.Equal(25.50m, item.Price);
        Assert.Equal("configurable", item.ProductType);
        Assert.NotNull(item.ProductOption);
    }

    [Theory]
    [InlineData("", "SKU", "Name", 1, 10, "Item ID cannot be empty")]
    [InlineData("ID", "", "Name", 1, 10, "SKU cannot be empty")]
    [InlineData("ID", "SKU", "", 1, 10, "Name cannot be empty")]
    [InlineData("ID", "SKU", "Name", 0, 10, "Quantity must be greater than zero")]
    [InlineData("ID", "SKU", "Name", -1, 10, "Quantity must be greater than zero")]
    [InlineData("ID", "SKU", "Name", 1, -1, "Price cannot be negative")]
    public void CartItem_Should_ThrowException_When_InvalidParameters(
        string itemId, string sku, string name, decimal qty, decimal price, string expectedMessage)
    {
        // Act & Assert
        var exception = Assert.Throws<ArgumentException>(() => 
            new CartItem(itemId, sku, name, qty, price));
        Assert.Contains(expectedMessage, exception.Message);
    }

    [Fact]
    public void Update_Should_UpdateCart_When_ValidParameters()
    {
        // Arrange
        var existingCart = CreateTestCart();
        var items = new List<CartItem>
        {
            new CartItem("item-1", "SKU001", "Product 1", 2, 50.00m),
            new CartItem("item-2", "SKU002", "Product 2", 1, 30.00m)
        };

        // Act
        var updatedCart = Axon.Domain.Service.Cart.Domain.Cart.Update(existingCart: existingCart, items: items);

        // Assert
        Assert.NotEmpty(updatedCart.Items);
        Assert.Equal(existingCart.CartId, updatedCart.CartId);
        Assert.NotEqual(existingCart.UpdatedAt, updatedCart.UpdatedAt);
    }

    private static Axon.Domain.Service.Cart.Domain.Cart CreateTestCart()
    {
        var currency = new CartCurrency("USD", "USD");
        var customer = new CartCustomer("<EMAIL>", 1, false, "John", "Doe");
        return Axon.Domain.Service.Cart.Domain.Cart.Create("test-cart", "customer-123", "1", DateTimeOffset.UtcNow.AddSeconds(-1), currency, customer);
    }
}