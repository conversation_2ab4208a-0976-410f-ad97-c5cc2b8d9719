using Axon.Domain.Service.Cart.Domain;
using Axon.Domain.Service.Cart.Domain.Exceptions;
using Axon.Domain.Service.Cart.Infrastructure;
using Axon.Domain.Service.Cart.Infrastructure.Persistence;
using Axon.Domain.Service.Tests.Infrastructure.Persistence;
using Microsoft.Extensions.Logging;
using Moq;

namespace Axon.Domain.Service.Tests.Cart.Infrastructure;

public class CartRepositoryTests : IDisposable
{
    private readonly CartDbContext _context;
    private readonly Mock<ILogger<CartRepository>> _loggerMock;
    private readonly CartRepository _repository;

    public CartRepositoryTests()
    {
        _context = TestDbContextHelper.CreateCartDbContext();
        _loggerMock = new Mock<ILogger<CartRepository>>();
        _repository = new CartRepository(_context, _loggerMock.Object);
    }

    [Fact]
    public async Task GetByIdAsync_Should_Return_Latest_Version_Of_Cart()
    {
        // Arrange
        var cart = CreateTestCart();
        await _repository.AddAsync(cart);
        
        // Create a new version
        var item = new CartItem("ITEM-001", "SKU-001", "New Product", 2, 50m, null, null);
        var updatedCart = Axon.Domain.Service.Cart.Domain.Cart.WithItem(cart, item);
        await _repository.SaveNewVersionAsync(updatedCart);
        
        _context.ChangeTracker.Clear(); // Clear tracker to ensure we're loading from DB

        // Act
        var retrievedCart = await _repository.GetByIdAsync(cart.CartId);

        // Assert
        Assert.NotNull(retrievedCart);
        Assert.Equal(cart.CartId, retrievedCart.CartId);
        Assert.Equal(2, retrievedCart.Version); // Should get version 2
        Assert.Single(retrievedCart.Items);
        
        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((o, t) => o.ToString()!.Contains($"Fetching latest version of cart {cart.CartId}")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task GetByIdAndVersionAsync_Should_Return_Specific_Version()
    {
        // Arrange
        var cart = CreateTestCart();
        await _repository.AddAsync(cart);
        
        var item = new CartItem("ITEM-001", "SKU-001", "New Product", 2, 50m, null, null);
        var updatedCart = Axon.Domain.Service.Cart.Domain.Cart.WithItem(cart, item);
        await _repository.SaveNewVersionAsync(updatedCart);
        
        _context.ChangeTracker.Clear();

        // Act
        var version1 = await _repository.GetByIdAndVersionAsync(cart.CartId, 1);
        var version2 = await _repository.GetByIdAndVersionAsync(cart.CartId, 2);

        // Assert
        Assert.NotNull(version1);
        Assert.NotNull(version2);
        Assert.Equal(1, version1.Version);
        Assert.Equal(2, version2.Version);
        Assert.Empty(version1.Items);
        Assert.Single(version2.Items);
    }

    [Fact]
    public async Task GetAllVersionsAsync_Should_Return_All_Versions()
    {
        // Arrange
        var cart = CreateTestCart();
        await _repository.AddAsync(cart);
        
        var item1 = new CartItem("ITEM-001", "SKU-001", "Product 1", 1, 50m);
        var cart2 = Axon.Domain.Service.Cart.Domain.Cart.WithItem(cart, item1);
        await _repository.SaveNewVersionAsync(cart2);
        
        var item2 = new CartItem("ITEM-002", "SKU-002", "Product 2", 1, 30m);
        var cart3 = Axon.Domain.Service.Cart.Domain.Cart.WithItem(cart2, item2);
        await _repository.SaveNewVersionAsync(cart3);

        // Act
        var allVersions = await _repository.GetAllVersionsAsync(cart.CartId);

        // Assert
        Assert.Equal(3, allVersions.Count);
        Assert.Equal(1, allVersions[0].Version);
        Assert.Equal(2, allVersions[1].Version);
        Assert.Equal(3, allVersions[2].Version);
        Assert.Empty(allVersions[0].Items);
        Assert.Single(allVersions[1].Items);
        Assert.Equal(2, allVersions[2].Items.Count);
    }

    [Fact]
    public async Task GetByIdAsync_Should_Return_Null_When_Cart_Not_Found()
    {
        // Arrange
        var nonExistentId = "CART-NOT-FOUND";

        // Act
        var result = await _repository.GetByIdAsync(nonExistentId);

        // Assert
        Assert.Null(result);
        
        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((o, t) => o.ToString()!.Contains($"Cart {nonExistentId} not found")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task AddAsync_Should_Add_New_Cart()
    {
        // Arrange
        var cart = CreateTestCart();

        // Act
        var result = await _repository.AddAsync(cart);

        // Assert
        Assert.Equal(cart, result);
        var savedCart = await _context.Carts.FindAsync(cart.CartId, 1);
        Assert.NotNull(savedCart);
        Assert.Equal(cart.CartId, savedCart.CartId);
        Assert.Equal(1, savedCart.Version);
        
        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((o, t) => o.ToString()!.Contains($"Cart added to repository: {cart.CartId} version 1")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task AddAsync_Should_Throw_DuplicateCartException_When_Cart_Already_Exists()
    {
        // Arrange
        var cart = CreateTestCart();
        await _repository.AddAsync(cart);
        var duplicateCart = CreateTestCart();
        duplicateCart = Axon.Domain.Service.Cart.Domain.Cart.Create(
            cart.CartId, // Same cart ID
            cart.CustomerId,
            cart.StoreId,
            DateTimeOffset.UtcNow,
            cart.Currency,
            cart.Customer
        );

        // Act & Assert
        await Assert.ThrowsAsync<DuplicateCartException>(() => _repository.AddAsync(duplicateCart));
        
        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Warning,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((o, t) => o.ToString()!.Contains($"Attempted to add cart that already exists: {cart.CartId}")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task SaveNewVersionAsync_Should_Save_New_Version()
    {
        // Arrange
        var cart = CreateTestCart();
        await _repository.AddAsync(cart);
        
        var item = new CartItem("ITEM-001", "SKU-001", "New Product", 2, 50m);
        var updatedCart = Axon.Domain.Service.Cart.Domain.Cart.WithItem(cart, item);

        // Act
        var result = await _repository.SaveNewVersionAsync(updatedCart);

        // Assert
        Assert.Equal(updatedCart, result);
        Assert.Equal(2, result.Version);
        
        // Verify both versions exist
        var version1 = await _context.Carts.FindAsync(cart.CartId, 1);
        var version2 = await _context.Carts.FindAsync(cart.CartId, 2);
        Assert.NotNull(version1);
        Assert.NotNull(version2);
        Assert.Empty(version1.Items);
        Assert.Single(version2.Items);
    }

    [Fact]
    public async Task SaveNewVersionAsync_Should_Throw_When_Version_Already_Exists()
    {
        // Arrange
        var cart = CreateTestCart();
        await _repository.AddAsync(cart);
        
        var item = new CartItem("ITEM-001", "SKU-001", "New Product", 2, 50m);
        var updatedCart = Axon.Domain.Service.Cart.Domain.Cart.WithItem(cart, item);
        await _repository.SaveNewVersionAsync(updatedCart);

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _repository.SaveNewVersionAsync(updatedCart));
    }

    [Fact]
    #pragma warning disable CS0618 // Type or member is obsolete
    public async Task UpdateAsync_Should_Throw_NotSupportedException()
    {
        // Arrange
        var cart = CreateTestCart();

        // Act & Assert
        await Assert.ThrowsAsync<NotSupportedException>(() => _repository.UpdateAsync(cart));
    }
    #pragma warning restore CS0618 // Type or member is obsolete

    [Fact]
    public async Task ExistsAsync_Should_Return_True_When_Cart_Exists()
    {
        // Arrange
        var cart = CreateTestCart();
        await _repository.AddAsync(cart);

        // Act
        var exists = await _repository.ExistsAsync(cart.CartId);

        // Assert
        Assert.True(exists);
    }

    [Fact]
    public async Task ExistsAsync_Should_Return_False_When_Cart_Not_Found()
    {
        // Arrange
        var nonExistentId = "CART-NOT-FOUND";

        // Act
        var exists = await _repository.ExistsAsync(nonExistentId);

        // Assert
        Assert.False(exists);
    }

    [Fact]
    public async Task ExistsAsync_With_Version_Should_Return_True_When_Version_Exists()
    {
        // Arrange
        var cart = CreateTestCart();
        await _repository.AddAsync(cart);

        // Act
        var exists = await _repository.ExistsAsync(cart.CartId, 1);

        // Assert
        Assert.True(exists);
    }

    [Fact]
    public async Task ExistsAsync_With_Version_Should_Return_False_When_Version_Not_Found()
    {
        // Arrange
        var cart = CreateTestCart();
        await _repository.AddAsync(cart);

        // Act
        var exists = await _repository.ExistsAsync(cart.CartId, 2);

        // Assert
        Assert.False(exists);
    }

    [Fact]
    public async Task Repository_Should_Include_Items_When_Loading_Cart()
    {
        // Arrange
        var cart = CreateTestCart();
        var item = new CartItem("ITEM-001", "SKU-001", "Test Product", 1, 100m, "simple", null);
        var cartWithItem = Axon.Domain.Service.Cart.Domain.Cart.Create(
            cart.CartId,
            cart.CustomerId,
            cart.StoreId,
            cart.CreatedAt,
            cart.Currency,
            cart.Customer,
            new List<CartItem> { item }
        );
        await _repository.AddAsync(cartWithItem);
        _context.ChangeTracker.Clear();

        // Act
        var retrievedCart = await _repository.GetByIdAsync(cart.CartId);

        // Assert
        Assert.NotNull(retrievedCart);
        Assert.NotEmpty(retrievedCart.Items);
        Assert.Single(retrievedCart.Items);
        Assert.Equal("ITEM-001", retrievedCart.Items.First().ItemId);
    }

    [Fact]
    public async Task Repository_Should_Include_Addresses_When_Loading_Cart()
    {
        // Arrange
        var cart = CreateTestCart();
        var billingAddress = new CartAddress(
            Id: "addr-1",
            Region: "NY",
            Country: "US",
            Street: new List<string> { "123 Main St" },
            City: "New York",
            Postcode: "10001",
            Firstname: "John",
            Lastname: "Doe",
            Telephone: "555-1234"
        );
        var cartWithAddress = Axon.Domain.Service.Cart.Domain.Cart.Create(
            cart.CartId,
            cart.CustomerId,
            cart.StoreId,
            cart.CreatedAt,
            cart.Currency,
            cart.Customer,
            billingAddress: billingAddress
        );
        await _repository.AddAsync(cartWithAddress);
        _context.ChangeTracker.Clear();

        // Act
        var retrievedCart = await _repository.GetByIdAsync(cart.CartId);

        // Assert
        Assert.NotNull(retrievedCart);
        Assert.NotNull(retrievedCart.BillingAddress);
        Assert.Equal("addr-1", retrievedCart.BillingAddress.Id);
        Assert.Equal("New York", retrievedCart.BillingAddress.City);
    }

    private Axon.Domain.Service.Cart.Domain.Cart CreateTestCart()
    {
        return Axon.Domain.Service.Cart.Domain.Cart.Create(
            cartId: $"CART-{Guid.NewGuid().ToString().Substring(0, 8)}",
            customerId: "CUST-123",
            storeId: "STORE-1",
            createdAt: DateTimeOffset.UtcNow,
            currency: new CartCurrency("USD", "USD"),
            customer: new CartCustomer("<EMAIL>", 1, false, "Test", "User")
        );
    }

    public void Dispose()
    {
        _context.Dispose();
    }
}