using Axon.Domain.Service.Cart.Infrastructure.HealthChecks;
using Axon.Domain.Service.Cart.Infrastructure.Persistence;
using Axon.Domain.Service.Order.Infrastructure.HealthChecks;
using Axon.Domain.Service.Order.Infrastructure.Persistence;
using Axon.Domain.Service.Shared.Infrastructure.HealthChecks;
using Axon.Domain.Service.Shared.Infrastructure.Persistence;
using Axon.Domain.Service.Tests.Infrastructure.Persistence;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;
using Moq;

namespace Axon.Domain.Service.Tests.Infrastructure.HealthChecks;

public class DatabaseHealthCheckTests : IDisposable
{
    private readonly CartDbContext _cartContext;
    private readonly OrderDbContext _orderContext;
    private readonly SharedDbContext _sharedContext;
    private readonly Mock<ILogger<CartDatabaseHealthCheck>> _cartLoggerMock;
    private readonly Mock<ILogger<OrderDatabaseHealthCheck>> _orderLoggerMock;
    private readonly Mock<ILogger<AllDatabasesHealthCheck>> _allDbLoggerMock;

    public DatabaseHealthCheckTests()
    {
        _cartContext = TestDbContextHelper.CreateCartDbContext();
        _orderContext = TestDbContextHelper.CreateOrderDbContext();
        _sharedContext = TestDbContextHelper.CreateSharedDbContext();
        _cartLoggerMock = new Mock<ILogger<CartDatabaseHealthCheck>>();
        _orderLoggerMock = new Mock<ILogger<OrderDatabaseHealthCheck>>();
        _allDbLoggerMock = new Mock<ILogger<AllDatabasesHealthCheck>>();
    }

    [Fact]
    public async Task CartDatabaseHealthCheck_Should_Return_Healthy_When_Database_Is_Accessible()
    {
        // Arrange
        var healthCheck = new CartDatabaseHealthCheck(_cartContext, _cartLoggerMock.Object);
        var context = new HealthCheckContext
        {
            Registration = new HealthCheckRegistration("cart-database", _ => healthCheck, null, null)
        };

        // Act
        var result = await healthCheck.CheckHealthAsync(context);

        // Assert
        Assert.Equal(HealthStatus.Healthy, result.Status);
        Assert.Equal("Cart database is accessible", result.Description);
        Assert.Contains("database", result.Data);
        Assert.Equal("Cart", result.Data["database"]);
        Assert.Equal("cart", result.Data["schema"]);
        Assert.Equal("Connected", result.Data["status"]);
        
        _cartLoggerMock.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((o, t) => o.ToString()!.Contains("Cart database health check passed")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task OrderDatabaseHealthCheck_Should_Return_Healthy_When_Database_Is_Accessible()
    {
        // Arrange
        var healthCheck = new OrderDatabaseHealthCheck(_orderContext, _orderLoggerMock.Object);
        var context = new HealthCheckContext
        {
            Registration = new HealthCheckRegistration("order-database", _ => healthCheck, null, null)
        };

        // Act
        var result = await healthCheck.CheckHealthAsync(context);

        // Assert
        Assert.Equal(HealthStatus.Healthy, result.Status);
        Assert.Equal("Order database is accessible", result.Description);
        Assert.Contains("database", result.Data);
        Assert.Equal("Order", result.Data["database"]);
        Assert.Equal("order", result.Data["schema"]);
        Assert.Equal("Connected", result.Data["status"]);
        
        _orderLoggerMock.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((o, t) => o.ToString()!.Contains("Order database health check passed")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task AllDatabasesHealthCheck_Should_Return_Healthy_When_All_Databases_Are_Accessible()
    {
        // Arrange
        var healthCheck = new AllDatabasesHealthCheck(
            _cartContext, 
            _orderContext, 
            _sharedContext, 
            _allDbLoggerMock.Object);
        var context = new HealthCheckContext
        {
            Registration = new HealthCheckRegistration("all-databases", _ => healthCheck, null, null)
        };

        // Act
        var result = await healthCheck.CheckHealthAsync(context);

        // Assert
        Assert.Equal(HealthStatus.Healthy, result.Status);
        Assert.Equal("All databases are accessible", result.Description);
        Assert.Contains("cart_database", result.Data);
        Assert.Contains("order_database", result.Data);
        Assert.Contains("shared_database", result.Data);
        Assert.Equal("Connected", result.Data["cart_database"]);
        Assert.Equal("Connected", result.Data["order_database"]);
        Assert.Equal("Connected", result.Data["shared_database"]);
        Assert.Equal(3, result.Data["total_databases"]);
        Assert.Equal(3, result.Data["healthy_databases"]);
        
        _allDbLoggerMock.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((o, t) => o.ToString()!.Contains("All databases health check passed")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task AllDatabasesHealthCheck_Should_Return_Degraded_When_Some_Databases_Are_Unhealthy()
    {
        // This test would require mocking database connectivity failures
        // In a real-world scenario, you'd test with a database that's actually down
        // For now, we're testing the happy path with in-memory databases
        
        // Arrange
        var healthCheck = new AllDatabasesHealthCheck(
            _cartContext, 
            _orderContext, 
            _sharedContext, 
            _allDbLoggerMock.Object);
        var context = new HealthCheckContext
        {
            Registration = new HealthCheckRegistration("all-databases", _ => healthCheck, null, null)
        };

        // Act
        var result = await healthCheck.CheckHealthAsync(context);

        // Assert - In-memory databases are always "healthy"
        Assert.Equal(HealthStatus.Healthy, result.Status);
    }

    public void Dispose()
    {
        _cartContext.Dispose();
        _orderContext.Dispose();
        _sharedContext.Dispose();
    }
}