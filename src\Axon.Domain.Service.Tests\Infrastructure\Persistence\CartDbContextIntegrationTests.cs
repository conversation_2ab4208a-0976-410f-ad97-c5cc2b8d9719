using Axon.Domain.Service.Cart.Domain;
using Axon.Domain.Service.Cart.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;

namespace Axon.Domain.Service.Tests.Infrastructure.Persistence;

public class CartDbContextIntegrationTests : IDisposable
{
    private readonly CartDbContext _context;

    public CartDbContextIntegrationTests()
    {
        _context = TestDbContextHelper.CreateCartDbContext();
    }

    [Fact]
    public async Task Should_Save_And_Retrieve_Cart_With_All_Properties()
    {
        // Arrange
        var cart = CreateTestCart();

        // Act
        _context.Carts.Add(cart);
        await _context.SaveChangesAsync();

        // Clear the tracker to ensure we're loading from the "database"
        _context.ChangeTracker.Clear();

        var retrievedCart = await _context.Carts
            .Include(c => c.Items)
            .Include(c => c.BillingAddress)
            .Include(c => c.ShippingAddress)
            .FirstOrDefaultAsync(c => c.CartId == cart.CartId && c.Version == cart.Version);

        // Assert
        Assert.NotNull(retrievedCart);
        Assert.Equal(cart.CartId, retrievedCart.CartId);
        Assert.Equal(1, retrievedCart.Version);
        Assert.Equal(cart.CustomerId, retrievedCart.CustomerId);
        Assert.Equal(cart.StoreId, retrievedCart.StoreId);
        Assert.Equal(cart.IsActive, retrievedCart.IsActive);
        Assert.Equal(cart.CreatedAt, retrievedCart.CreatedAt);
        Assert.Equal(cart.UpdatedAt, retrievedCart.UpdatedAt);
    }

    [Fact]
    public async Task Should_Save_And_Retrieve_Cart_Items()
    {
        // Arrange
        var cart = CreateTestCart();
        
        // Act - Save version 1
        _context.Carts.Add(cart);
        await _context.SaveChangesAsync();
        _context.ChangeTracker.Clear();
        
        // Create and save version 2 with item
        var item = new CartItem("ITEM-001", "SKU-001", "Test Product", 2, 50.00m, "simple", null);
        var cartWithItem = Axon.Domain.Service.Cart.Domain.Cart.WithItem(cart, item);
        _context.Carts.Add(cartWithItem);
        await _context.SaveChangesAsync();
        _context.ChangeTracker.Clear();

        var retrievedCart = await _context.Carts
            .Include(c => c.Items)
            .FirstOrDefaultAsync(c => c.CartId == cart.CartId && c.Version == 2);

        // Assert
        Assert.NotNull(retrievedCart);
        Assert.Single(retrievedCart.Items);
        
        var firstItem = retrievedCart.Items.First();
        Assert.Equal("ITEM-001", firstItem.ItemId);
        Assert.Equal("SKU-001", firstItem.Sku);
        Assert.Equal("Test Product", firstItem.Name);
        Assert.Equal(2, firstItem.Qty);
        Assert.Equal(50.00m, firstItem.Price);
    }

    [Fact]
    public async Task Should_Save_And_Retrieve_Cart_Currency()
    {
        // Arrange
        var cart = CreateTestCart();

        // Act
        _context.Carts.Add(cart);
        await _context.SaveChangesAsync();
        _context.ChangeTracker.Clear();

        var retrievedCart = await _context.Carts
            .FirstOrDefaultAsync(c => c.CartId == cart.CartId);

        // Assert
        Assert.NotNull(retrievedCart);
        Assert.NotNull(retrievedCart.Currency);
        Assert.Equal("USD", retrievedCart.Currency.BaseCurrencyCode);
        Assert.Equal("USD", retrievedCart.Currency.QuoteCurrencyCode);
    }

    [Fact]
    public async Task Should_Save_And_Retrieve_Cart_Totals()
    {
        // Arrange
        var cart = CreateTestCart();
        var item1 = new CartItem("ITEM-001", "SKU-001", "Product 1", 2, 50.00m, "simple", null);
        var item2 = new CartItem("ITEM-002", "SKU-002", "Product 2", 1, 30.00m, "simple", null);
        var cartWithItems = Axon.Domain.Service.Cart.Domain.Cart.Create(
            cart.CartId,
            cart.CustomerId,
            cart.StoreId,
            cart.CreatedAt,
            cart.Currency,
            cart.Customer,
            new List<CartItem> { item1, item2 }
        );

        // Act
        _context.Carts.Add(cartWithItems);
        await _context.SaveChangesAsync();
        _context.ChangeTracker.Clear();

        var retrievedCart = await _context.Carts
            .FirstOrDefaultAsync(c => c.CartId == cart.CartId);

        // Assert
        Assert.NotNull(retrievedCart);
        Assert.NotNull(retrievedCart.Totals);
        Assert.Equal(130.00m, retrievedCart.Totals.Subtotal); // (2*50) + (1*30)
        Assert.Equal(130.00m, retrievedCart.Totals.BaseSubtotal);
        Assert.Equal(130.00m, retrievedCart.Totals.GrandTotal);
    }

    [Fact]
    public async Task Should_Save_And_Retrieve_Cart_Customer()
    {
        // Arrange
        var cart = CreateTestCart();

        // Act
        _context.Carts.Add(cart);
        await _context.SaveChangesAsync();
        _context.ChangeTracker.Clear();

        var retrievedCart = await _context.Carts
            .FirstOrDefaultAsync(c => c.CartId == cart.CartId);

        // Assert
        Assert.NotNull(retrievedCart);
        Assert.NotNull(retrievedCart.Customer);
        Assert.Equal("<EMAIL>", retrievedCart.Customer.Email);
        Assert.Equal(1, retrievedCart.Customer.GroupId);
        Assert.False(retrievedCart.Customer.IsGuest);
        Assert.Equal("John", retrievedCart.Customer.FirstName);
        Assert.Equal("Doe", retrievedCart.Customer.LastName);
    }

    [Fact]
    public async Task Should_Save_And_Retrieve_Billing_And_Shipping_Addresses()
    {
        // Arrange
        var cart = CreateTestCart();
        var billingAddress = new CartAddress(
            Id: "billing-addr-1",
            Region: "NY",
            Country: "US",
            Street: new List<string> { "123 Main St" },
            City: "New York",
            Postcode: "10001",
            Firstname: "John",
            Lastname: "Doe",
            Telephone: "555-1234"
        );
        var shippingAddress = new CartAddress(
            Id: "shipping-addr-1",
            Region: "CA",
            Country: "US",
            Street: new List<string> { "456 Oak Ave" },
            City: "Los Angeles",
            Postcode: "90001",
            Firstname: "Jane",
            Lastname: "Doe",
            Telephone: "555-5678"
        );
        var cartWithAddresses = Axon.Domain.Service.Cart.Domain.Cart.Create(
            cart.CartId,
            cart.CustomerId,
            cart.StoreId,
            cart.CreatedAt,
            cart.Currency,
            cart.Customer,
            billingAddress: billingAddress,
            shippingAddress: shippingAddress
        );

        // Act
        _context.Carts.Add(cartWithAddresses);
        await _context.SaveChangesAsync();
        _context.ChangeTracker.Clear();

        var retrievedCart = await _context.Carts
            .Include(c => c.BillingAddress)
            .Include(c => c.ShippingAddress)
            .FirstOrDefaultAsync(c => c.CartId == cart.CartId);

        // Assert
        Assert.NotNull(retrievedCart);
        Assert.NotNull(retrievedCart.BillingAddress);
        Assert.NotNull(retrievedCart.ShippingAddress);
        
        Assert.Equal("billing-addr-1", retrievedCart.BillingAddress.Id);
        Assert.Equal("New York", retrievedCart.BillingAddress.City);
        
        Assert.Equal("shipping-addr-1", retrievedCart.ShippingAddress.Id);
        Assert.Equal("Los Angeles", retrievedCart.ShippingAddress.City);
    }

    [Fact]
    public async Task Should_Use_Composite_Indexes_For_Queries()
    {
        // Arrange
        var cart1 = Axon.Domain.Service.Cart.Domain.Cart.Create(
            cartId: "CART-001",
            customerId: "cust-1",
            storeId: "store-1",
            createdAt: DateTimeOffset.UtcNow,
            currency: new CartCurrency("USD", "USD"),
            customer: new CartCustomer("<EMAIL>", 1, false, "John", "Doe")
        );
        var cart2 = Axon.Domain.Service.Cart.Domain.Cart.Create(
            cartId: "CART-002",
            customerId: "cust-2",
            storeId: "store-1",
            createdAt: DateTimeOffset.UtcNow,
            currency: new CartCurrency("USD", "USD"),
            customer: new CartCustomer("<EMAIL>", 2, false, "Jane", "Smith"),
            isActive: false
        );
        var cart3 = Axon.Domain.Service.Cart.Domain.Cart.Create(
            cartId: "CART-003",
            customerId: "cust-3",
            storeId: "store-2", // Different store
            createdAt: DateTimeOffset.UtcNow,
            currency: new CartCurrency("EUR", "EUR"),
            customer: new CartCustomer("<EMAIL>", 3, false, "Bob", "Johnson")
        );

        _context.Carts.AddRange(cart1, cart2, cart3);
        await _context.SaveChangesAsync();

        // Act
        var activeCarts = await _context.Carts
            .Where(c => c.StoreId == "store-1" && c.IsActive)
            .ToListAsync();

        // Assert
        Assert.Single(activeCarts);
        Assert.Equal("CART-001", activeCarts[0].CartId);
    }

    [Fact]
    public void Should_Use_Correct_Schema()
    {
        // This test verifies the schema configuration
        var entityType = _context.Model.FindEntityType(typeof(Axon.Domain.Service.Cart.Domain.Cart));
        Assert.NotNull(entityType);
        
        var schema = entityType.GetSchema();
        Assert.Equal("cart", schema);
    }

    [Fact]
    public async Task Should_Handle_Cart_Item_With_ProductOption()
    {
        // Arrange
        var cart = CreateTestCart();
        
        // Act - Save version 1
        _context.Carts.Add(cart);
        await _context.SaveChangesAsync();
        _context.ChangeTracker.Clear();
        
        // Create and save version 2 with item
        var productOption = new 
        { 
            color = "red", 
            size = "large",
            customText = "Happy Birthday!"
        };
        var item = new CartItem("ITEM-002", "SKU-002", "Custom Product", 1, 100.00m, "configurable", productOption);
        var cartWithItem = Axon.Domain.Service.Cart.Domain.Cart.WithItem(cart, item);
        _context.Carts.Add(cartWithItem);
        await _context.SaveChangesAsync();
        _context.ChangeTracker.Clear();

        var retrievedCart = await _context.Carts
            .Include(c => c.Items)
            .FirstOrDefaultAsync(c => c.CartId == cart.CartId && c.Version == 2);

        // Assert
        Assert.NotNull(retrievedCart);
        var cartItem = retrievedCart.Items.First();
        Assert.NotNull(cartItem.ProductOption);
        
        // The ProductOption should be stored as JSON and retrieved correctly
        var retrievedOption = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(
            System.Text.Json.JsonSerializer.Serialize(cartItem.ProductOption));
        Assert.Equal("red", retrievedOption!["color"]);
        Assert.Equal("large", retrievedOption["size"]);
        Assert.Equal("Happy Birthday!", retrievedOption["customText"]);
    }

    [Fact]
    public async Task Should_Handle_Address_Street_List_Conversion()
    {
        // Arrange
        var cart = CreateTestCart();
        
        // Act - Save version 1
        _context.Carts.Add(cart);
        await _context.SaveChangesAsync();
        _context.ChangeTracker.Clear();
        
        // Create and save version 2 with billing address
        var billingAddress = new CartAddress(
            Id: "addr-1",
            Region: "NY",
            Country: "US",
            Street: new List<string> { "123 Main St", "Apt 4B", "Building C" },
            City: "New York",
            Postcode: "10001",
            Firstname: "John",
            Lastname: "Doe",
            Telephone: "555-1234"
        );
        var cartWithAddress = Axon.Domain.Service.Cart.Domain.Cart.WithBillingAddress(cart, billingAddress);
        _context.Carts.Add(cartWithAddress);
        await _context.SaveChangesAsync();
        _context.ChangeTracker.Clear();

        var retrievedCart = await _context.Carts
            .Include(c => c.BillingAddress)
            .FirstOrDefaultAsync(c => c.CartId == cart.CartId && c.Version == 2);

        // Assert
        Assert.NotNull(retrievedCart?.BillingAddress);
        Assert.Equal(3, retrievedCart.BillingAddress.Street.Count);
        Assert.Equal("123 Main St", retrievedCart.BillingAddress.Street[0]);
        Assert.Equal("Apt 4B", retrievedCart.BillingAddress.Street[1]);
        Assert.Equal("Building C", retrievedCart.BillingAddress.Street[2]);
    }

    [Fact]
    public async Task Should_Store_Multiple_Versions_Of_Same_Cart()
    {
        // Arrange
        var cart = CreateTestCart();
        
        // Act - Save version 1
        _context.Carts.Add(cart);
        await _context.SaveChangesAsync();
        _context.ChangeTracker.Clear();
        
        // Create and save version 2
        var item1 = new CartItem("ITEM-001", "SKU-001", "Product 1", 1, 50.00m);
        var cart2 = Axon.Domain.Service.Cart.Domain.Cart.WithItem(cart, item1);
        _context.Carts.Add(cart2);
        await _context.SaveChangesAsync();
        _context.ChangeTracker.Clear();
        
        // Create and save version 3
        var item2 = new CartItem("ITEM-002", "SKU-002", "Product 2", 1, 30.00m);
        var cart3 = Axon.Domain.Service.Cart.Domain.Cart.WithItem(cart2, item2);
        _context.Carts.Add(cart3);
        await _context.SaveChangesAsync();
        _context.ChangeTracker.Clear();

        // Get all versions
        var allVersions = await _context.Carts
            .Where(c => c.CartId == cart.CartId)
            .OrderBy(c => c.Version)
            .Include(c => c.Items)
            .ToListAsync();

        // Assert
        Assert.Equal(3, allVersions.Count);
        Assert.Equal(1, allVersions[0].Version);
        Assert.Equal(2, allVersions[1].Version);
        Assert.Equal(3, allVersions[2].Version);
        
        Assert.Empty(allVersions[0].Items);
        Assert.Single(allVersions[1].Items);
        Assert.Equal(2, allVersions[2].Items.Count);
    }

    private Axon.Domain.Service.Cart.Domain.Cart CreateTestCart()
    {
        return Axon.Domain.Service.Cart.Domain.Cart.Create(
            cartId: "CART-001",
            customerId: "customer-123",
            storeId: "store-1",
            createdAt: DateTimeOffset.UtcNow,
            currency: new CartCurrency("USD", "USD"),
            customer: new CartCustomer("<EMAIL>", 1, false, "John", "Doe")
        );
    }

    public void Dispose()
    {
        _context.Dispose();
    }
}