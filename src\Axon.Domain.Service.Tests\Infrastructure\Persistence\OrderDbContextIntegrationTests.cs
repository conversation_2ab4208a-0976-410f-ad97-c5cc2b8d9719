using Axon.Domain.Service.Order.Domain;
using Axon.Domain.Service.Order.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;

namespace Axon.Domain.Service.Tests.Infrastructure.Persistence;

public class OrderDbContextIntegrationTests : IDisposable
{
    private readonly OrderDbContext _context;

    public OrderDbContextIntegrationTests()
    {
        _context = TestDbContextHelper.CreateOrderDbContext();
    }

    [Fact]
    public async Task Should_Save_And_Retrieve_Order_With_All_Properties()
    {
        // Arrange
        var order = CreateTestOrder();

        // Act
        _context.Orders.Add(order);
        await _context.SaveChangesAsync();

        // Clear the tracker to ensure we're loading from the "database"
        _context.ChangeTracker.Clear();

        var retrievedOrder = await _context.Orders
            .Include(o => o.Items)
            .Include(o => o.BillingAddress)
            .Include(o => o.ShippingAddress)
            .Include(o => o.Payment)
            .Include(o => o.ShippingMethod)
            .FirstOrDefaultAsync(o => o.Id == order.Id);

        // Assert
        Assert.NotNull(retrievedOrder);
        Assert.Equal(order.Id, retrievedOrder.Id);
        Assert.Equal(order.IncrementId, retrievedOrder.IncrementId);
        Assert.Equal(order.State, retrievedOrder.State);
        Assert.Equal(order.Status, retrievedOrder.Status);
        Assert.Equal(order.CustomerEmail, retrievedOrder.CustomerEmail);
        Assert.Equal(order.GrandTotal, retrievedOrder.GrandTotal);
        Assert.Equal(order.CreatedAt, retrievedOrder.CreatedAt);
    }

    [Fact]
    public async Task Should_Save_And_Retrieve_Order_Items()
    {
        // Arrange
        var order = CreateTestOrder();

        // Act
        _context.Orders.Add(order);
        await _context.SaveChangesAsync();
        _context.ChangeTracker.Clear();

        var retrievedOrder = await _context.Orders
            .Include(o => o.Items)
            .FirstOrDefaultAsync(o => o.Id == order.Id);

        // Assert
        Assert.NotNull(retrievedOrder);
        Assert.Equal(2, retrievedOrder.Items.Count);
        
        var firstItem = retrievedOrder.Items.First();
        Assert.Equal(1001, firstItem.ItemId);
        Assert.Equal("SKU001", firstItem.Sku);
        Assert.Equal("Product 1", firstItem.Name);
        Assert.Equal(2, firstItem.Qty);
        Assert.Equal(50.00m, firstItem.Price);
    }

    [Fact]
    public async Task Should_Save_And_Retrieve_Billing_Address()
    {
        // Arrange
        var order = CreateTestOrder();

        // Act
        _context.Orders.Add(order);
        await _context.SaveChangesAsync();
        _context.ChangeTracker.Clear();

        var retrievedOrder = await _context.Orders
            .Include(o => o.BillingAddress)
            .FirstOrDefaultAsync(o => o.Id == order.Id);

        // Assert
        Assert.NotNull(retrievedOrder);
        Assert.NotNull(retrievedOrder.BillingAddress);
        Assert.Equal("John", retrievedOrder.BillingAddress.Firstname);
        Assert.Equal("Doe", retrievedOrder.BillingAddress.Lastname);
        Assert.Equal("New York", retrievedOrder.BillingAddress.City);
        Assert.Equal("US", retrievedOrder.BillingAddress.CountryId);
        Assert.Equal(2, retrievedOrder.BillingAddress.Street.Count);
        Assert.Contains("123 Main St", retrievedOrder.BillingAddress.Street);
        Assert.Contains("Apt 4B", retrievedOrder.BillingAddress.Street);
    }

    [Fact]
    public async Task Should_Handle_Null_Shipping_Address()
    {
        // Arrange
        var order = new Axon.Domain.Service.Order.Domain.Order(
            id: Guid.NewGuid(),
            incrementId: "ORD-002",
            state: "processing",
            status: "pending",
            customerId: null,
            customerEmail: "<EMAIL>",
            customerFirstname: "Test",
            customerLastname: "User",
            billingAddress: new Address("Test", "User", new List<string> { "123 St" }, "City", null, null, "US", "555-1234"),
            items: new List<OrderItem> 
            { 
                new OrderItem(1, "SKU1", 1, 100m, 100m, 100m, 100m, "Item", "simple") 
            },
            payment: new Payment("card", 100m, 100m),
            shippingAddress: null, // No shipping address
            shippingMethod: null,
            totalQtyOrdered: 1,
            grandTotal: 100m,
            baseGrandTotal: 100m,
            createdAt: DateTimeOffset.UtcNow
        );

        // Act
        _context.Orders.Add(order);
        await _context.SaveChangesAsync();
        _context.ChangeTracker.Clear();

        var retrievedOrder = await _context.Orders
            .Include(o => o.ShippingAddress)
            .FirstOrDefaultAsync(o => o.Id == order.Id);

        // Assert
        Assert.NotNull(retrievedOrder);
        Assert.Null(retrievedOrder.ShippingAddress);
    }

    [Fact]
    public async Task Should_Query_Orders_By_Customer_Email()
    {
        // Arrange
        var order1 = CreateTestOrder();
        var order2 = CreateTestOrder();
        order2 = new Axon.Domain.Service.Order.Domain.Order(
            id: Guid.NewGuid(),
            incrementId: "ORD-003",
            state: order2.State,
            status: order2.Status,
            customerId: order2.CustomerId,
            customerEmail: "<EMAIL>",
            customerFirstname: order2.CustomerFirstname,
            customerLastname: order2.CustomerLastname,
            billingAddress: order2.BillingAddress,
            items: order2.Items,
            payment: order2.Payment,
            shippingAddress: order2.ShippingAddress,
            shippingMethod: order2.ShippingMethod,
            totalQtyOrdered: order2.TotalQtyOrdered,
            grandTotal: order2.GrandTotal,
            baseGrandTotal: order2.BaseGrandTotal,
            createdAt: order2.CreatedAt
        );

        _context.Orders.AddRange(order1, order2);
        await _context.SaveChangesAsync();

        // Act
        var orders = await _context.Orders
            .Where(o => o.CustomerEmail == "<EMAIL>")
            .ToListAsync();

        // Assert
        Assert.Single(orders);
        Assert.Equal("<EMAIL>", orders[0].CustomerEmail);
    }

    [Fact]
    public void Should_Use_Correct_Schema()
    {
        // This test verifies the schema configuration
        // In a real database, this would create tables in the 'order' schema
        var entityType = _context.Model.FindEntityType(typeof(Axon.Domain.Service.Order.Domain.Order));
        Assert.NotNull(entityType);
        
        var schema = entityType.GetSchema();
        Assert.Equal("order", schema);
    }

    [Fact]
    public async Task Should_Handle_Multiple_Contexts_Same_Database()
    {
        // Note: This test demonstrates using multiple contexts with the same in-memory database.
        // In production with PostgreSQL, you'd need to handle concurrency conflicts properly.
        
        // Arrange
        var dbName = Guid.NewGuid().ToString();
        var orderId = Guid.NewGuid();
        
        // Create and save order in first context
        using (var context1 = TestDbContextHelper.CreateOrderDbContext(dbName))
        {
            var order = new Axon.Domain.Service.Order.Domain.Order(
                id: orderId,
                incrementId: "ORD-MULTI-001",
                state: "processing",
                status: "pending",
                customerId: 12345,
                customerEmail: "<EMAIL>",
                customerFirstname: "Test",
                customerLastname: "User",
                billingAddress: new Address("Test", "User", new List<string> { "123 St" }, "City", null, null, "US", "555-1234"),
                items: new List<OrderItem> 
                { 
                    new OrderItem(1, "SKU1", 1, 100m, 100m, 100m, 100m, "Item", "simple") 
                },
                payment: new Payment("card", 100m, 100m),
                shippingAddress: null,
                shippingMethod: null,
                totalQtyOrdered: 1,
                grandTotal: 100m,
                baseGrandTotal: 100m,
                createdAt: DateTimeOffset.UtcNow
            );
            
            context1.Orders.Add(order);
            await context1.SaveChangesAsync();
        }

        // Act & Assert - Read from second context
        using (var context2 = TestDbContextHelper.CreateOrderDbContext(dbName))
        {
            var retrievedOrder = await context2.Orders.FindAsync(orderId);
            
            Assert.NotNull(retrievedOrder);
            Assert.Equal("ORD-MULTI-001", retrievedOrder.IncrementId);
            Assert.Equal("processing", retrievedOrder.State);
            Assert.Equal("<EMAIL>", retrievedOrder.CustomerEmail);
        }
    }

    private Axon.Domain.Service.Order.Domain.Order CreateTestOrder()
    {
        var items = new List<OrderItem>
        {
            new OrderItem(
                ItemId: 1001,
                Sku: "SKU001",
                Qty: 2,
                Price: 50.00m,
                BasePrice: 50.00m,
                RowTotal: 100.00m,
                BaseRowTotal: 100.00m,
                Name: "Product 1",
                ProductType: "simple"
            ),
            new OrderItem(
                ItemId: 1002,
                Sku: "SKU002",
                Qty: 1,
                Price: 75.00m,
                BasePrice: 75.00m,
                RowTotal: 75.00m,
                BaseRowTotal: 75.00m,
                Name: "Product 2",
                ProductType: "configurable"
            )
        };

        var billingAddress = new Address(
            Firstname: "John",
            Lastname: "Doe",
            Street: new List<string> { "123 Main St", "Apt 4B" },
            City: "New York",
            Region: "NY",
            Postcode: "10001",
            CountryId: "US",
            Telephone: "555-1234"
        );

        var shippingAddress = new Address(
            Firstname: "John",
            Lastname: "Doe",
            Street: new List<string> { "456 Oak Ave" },
            City: "New York",
            Region: "NY",
            Postcode: "10002",
            CountryId: "US",
            Telephone: "555-1234"
        );

        var payment = new Payment(
            Method: "credit_card",
            AmountOrdered: 175.00m,
            BaseAmountOrdered: 175.00m
        );

        var shippingMethod = new ShippingMethod(
            MethodCode: "flatrate",
            CarrierCode: "flatrate"
        );

        return new Axon.Domain.Service.Order.Domain.Order(
            id: Guid.NewGuid(),
            incrementId: "ORD-001",
            state: "processing",
            status: "pending",
            customerId: 12345,
            customerEmail: "<EMAIL>",
            customerFirstname: "John",
            customerLastname: "Doe",
            billingAddress: billingAddress,
            items: items,
            payment: payment,
            shippingAddress: shippingAddress,
            shippingMethod: shippingMethod,
            totalQtyOrdered: 3,
            grandTotal: 175.00m,
            baseGrandTotal: 175.00m,
            createdAt: DateTimeOffset.UtcNow
        );
    }

    public void Dispose()
    {
        _context.Dispose();
    }
}