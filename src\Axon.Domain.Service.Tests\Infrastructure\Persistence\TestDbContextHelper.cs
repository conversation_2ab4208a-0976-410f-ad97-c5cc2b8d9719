using Axon.Domain.Service.Cart.Infrastructure.Persistence;
using Axon.Domain.Service.Order.Infrastructure.Persistence;
using Axon.Domain.Service.Shared.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace Axon.Domain.Service.Tests.Infrastructure.Persistence;

public static class TestDbContextHelper
{
    public static OrderDbContext CreateOrderDbContext(string? databaseName = null)
    {
        var options = new DbContextOptionsBuilder<OrderDbContext>()
            .UseInMemoryDatabase(databaseName ?? Guid.NewGuid().ToString())
            .Options;

        return new OrderDbContext(options);
    }

    public static CartDbContext CreateCartDbContext(string? databaseName = null)
    {
        var options = new DbContextOptionsBuilder<CartDbContext>()
            .UseInMemoryDatabase(databaseName ?? Guid.NewGuid().ToString())
            .Options;

        return new CartDbContext(options);
    }

    public static SharedDbContext CreateSharedDbContext(string? databaseName = null)
    {
        var options = new DbContextOptionsBuilder<SharedDbContext>()
            .UseInMemoryDatabase(databaseName ?? Guid.NewGuid().ToString())
            .Options;

        return new SharedDbContext(options);
    }

    public static IServiceProvider CreateServiceProvider(string? databaseName = null)
    {
        var services = new ServiceCollection();
        var dbName = databaseName ?? Guid.NewGuid().ToString();

        services.AddDbContext<OrderDbContext>(options =>
            options.UseInMemoryDatabase(dbName));
        
        services.AddDbContext<CartDbContext>(options =>
            options.UseInMemoryDatabase(dbName));
        
        services.AddDbContext<SharedDbContext>(options =>
            options.UseInMemoryDatabase(dbName));

        services.AddLogging();

        return services.BuildServiceProvider();
    }
}