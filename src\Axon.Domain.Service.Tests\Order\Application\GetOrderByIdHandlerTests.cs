using Axon.Contracts.Order.Queries;
using Axon.Domain.Service.Order.Application;
using Axon.Domain.Service.Order.Domain;
using Microsoft.Extensions.Logging;
using Moq;

namespace Axon.Domain.Service.Tests.Order.Application;

public class GetOrderByIdHandlerTests
{
    [Fact]
    public void Handle_ReturnsOrderResponse_WhenOrderExists()
    {
        // Arrange
        var orderId = Guid.NewGuid();
        var order = new Axon.Domain.Service.Order.Domain.Order(
            orderId,
            "ORDER123",
            "new",
            "pending",
            456,
            "<EMAIL>",
            "<PERSON>",
            "Doe",
            new Axon.Domain.Service.Order.Domain.Address(
                "<PERSON>", "Doe", ["123 Main St"], "Metropolis", "CA", "12345", "US", "555-1234"),
            [
                new Axon.Domain.Service.Order.Domain.OrderItem(789, "SKU1", 2, 10.00m, 10.00m, 20.00m, 20.00m, "Widget", "simple")
            ],
            new Axon.Domain.Service.Order.Domain.Payment("credit_card", 20.00m, 20.00m),
            null,
            null,
            2,
            20.00m,
            20.00m,
            DateTimeOffset.UtcNow
        );

        var repoMock = new Mock<IOrderRepository>();
        var loggerMock = new Mock<ILogger<GetOrderByIdHandler>>();
        repoMock.Setup(r => r.GetById(orderId)).Returns(order);
        
        var handler = new GetOrderByIdHandler(repoMock.Object, loggerMock.Object);
        var query = new GetOrderByIdQuery { OrderId = orderId };

        // Act
        var result = handler.Handle(query);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(orderId, result!.OrderId);
        Assert.Equal("ORDER123", result.IncrementId);
        Assert.Equal("<EMAIL>", result.CustomerEmail);
        Assert.Equal("John", result.BillingAddress.Firstname);
        Assert.Single(result.Items);
        Assert.Equal("SKU1", result.Items[0].Sku);
        Assert.Equal(2, result.Items[0].Qty);
        Assert.Equal("credit_card", result.Payment.Method);
        Assert.Null(result.ShippingAddress); // Not set in test data
        
        repoMock.Verify(r => r.GetById(orderId), Times.Once);
        loggerMock.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Received request to get order by id")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public void Handle_ReturnsNull_WhenOrderDoesNotExist()
    {
        // Arrange
        var orderId = Guid.NewGuid();
        var repoMock = new Mock<IOrderRepository>();
        var loggerMock = new Mock<ILogger<GetOrderByIdHandler>>();
        repoMock.Setup(r => r.GetById(orderId)).Returns((Axon.Domain.Service.Order.Domain.Order?)null);
        
        var handler = new GetOrderByIdHandler(repoMock.Object, loggerMock.Object);
        var query = new GetOrderByIdQuery { OrderId = orderId };

        // Act
        var result = handler.Handle(query);

        // Assert
        Assert.Null(result);
        repoMock.Verify(r => r.GetById(orderId), Times.Once);
        loggerMock.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Received request to get order by id")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public void Handle_MapsAllFieldsCorrectly_WhenOrderHasAllData()
    {
        // Arrange
        var orderId = Guid.NewGuid();
        var order = new Axon.Domain.Service.Order.Domain.Order(
            orderId,
            "ORDER456",
            "processing",
            "confirmed", 
            123,
            "<EMAIL>",
            "Jane",
            "Smith",
            new Axon.Domain.Service.Order.Domain.Address(
                "Jane", "Smith", ["456 Oak Ave", "Apt 2"], "Gotham", "NY", "54321", "US", "555-5678"),
            [
                new Axon.Domain.Service.Order.Domain.OrderItem(101, "SKU1", 1, 15.50m, 15.50m, 15.50m, 15.50m, "Premium Widget", "configurable"),
                new Axon.Domain.Service.Order.Domain.OrderItem(102, "SKU2", 3, 8.25m, 8.25m, 24.75m, 24.75m, "Simple Gadget", "simple")
            ],
            new Axon.Domain.Service.Order.Domain.Payment("paypal", 40.25m, 40.25m),
            new Axon.Domain.Service.Order.Domain.Address(
                "John", "Smith", ["789 Pine St"], "Star City", "CA", "98765", "US", "555-9999"),
            new Axon.Domain.Service.Order.Domain.ShippingMethod("express", "FEDEX"),
            4,
            40.25m,
            40.25m,
            DateTimeOffset.UtcNow
        );

        var repoMock = new Mock<IOrderRepository>();
        var loggerMock = new Mock<ILogger<GetOrderByIdHandler>>();
        repoMock.Setup(r => r.GetById(orderId)).Returns(order);
        
        var handler = new GetOrderByIdHandler(repoMock.Object, loggerMock.Object);
        var query = new GetOrderByIdQuery { OrderId = orderId };

        // Act
        var result = handler.Handle(query);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(orderId, result!.OrderId);
        Assert.Equal("ORDER456", result.IncrementId);
        Assert.Equal("<EMAIL>", result.CustomerEmail);
        Assert.Equal("Jane", result.CustomerFirstname);
        Assert.Equal("Smith", result.CustomerLastname);
        Assert.Equal(123, result.CustomerId);
        Assert.Equal(1, result.StoreId); // Default value
        
        // Billing Address
        Assert.Equal("Jane", result.BillingAddress.Firstname);
        Assert.Equal("Smith", result.BillingAddress.Lastname);
        Assert.Equal(2, result.BillingAddress.Street.Count);
        Assert.Equal("456 Oak Ave", result.BillingAddress.Street[0]);
        Assert.Equal("Gotham", result.BillingAddress.City);
        Assert.Equal("NY", result.BillingAddress.Region);
        Assert.Equal("54321", result.BillingAddress.Postcode);
        Assert.Equal("US", result.BillingAddress.CountryId);
        Assert.Equal("555-5678", result.BillingAddress.Telephone);

        // Shipping Address
        Assert.NotNull(result.ShippingAddress);
        Assert.Equal("John", result.ShippingAddress.Firstname);
        Assert.Equal("Smith", result.ShippingAddress.Lastname);
        Assert.Single(result.ShippingAddress.Street);
        Assert.Equal("789 Pine St", result.ShippingAddress.Street[0]);
        Assert.Equal("Star City", result.ShippingAddress.City);

        // Items
        Assert.Equal(2, result.Items.Count);
        Assert.Equal("SKU1", result.Items[0].Sku);
        Assert.Equal(1, result.Items[0].Qty);
        Assert.Equal(15.50m, result.Items[0].Price);
        Assert.Equal("Premium Widget", result.Items[0].Name);
        Assert.Equal("configurable", result.Items[0].ProductType);
        
        // Payment
        Assert.Equal("paypal", result.Payment.Method);
        Assert.Equal(40.25m, result.Payment.AmountOrdered);
        Assert.Equal(40.25m, result.Payment.BaseAmountOrdered);
        
        // Shipping Method
        Assert.NotNull(result.ShippingMethod);
        Assert.Equal("express", result.ShippingMethod.MethodCode);
        Assert.Equal("FEDEX", result.ShippingMethod.CarrierCode);
        
        // New fields that should now be available
        Assert.Equal("processing", result.State);
        Assert.Equal("confirmed", result.Status);
        Assert.Equal(40.25m, result.GrandTotal);
        Assert.Equal(40.25m, result.BaseGrandTotal);
        Assert.Equal(4, result.TotalQtyOrdered);
        Assert.NotNull(result.CreatedAt);
    }
} 