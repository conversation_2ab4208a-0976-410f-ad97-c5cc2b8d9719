using Axon.Contracts.Order.Commands;
using Axon.Domain.Service.Order.Application;
using Axon.Domain.Service.Order.Domain;
using Moq;

namespace Axon.Domain.Service.Tests.Order.Application;

public class RecordOrderCreatedHandlerTests
{
    [Fact]
    public void Handler_Maps_Command_To_Domain_And_Event()
    {
        var repoMock = new Mock<IOrderRepository>();
        var handler = new RecordOrderCreatedHandler(repoMock.Object);

        var command = new RecordOrderCreatedCommand
        {
            IncrementId = "C123",
            State = "new",
            Status = "pending",
            CustomerId = 456,
            CustomerEmail = "<EMAIL>",
            CustomerFirstname = "<PERSON>",
            CustomerLastname = "Doe",
            BillingAddress = new Axon.Contracts.Order.Commands.Address
            {
                Firstname = "John",
                Lastname = "Doe",
                Street = new List<string> { "123 Main" },
                City = "City",
                CountryId = "US",
                Telephone = "123456"
            },
            Items = new List<Axon.Contracts.Order.Commands.OrderItem>
            {
                new Axon.Contracts.Order.Commands.OrderItem
                {
                    ItemId = 1,
                    Sku = "sku",
                    Qty = 1,
                    Price = 10,
                    BasePrice = 10,
                    RowTotal = 10,
                    BaseRowTotal = 10,
                    Name = "name",
                    ProductType = "type"
                }
            },
            Payment = new Axon.Contracts.Order.Commands.Payment 
            { 
                Method = "CreditCard", 
                AmountOrdered = 10, 
                BaseAmountOrdered = 10 
            },
            TotalQtyOrdered = 1,
            GrandTotal = 10,
            BaseGrandTotal = 10,
            CreatedAt = DateTimeOffset.UtcNow
        };

        var evt = handler.Handle(command);

        Assert.Equal(command.IncrementId, evt.IncrementId);
        Assert.Equal(command.CustomerEmail, evt.CustomerEmail);
        repoMock.Verify(r => r.Save(It.IsAny<Service.Order.Domain.Order>()), Times.Once);
    }

    [Fact]
    public void Handler_Maps_All_Fields_Correctly()
    {
        var repoMock = new Mock<IOrderRepository>();
        var handler = new RecordOrderCreatedHandler(repoMock.Object);

        var command = new RecordOrderCreatedCommand
        {
            IncrementId = "C123",
            State = "processing",
            Status = "confirmed",
            CustomerId = 789,
            CustomerEmail = "<EMAIL>",
            CustomerFirstname = "Jane",
            CustomerLastname = "Smith",
            StoreId = 42,
            Items = new List<Axon.Contracts.Order.Commands.OrderItem>
            {
                new Axon.Contracts.Order.Commands.OrderItem
                {
                    ItemId = 100,
                    Sku = "sku1",
                    Qty = 2,
                    Price = 10.5m,
                    BasePrice = 10.5m,
                    RowTotal = 21.0m,
                    BaseRowTotal = 21.0m,
                    Name = "Widget",
                    ProductType = "typeA",
                    ProductOption = new Axon.Contracts.Order.Commands.ProductOption { ExtensionAttributes = "ext1" }
                },
                new Axon.Contracts.Order.Commands.OrderItem
                {
                    ItemId = 101,
                    Sku = "sku2",
                    Qty = 1,
                    Price = 20m,
                    BasePrice = 20m,
                    RowTotal = 20m,
                    BaseRowTotal = 20m,
                    Name = "Gadget",
                    ProductType = "typeB",
                    ProductOption = null
                }
            },
            BillingAddress = new Axon.Contracts.Order.Commands.Address
            {
                Firstname = "Jane",
                Lastname = "Smith",
                Street = new List<string> { "123 Main", "Apt 4" },
                City = "Metropolis",
                Region = "CA",
                Postcode = "90210",
                CountryId = "US",
                Telephone = "555-1234"
            },
            ShippingAddress = new Axon.Contracts.Order.Commands.Address
            {
                Firstname = "John",
                Lastname = "Doe",
                Street = new List<string> { "456 Elm" },
                City = "Gotham",
                Region = "NY",
                Postcode = "10001",
                CountryId = "US",
                Telephone = "555-5678"
            },
            Payment = new Axon.Contracts.Order.Commands.Payment 
            { 
                Method = "credit_card", 
                AmountOrdered = 41.0m, 
                BaseAmountOrdered = 41.0m 
            },
            ShippingMethod = new Axon.Contracts.Order.Commands.ShippingMethod { MethodCode = "EXP", CarrierCode = "UPS" },
            TotalQtyOrdered = 3,
            GrandTotal = 41.0m,
            BaseGrandTotal = 41.0m,
            CreatedAt = DateTimeOffset.UtcNow
        };

        var evt = handler.Handle(command);

        Assert.Equal(command.IncrementId, evt.IncrementId);
        Assert.Equal(command.CustomerEmail, evt.CustomerEmail);
        Assert.Equal(command.CustomerFirstname, evt.CustomerFirstname);
        Assert.Equal(command.CustomerLastname, evt.CustomerLastname);
        Assert.Equal(command.StoreId, evt.StoreId);

        Assert.Equal(command.Items.Count, evt.Items.Count);
        Assert.Equal(command.Items[0].Sku, evt.Items[0].Sku);
        Assert.Equal(command.Items[0].Qty, evt.Items[0].Qty);
        Assert.Equal(command.Items[0].Price, evt.Items[0].Price);
        Assert.Equal(command.Items[0].Name, evt.Items[0].Name);
        Assert.Equal(command.Items[0].ProductType, evt.Items[0].ProductType);
        Assert.Null(evt.Items[0].ProductOption);
        Assert.Null(evt.Items[1].ProductOption);

        Assert.Equal(command.BillingAddress.Firstname, evt.BillingAddress.Firstname);
        Assert.Equal(command.BillingAddress.Lastname, evt.BillingAddress.Lastname);
        Assert.Equal(command.BillingAddress.Street, evt.BillingAddress.Street);
        Assert.Equal(command.BillingAddress.City, evt.BillingAddress.City);
        Assert.Equal(command.BillingAddress.Region, evt.BillingAddress.Region);
        Assert.Equal(command.BillingAddress.Postcode, evt.BillingAddress.Postcode);
        Assert.Equal(command.BillingAddress.CountryId, evt.BillingAddress.CountryId);
        Assert.Equal(command.BillingAddress.Telephone, evt.BillingAddress.Telephone);

        Assert.NotNull(evt.ShippingAddress);
        Assert.Equal(command.ShippingAddress.Firstname, evt.ShippingAddress.Firstname);
        Assert.Equal(command.ShippingAddress.Lastname, evt.ShippingAddress.Lastname);
        Assert.Equal(command.ShippingAddress.Street, evt.ShippingAddress.Street);
        Assert.Equal(command.ShippingAddress.City, evt.ShippingAddress.City);
        Assert.Equal(command.ShippingAddress.Region, evt.ShippingAddress.Region);
        Assert.Equal(command.ShippingAddress.Postcode, evt.ShippingAddress.Postcode);
        Assert.Equal(command.ShippingAddress.CountryId, evt.ShippingAddress.CountryId);
        Assert.Equal(command.ShippingAddress.Telephone, evt.ShippingAddress.Telephone);

        Assert.Equal(command.Payment.Method, evt.Payment.Method);
        Assert.NotNull(evt.ShippingMethod);
        Assert.Equal(command.ShippingMethod.MethodCode, evt.ShippingMethod.MethodCode);
        Assert.Equal(command.ShippingMethod.CarrierCode, evt.ShippingMethod.CarrierCode);

        repoMock.Verify(r => r.Save(It.IsAny<Service.Order.Domain.Order>()), Times.Once);
    }
}