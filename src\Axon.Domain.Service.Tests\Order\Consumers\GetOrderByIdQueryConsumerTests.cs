using Axon.Contracts.Order.Queries;
using Axon.Domain.Service.Order.Application;
using Axon.Domain.Service.Order.Consumers;
using Axon.Domain.Service.Order.Domain;
using MassTransit;
using MassTransit.Testing;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;

namespace Axon.Domain.Service.Tests.Order.Consumers;

public class GetOrderByIdQueryConsumerTests
{
    private class InMemoryOrderRepository : IOrderRepository
    {
        public List<Axon.Domain.Service.Order.Domain.Order> SavedOrders { get; } = new();

        public void Save(Axon.Domain.Service.Order.Domain.Order order) => SavedOrders.Add(order);

        public Axon.Domain.Service.Order.Domain.Order? GetById(Guid orderId) =>
            SavedOrders.FirstOrDefault(o => o.Id == orderId);
    }

    [Fact]
    public async Task Consume_ReturnsOrderResponse_WhenOrderExists()
    {
        // Arrange
        var orderId = Guid.NewGuid();
        var order = new Axon.Domain.Service.Order.Domain.Order(
            orderId,
            "ORDER123",
            "new",
            "pending",
            456,
            "<EMAIL>",
            "<PERSON>",
            "Doe",
            new Axon.Domain.Service.Order.Domain.Address(
                "John", "Doe", ["123 Main St"], "Metropolis", "CA", "12345", "US", "555-1234"),
            [
                new Axon.Domain.Service.Order.Domain.OrderItem(789, "SKU1", 2, 10.00m, 10.00m, 20.00m, 20.00m, "Widget", "simple")
            ],
            new Axon.Domain.Service.Order.Domain.Payment("credit_card", 20.00m, 20.00m),
            null,
            null,
            2,
            20.00m,
            20.00m,
            DateTimeOffset.UtcNow
        );

        var services = new ServiceCollection();
        var repository = new InMemoryOrderRepository();
        repository.SavedOrders.Add(order);
        services.AddSingleton<IOrderRepository>(repository);
        services.AddScoped<IGetOrderByIdHandler, GetOrderByIdHandler>();
        services.AddMassTransitTestHarness(cfg => { cfg.AddConsumer<GetOrderByIdQueryConsumer>(); });
        services.AddLogging();

        await using var provider = services.BuildServiceProvider(true);
        var harness = provider.GetRequiredService<ITestHarness>();
        await harness.Start();

        var query = new GetOrderByIdQuery { OrderId = orderId };

        // Act
        var requestClient = harness.GetRequestClient<GetOrderByIdQuery>();
        var response = await requestClient.GetResponse<GetOrderByIdResponse>(query);

        // Assert
        Assert.True(await harness.Consumed.Any<GetOrderByIdQuery>());
        Assert.NotNull(response.Message);
        Assert.Equal(orderId, response.Message.OrderId);
        Assert.Equal("ORDER123", response.Message.IncrementId);
        Assert.Equal("<EMAIL>", response.Message.CustomerEmail);

        await harness.Stop();
    }

    [Fact]
    public async Task Consume_ThrowsException_WhenOrderDoesNotExist()
    {
        // Arrange
        var orderId = Guid.NewGuid();
        var services = new ServiceCollection();
        services.AddSingleton<IOrderRepository, InMemoryOrderRepository>();
        services.AddScoped<IGetOrderByIdHandler, GetOrderByIdHandler>();
        services.AddMassTransitTestHarness(cfg => { cfg.AddConsumer<GetOrderByIdQueryConsumer>(); });
        services.AddLogging();

        await using var provider = services.BuildServiceProvider(true);
        var harness = provider.GetRequiredService<ITestHarness>();
        await harness.Start();

        var query = new GetOrderByIdQuery { OrderId = orderId };

        // Act & Assert
        var requestClient = harness.GetRequestClient<GetOrderByIdQuery>();
        await Assert.ThrowsAsync<RequestFaultException>(async () =>
            await requestClient.GetResponse<GetOrderByIdResponse>(query));

        Assert.True(await harness.Consumed.Any<GetOrderByIdQuery>());

        await harness.Stop();
    }
} 