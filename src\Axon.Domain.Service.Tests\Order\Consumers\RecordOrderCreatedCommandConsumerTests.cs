using Axon.Contracts.Order.Commands;
using Axon.Contracts.Order.Events;
using Axon.Domain.Service.Order.Application;
using Axon.Domain.Service.Order.Consumers;
using Axon.Domain.Service.Order.Domain;
using MassTransit;
using MassTransit.Testing;
using Microsoft.Extensions.DependencyInjection;

namespace Axon.Domain.Service.Tests.Order.Consumers;

public class RecordOrderCreatedCommandConsumerTests
{
    private class InMemoryOrderRepository : IOrderRepository
    {
        public List<Axon.Domain.Service.Order.Domain.Order> SavedOrders { get; } = new();

        public void Save(Axon.Domain.Service.Order.Domain.Order order) => SavedOrders.Add(order);

        public Axon.Domain.Service.Order.Domain.Order? GetById(Guid orderId) =>
            SavedOrders.FirstOrDefault(o => o.Id == orderId);
    }

    [Fact]
    public async Task Consume_MapsCommandToEventAndPublishes()
    {
        // Arrange
        var services = new ServiceCollection();
        services.AddSingleton<IOrderRepository, InMemoryOrderRepository>();
        services.AddScoped<IRecordOrderCreated<PERSON><PERSON><PERSON>, RecordOrderCreatedHandler>();
        services.AddMassTransitTestHarness(cfg => { cfg.AddConsumer<RecordOrderCreatedCommandConsumer>(); });
        services.AddLogging();

        await using var provider = services.BuildServiceProvider(true);
        var harness = provider.GetRequiredService<ITestHarness>();
        await harness.Start();

        var command = new RecordOrderCreatedCommand
        {
            IncrementId = "C123",
            CustomerEmail = "<EMAIL>",
            CustomerFirstname = "John",
            CustomerLastname = "Doe",
            StoreId = 1,
            Items =
            [
                new Axon.Contracts.Order.Commands.OrderItem
                    { Sku = "SKU1", Qty = 2, Price = 10, Name = "Item1", ProductType = "TypeA" }
            ],
            BillingAddress = new Axon.Contracts.Order.Commands.Address
            {
                Firstname = "John", Lastname = "Doe", Street = ["123 Main"], City = "City", CountryId = "US",
                Telephone = "123456"
            },
            ShippingAddress = new Axon.Contracts.Order.Commands.Address
            {
                Firstname = "Jane", Lastname = "Doe", Street = ["123 Main"], City = "City", CountryId = "US",
                Telephone = "123456"
            },
            Payment = new Axon.Contracts.Order.Commands.Payment { Method = "CreditCard" },
            ShippingMethod = new Axon.Contracts.Order.Commands.ShippingMethod
                { MethodCode = "STD", CarrierCode = "UPS" }
        };

        // Act
        var sendEndpoint = await harness.Bus.GetSendEndpoint(new Uri("queue:RecordOrderCreatedCommand"));
        await sendEndpoint.Send(command);

        // Assert
        Assert.True(await harness.Consumed.Any<RecordOrderCreatedCommand>());
        Assert.True(await harness.Published.Any<OrderCreatedEvent>());

        var published = harness.Published.Select<OrderCreatedEvent>().First().Context.Message;
        Assert.Equal(command.IncrementId, published.IncrementId);
        Assert.Equal(command.CustomerEmail, published.CustomerEmail);

        await harness.Stop();
    }
}