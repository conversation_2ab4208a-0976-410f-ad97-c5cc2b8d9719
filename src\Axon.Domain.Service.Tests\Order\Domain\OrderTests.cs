using Axon.Domain.Service.Order.Domain;
using Moq;

namespace Axon.Domain.Service.Tests.Order.Domain;

public class OrderTests
{
    [Fact]
    public void Cannot_Create_Order_With_No_Items()
    {
        var ex = Assert.Throws<ArgumentException>(() =>
            new Service.Order.Domain.Order(
                Guid.NewGuid(),
                "id1",
                "new",
                "pending",
                123,
                "<EMAIL>",
                "<PERSON>",
                "<PERSON><PERSON>",
                new Address("<PERSON>", "<PERSON><PERSON>", new List<string> { "123 Main" }, "City", null, null, "US", "123456"),
                new List<OrderItem>(), // No items
                new Payment("CreditCard", 100.00m, 100.00m)
            )
        );
        Assert.Contains("at least one item", ex.Message);
    }

    [Fact]
    public void Cannot_Create_Order_With_Null_BillingAddress()
    {
        var ex = Assert.Throws<ArgumentNullException>(() =>
            new Service.Order.Domain.Order(
                Guid.NewGuid(),
                "id1",
                "new",
                "pending",
                123,
                "<EMAIL>",
                "<PERSON>",
                "<PERSON><PERSON>",
                null, // Null billing address
                new List<OrderItem> { new OrderItem(1, "sku", 1, 10, 10, 10, 10, "name", "type") },
                new Payment("CreditCard", 100.00m, 100.00m)
            )
        );
        Assert.Equal("billingAddress", ex.ParamName);
    }
}