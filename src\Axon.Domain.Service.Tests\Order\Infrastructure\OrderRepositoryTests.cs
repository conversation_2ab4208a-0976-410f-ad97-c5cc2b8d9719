using Axon.Domain.Service.Order.Domain;
using Axon.Domain.Service.Order.Infrastructure;
using Axon.Domain.Service.Order.Infrastructure.Persistence;
using Axon.Domain.Service.Tests.Infrastructure.Persistence;
using Microsoft.Extensions.Logging;
using Moq;

namespace Axon.Domain.Service.Tests.Order.Infrastructure;

public class OrderRepositoryTests : IDisposable
{
    private readonly OrderDbContext _context;
    private readonly Mock<ILogger<OrderRepository>> _loggerMock;
    private readonly OrderRepository _repository;

    public OrderRepositoryTests()
    {
        _context = TestDbContextHelper.CreateOrderDbContext();
        _loggerMock = new Mock<ILogger<OrderRepository>>();
        _repository = new OrderRepository(_context, _loggerMock.Object);
    }

    [Fact]
    public void Save_Should_Add_New_Order()
    {
        // Arrange
        var order = CreateTestOrder();

        // Act
        _repository.Save(order);

        // Assert
        var savedOrder = _context.Orders.Find(order.Id);
        Assert.NotNull(savedOrder);
        Assert.Equal(order.Id, savedOrder.Id);
        Assert.Equal(order.IncrementId, savedOrder.IncrementId);
        
        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((o, t) => o.ToString()!.Contains($"Saving order {order.Id}")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public void Save_Should_Update_Existing_Order()
    {
        // Arrange
        var order = CreateTestOrder();
        _repository.Save(order);

        // Create updated order with same ID but different state
        var updatedOrder = new Axon.Domain.Service.Order.Domain.Order(
            id: order.Id,
            incrementId: order.IncrementId,
            state: "completed",
            status: "paid",
            customerId: order.CustomerId,
            customerEmail: order.CustomerEmail,
            customerFirstname: order.CustomerFirstname,
            customerLastname: order.CustomerLastname,
            billingAddress: order.BillingAddress,
            items: order.Items,
            payment: order.Payment,
            shippingAddress: order.ShippingAddress,
            shippingMethod: order.ShippingMethod,
            totalQtyOrdered: order.TotalQtyOrdered,
            grandTotal: order.GrandTotal,
            baseGrandTotal: order.BaseGrandTotal,
            createdAt: order.CreatedAt
        );

        // Act
        _repository.Save(updatedOrder);

        // Assert
        var savedOrder = _context.Orders.Find(order.Id);
        Assert.NotNull(savedOrder);
        Assert.Equal("completed", savedOrder.State);
        Assert.Equal("paid", savedOrder.Status);
    }

    [Fact]
    public void Save_Should_Throw_ArgumentNullException_When_Order_Is_Null()
    {
        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => _repository.Save(null!));
    }

    [Fact]
    public void GetById_Should_Return_Order_With_All_Includes()
    {
        // Arrange
        var order = CreateTestOrder();
        _repository.Save(order);
        _context.ChangeTracker.Clear(); // Clear tracker to ensure we're loading from DB

        // Act
        var retrievedOrder = _repository.GetById(order.Id);

        // Assert
        Assert.NotNull(retrievedOrder);
        Assert.Equal(order.Id, retrievedOrder.Id);
        Assert.NotNull(retrievedOrder.Items);
        Assert.NotEmpty(retrievedOrder.Items);
        Assert.NotNull(retrievedOrder.BillingAddress);
        Assert.NotNull(retrievedOrder.Payment);
        
        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((o, t) => o.ToString()!.Contains($"Fetching order {order.Id}")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public void GetById_Should_Return_Null_When_Order_Not_Found()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid();

        // Act
        var result = _repository.GetById(nonExistentId);

        // Assert
        Assert.Null(result);
        
        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((o, t) => o.ToString()!.Contains($"Order {nonExistentId} not found")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public void Clear_Should_Remove_All_Orders()
    {
        // Arrange
        var order1 = CreateTestOrder();
        var order2 = CreateTestOrder();
        _repository.Save(order1);
        _repository.Save(order2);

        // Act
        _repository.Clear();

        // Assert
        Assert.Empty(_context.Orders);
        
        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((o, t) => o.ToString()!.Contains("Clearing all orders")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    private Axon.Domain.Service.Order.Domain.Order CreateTestOrder()
    {
        return new Axon.Domain.Service.Order.Domain.Order(
            id: Guid.NewGuid(),
            incrementId: $"ORD-{Guid.NewGuid().ToString().Substring(0, 8)}",
            state: "processing",
            status: "pending",
            customerId: 12345,
            customerEmail: "<EMAIL>",
            customerFirstname: "Test",
            customerLastname: "User",
            billingAddress: new Address(
                Firstname: "Test",
                Lastname: "User",
                Street: new List<string> { "123 Test St" },
                City: "Test City",
                Region: "TC",
                Postcode: "12345",
                CountryId: "US",
                Telephone: "555-1234"
            ),
            items: new List<OrderItem>
            {
                new OrderItem(
                    ItemId: 1,
                    Sku: "TEST-SKU",
                    Qty: 1,
                    Price: 100m,
                    BasePrice: 100m,
                    RowTotal: 100m,
                    BaseRowTotal: 100m,
                    Name: "Test Product",
                    ProductType: "simple"
                )
            },
            payment: new Payment(
                Method: "credit_card",
                AmountOrdered: 100m,
                BaseAmountOrdered: 100m
            ),
            shippingAddress: null,
            shippingMethod: null,
            totalQtyOrdered: 1,
            grandTotal: 100m,
            baseGrandTotal: 100m,
            createdAt: DateTimeOffset.UtcNow
        );
    }

    public void Dispose()
    {
        _context.Dispose();
    }
}