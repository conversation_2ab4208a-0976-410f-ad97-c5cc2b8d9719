using Axon.Contracts.Cart.Commands;
using Axon.Contracts.Cart.Events;
using Axon.Domain.Service.Cart.Domain;
using Axon.Domain.Service.Cart.Domain.Exceptions;

namespace Axon.Domain.Service.Cart.Application;

public interface IRecordCartUpdatedHandler
{
    Task<CartUpdatedEvent> Handle(RecordCartUpdatedCommand command, CancellationToken cancellationToken = default);
}

public class RecordCartUpdatedHandler : IRecordCartUpdatedHandler
{
    private readonly ICartRepository _cartRepository;
    private readonly ILogger<RecordCartUpdatedHandler> _logger;

    public RecordCartUpdatedHandler(
        ICartRepository cartRepository,
        ILogger<RecordCartUpdatedHandler> logger)
    {
        _cartRepository = cartRepository;
        _logger = logger;
    }

    public async Task<CartUpdatedEvent> Handle(RecordCartUpdatedCommand command, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Processing RecordCartUpdatedCommand for cart: {CartId}", command.CartId);

        try
        {
            var existingCart = await _cartRepository.GetByIdAsync(command.CartId, cancellationToken);

            if (existingCart == null)
                throw new NonexistentCartException(command.CartId);

            var customer = existingCart.Customer;

            // Map items
            var addedItems = command.Items.Select(i => new Domain.CartItem(
                i.ItemId,
                i.Sku,
                i.Name,
                i.Qty,
                i.Price,
                i.ProductType,
                i.ProductOption)).ToList();

            // Map addresses if provided
            Domain.CartAddress? billingAddress = null;
            if (command.BillingAddress != null)
            {
                billingAddress = new Domain.CartAddress(
                    command.BillingAddress.Id,
                    command.BillingAddress.Region,
                    command.BillingAddress.Country,
                    command.BillingAddress.Street,
                    command.BillingAddress.City,
                    command.BillingAddress.Postcode,
                    command.BillingAddress.Firstname,
                    command.BillingAddress.Lastname,
                    command.BillingAddress.Telephone);
            }

            Domain.CartAddress? shippingAddress = null;
            if (command.ShippingAddress != null)
            {
                shippingAddress = new Domain.CartAddress(
                    command.ShippingAddress.Id,
                    command.ShippingAddress.Region,
                    command.ShippingAddress.Country,
                    command.ShippingAddress.Street,
                    command.ShippingAddress.City,
                    command.ShippingAddress.Postcode,
                    command.ShippingAddress.Firstname,
                    command.ShippingAddress.Lastname,
                    command.ShippingAddress.Telephone);
            }

            // Map payment method if provided
            Domain.CartPaymentMethod? paymentMethod = null;
            if (command.PaymentMethod != null)
            {
                paymentMethod = new Domain.CartPaymentMethod(
                    command.PaymentMethod.Method, 
                    command.PaymentMethod.PoNumber);
            }

            // Map shipping method if provided
            Domain.CartShippingMethod? shippingMethod = null;
            if (command.ShippingMethod != null)
            {
                shippingMethod = new Domain.CartShippingMethod(
                    command.ShippingMethod.CarrierCode,
                    command.ShippingMethod.MethodCode,
                    command.ShippingMethod.MethodTitle,
                    command.ShippingMethod.Amount);
            }

            // Update cart with all data at once using the immutable pattern
            var cart = await Domain.Cart.Update(
                cartRepository: _cartRepository,
                existingCartId: command.CartId,
                items: addedItems,
                billingAddress: billingAddress,
                shippingAddress: shippingAddress,
                paymentMethod: paymentMethod,
                shippingMethod: shippingMethod,
                isNegotiableQuote: command.IsNegotiableQuote,
                isMultiShipping: command.IsMultiShipping);

            // Save to repository
            await _cartRepository.SaveNewVersionAsync(cart, cancellationToken);

            _logger.LogInformation("Successfully created cart: {CartId} with {ItemCount} items", cart.CartId, cart.Items.Count);

            // Return event
            return MapToEvent(cart);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing RecordCartUpdatedCommand for cart: {CartId}", command.CartId);
            throw;
        }
    }

    private CartUpdatedEvent MapToEvent(Domain.Cart cart)
    {
        return new CartUpdatedEvent
        {
            CartId = cart.CartId,
            StoreId = cart.StoreId,
            UpdatedAt = cart.UpdatedAt,
            Totals = new Contracts.Cart.Events.CartTotals
            {
                GrandTotal = cart.Totals.GrandTotal,
                BaseTaxAmount = cart.Totals.BaseTaxAmount,
                TaxAmount = cart.Totals.TaxAmount,
                BaseSubtotal = cart.Totals.BaseSubtotal,
                Subtotal = cart.Totals.Subtotal
            },
            IsNegotiableQuote = cart.IsNegotiableQuote,
            IsMultiShipping = cart.IsMultiShipping,
            ItemsCount = cart.Items.Count,
            ItemsQty = cart.Items.Sum(i => i.Qty),
            Items = cart.Items.Select(i => new Contracts.Cart.Events.CartItem
            {
                ItemId = i.ItemId,
                Sku = i.Sku,
                Name = i.Name,
                Qty = i.Qty,
                Price = i.Price,
                ProductType = i.ProductType,
                ProductOption = i.ProductOption
            }).ToList(),
            BillingAddress = cart.BillingAddress != null ? new Contracts.Cart.Events.CartAddress
            {
                Id = cart.BillingAddress.Id,
                Region = cart.BillingAddress.Region,
                Country = cart.BillingAddress.Country,
                Street = cart.BillingAddress.Street,
                City = cart.BillingAddress.City,
                Postcode = cart.BillingAddress.Postcode,
                Firstname = cart.BillingAddress.Firstname,
                Lastname = cart.BillingAddress.Lastname,
                Telephone = cart.BillingAddress.Telephone
            } : null,
            ShippingAddress = cart.ShippingAddress != null ? new Contracts.Cart.Events.CartAddress
            {
                Id = cart.ShippingAddress.Id,
                Region = cart.ShippingAddress.Region,
                Country = cart.ShippingAddress.Country,
                Street = cart.ShippingAddress.Street,
                City = cart.ShippingAddress.City,
                Postcode = cart.ShippingAddress.Postcode,
                Firstname = cart.ShippingAddress.Firstname,
                Lastname = cart.ShippingAddress.Lastname,
                Telephone = cart.ShippingAddress.Telephone
            } : null,
            PaymentMethod = cart.PaymentMethod != null ? new Contracts.Cart.Events.CartPaymentMethod
            {
                Method = cart.PaymentMethod.Method,
                PoNumber = cart.PaymentMethod.PoNumber
            } : null,
            ShippingMethod = cart.ShippingMethod != null ? new Contracts.Cart.Events.CartShippingMethod
            {
                CarrierCode = cart.ShippingMethod.CarrierCode,
                MethodCode = cart.ShippingMethod.MethodCode,
                MethodTitle = cart.ShippingMethod.MethodTitle,
                Amount = cart.ShippingMethod.Amount
            } : null,
        };
    }
}