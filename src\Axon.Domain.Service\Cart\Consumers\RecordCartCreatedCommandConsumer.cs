using Axon.Contracts.Cart.Commands;
using Axon.Contracts.Cart.Events;
using Axon.Domain.Service.Cart.Application;
using Axon.Domain.Service.Cart.Domain.Exceptions;
using MassTransit;

namespace Axon.Domain.Service.Cart.Consumers;

public class RecordCartCreatedCommandConsumer : IConsumer<RecordCartCreatedCommand>
{
    private readonly IRecordCartCreatedHandler _handler;
    private readonly ILogger<RecordCartCreatedCommandConsumer> _logger;

    public RecordCartCreatedCommandConsumer(
        IRecordCartCreatedHandler handler,
        ILogger<RecordCartCreatedCommandConsumer> logger)
    {
        _handler = handler;
        _logger = logger;
    }

    public async Task Consume(ConsumeContext<RecordCartCreatedCommand> context)
    {
        _logger.LogInformation("Received RecordCartCreatedCommand for cart: {CartId} with idempotency key: {IdempotencyKey}",
            context.Message.CartId, context.Message.IdempotencyKey);

        try
        {
            // Process the command
            var cartCreatedEvent = await _handler.Handle(context.Message, context.CancellationToken);

            // Publish the event
            await context.Publish(cartCreatedEvent, context.CancellationToken);

            _logger.LogInformation("Published CartCreatedEvent for cart: {CartId}", cartCreatedEvent.CartId);

            // Respond to the request
            await context.RespondAsync(cartCreatedEvent);

            _logger.LogInformation("Successfully processed RecordCartCreatedCommand for cart: {CartId}", context.Message.CartId);
        }
        catch (DuplicateCartException ex)
        {
            _logger.LogWarning(ex, "Cart already exists: {CartId}", context.Message.CartId);
            
            // Respond with failure event instead of throwing
            var failureEvent = new CartCreationFailedEvent
            {
                CartId = context.Message.CartId,
                Reason = ex.Message,
                ErrorCode = "DUPLICATE_CART",
                Timestamp = DateTimeOffset.UtcNow
            };
            
            await context.RespondAsync(failureEvent);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing RecordCartCreatedCommand for cart: {CartId}", context.Message.CartId);
            throw; // Let MassTransit handle retry logic for other exceptions
        }
    }
}