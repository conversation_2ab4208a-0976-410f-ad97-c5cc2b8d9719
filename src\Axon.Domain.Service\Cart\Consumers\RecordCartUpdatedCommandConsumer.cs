using Axon.Contracts.Cart.Commands;
using Axon.Contracts.Cart.Events;
using Axon.Domain.Service.Cart.Application;
using Axon.Domain.Service.Cart.Domain.Exceptions;
using MassTransit;

namespace Axon.Domain.Service.Cart.Consumers;

public class RecordCartUpdatedCommandConsumer : IConsumer<RecordCartUpdatedCommand>
{
    private readonly IRecordCartUpdatedHandler _handler;
    private readonly ILogger<RecordCartUpdatedCommandConsumer> _logger;

    public RecordCartUpdatedCommandConsumer(
        IRecordCartUpdatedHandler handler,
        ILogger<RecordCartUpdatedCommandConsumer> logger)
    {
        _handler = handler;
        _logger = logger;
    }

    public async Task Consume(ConsumeContext<RecordCartUpdatedCommand> context)
    {
        _logger.LogInformation("Received RecordCartUpdatedCommand for cart: {CartId} with idempotency key: {IdempotencyKey}",
        context.Message.CartId, context.Message.IdempotencyKey);

        try
        {
            // Process the command
            var cartUpdatedEvent = await _handler.Handle(context.Message, context.CancellationToken);

            // Publish the event
            await context.Publish(cartUpdatedEvent, context.CancellationToken);

            _logger.LogInformation("Published CartUpdatedEvent for cart: {CartId}",
                cartUpdatedEvent.CartId);

            // Respond to the request
            await context.RespondAsync(cartUpdatedEvent);
        }
        catch (NonexistentCartException ex)
        {
            var failureEvent = new CartUpdateFailedEvent
            {
                CartId = context.Message.CartId,
                Reason = ex.Message,
                ErrorCode = "NONEXISTENT_CART"
            };
            await context.RespondAsync(failureEvent);
            throw;
        }
    }
}