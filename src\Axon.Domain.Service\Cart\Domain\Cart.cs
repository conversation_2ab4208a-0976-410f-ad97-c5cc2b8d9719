namespace Axon.Domain.Service.Cart.Domain;

public class Cart
{
    private readonly List<CartItem> _items;

    public string CartId { get; }
    public int Version { get; }
    public string? CustomerId { get; }
    public string StoreId { get; }
    public DateTimeOffset CreatedAt { get; }
    public DateTimeOffset UpdatedAt { get; }
    public CartCurrency Currency { get; }
    public CartTotals Totals { get; }
    public bool IsActive { get; }
    public bool IsVirtual { get; }
    public bool IsNegotiableQuote { get; }
    public bool IsMultiShipping { get; }
    public CartCustomer Customer { get; }
    public CartAddress? BillingAddress { get; }
    public CartAddress? ShippingAddress { get; }
    public CartPaymentMethod? PaymentMethod { get; }
    public CartShippingMethod? ShippingMethod { get; }
    public IReadOnlyList<CartItem> Items => _items.AsReadOnly();

    // Private constructor for EF Core
    private Cart()
    {
        CartId = string.Empty;
        Version = 1;
        StoreId = string.Empty;
        Currency = new CartCurrency(string.Empty, string.Empty);
        Totals = new CartTotals();
        Customer = new CartCustomer(null, null, true, null, null);
        _items = new List<CartItem>();
    }

    // Private constructor for creating new instances with all properties
    private Cart(
        string cartId,
        int version,
        string? customerId,
        string storeId,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        CartCurrency currency,
        CartTotals totals,
        bool isActive,
        bool isVirtual,
        bool isNegotiableQuote,
        bool isMultiShipping,
        CartCustomer customer,
        CartAddress? billingAddress,
        CartAddress? shippingAddress,
        CartPaymentMethod? paymentMethod,
        CartShippingMethod? shippingMethod,
        List<CartItem> items)
    {
        CartId = cartId;
        Version = version;
        CustomerId = customerId;
        StoreId = storeId;
        CreatedAt = createdAt;
        UpdatedAt = updatedAt;
        Currency = currency;
        Totals = totals;
        IsActive = isActive;
        IsVirtual = isVirtual;
        IsNegotiableQuote = isNegotiableQuote;
        IsMultiShipping = isMultiShipping;
        Customer = customer;
        BillingAddress = billingAddress;
        ShippingAddress = shippingAddress;
        PaymentMethod = paymentMethod;
        ShippingMethod = shippingMethod;
        _items = new List<CartItem>(items);
    }

    // Factory method for creating a new cart
    public static Cart Create(
        string cartId,
        string? customerId,
        string storeId,
        DateTimeOffset createdAt,
        CartCurrency currency,
        CartCustomer customer,
        List<CartItem>? items = null,
        CartAddress? billingAddress = null,
        CartAddress? shippingAddress = null,
        CartPaymentMethod? paymentMethod = null,
        CartShippingMethod? shippingMethod = null,
        bool isNegotiableQuote = false,
        bool isMultiShipping = false,
        bool isActive = true)
    {
        if (string.IsNullOrWhiteSpace(cartId))
            throw new ArgumentException("Cart ID cannot be empty", nameof(cartId));
        if (string.IsNullOrWhiteSpace(storeId))
            throw new ArgumentException("Store ID cannot be empty", nameof(storeId));
        if (currency == null)
            throw new ArgumentNullException(nameof(currency));
        if (customer == null)
            throw new ArgumentNullException(nameof(customer));

        var cartItems = items ?? new List<CartItem>();
        var totals = CalculateTotals(cartItems, shippingMethod);
        var isVirtual = cartItems.Count == 0 ? false : cartItems.All(i => i.ProductType == "virtual" || i.ProductType == "downloadable");

        return new Cart(
            cartId: cartId,
            version: 1,
            customerId: customerId,
            storeId: storeId,
            createdAt: createdAt,
            updatedAt: createdAt,
            currency: currency,
            totals: totals,
            isActive: isActive,
            isVirtual: isVirtual,
            isNegotiableQuote: isNegotiableQuote,
            isMultiShipping: isMultiShipping,
            customer: customer,
            billingAddress: billingAddress,
            shippingAddress: shippingAddress,
            paymentMethod: paymentMethod,
            shippingMethod: shippingMethod,
            items: cartItems);
    }

    // Factory method for adding an item (returns new cart instance)
    public static Cart WithItem(Cart existingCart, CartItem item)
    {
        if (existingCart == null)
            throw new ArgumentNullException(nameof(existingCart));
        if (item == null)
            throw new ArgumentNullException(nameof(item));

        var newItems = new List<CartItem>(existingCart._items) { item };
        var totals = CalculateTotals(newItems, existingCart.ShippingMethod);
        var isVirtual = newItems.All(i => i.ProductType == "virtual" || i.ProductType == "downloadable");

        return new Cart(
            cartId: existingCart.CartId,
            version: existingCart.Version + 1,
            customerId: existingCart.CustomerId,
            storeId: existingCart.StoreId,
            createdAt: existingCart.CreatedAt,
            updatedAt: DateTimeOffset.UtcNow,
            currency: existingCart.Currency,
            totals: totals,
            isActive: existingCart.IsActive,
            isVirtual: isVirtual,
            isNegotiableQuote: existingCart.IsNegotiableQuote,
            isMultiShipping: existingCart.IsMultiShipping,
            customer: existingCart.Customer,
            billingAddress: existingCart.BillingAddress,
            shippingAddress: existingCart.ShippingAddress,
            paymentMethod: existingCart.PaymentMethod,
            shippingMethod: existingCart.ShippingMethod,
            items: newItems);
    }

    // Factory method for adding multiple items
    public static Cart WithItems(Cart existingCart, IEnumerable<CartItem> items)
    {
        if (existingCart == null)
            throw new ArgumentNullException(nameof(existingCart));
        if (items == null)
            throw new ArgumentNullException(nameof(items));

        var newItems = new List<CartItem>(existingCart._items);
        newItems.AddRange(items);
        var totals = CalculateTotals(newItems, existingCart.ShippingMethod);
        var isVirtual = newItems.All(i => i.ProductType == "virtual" || i.ProductType == "downloadable");

        return new Cart(
            cartId: existingCart.CartId,
            version: existingCart.Version + 1,
            customerId: existingCart.CustomerId,
            storeId: existingCart.StoreId,
            createdAt: existingCart.CreatedAt,
            updatedAt: DateTimeOffset.UtcNow,
            currency: existingCart.Currency,
            totals: totals,
            isActive: existingCart.IsActive,
            isVirtual: isVirtual,
            isNegotiableQuote: existingCart.IsNegotiableQuote,
            isMultiShipping: existingCart.IsMultiShipping,
            customer: existingCart.Customer,
            billingAddress: existingCart.BillingAddress,
            shippingAddress: existingCart.ShippingAddress,
            paymentMethod: existingCart.PaymentMethod,
            shippingMethod: existingCart.ShippingMethod,
            items: newItems);
    }

    // Factory method for removing an item
    public static Cart WithoutItem(Cart existingCart, string itemId)
    {
        if (existingCart == null)
            throw new ArgumentNullException(nameof(existingCart));

        var newItems = existingCart._items.Where(i => i.ItemId != itemId).ToList();
        var totals = CalculateTotals(newItems, existingCart.ShippingMethod);
        var isVirtual = newItems.All(i => i.ProductType == "virtual" || i.ProductType == "downloadable");

        return new Cart(
            cartId: existingCart.CartId,
            version: existingCart.Version + 1,
            customerId: existingCart.CustomerId,
            storeId: existingCart.StoreId,
            createdAt: existingCart.CreatedAt,
            updatedAt: DateTimeOffset.UtcNow,
            currency: existingCart.Currency,
            totals: totals,
            isActive: existingCart.IsActive,
            isVirtual: isVirtual,
            isNegotiableQuote: existingCart.IsNegotiableQuote,
            isMultiShipping: existingCart.IsMultiShipping,
            customer: existingCart.Customer,
            billingAddress: existingCart.BillingAddress,
            shippingAddress: existingCart.ShippingAddress,
            paymentMethod: existingCart.PaymentMethod,
            shippingMethod: existingCart.ShippingMethod,
            items: newItems);
    }

    // Factory method for setting billing address
    public static Cart WithBillingAddress(Cart existingCart, CartAddress address)
    {
        if (existingCart == null)
            throw new ArgumentNullException(nameof(existingCart));
        if (address == null)
            throw new ArgumentNullException(nameof(address));

        return new Cart(
            cartId: existingCart.CartId,
            version: existingCart.Version + 1,
            customerId: existingCart.CustomerId,
            storeId: existingCart.StoreId,
            createdAt: existingCart.CreatedAt,
            updatedAt: DateTimeOffset.UtcNow,
            currency: existingCart.Currency,
            totals: existingCart.Totals,
            isActive: existingCart.IsActive,
            isVirtual: existingCart.IsVirtual,
            isNegotiableQuote: existingCart.IsNegotiableQuote,
            isMultiShipping: existingCart.IsMultiShipping,
            customer: existingCart.Customer,
            billingAddress: address,
            shippingAddress: existingCart.ShippingAddress,
            paymentMethod: existingCart.PaymentMethod,
            shippingMethod: existingCart.ShippingMethod,
            items: existingCart._items.ToList());
    }

    // Factory method for setting shipping address
    public static Cart WithShippingAddress(Cart existingCart, CartAddress address)
    {
        if (existingCart == null)
            throw new ArgumentNullException(nameof(existingCart));
        if (address == null)
            throw new ArgumentNullException(nameof(address));

        return new Cart(
            cartId: existingCart.CartId,
            version: existingCart.Version + 1,
            customerId: existingCart.CustomerId,
            storeId: existingCart.StoreId,
            createdAt: existingCart.CreatedAt,
            updatedAt: DateTimeOffset.UtcNow,
            currency: existingCart.Currency,
            totals: existingCart.Totals,
            isActive: existingCart.IsActive,
            isVirtual: existingCart.IsVirtual,
            isNegotiableQuote: existingCart.IsNegotiableQuote,
            isMultiShipping: existingCart.IsMultiShipping,
            customer: existingCart.Customer,
            billingAddress: existingCart.BillingAddress,
            shippingAddress: address,
            paymentMethod: existingCart.PaymentMethod,
            shippingMethod: existingCart.ShippingMethod,
            items: existingCart._items.ToList());
    }

    // Factory method for setting payment method
    public static Cart WithPaymentMethod(Cart existingCart, CartPaymentMethod payment)
    {
        if (existingCart == null)
            throw new ArgumentNullException(nameof(existingCart));
        if (payment == null)
            throw new ArgumentNullException(nameof(payment));

        return new Cart(
            cartId: existingCart.CartId,
            version: existingCart.Version + 1,
            customerId: existingCart.CustomerId,
            storeId: existingCart.StoreId,
            createdAt: existingCart.CreatedAt,
            updatedAt: DateTimeOffset.UtcNow,
            currency: existingCart.Currency,
            totals: existingCart.Totals,
            isActive: existingCart.IsActive,
            isVirtual: existingCart.IsVirtual,
            isNegotiableQuote: existingCart.IsNegotiableQuote,
            isMultiShipping: existingCart.IsMultiShipping,
            customer: existingCart.Customer,
            billingAddress: existingCart.BillingAddress,
            shippingAddress: existingCart.ShippingAddress,
            paymentMethod: payment,
            shippingMethod: existingCart.ShippingMethod,
            items: existingCart._items.ToList());
    }

    // Factory method for setting shipping method
    public static Cart WithShippingMethod(Cart existingCart, CartShippingMethod shipping)
    {
        if (existingCart == null)
            throw new ArgumentNullException(nameof(existingCart));
        if (shipping == null)
            throw new ArgumentNullException(nameof(shipping));

        var totals = CalculateTotals(existingCart._items, shipping);

        return new Cart(
            cartId: existingCart.CartId,
            version: existingCart.Version + 1,
            customerId: existingCart.CustomerId,
            storeId: existingCart.StoreId,
            createdAt: existingCart.CreatedAt,
            updatedAt: DateTimeOffset.UtcNow,
            currency: existingCart.Currency,
            totals: totals,
            isActive: existingCart.IsActive,
            isVirtual: existingCart.IsVirtual,
            isNegotiableQuote: existingCart.IsNegotiableQuote,
            isMultiShipping: existingCart.IsMultiShipping,
            customer: existingCart.Customer,
            billingAddress: existingCart.BillingAddress,
            shippingAddress: existingCart.ShippingAddress,
            paymentMethod: existingCart.PaymentMethod,
            shippingMethod: shipping,
            items: existingCart._items.ToList());
    }

    // Factory method for setting negotiable quote status
    public static Cart WithNegotiableQuoteStatus(Cart existingCart, bool isNegotiable)
    {
        if (existingCart == null)
            throw new ArgumentNullException(nameof(existingCart));

        return new Cart(
            cartId: existingCart.CartId,
            version: existingCart.Version + 1,
            customerId: existingCart.CustomerId,
            storeId: existingCart.StoreId,
            createdAt: existingCart.CreatedAt,
            updatedAt: DateTimeOffset.UtcNow,
            currency: existingCart.Currency,
            totals: existingCart.Totals,
            isActive: existingCart.IsActive,
            isVirtual: existingCart.IsVirtual,
            isNegotiableQuote: isNegotiable,
            isMultiShipping: existingCart.IsMultiShipping,
            customer: existingCart.Customer,
            billingAddress: existingCart.BillingAddress,
            shippingAddress: existingCart.ShippingAddress,
            paymentMethod: existingCart.PaymentMethod,
            shippingMethod: existingCart.ShippingMethod,
            items: existingCart._items.ToList());
    }

    // Factory method for setting multi-shipping status
    public static Cart WithMultiShippingStatus(Cart existingCart, bool isMultiShipping)
    {
        if (existingCart == null)
            throw new ArgumentNullException(nameof(existingCart));

        return new Cart(
            cartId: existingCart.CartId,
            version: existingCart.Version + 1,
            customerId: existingCart.CustomerId,
            storeId: existingCart.StoreId,
            createdAt: existingCart.CreatedAt,
            updatedAt: DateTimeOffset.UtcNow,
            currency: existingCart.Currency,
            totals: existingCart.Totals,
            isActive: existingCart.IsActive,
            isVirtual: existingCart.IsVirtual,
            isNegotiableQuote: existingCart.IsNegotiableQuote,
            isMultiShipping: isMultiShipping,
            customer: existingCart.Customer,
            billingAddress: existingCart.BillingAddress,
            shippingAddress: existingCart.ShippingAddress,
            paymentMethod: existingCart.PaymentMethod,
            shippingMethod: existingCart.ShippingMethod,
            items: existingCart._items.ToList());
    }

    // Factory method for deactivating cart
    public static Cart AsDeactivated(Cart existingCart)
    {
        if (existingCart == null)
            throw new ArgumentNullException(nameof(existingCart));

        return new Cart(
            cartId: existingCart.CartId,
            version: existingCart.Version + 1,
            customerId: existingCart.CustomerId,
            storeId: existingCart.StoreId,
            createdAt: existingCart.CreatedAt,
            updatedAt: DateTimeOffset.UtcNow,
            currency: existingCart.Currency,
            totals: existingCart.Totals,
            isActive: false,
            isVirtual: existingCart.IsVirtual,
            isNegotiableQuote: existingCart.IsNegotiableQuote,
            isMultiShipping: existingCart.IsMultiShipping,
            customer: existingCart.Customer,
            billingAddress: existingCart.BillingAddress,
            shippingAddress: existingCart.ShippingAddress,
            paymentMethod: existingCart.PaymentMethod,
            shippingMethod: existingCart.ShippingMethod,
            items: existingCart._items.ToList());
    }

    // Helper method to calculate totals
    private static CartTotals CalculateTotals(List<CartItem> items, CartShippingMethod? shippingMethod)
    {
        var subtotal = items.Sum(i => i.Qty * i.Price);
        var shippingAmount = shippingMethod?.Amount ?? 0;

        // Simple calculation - in real world would include tax calculations
        return new CartTotals
        {
            Subtotal = subtotal,
            BaseSubtotal = subtotal,
            GrandTotal = subtotal + shippingAmount,
            TaxAmount = 0, // Would be calculated based on tax rules
            BaseTaxAmount = 0
        };
    }
}

public class CartItem
{
    public string ItemId { get; }
    public string Sku { get; }
    public string Name { get; }
    public decimal Qty { get; }
    public decimal Price { get; }
    public string? ProductType { get; }
    public object? ProductOption { get; }

    public CartItem(string itemId, string sku, string name, decimal qty, decimal price, string? productType = null, object? productOption = null)
    {
        if (string.IsNullOrWhiteSpace(itemId))
            throw new ArgumentException("Item ID cannot be empty", nameof(itemId));
        if (string.IsNullOrWhiteSpace(sku))
            throw new ArgumentException("SKU cannot be empty", nameof(sku));
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Name cannot be empty", nameof(name));
        if (qty <= 0)
            throw new ArgumentException("Quantity must be greater than zero", nameof(qty));
        if (price < 0)
            throw new ArgumentException("Price cannot be negative", nameof(price));

        ItemId = itemId;
        Sku = sku;
        Name = name;
        Qty = qty;
        Price = price;
        ProductType = productType;
        ProductOption = productOption;
    }
}

public record CartCurrency(string BaseCurrencyCode, string QuoteCurrencyCode);

public record CartTotals
{
    public decimal GrandTotal { get; init; }
    public decimal BaseTaxAmount { get; init; }
    public decimal TaxAmount { get; init; }
    public decimal BaseSubtotal { get; init; }
    public decimal Subtotal { get; init; }
}

public record CartCustomer(
    string? Email,
    int? GroupId,
    bool IsGuest,
    string? FirstName,
    string? LastName);

public record CartAddress(
    string? Id,
    string? Region,
    string Country,
    List<string> Street,
    string City,
    string Postcode,
    string Firstname,
    string Lastname,
    string? Telephone);

public record CartPaymentMethod(string Method, string? PoNumber);

public record CartShippingMethod(
    string CarrierCode,
    string MethodCode,
    string MethodTitle,
    decimal Amount);