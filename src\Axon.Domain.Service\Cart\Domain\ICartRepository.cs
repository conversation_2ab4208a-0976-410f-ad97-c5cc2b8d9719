namespace Axon.Domain.Service.Cart.Domain;

public interface ICartRepository
{
    Task<Cart?> GetByIdAsync(string cartId, CancellationToken cancellationToken = default);
    Task<Cart?> GetByIdAndVersionAsync(string cartId, int version, CancellationToken cancellationToken = default);
    Task<List<Cart>> GetAllVersionsAsync(string cartId, CancellationToken cancellationToken = default);
    Task<Cart> AddAsync(Cart cart, CancellationToken cancellationToken = default);
    Task<Cart> SaveNewVersionAsync(Cart cart, CancellationToken cancellationToken = default);
    Task<bool> ExistsAsync(string cartId, CancellationToken cancellationToken = default);
    Task<bool> ExistsAsync(string cartId, int version, CancellationToken cancellationToken = default);
    
    [Obsolete("UpdateAsync is not supported for immutable carts. Use SaveNewVersionAsync to save a new version.")]
    Task<Cart> UpdateAsync(Cart cart, CancellationToken cancellationToken = default);
}