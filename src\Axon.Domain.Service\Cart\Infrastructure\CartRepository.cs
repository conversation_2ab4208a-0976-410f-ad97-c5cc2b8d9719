using Axon.Domain.Service.Cart.Domain;
using Axon.Domain.Service.Cart.Domain.Exceptions;
using Axon.Domain.Service.Cart.Infrastructure.Persistence;
using Axon.Domain.Service.Shared.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;

namespace Axon.Domain.Service.Cart.Infrastructure;

public class CartRepository : EfRepositoryBase<Domain.Cart, string, CartDbContext>, ICartRepository
{
    private readonly ILogger<CartRepository> _logger;

    public CartRepository(CartDbContext context, ILogger<CartRepository> logger) 
        : base(context)
    {
        _logger = logger;
    }

    public override async Task<Domain.Cart?> GetByIdAsync(string cartId, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Fetching latest version of cart {CartId}", cartId);
        
        // First, get the latest version number (lightweight query)
        var latestVersion = await Context.Carts
            .Where(c => c.CartId == cartId)
            .MaxAsync(c => (int?)c.Version, cancellationToken);
            
        if (latestVersion == null)
        {
            _logger.LogInformation("Cart {CartId} not found", cartId);
            return null;
        }
        
        // Then fetch the specific version with all related data
        // Using AsSplitQuery to avoid cartesian product with multiple includes
        var cart = await Context.Carts
            .AsSplitQuery()
            .Include(c => c.Items)
            .Include(c => c.BillingAddress)
            .Include(c => c.ShippingAddress)
            .FirstOrDefaultAsync(c => c.CartId == cartId && c.Version == latestVersion.Value, cancellationToken);

        if (cart != null)
        {
            _logger.LogInformation("Fetched cart {CartId} version {Version}", cartId, cart.Version);
        }

        return cart;
    }

    public async Task<Domain.Cart?> GetByIdAndVersionAsync(string cartId, int version, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Fetching cart {CartId} version {Version}", cartId, version);
        
        var cart = await Context.Carts
            .AsSplitQuery()
            .Include(c => c.Items)
            .Include(c => c.BillingAddress)
            .Include(c => c.ShippingAddress)
            .FirstOrDefaultAsync(c => c.CartId == cartId && c.Version == version, cancellationToken);

        if (cart != null)
        {
            _logger.LogInformation("Fetched cart {CartId} version {Version}", cartId, version);
        }
        else
        {
            _logger.LogInformation("Cart {CartId} version {Version} not found", cartId, version);
        }

        return cart;
    }

    public async Task<List<Domain.Cart>> GetAllVersionsAsync(string cartId, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Fetching all versions of cart {CartId}", cartId);
        
        var carts = await Context.Carts
            .AsSplitQuery()
            .Include(c => c.Items)
            .Include(c => c.BillingAddress)
            .Include(c => c.ShippingAddress)
            .Where(c => c.CartId == cartId)
            .OrderBy(c => c.Version)
            .ToListAsync(cancellationToken);

        _logger.LogInformation("Fetched {Count} versions of cart {CartId}", carts.Count, cartId);
        
        return carts;
    }

    public new async Task<Domain.Cart> AddAsync(Domain.Cart cart, CancellationToken cancellationToken = default)
    {
        // Check if this cart ID already exists (any version)
        var latestVersion = await Context.Carts
            .Where(c => c.CartId == cart.CartId)
            .OrderByDescending(c => c.Version)
            .Select(c => c.Version)
            .FirstOrDefaultAsync(cancellationToken);

        if (latestVersion > 0 && cart.Version == 1)
        {
            _logger.LogWarning("Attempted to add cart that already exists: {CartId}", cart.CartId);
            throw new DuplicateCartException(cart.CartId);
        }

        _logger.LogInformation("Adding cart {CartId} version {Version} to repository", cart.CartId, cart.Version);
        
        await base.AddAsync(cart, cancellationToken);
        await SaveChangesAsync(cancellationToken);
        
        _logger.LogInformation("Cart added to repository: {CartId} version {Version}", cart.CartId, cart.Version);
        return cart;
    }

    public async Task<Domain.Cart> SaveNewVersionAsync(Domain.Cart cart, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Saving new version {Version} of cart {CartId}", cart.Version, cart.CartId);
        
        // Verify this is indeed a new version
        var exists = await Context.Carts
            .AnyAsync(c => c.CartId == cart.CartId && c.Version == cart.Version, cancellationToken);
            
        if (exists)
        {
            throw new InvalidOperationException($"Cart {cart.CartId} version {cart.Version} already exists");
        }

        // Ensure change tracker is clear to avoid conflicts with owned entities
        Context.ChangeTracker.Clear();
        
        await base.AddAsync(cart, cancellationToken);
        await SaveChangesAsync(cancellationToken);
        
        _logger.LogInformation("Saved cart {CartId} version {Version}", cart.CartId, cart.Version);
        return cart;
    }

    [Obsolete("UpdateAsync is not supported for immutable carts. Use SaveNewVersionAsync to save a new version.")]
    public Task<Domain.Cart> UpdateAsync(Domain.Cart cart, CancellationToken cancellationToken = default)
    {
        throw new NotSupportedException("Cart entities are immutable. Use SaveNewVersionAsync to save a new version of the cart.");
    }

    public override async Task<bool> ExistsAsync(string cartId, CancellationToken cancellationToken = default)
    {
        return await Context.Carts.AnyAsync(c => c.CartId == cartId, cancellationToken);
    }

    public async Task<bool> ExistsAsync(string cartId, int version, CancellationToken cancellationToken = default)
    {
        return await Context.Carts.AnyAsync(c => c.CartId == cartId && c.Version == version, cancellationToken);
    }
}