using Axon.Domain.Service.Cart.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace Axon.Domain.Service.Cart.Infrastructure.HealthChecks;

public class CartDatabaseHealthCheck : IHealthCheck
{
    private readonly CartDbContext _context;
    private readonly ILogger<CartDatabaseHealthCheck> _logger;

    public CartDatabaseHealthCheck(CartDbContext context, ILogger<CartDatabaseHealthCheck> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        try
        {
            // Check if we can connect to the database
            var canConnect = await _context.Database.CanConnectAsync(cancellationToken);
            
            if (!canConnect)
            {
                _logger.LogWarning("Cart database health check failed: Cannot connect to database");
                return HealthCheckResult.Unhealthy("Cannot connect to Cart database");
            }

            // Perform a simple query to verify the schema is accessible
            var cartCount = await _context.Carts
                .Where(c => c.Version == 1)
                .Take(1)
                .CountAsync(cancellationToken);

            _logger.LogInformation("Cart database health check passed");
            
            return HealthCheckResult.Healthy("Cart database is accessible", new Dictionary<string, object>
            {
                ["database"] = "Cart",
                ["schema"] = "cart",
                ["status"] = "Connected"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Cart database health check failed with exception");
            
            return HealthCheckResult.Unhealthy(
                "Cart database health check failed",
                ex,
                new Dictionary<string, object>
                {
                    ["database"] = "Cart",
                    ["error"] = ex.Message
                });
        }
    }
}