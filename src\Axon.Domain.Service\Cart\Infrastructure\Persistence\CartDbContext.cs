using Microsoft.EntityFrameworkCore;

namespace Axon.Domain.Service.Cart.Infrastructure.Persistence;

public class CartDbContext : DbContext
{
    public CartDbContext(DbContextOptions<CartDbContext> options) : base(options)
    {
    }

    public DbSet<Domain.Cart> Carts { get; set; } = null!;

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.HasDefaultSchema("cart");
        
        // Apply all configurations from the current assembly
        modelBuilder.ApplyConfigurationsFromAssembly(typeof(CartDbContext).Assembly,
            t => t.Namespace != null && t.Namespace.Contains("Cart.Infrastructure.Persistence.Configurations"));

        base.OnModelCreating(modelBuilder);
    }
}