using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System.Text.Json;

namespace Axon.Domain.Service.Cart.Infrastructure.Persistence.Configurations;

public class CartConfiguration : IEntityTypeConfiguration<Domain.Cart>
{
    public void Configure(EntityTypeBuilder<Domain.Cart> builder)
    {
        builder.ToTable("carts");

        // Composite key on CartId and Version for versioning
        builder.HasKey(c => new { c.CartId, c.Version });
        
        builder.Property(c => c.CartId)
            .HasMaxLength(100);
        
        builder.Property(c => c.Version)
            .IsRequired();

        builder.Property(c => c.CustomerId)
            .HasMaxLength(100);
        builder.HasIndex(c => c.CustomerId);

        builder.Property(c => c.StoreId)
            .IsRequired()
            .HasMaxLength(50);
        builder.HasIndex(c => c.StoreId);

        builder.Property(c => c.IsActive);
        builder.HasIndex(c => c.IsActive);

        builder.Property(c => c.IsVirtual);
        builder.Property(c => c.IsNegotiableQuote);
        builder.Property(c => c.IsMultiShipping);

        builder.Property(c => c.CreatedAt);
        builder.HasIndex(c => c.CreatedAt);

        builder.Property(c => c.UpdatedAt);
        builder.HasIndex(c => c.UpdatedAt);

        // Configure owned entities
        builder.OwnsOne(c => c.Currency, currencyBuilder =>
        {
            currencyBuilder.Property(cur => cur.BaseCurrencyCode)
                .HasMaxLength(10)
                .HasColumnName("BaseCurrencyCode");
            currencyBuilder.Property(cur => cur.QuoteCurrencyCode)
                .HasMaxLength(10)
                .HasColumnName("QuoteCurrencyCode");
        });

        builder.OwnsOne(c => c.Totals, totalsBuilder =>
        {
            totalsBuilder.Property(t => t.GrandTotal)
                .HasPrecision(19, 4)
                .HasColumnName("GrandTotal");
            totalsBuilder.Property(t => t.BaseTaxAmount)
                .HasPrecision(19, 4)
                .HasColumnName("BaseTaxAmount");
            totalsBuilder.Property(t => t.TaxAmount)
                .HasPrecision(19, 4)
                .HasColumnName("TaxAmount");
            totalsBuilder.Property(t => t.BaseSubtotal)
                .HasPrecision(19, 4)
                .HasColumnName("BaseSubtotal");
            totalsBuilder.Property(t => t.Subtotal)
                .HasPrecision(19, 4)
                .HasColumnName("Subtotal");
        });

        builder.OwnsOne(c => c.Customer, customerBuilder =>
        {
            customerBuilder.Property(cust => cust.Email)
                .HasMaxLength(255)
                .HasColumnName("CustomerEmail");
            customerBuilder.Property(cust => cust.GroupId)
                .HasColumnName("CustomerGroupId");
            customerBuilder.Property(cust => cust.IsGuest)
                .HasColumnName("CustomerIsGuest");
            customerBuilder.Property(cust => cust.FirstName)
                .HasMaxLength(100)
                .HasColumnName("CustomerFirstName");
            customerBuilder.Property(cust => cust.LastName)
                .HasMaxLength(100)
                .HasColumnName("CustomerLastName");
        });

        builder.OwnsOne(c => c.BillingAddress, addressBuilder =>
        {
            addressBuilder.ToTable("cart_billing_addresses");
            addressBuilder.WithOwner().HasForeignKey("CartId", "Version");
            
            addressBuilder.Property(a => a.Id).HasMaxLength(50);
            addressBuilder.Property(a => a.Firstname).HasMaxLength(100);
            addressBuilder.Property(a => a.Lastname).HasMaxLength(100);
            addressBuilder.Property(a => a.City).HasMaxLength(100);
            addressBuilder.Property(a => a.Country).HasMaxLength(100);
            addressBuilder.Property(a => a.Telephone).HasMaxLength(50);
            addressBuilder.Property(a => a.Region).HasMaxLength(100);
            addressBuilder.Property(a => a.Postcode).HasMaxLength(20);
            
            addressBuilder.Property(a => a.Street)
                .HasConversion(
                    v => string.Join(';', v),
                    v => v.Split(';', StringSplitOptions.RemoveEmptyEntries).ToList())
                .HasMaxLength(500)
                .Metadata.SetValueComparer(new ValueComparer<List<string>>(
                    (c1, c2) => c1 != null && c2 != null && c1.SequenceEqual(c2),
                    c => c != null ? c.Aggregate(0, (a, v) => HashCode.Combine(a, v.GetHashCode())) : 0,
                    c => c != null ? c.ToList() : new List<string>()));
        });

        builder.OwnsOne(c => c.ShippingAddress, addressBuilder =>
        {
            addressBuilder.ToTable("cart_shipping_addresses");
            addressBuilder.WithOwner().HasForeignKey("CartId", "Version");
            
            addressBuilder.Property(a => a.Id).HasMaxLength(50);
            addressBuilder.Property(a => a.Firstname).HasMaxLength(100);
            addressBuilder.Property(a => a.Lastname).HasMaxLength(100);
            addressBuilder.Property(a => a.City).HasMaxLength(100);
            addressBuilder.Property(a => a.Country).HasMaxLength(100);
            addressBuilder.Property(a => a.Telephone).HasMaxLength(50);
            addressBuilder.Property(a => a.Region).HasMaxLength(100);
            addressBuilder.Property(a => a.Postcode).HasMaxLength(20);
            
            addressBuilder.Property(a => a.Street)
                .HasConversion(
                    v => string.Join(';', v),
                    v => v.Split(';', StringSplitOptions.RemoveEmptyEntries).ToList())
                .HasMaxLength(500)
                .Metadata.SetValueComparer(new ValueComparer<List<string>>(
                    (c1, c2) => c1 != null && c2 != null && c1.SequenceEqual(c2),
                    c => c != null ? c.Aggregate(0, (a, v) => HashCode.Combine(a, v.GetHashCode())) : 0,
                    c => c != null ? c.ToList() : new List<string>()));
        });

        builder.OwnsOne(c => c.PaymentMethod, paymentBuilder =>
        {
            paymentBuilder.Property(p => p.Method)
                .HasMaxLength(100)
                .HasColumnName("PaymentMethod");
            paymentBuilder.Property(p => p.PoNumber)
                .HasMaxLength(100)
                .HasColumnName("PaymentPoNumber");
        });

        builder.OwnsOne(c => c.ShippingMethod, shippingBuilder =>
        {
            shippingBuilder.Property(s => s.CarrierCode)
                .HasMaxLength(100)
                .HasColumnName("ShippingCarrierCode");
            shippingBuilder.Property(s => s.MethodCode)
                .HasMaxLength(100)
                .HasColumnName("ShippingMethodCode");
            shippingBuilder.Property(s => s.MethodTitle)
                .HasMaxLength(255)
                .HasColumnName("ShippingMethodTitle");
            shippingBuilder.Property(s => s.Amount)
                .HasPrecision(19, 4)
                .HasColumnName("ShippingAmount");
        });

        // Configure collection using backing field
        builder.OwnsMany(c => c.Items, itemBuilder =>
        {
            itemBuilder.ToTable("cart_items");
            itemBuilder.WithOwner().HasForeignKey("CartId", "Version");
            
            itemBuilder.Property(i => i.ItemId)
                .IsRequired()
                .HasMaxLength(50);
            itemBuilder.HasIndex(i => i.ItemId);
            
            itemBuilder.Property(i => i.Sku)
                .IsRequired()
                .HasMaxLength(100);
            itemBuilder.HasIndex(i => i.Sku);
            
            // Covering index for SKU lookups within cart context
            itemBuilder.HasIndex("Sku", "CartId", "Version")
                .HasDatabaseName("IX_CartItems_Sku_CartId_Version")
                .IncludeProperties(i => new { i.ItemId, i.Qty, i.Price });
            
            itemBuilder.Property(i => i.Name)
                .IsRequired()
                .HasMaxLength(255);
            
            itemBuilder.Property(i => i.ProductType)
                .HasMaxLength(100);
            
            itemBuilder.Property(i => i.Qty).HasPrecision(12, 4);
            itemBuilder.Property(i => i.Price).HasPrecision(19, 4);
            
            // Store ProductOption as JSON
            itemBuilder.Property(i => i.ProductOption)
                .HasConversion(
                    v => v == null ? null : JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                    v => v == null ? null : JsonSerializer.Deserialize<object>(v, (JsonSerializerOptions?)null))
                .HasColumnType("jsonb");
        });

        // Configure backing field for items collection
        builder.Navigation(c => c.Items).UsePropertyAccessMode(PropertyAccessMode.Field);

        // Composite indexes
        builder.HasIndex(c => new { c.StoreId, c.IsActive })
            .HasDatabaseName("IX_Carts_StoreId_IsActive");
        
        // Index to efficiently find latest version of a cart
        builder.HasIndex(c => new { c.CartId, c.Version })
            .HasDatabaseName("IX_Carts_CartId_Version")
            .IsDescending(false, true); // Version descending for latest version queries
            
        // Additional index on CartId alone for MAX(Version) queries
        builder.HasIndex(c => c.CartId)
            .HasDatabaseName("IX_Carts_CartId")
            .IncludeProperties(c => c.Version); // Include Version as covered column
            
        // Covering index for active carts by customer
        builder.HasIndex(c => new { c.CustomerId, c.IsActive })
            .HasDatabaseName("IX_Carts_CustomerId_IsActive")
            .IncludeProperties(c => new { c.Version, c.CreatedAt, c.UpdatedAt });
            
        // Covering index for recent carts by store
        builder.HasIndex(c => new { c.StoreId, c.CreatedAt })
            .HasDatabaseName("IX_Carts_StoreId_CreatedAt_Desc")
            .IsDescending(false, true)
            .IncludeProperties(c => new { c.CartId, c.Version, c.IsActive });
            
        // Covering index specifically for latest version queries
        builder.HasIndex(c => new { c.CartId, c.Version })
            .HasDatabaseName("IX_Carts_CartId_LatestVersion_Covering")
            .IsDescending(false, true) // Version descending for latest version queries
            .IncludeProperties(c => new { c.CustomerId, c.IsActive, c.CreatedAt, c.UpdatedAt });
    }
}