using Axon.Domain.Service.Cart.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;

namespace Axon.Domain.Service.Cart.Infrastructure.Persistence.DesignTime;

/// <summary>
/// Design-time factory for CartDbContext to support EF Core tools and migrations
/// </summary>
public class CartDbContextFactory : IDesignTimeDbContextFactory<CartDbContext>
{
    public CartDbContext CreateDbContext(string[] args)
    {
        // Build configuration
        var configuration = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json", optional: false)
            .AddJsonFile("appsettings.Development.json", optional: true)
            .AddEnvironmentVariables()
            .Build();

        // Create options
        var optionsBuilder = new DbContextOptionsBuilder<CartDbContext>();
        var connectionString = configuration.GetConnectionString("CartDb");
        
        optionsBuilder.UseNpgsql(connectionString);

        return new CartDbContext(optionsBuilder.Options);
    }
}