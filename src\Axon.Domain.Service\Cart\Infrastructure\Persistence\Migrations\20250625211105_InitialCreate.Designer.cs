﻿// <auto-generated />
using System;
using Axon.Domain.Service.Cart.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Axon.Domain.Service.Cart.Infrastructure.Persistence.Migrations
{
    [DbContext(typeof(CartDbContext))]
    [Migration("20250625211105_InitialCreate")]
    partial class InitialCreate
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasDefaultSchema("cart")
                .HasAnnotation("ProductVersion", "9.0.1")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("Axon.Domain.Service.Cart.Domain.Cart", b =>
                {
                    b.Property<string>("CartId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("Version")
                        .HasColumnType("integer");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CustomerId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsMultiShipping")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsNegotiableQuote")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsVirtual")
                        .HasColumnType("boolean");

                    b.Property<string>("StoreId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("CartId", "Version");

                    b.HasIndex("CartId")
                        .HasDatabaseName("IX_Carts_CartId");

                    NpgsqlIndexBuilderExtensions.IncludeProperties(b.HasIndex("CartId"), new[] { "Version" });

                    b.HasIndex("CreatedAt");

                    b.HasIndex("CustomerId");

                    b.HasIndex("IsActive");

                    b.HasIndex("StoreId");

                    b.HasIndex("UpdatedAt");

                    b.HasIndex("CartId", "Version")
                        .IsDescending(false, true)
                        .HasDatabaseName("IX_Carts_CartId_LatestVersion_Covering");

                    NpgsqlIndexBuilderExtensions.IncludeProperties(b.HasIndex("CartId", "Version"), new[] { "CustomerId", "IsActive", "CreatedAt", "UpdatedAt" });

                    b.HasIndex("CustomerId", "IsActive")
                        .HasDatabaseName("IX_Carts_CustomerId_IsActive");

                    NpgsqlIndexBuilderExtensions.IncludeProperties(b.HasIndex("CustomerId", "IsActive"), new[] { "Version", "CreatedAt", "UpdatedAt" });

                    b.HasIndex("StoreId", "CreatedAt")
                        .IsDescending(false, true)
                        .HasDatabaseName("IX_Carts_StoreId_CreatedAt_Desc");

                    NpgsqlIndexBuilderExtensions.IncludeProperties(b.HasIndex("StoreId", "CreatedAt"), new[] { "CartId", "Version", "IsActive" });

                    b.HasIndex("StoreId", "IsActive")
                        .HasDatabaseName("IX_Carts_StoreId_IsActive");

                    b.ToTable("carts", "cart");
                });

            modelBuilder.Entity("Axon.Domain.Service.Cart.Domain.Cart", b =>
                {
                    b.OwnsOne("Axon.Domain.Service.Cart.Domain.CartCurrency", "Currency", b1 =>
                        {
                            b1.Property<string>("CartId")
                                .HasColumnType("character varying(100)");

                            b1.Property<int>("CartVersion")
                                .HasColumnType("integer");

                            b1.Property<string>("BaseCurrencyCode")
                                .IsRequired()
                                .HasMaxLength(10)
                                .HasColumnType("character varying(10)")
                                .HasColumnName("BaseCurrencyCode");

                            b1.Property<string>("QuoteCurrencyCode")
                                .IsRequired()
                                .HasMaxLength(10)
                                .HasColumnType("character varying(10)")
                                .HasColumnName("QuoteCurrencyCode");

                            b1.HasKey("CartId", "CartVersion");

                            b1.ToTable("carts", "cart");

                            b1.WithOwner()
                                .HasForeignKey("CartId", "CartVersion");
                        });

                    b.OwnsOne("Axon.Domain.Service.Cart.Domain.CartCustomer", "Customer", b1 =>
                        {
                            b1.Property<string>("CartId")
                                .HasColumnType("character varying(100)");

                            b1.Property<int>("CartVersion")
                                .HasColumnType("integer");

                            b1.Property<string>("Email")
                                .HasMaxLength(255)
                                .HasColumnType("character varying(255)")
                                .HasColumnName("CustomerEmail");

                            b1.Property<string>("FirstName")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("CustomerFirstName");

                            b1.Property<int?>("GroupId")
                                .HasColumnType("integer")
                                .HasColumnName("CustomerGroupId");

                            b1.Property<bool>("IsGuest")
                                .HasColumnType("boolean")
                                .HasColumnName("CustomerIsGuest");

                            b1.Property<string>("LastName")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("CustomerLastName");

                            b1.HasKey("CartId", "CartVersion");

                            b1.ToTable("carts", "cart");

                            b1.WithOwner()
                                .HasForeignKey("CartId", "CartVersion");
                        });

                    b.OwnsOne("Axon.Domain.Service.Cart.Domain.CartPaymentMethod", "PaymentMethod", b1 =>
                        {
                            b1.Property<string>("CartId")
                                .HasColumnType("character varying(100)");

                            b1.Property<int>("CartVersion")
                                .HasColumnType("integer");

                            b1.Property<string>("Method")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("PaymentMethod");

                            b1.Property<string>("PoNumber")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("PaymentPoNumber");

                            b1.HasKey("CartId", "CartVersion");

                            b1.ToTable("carts", "cart");

                            b1.WithOwner()
                                .HasForeignKey("CartId", "CartVersion");
                        });

                    b.OwnsOne("Axon.Domain.Service.Cart.Domain.CartShippingMethod", "ShippingMethod", b1 =>
                        {
                            b1.Property<string>("CartId")
                                .HasColumnType("character varying(100)");

                            b1.Property<int>("CartVersion")
                                .HasColumnType("integer");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(19, 4)
                                .HasColumnType("numeric(19,4)")
                                .HasColumnName("ShippingAmount");

                            b1.Property<string>("CarrierCode")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("ShippingCarrierCode");

                            b1.Property<string>("MethodCode")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("ShippingMethodCode");

                            b1.Property<string>("MethodTitle")
                                .IsRequired()
                                .HasMaxLength(255)
                                .HasColumnType("character varying(255)")
                                .HasColumnName("ShippingMethodTitle");

                            b1.HasKey("CartId", "CartVersion");

                            b1.ToTable("carts", "cart");

                            b1.WithOwner()
                                .HasForeignKey("CartId", "CartVersion");
                        });

                    b.OwnsOne("Axon.Domain.Service.Cart.Domain.CartTotals", "Totals", b1 =>
                        {
                            b1.Property<string>("CartId")
                                .HasColumnType("character varying(100)");

                            b1.Property<int>("CartVersion")
                                .HasColumnType("integer");

                            b1.Property<decimal>("BaseSubtotal")
                                .HasPrecision(19, 4)
                                .HasColumnType("numeric(19,4)")
                                .HasColumnName("BaseSubtotal");

                            b1.Property<decimal>("BaseTaxAmount")
                                .HasPrecision(19, 4)
                                .HasColumnType("numeric(19,4)")
                                .HasColumnName("BaseTaxAmount");

                            b1.Property<decimal>("GrandTotal")
                                .HasPrecision(19, 4)
                                .HasColumnType("numeric(19,4)")
                                .HasColumnName("GrandTotal");

                            b1.Property<decimal>("Subtotal")
                                .HasPrecision(19, 4)
                                .HasColumnType("numeric(19,4)")
                                .HasColumnName("Subtotal");

                            b1.Property<decimal>("TaxAmount")
                                .HasPrecision(19, 4)
                                .HasColumnType("numeric(19,4)")
                                .HasColumnName("TaxAmount");

                            b1.HasKey("CartId", "CartVersion");

                            b1.ToTable("carts", "cart");

                            b1.WithOwner()
                                .HasForeignKey("CartId", "CartVersion");
                        });

                    b.OwnsOne("Axon.Domain.Service.Cart.Domain.CartAddress", "BillingAddress", b1 =>
                        {
                            b1.Property<string>("CartId")
                                .HasColumnType("character varying(100)");

                            b1.Property<int>("Version")
                                .HasColumnType("integer");

                            b1.Property<string>("City")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.Property<string>("Country")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.Property<string>("Firstname")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.Property<string>("Id")
                                .HasMaxLength(50)
                                .HasColumnType("character varying(50)");

                            b1.Property<string>("Lastname")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.Property<string>("Postcode")
                                .IsRequired()
                                .HasMaxLength(20)
                                .HasColumnType("character varying(20)");

                            b1.Property<string>("Region")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.Property<string>("Street")
                                .IsRequired()
                                .HasMaxLength(500)
                                .HasColumnType("character varying(500)");

                            b1.Property<string>("Telephone")
                                .HasMaxLength(50)
                                .HasColumnType("character varying(50)");

                            b1.HasKey("CartId", "Version");

                            b1.ToTable("cart_billing_addresses", "cart");

                            b1.WithOwner()
                                .HasForeignKey("CartId", "Version");
                        });

                    b.OwnsOne("Axon.Domain.Service.Cart.Domain.CartAddress", "ShippingAddress", b1 =>
                        {
                            b1.Property<string>("CartId")
                                .HasColumnType("character varying(100)");

                            b1.Property<int>("Version")
                                .HasColumnType("integer");

                            b1.Property<string>("City")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.Property<string>("Country")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.Property<string>("Firstname")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.Property<string>("Id")
                                .HasMaxLength(50)
                                .HasColumnType("character varying(50)");

                            b1.Property<string>("Lastname")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.Property<string>("Postcode")
                                .IsRequired()
                                .HasMaxLength(20)
                                .HasColumnType("character varying(20)");

                            b1.Property<string>("Region")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.Property<string>("Street")
                                .IsRequired()
                                .HasMaxLength(500)
                                .HasColumnType("character varying(500)");

                            b1.Property<string>("Telephone")
                                .HasMaxLength(50)
                                .HasColumnType("character varying(50)");

                            b1.HasKey("CartId", "Version");

                            b1.ToTable("cart_shipping_addresses", "cart");

                            b1.WithOwner()
                                .HasForeignKey("CartId", "Version");
                        });

                    b.OwnsMany("Axon.Domain.Service.Cart.Domain.CartItem", "Items", b1 =>
                        {
                            b1.Property<string>("CartId")
                                .HasColumnType("character varying(100)");

                            b1.Property<int>("Version")
                                .HasColumnType("integer");

                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer");

                            NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b1.Property<int>("Id"));

                            b1.Property<string>("ItemId")
                                .IsRequired()
                                .HasMaxLength(50)
                                .HasColumnType("character varying(50)");

                            b1.Property<string>("Name")
                                .IsRequired()
                                .HasMaxLength(255)
                                .HasColumnType("character varying(255)");

                            b1.Property<decimal>("Price")
                                .HasPrecision(19, 4)
                                .HasColumnType("numeric(19,4)");

                            b1.Property<string>("ProductOption")
                                .HasColumnType("jsonb");

                            b1.Property<string>("ProductType")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.Property<decimal>("Qty")
                                .HasPrecision(12, 4)
                                .HasColumnType("numeric(12,4)");

                            b1.Property<string>("Sku")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.HasKey("CartId", "Version", "Id");

                            b1.HasIndex("ItemId");

                            b1.HasIndex("Sku");

                            b1.HasIndex("Sku", "CartId", "Version")
                                .HasDatabaseName("IX_CartItems_Sku_CartId_Version");

                            NpgsqlIndexBuilderExtensions.IncludeProperties(b1.HasIndex("Sku", "CartId", "Version"), new[] { "ItemId", "Qty", "Price" });

                            b1.ToTable("cart_items", "cart");

                            b1.WithOwner()
                                .HasForeignKey("CartId", "Version");
                        });

                    b.Navigation("BillingAddress");

                    b.Navigation("Currency")
                        .IsRequired();

                    b.Navigation("Customer")
                        .IsRequired();

                    b.Navigation("Items");

                    b.Navigation("PaymentMethod");

                    b.Navigation("ShippingAddress");

                    b.Navigation("ShippingMethod");

                    b.Navigation("Totals")
                        .IsRequired();
                });
#pragma warning restore 612, 618
        }
    }
}
