﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Axon.Domain.Service.Cart.Infrastructure.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class InitialCreate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.EnsureSchema(
                name: "cart");

            migrationBuilder.CreateTable(
                name: "carts",
                schema: "cart",
                columns: table => new
                {
                    CartId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Version = table.Column<int>(type: "integer", nullable: false),
                    CustomerId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    StoreId = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    CreatedAt = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    BaseCurrencyCode = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false),
                    QuoteCurrencyCode = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false),
                    GrandTotal = table.Column<decimal>(type: "numeric(19,4)", precision: 19, scale: 4, nullable: false),
                    BaseTaxAmount = table.Column<decimal>(type: "numeric(19,4)", precision: 19, scale: 4, nullable: false),
                    TaxAmount = table.Column<decimal>(type: "numeric(19,4)", precision: 19, scale: 4, nullable: false),
                    BaseSubtotal = table.Column<decimal>(type: "numeric(19,4)", precision: 19, scale: 4, nullable: false),
                    Subtotal = table.Column<decimal>(type: "numeric(19,4)", precision: 19, scale: 4, nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    IsVirtual = table.Column<bool>(type: "boolean", nullable: false),
                    IsNegotiableQuote = table.Column<bool>(type: "boolean", nullable: false),
                    IsMultiShipping = table.Column<bool>(type: "boolean", nullable: false),
                    CustomerEmail = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    CustomerGroupId = table.Column<int>(type: "integer", nullable: true),
                    CustomerIsGuest = table.Column<bool>(type: "boolean", nullable: false),
                    CustomerFirstName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    CustomerLastName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    PaymentMethod = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    PaymentPoNumber = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    ShippingCarrierCode = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    ShippingMethodCode = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    ShippingMethodTitle = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    ShippingAmount = table.Column<decimal>(type: "numeric(19,4)", precision: 19, scale: 4, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_carts", x => new { x.CartId, x.Version });
                });

            migrationBuilder.CreateTable(
                name: "cart_billing_addresses",
                schema: "cart",
                columns: table => new
                {
                    CartId = table.Column<string>(type: "character varying(100)", nullable: false),
                    Version = table.Column<int>(type: "integer", nullable: false),
                    Id = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    Region = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    Country = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Street = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    City = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Postcode = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    Firstname = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Lastname = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Telephone = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_cart_billing_addresses", x => new { x.CartId, x.Version });
                    table.ForeignKey(
                        name: "FK_cart_billing_addresses_carts_CartId_Version",
                        columns: x => new { x.CartId, x.Version },
                        principalSchema: "cart",
                        principalTable: "carts",
                        principalColumns: new[] { "CartId", "Version" },
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "cart_items",
                schema: "cart",
                columns: table => new
                {
                    CartId = table.Column<string>(type: "character varying(100)", nullable: false),
                    Version = table.Column<int>(type: "integer", nullable: false),
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    ItemId = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Sku = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Name = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    Qty = table.Column<decimal>(type: "numeric(12,4)", precision: 12, scale: 4, nullable: false),
                    Price = table.Column<decimal>(type: "numeric(19,4)", precision: 19, scale: 4, nullable: false),
                    ProductType = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    ProductOption = table.Column<string>(type: "jsonb", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_cart_items", x => new { x.CartId, x.Version, x.Id });
                    table.ForeignKey(
                        name: "FK_cart_items_carts_CartId_Version",
                        columns: x => new { x.CartId, x.Version },
                        principalSchema: "cart",
                        principalTable: "carts",
                        principalColumns: new[] { "CartId", "Version" },
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "cart_shipping_addresses",
                schema: "cart",
                columns: table => new
                {
                    CartId = table.Column<string>(type: "character varying(100)", nullable: false),
                    Version = table.Column<int>(type: "integer", nullable: false),
                    Id = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    Region = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    Country = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Street = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    City = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Postcode = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    Firstname = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Lastname = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Telephone = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_cart_shipping_addresses", x => new { x.CartId, x.Version });
                    table.ForeignKey(
                        name: "FK_cart_shipping_addresses_carts_CartId_Version",
                        columns: x => new { x.CartId, x.Version },
                        principalSchema: "cart",
                        principalTable: "carts",
                        principalColumns: new[] { "CartId", "Version" },
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_cart_items_ItemId",
                schema: "cart",
                table: "cart_items",
                column: "ItemId");

            migrationBuilder.CreateIndex(
                name: "IX_cart_items_Sku",
                schema: "cart",
                table: "cart_items",
                column: "Sku");

            migrationBuilder.CreateIndex(
                name: "IX_CartItems_Sku_CartId_Version",
                schema: "cart",
                table: "cart_items",
                columns: new[] { "Sku", "CartId", "Version" })
                .Annotation("Npgsql:IndexInclude", new[] { "ItemId", "Qty", "Price" });

            migrationBuilder.CreateIndex(
                name: "IX_Carts_CartId",
                schema: "cart",
                table: "carts",
                column: "CartId")
                .Annotation("Npgsql:IndexInclude", new[] { "Version" });

            migrationBuilder.CreateIndex(
                name: "IX_Carts_CartId_LatestVersion_Covering",
                schema: "cart",
                table: "carts",
                columns: new[] { "CartId", "Version" },
                descending: new[] { false, true })
                .Annotation("Npgsql:IndexInclude", new[] { "CustomerId", "IsActive", "CreatedAt", "UpdatedAt" });

            migrationBuilder.CreateIndex(
                name: "IX_carts_CreatedAt",
                schema: "cart",
                table: "carts",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_carts_CustomerId",
                schema: "cart",
                table: "carts",
                column: "CustomerId");

            migrationBuilder.CreateIndex(
                name: "IX_Carts_CustomerId_IsActive",
                schema: "cart",
                table: "carts",
                columns: new[] { "CustomerId", "IsActive" })
                .Annotation("Npgsql:IndexInclude", new[] { "Version", "CreatedAt", "UpdatedAt" });

            migrationBuilder.CreateIndex(
                name: "IX_carts_IsActive",
                schema: "cart",
                table: "carts",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_carts_StoreId",
                schema: "cart",
                table: "carts",
                column: "StoreId");

            migrationBuilder.CreateIndex(
                name: "IX_Carts_StoreId_CreatedAt_Desc",
                schema: "cart",
                table: "carts",
                columns: new[] { "StoreId", "CreatedAt" },
                descending: new[] { false, true })
                .Annotation("Npgsql:IndexInclude", new[] { "CartId", "Version", "IsActive" });

            migrationBuilder.CreateIndex(
                name: "IX_Carts_StoreId_IsActive",
                schema: "cart",
                table: "carts",
                columns: new[] { "StoreId", "IsActive" });

            migrationBuilder.CreateIndex(
                name: "IX_carts_UpdatedAt",
                schema: "cart",
                table: "carts",
                column: "UpdatedAt");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "cart_billing_addresses",
                schema: "cart");

            migrationBuilder.DropTable(
                name: "cart_items",
                schema: "cart");

            migrationBuilder.DropTable(
                name: "cart_shipping_addresses",
                schema: "cart");

            migrationBuilder.DropTable(
                name: "carts",
                schema: "cart");
        }
    }
}
