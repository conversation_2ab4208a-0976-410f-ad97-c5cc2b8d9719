# Use the official .NET 9.0 SDK image for build
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
ARG TARGETPLATFORM
ARG RUNTIME_ID=linux-x64

# Set RUNTIME_ID based on TARGETPLATFORM if not explicitly provided
RUN if [ "$TARGETPLATFORM" = "linux/arm64" ]; then \
        echo "linux-arm64" > /tmp/runtime_id; \
    elif [ "$TARGETPLATFORM" = "linux/amd64" ]; then \
        echo "linux-x64" > /tmp/runtime_id; \
    else \
        echo "$RUNTIME_ID" > /tmp/runtime_id; \
    fi

WORKDIR /app

# Copy csproj files first for better caching
COPY src/Axon.Domain.Service/*.csproj ./src/Axon.Domain.Service/
COPY src/Axon.Core/*.csproj ./src/Axon.Core/
COPY src/Axon.Core.MassTransit/*.csproj ./src/Axon.Core.MassTransit/
COPY src/Axon.Contracts/*.csproj ./src/Axon.Contracts/

# Restore dependencies - this layer will be cached unless csproj files change
RUN dotnet restore src/Axon.Domain.Service/Axon.Domain.Service.csproj

# Copy the rest of the source code
COPY . .

# Build and publish - using the correct runtime ID
RUN RUNTIME_ID=$(cat /tmp/runtime_id) && \
    echo "Building for runtime: $RUNTIME_ID" && \
    dotnet publish src/Axon.Domain.Service/Axon.Domain.Service.csproj -c Release -o out -r $RUNTIME_ID --self-contained false --no-restore

# Use the official .NET 9.0 ASP.NET runtime image for runtime
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS runtime
ARG TARGETPLATFORM
ARG RUNTIME_ID=linux-x64

# Set RUNTIME_ID based on TARGETPLATFORM if not explicitly provided
RUN if [ "$TARGETPLATFORM" = "linux/arm64" ]; then \
        echo "linux-arm64" > /tmp/runtime_id; \
    elif [ "$TARGETPLATFORM" = "linux/amd64" ]; then \
        echo "linux-x64" > /tmp/runtime_id; \
    else \
        echo "$RUNTIME_ID" > /tmp/runtime_id; \
    fi

WORKDIR /app
COPY --from=build /app/out .

# Verify architecture compatibility
RUN RUNTIME_ID=$(cat /tmp/runtime_id) && \
    echo "=== Architecture Verification ===" && \
    echo "=== Runtime ID: $RUNTIME_ID ===" && \
    echo "=== Target Platform: $TARGETPLATFORM ===" && \
    echo "=== Container Architecture ===" && \
    uname -m && \
    echo "=== Assembly Architecture ===" && \
    file /app/Axon.Domain.Service.dll 2>/dev/null || echo "Assembly not found"

EXPOSE 7502
ENTRYPOINT ["dotnet", "Axon.Domain.Service.dll"]