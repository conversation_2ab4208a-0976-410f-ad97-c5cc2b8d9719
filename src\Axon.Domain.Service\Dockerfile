# Use the official .NET 9.0 SDK image for build
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /app

# Copy csproj files first for better caching
COPY src/Axon.Domain.Service/*.csproj ./src/Axon.Domain.Service/
COPY src/Axon.Core/*.csproj ./src/Axon.Core/
COPY src/Axon.Core.MassTransit/*.csproj ./src/Axon.Core.MassTransit/
COPY src/Axon.Contracts/*.csproj ./src/Axon.Contracts/

# Restore dependencies - this layer will be cached unless csproj files change
RUN dotnet restore src/Axon.Domain.Service/Axon.Domain.Service.csproj

# Copy the rest of the source code
COPY . .

# Build and publish - using --no-restore to skip redundant restore
RUN dotnet publish src/Axon.Domain.Service/Axon.Domain.Service.csproj -c Release -o out --no-restore

# Use the official .NET 9.0 ASP.NET runtime image for runtime
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS runtime
WORKDIR /app
COPY --from=build /app/out .
EXPOSE 7502
ENTRYPOINT ["dotnet", "Axon.Domain.Service.dll"]