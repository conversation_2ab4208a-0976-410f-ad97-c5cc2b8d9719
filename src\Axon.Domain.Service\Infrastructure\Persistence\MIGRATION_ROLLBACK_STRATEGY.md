# Migration Rollback Strategy

## Overview

This document outlines the strategy for rolling back Entity Framework Core migrations in the Axon Domain Service. All migration rollback scripts are located in the `Infrastructure/Persistence/RollbackScripts` directory.

## Rollback Principles

1. **Data Preservation**: Where possible, rollback scripts attempt to preserve data. However, some rollbacks may result in data loss (e.g., reverting from versioned to non-versioned carts).

2. **Transaction Safety**: All rollback scripts use database transactions to ensure atomicity.

3. **Dependency Order**: Rollbacks must be executed in reverse chronological order to respect dependencies.

## Migration Inventory

### Cart Context Migrations

1. **InitialCreate**
   - Creates all cart tables with versioning support and covering indexes
   - Includes: carts (with composite key on CartId/Version), cart_items, cart_billing_addresses, cart_shipping_addresses
   - Rollback: `Rollback_InitialCreate_Cart.sql`
   - **WARNING**: Complete data loss for all carts

### Order Context Migrations

1. **InitialCreate**
   - Creates all order tables with covering indexes
   - Includes: orders, order_items, order_billing_addresses, order_shipping_addresses
   - Rollback: `Rollback_InitialCreate_Order.sql`
   - **WARNING**: Complete data loss for all orders

### Shared Context Migrations

1. **InitialCreate**
   - Creates shared schema
   - Rollback: `Rollback_InitialCreate_Shared.sql`
   - **WARNING**: Drops entire schema

## Rollback Procedures

### Development Environment

1. **Using EF Core CLI** (Recommended for development):
   ```bash
   # Remove the latest migration
   dotnet ef migrations remove --project src/Axon.Domain.Service --context CartDbContext
   
   # Update database to previous migration
   dotnet ef database update <PreviousMigration> --project src/Axon.Domain.Service --context CartDbContext
   ```

2. **Using SQL Scripts**:
   ```bash
   # Connect to database
   psql -h localhost -U postgres -d axon_dev
   
   # Execute rollback script
   \i /path/to/RollbackScripts/Rollback_<MigrationName>.sql
   ```

### Production Environment

1. **Pre-Rollback Checklist**:
   - [ ] Create full database backup
   - [ ] Test rollback script in staging environment
   - [ ] Verify no active transactions
   - [ ] Schedule maintenance window
   - [ ] Prepare rollforward plan

2. **Rollback Execution**:
   ```bash
   # 1. Create backup
   pg_dump -h $DB_HOST -U $DB_USER -d $DB_NAME > backup_$(date +%Y%m%d_%H%M%S).sql
   
   # 2. Execute rollback in transaction
   psql -h $DB_HOST -U $DB_USER -d $DB_NAME < RollbackScripts/Rollback_<MigrationName>.sql
   
   # 3. Verify rollback success
   # Check table structures, indexes, and data integrity
   ```

3. **Post-Rollback Verification**:
   - Verify table structures match expected state
   - Check all indexes are correctly recreated
   - Validate data integrity
   - Test application functionality
   - Monitor for errors

## Emergency Rollback

For critical production issues:

1. **Immediate Actions**:
   ```sql
   -- Check current migration state
   SELECT * FROM "__EFMigrationsHistory" ORDER BY "MigrationId" DESC;
   
   -- Remove migration record
   DELETE FROM "__EFMigrationsHistory" WHERE "MigrationId" = '<MigrationName>';
   ```

2. **Execute Rollback Script**:
   - Use appropriate rollback script from `RollbackScripts/`
   - Monitor execution for errors

3. **Application Rollback**:
   - Deploy previous application version
   - Clear application caches
   - Restart services

## Rollback Testing

### Test Strategy

1. **Unit Testing**: Each rollback script should be tested in isolated environment
2. **Integration Testing**: Test full rollback sequence with sample data
3. **Performance Testing**: Verify rollback execution time on production-size datasets

### Test Procedure

```bash
# 1. Create test database from production backup
createdb -T axon_prod axon_rollback_test

# 2. Apply migration
dotnet ef database update <Migration> --connection "Host=localhost;Database=axon_rollback_test"

# 3. Execute rollback
psql -d axon_rollback_test < RollbackScripts/Rollback_<MigrationName>.sql

# 4. Verify state
# Compare schema with expected state
```

## Best Practices

1. **Always Test First**: Never execute untested rollback scripts in production
2. **Backup Before Rollback**: Always create a full backup before any rollback
3. **Document Changes**: Record all rollback operations in deployment log
4. **Monitor After Rollback**: Watch application logs and metrics closely
5. **Communicate**: Inform team and stakeholders before rollback

## Rollback Decision Matrix

| Scenario | Rollback Method | Risk Level | Data Loss |
|----------|----------------|------------|-----------|
| Index changes only | SQL Script | Low | None |
| Schema changes (additive) | EF CLI or SQL | Medium | None |
| Schema changes (destructive) | SQL Script | High | Possible |
| Initial migration | SQL Script | Critical | Complete |

## Recovery Procedures

If rollback fails:

1. **Restore from Backup**:
   ```bash
   pg_restore -h $DB_HOST -U $DB_USER -d $DB_NAME backup.sql
   ```

2. **Manual Recovery**:
   - Document current state
   - Apply fixes incrementally
   - Test each change

3. **Support Contacts**:
   - Database Team: [contact]
   - DevOps Team: [contact]
   - On-Call Engineer: [contact]