-- Rollback script for InitialCreate migration (Order)
-- This script completely removes all Order-related tables and indexes
-- WARNING: This will result in complete data loss for all orders

BEGIN TRANSACTION;

-- Drop order_items table first (due to foreign key constraint)
DROP TABLE IF EXISTS order_items;

-- Drop order address tables
DROP TABLE IF EXISTS order_billing_addresses;
DROP TABLE IF EXISTS order_shipping_addresses;

-- Drop orders table
DROP TABLE IF EXISTS orders;

COMMIT;