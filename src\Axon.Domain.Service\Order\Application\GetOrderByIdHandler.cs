using Axon.Contracts.Order.Queries;
using Axon.Contracts.Order.Events;
using Axon.Domain.Service.Order.Domain;

namespace Axon.Domain.Service.Order.Application;

public interface IGetOrderByIdHandler
{
    GetOrderByIdResponse? Handle(GetOrderByIdQuery query);
}

public class GetOrderByIdHandler : IGetOrderByIdHandler
{
    private readonly IOrderRepository _orderRepository;
    private readonly ILogger<GetOrderByIdHandler> _logger;

    public GetOrderByIdHandler(IOrderRepository orderRepository, ILogger<GetOrderByIdHandler> logger)
    {
        _orderRepository = orderRepository;
        _logger = logger;
    }

    public GetOrderByIdResponse? Handle(GetOrderByIdQuery query)
    {
        _logger.LogInformation("Received request to get order by id {OrderId}", query.OrderId);
        
        var order = _orderRepository.GetById(query.OrderId);
        
        if (order == null)
            return null;

        return new GetOrderByIdResponse
        {
            OrderId = order.Id,
            IncrementId = order.IncrementId,
            CustomerEmail = order.CustomerEmail,
            CustomerFirstname = order.CustomerFirstname,
            CustomerLastname = order.CustomerLastname,
            CustomerId = order.CustomerId,
            StoreId = 1, // Default value, not stored in domain model currently
            Items = order.Items.Select(i => new Axon.Contracts.Order.Events.OrderItem
            {
                ItemId = i.ItemId,
                Sku = i.Sku,
                Qty = i.Qty,
                Price = i.Price,
                BasePrice = i.BasePrice,
                RowTotal = i.RowTotal,
                BaseRowTotal = i.BaseRowTotal,
                Name = i.Name,
                ProductType = i.ProductType,
                ProductOption = null // Not stored in domain model currently
            }).ToList(),
            BillingAddress = new Axon.Contracts.Order.Events.Address
            {
                Firstname = order.BillingAddress.Firstname,
                Lastname = order.BillingAddress.Lastname,
                Street = order.BillingAddress.Street,
                City = order.BillingAddress.City,
                Region = order.BillingAddress.Region,
                Postcode = order.BillingAddress.Postcode,
                CountryId = order.BillingAddress.CountryId,
                Telephone = order.BillingAddress.Telephone
            },
            ShippingAddress = order.ShippingAddress != null ? new Axon.Contracts.Order.Events.Address
            {
                Firstname = order.ShippingAddress.Firstname,
                Lastname = order.ShippingAddress.Lastname,
                Street = order.ShippingAddress.Street,
                City = order.ShippingAddress.City,
                Region = order.ShippingAddress.Region,
                Postcode = order.ShippingAddress.Postcode,
                CountryId = order.ShippingAddress.CountryId,
                Telephone = order.ShippingAddress.Telephone
            } : null,
            Payment = new Axon.Contracts.Order.Events.Payment
            {
                Method = order.Payment.Method,
                AmountOrdered = order.Payment.AmountOrdered,
                BaseAmountOrdered = order.Payment.BaseAmountOrdered
            },
            ShippingMethod = order.ShippingMethod != null ? new Axon.Contracts.Order.Events.ShippingMethod
            {
                MethodCode = order.ShippingMethod.MethodCode,
                CarrierCode = order.ShippingMethod.CarrierCode
            } : null,
            State = order.State,
            Status = order.Status,
            GrandTotal = order.GrandTotal,
            BaseGrandTotal = order.BaseGrandTotal,
            TotalQtyOrdered = order.TotalQtyOrdered,
            CreatedAt = order.CreatedAt
        };
    }
} 