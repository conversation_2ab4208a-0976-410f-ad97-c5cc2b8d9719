using Axon.Contracts.Order.Commands;
using Axon.Contracts.Order.Events;
using Axon.Domain.Service.Order.Domain;

namespace Axon.Domain.Service.Order.Application;

public interface IRecordOrderCreatedHandler
{
    OrderCreatedEvent Handle(RecordOrderCreatedCommand command);
}

public class RecordOrderCreatedHandler : IRecordOrderCreatedHandler
{
    private readonly IOrderRepository _orderRepository;

    public RecordOrderCreatedHandler(IOrderRepository orderRepository)
    {
        _orderRepository = orderRepository;
    }

    public OrderCreatedEvent Handle(RecordOrderCreatedCommand command)
    {
        var order = new Axon.Domain.Service.Order.Domain.Order(
            Guid.NewGuid(),
            command.IncrementId,
            command.State,
            command.Status,
            command.CustomerId,
            command.CustomerEmail,
            command.CustomerFirstname,
            command.CustomerLastname,
            new Axon.Domain.Service.Order.Domain.Address(
                command.BillingAddress.Firstname,
                command.BillingAddress.Lastname,
                command.BillingAddress.Street,
                command.BillingAddress.City,
                command.BillingAddress.Region,
                command.BillingAddress.Postcode,
                command.BillingAddress.CountryId,
                command.BillingAddress.Telephone
            ),
            command.Items.Select(i => new Axon.Domain.Service.Order.Domain.OrderItem(
                i.ItemId, i.Sku, i.Qty, i.Price, i.BasePrice, i.RowTotal, i.BaseRowTotal, i.Name, i.ProductType)).ToList(),
            new Axon.Domain.Service.Order.Domain.Payment(command.Payment.Method, command.Payment.AmountOrdered, command.Payment.BaseAmountOrdered),
            command.ShippingAddress != null
                ? new Axon.Domain.Service.Order.Domain.Address(
                    command.ShippingAddress.Firstname,
                    command.ShippingAddress.Lastname,
                    command.ShippingAddress.Street,
                    command.ShippingAddress.City,
                    command.ShippingAddress.Region,
                    command.ShippingAddress.Postcode,
                    command.ShippingAddress.CountryId,
                    command.ShippingAddress.Telephone
                )
                : null,
            command.ShippingMethod != null
                ? new Axon.Domain.Service.Order.Domain.ShippingMethod(command.ShippingMethod.MethodCode, command.ShippingMethod.CarrierCode)
                : null,
            command.TotalQtyOrdered,
            command.GrandTotal,
            command.BaseGrandTotal,
            command.CreatedAt
        );

        _orderRepository.Save(order);

        return new OrderCreatedEvent
        {
            OrderId = order.Id,
            IncrementId = order.IncrementId,
            CustomerEmail = order.CustomerEmail,
            CustomerFirstname = order.CustomerFirstname,
            CustomerLastname = order.CustomerLastname,
            StoreId = command.StoreId,
            Items = order.Items.Select(i => new Axon.Contracts.Order.Events.OrderItem
            {
                Sku = i.Sku,
                Qty = i.Qty,
                Price = i.Price,
                Name = i.Name,
                ProductType = i.ProductType,
                ProductOption = null
            }).ToList(),
            BillingAddress = new Axon.Contracts.Order.Events.Address
            {
                Firstname = order.BillingAddress.Firstname,
                Lastname = order.BillingAddress.Lastname,
                Street = order.BillingAddress.Street,
                City = order.BillingAddress.City,
                Region = order.BillingAddress.Region,
                Postcode = order.BillingAddress.Postcode,
                CountryId = order.BillingAddress.CountryId,
                Telephone = order.BillingAddress.Telephone
            },
            ShippingAddress = order.ShippingAddress != null
                ? new Axon.Contracts.Order.Events.Address
                {
                    Firstname = order.ShippingAddress.Firstname,
                    Lastname = order.ShippingAddress.Lastname,
                    Street = order.ShippingAddress.Street,
                    City = order.ShippingAddress.City,
                    Region = order.ShippingAddress.Region,
                    Postcode = order.ShippingAddress.Postcode,
                    CountryId = order.ShippingAddress.CountryId,
                    Telephone = order.ShippingAddress.Telephone
                }
                : null,
            Payment = new Axon.Contracts.Order.Events.Payment
            {
                Method = order.Payment.Method
            },
            ShippingMethod = order.ShippingMethod != null
                ? new Axon.Contracts.Order.Events.ShippingMethod
                {
                    MethodCode = order.ShippingMethod.MethodCode,
                    CarrierCode = order.ShippingMethod.CarrierCode
                }
                : null
        };
    }
} 