using Axon.Contracts.Order.Queries;
using MassTransit;
using Microsoft.Extensions.Logging;
using Axon.Domain.Service.Order.Application;

namespace Axon.Domain.Service.Order.Consumers;

public class GetOrderByIdQueryConsumer : IConsumer<GetOrderByIdQuery>
{
    private readonly ILogger<GetOrderByIdQueryConsumer> _logger;
    private readonly IGetOrderByIdHandler _handler;

    public GetOrderByIdQueryConsumer(
        ILogger<GetOrderByIdQueryConsumer> logger,
        IGetOrderByIdHandler handler)
    {
        _logger = logger;
        _handler = handler;
    }

    public async Task Consume(ConsumeContext<GetOrderByIdQuery> context)
    {
        _logger.LogInformation("Consuming GetOrderByIdQuery with OrderId {OrderId}", context.Message.OrderId);

        var orderResponse = _handler.Handle(context.Message);

        if (orderResponse != null)
        {
            _logger.LogInformation("Found order with OrderId {OrderId} and IncrementId {IncrementId}", 
                orderResponse.OrderId, orderResponse.IncrementId);
            
            await context.RespondAsync(orderResponse);
        }
        else
        {
            _logger.LogWarning("Order with OrderId {OrderId} not found", context.Message.OrderId);
            
            // Respond with a fault to indicate the order was not found
            throw new InvalidOperationException($"Order with ID {context.Message.OrderId} not found");
        }
    }
} 