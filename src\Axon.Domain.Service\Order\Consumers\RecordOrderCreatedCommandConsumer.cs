using Axon.Contracts.Order.Commands;
using MassTransit;
using Microsoft.Extensions.Logging;
using Axon.Domain.Service.Order.Application;

namespace Axon.Domain.Service.Order.Consumers;

public class RecordOrderCreatedCommandConsumer : IConsumer<RecordOrderCreatedCommand>
{
    private readonly ILogger<RecordOrderCreatedCommandConsumer> _logger;
    private readonly IRecordOrderCreatedHandler _handler;

    public RecordOrderCreatedCommandConsumer(
        ILogger<RecordOrderCreatedCommandConsumer> logger,
        IRecordOrderCreatedHandler handler)
    {
        _logger = logger;
        _handler = handler;
    }

    public async Task Consume(ConsumeContext<RecordOrderCreatedCommand> context)
    {
        _logger.LogInformation("Consumed {@RecordOrderCreatedCommand}", context.Message);

        var orderCreatedEvent = _handler.Handle(context.Message);

        await context.Publish(orderCreatedEvent, publishContext => {
            publishContext.Headers.Set("OrderId", orderCreatedEvent.OrderId.ToString());
        }, context.CancellationToken);
        _logger.LogInformation("Published {@OrderCreatedEvent}", orderCreatedEvent);

        await context.RespondAsync(orderCreatedEvent);
    }
} 