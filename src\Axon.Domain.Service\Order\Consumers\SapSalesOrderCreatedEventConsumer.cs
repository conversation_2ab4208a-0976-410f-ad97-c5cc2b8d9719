using Axon.Contracts.Order.Events;
using MassTransit;

namespace Axon.Domain.Service.Order.Consumers;

public class SapSalesOrderCreatedEventConsumer : IConsumer<SapSalesOrderCreatedEvent>
{
    private readonly ILogger<SapSalesOrderCreatedEventConsumer> _logger;
    // Inject any needed application services herepwc lift

    public SapSalesOrderCreatedEventConsumer(ILogger<SapSalesOrderCreatedEventConsumer> logger)
    {
        _logger = logger;
    }

    public async Task Consume(ConsumeContext<SapSalesOrderCreatedEvent> context)
    {
        var sapEvent = context.Message;
        var erpOrderId = sapEvent.OrderNumber;

        // Get the OrderId from the headers since we aren't writing to the DB yet..
        var orderId = context.Headers.TryGetHeader("OrderId", out var headerValue) && headerValue is string s
            ? s
            : "UNKNOWN";

        var acknowledgedEvent = new OrderAcknowledgedByErpEvent(
            Guid.Parse(orderId), 
            new OrderAcknowledgedByErpEvent.OrderRef(erpOrderId)
        );

        _logger.LogInformation("Publishing OrderAcknowledgedByErpEvent for ERP OrderNumber {OrderNumber}, OrderId {OrderId}", erpOrderId, orderId);
        await context.Publish(acknowledgedEvent, context.CancellationToken);
        _logger.LogInformation("Published OrderAcknowledgedByErpEvent for ERP OrderNumber {OrderNumber}, OrderId {OrderId}", erpOrderId, orderId);
    }
} 