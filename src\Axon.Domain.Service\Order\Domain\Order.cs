namespace Axon.Domain.Service.Order.Domain;

public class Order
{
    public Guid Id { get; }
    public string IncrementId { get; }
    public string State { get; }
    public string Status { get; }
    public int? CustomerId { get; }
    public string CustomerEmail { get; }
    public string? CustomerFirstname { get; }
    public string? CustomerLastname { get; }
    public Address BillingAddress { get; }
    public Address? ShippingAddress { get; }
    public List<OrderItem> Items { get; }
    public Payment Payment { get; }
    public ShippingMethod? ShippingMethod { get; }
    public decimal TotalQtyOrdered { get; }
    public decimal GrandTotal { get; }
    public decimal BaseGrandTotal { get; }
    public DateTimeOffset? CreatedAt { get; }

    // Private parameterless constructor for Entity Framework
    private Order()
    {
        Items = new List<OrderItem>();
        CustomerEmail = string.Empty;
        IncrementId = string.Empty;
        State = string.Empty;
        Status = string.Empty;
        BillingAddress = null!;
        Payment = null!;
    }

    public Order(Guid id, string incrementId, string state, string status, int? customerId, string customerEmail, 
        string? customerFirstname, string? customerLastname, Address? billingAddress, List<OrderItem> items, 
        Payment payment, Address? shippingAddress = null, ShippingMethod? shippingMethod = null, 
        decimal totalQtyOrdered = 0, decimal grandTotal = 0, decimal baseGrandTotal = 0, DateTimeOffset? createdAt = null)
    {
        if (id == Guid.Empty) throw new ArgumentException("ID required");
        if (string.IsNullOrWhiteSpace(customerEmail)) throw new ArgumentException("Customer email required");
        if (items == null || items.Count == 0) throw new ArgumentException("Order must have at least one item.");
        
        Id = id;
        IncrementId = incrementId;
        State = state;
        Status = status;
        CustomerId = customerId;
        CustomerEmail = customerEmail;
        CustomerFirstname = customerFirstname;
        CustomerLastname = customerLastname;
        BillingAddress = billingAddress ?? throw new ArgumentNullException(nameof(billingAddress));
        ShippingAddress = shippingAddress;
        Items = items;
        Payment = payment ?? throw new ArgumentNullException(nameof(payment));
        ShippingMethod = shippingMethod;
        TotalQtyOrdered = totalQtyOrdered;
        GrandTotal = grandTotal;
        BaseGrandTotal = baseGrandTotal;
        CreatedAt = createdAt;
    }
} 