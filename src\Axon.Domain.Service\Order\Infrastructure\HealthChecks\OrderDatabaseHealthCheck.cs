using Axon.Domain.Service.Order.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace Axon.Domain.Service.Order.Infrastructure.HealthChecks;

public class OrderDatabaseHealthCheck : IHealthCheck
{
    private readonly OrderDbContext _context;
    private readonly ILogger<OrderDatabaseHealthCheck> _logger;

    public OrderDatabaseHealthCheck(OrderDbContext context, ILogger<OrderDatabaseHealthCheck> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        try
        {
            // Check if we can connect to the database
            var canConnect = await _context.Database.CanConnectAsync(cancellationToken);
            
            if (!canConnect)
            {
                _logger.LogWarning("Order database health check failed: Cannot connect to database");
                return HealthCheckResult.Unhealthy("Cannot connect to Order database");
            }

            // Perform a simple query to verify the schema is accessible
            var orderCount = await _context.Orders
                .Take(1)
                .CountAsync(cancellationToken);

            _logger.LogInformation("Order database health check passed");
            
            return HealthCheckResult.Healthy("Order database is accessible", new Dictionary<string, object>
            {
                ["database"] = "Order",
                ["schema"] = "order",
                ["status"] = "Connected"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Order database health check failed with exception");
            
            return HealthCheckResult.Unhealthy(
                "Order database health check failed",
                ex,
                new Dictionary<string, object>
                {
                    ["database"] = "Order",
                    ["error"] = ex.Message
                });
        }
    }
}