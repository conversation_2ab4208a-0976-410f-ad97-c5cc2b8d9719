using Axon.Domain.Service.Shared.Infrastructure.Persistence;
using Axon.Domain.Service.Order.Domain;
using Axon.Domain.Service.Order.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;

namespace Axon.Domain.Service.Order.Infrastructure;

public class OrderRepository : EfRepositoryBase<Domain.Order, Guid, OrderDbContext>, IOrderRepository
{
    private readonly ILogger<OrderRepository> _logger;

    public OrderRepository(OrderDbContext context, ILogger<OrderRepository> logger) 
        : base(context)
    {
        _logger = logger;
    }

    public void Save(Domain.Order order)
    {
        if (order == null)
            throw new ArgumentNullException(nameof(order));

        _logger.LogInformation("Saving order {OrderId}", order.Id);

        var existingOrder = DbSet.Local.FirstOrDefault(o => o.Id == order.Id);
        if (existingOrder != null)
        {
            Context.Entry(existingOrder).State = EntityState.Detached;
        }

        var trackedOrder = Context.Orders.Find(order.Id);
        if (trackedOrder != null)
        {
            Context.Entry(trackedOrder).CurrentValues.SetValues(order);
            Context.Entry(trackedOrder).State = EntityState.Modified;
        }
        else
        {
            Context.Orders.Add(order);
        }

        Context.SaveChanges();

        _logger.LogInformation("Saved order {OrderId}", order.Id);
    }

    public Domain.Order? GetById(Guid orderId)
    {
        _logger.LogInformation("Fetching order {OrderId}", orderId);

        var order = Context.Orders
            .AsSplitQuery()
            .Include(o => o.Items)
            .Include(o => o.BillingAddress)
            .Include(o => o.ShippingAddress)
            .Include(o => o.Payment)
            .Include(o => o.ShippingMethod)
            .FirstOrDefault(o => o.Id == orderId);

        if (order != null)
        {
            _logger.LogInformation("Fetched order {OrderId}", order.Id);
        }
        else
        {
            _logger.LogInformation("Order {OrderId} not found", orderId);
        }

        return order;
    }

    public void Clear()
    {
        _logger.LogInformation("Clearing all orders");
        
        Context.Orders.RemoveRange(Context.Orders);
        Context.SaveChanges();
        
        _logger.LogInformation("Cleared all orders");
    }
}