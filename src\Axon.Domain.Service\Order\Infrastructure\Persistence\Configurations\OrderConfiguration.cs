using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Axon.Domain.Service.Order.Infrastructure.Persistence.Configurations;

public class OrderConfiguration : IEntityTypeConfiguration<Domain.Order>
{
    public void Configure(EntityTypeBuilder<Domain.Order> builder)
    {
        builder.ToTable("orders");

        builder.HasKey(o => o.Id);
        
        builder.Property(o => o.Id)
            .ValueGeneratedNever();

        builder.Property(o => o.IncrementId)
            .IsRequired()
            .HasMaxLength(50);

        builder.HasIndex(o => o.IncrementId)
            .IsUnique();

        builder.Property(o => o.State)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(o => o.Status)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(o => o.CustomerEmail)
            .IsRequired()
            .HasMaxLength(255);

        builder.HasIndex(o => o.CustomerEmail);

        builder.Property(o => o.CustomerFirstname)
            .HasMaxLength(100);

        builder.Property(o => o.CustomerLastname)
            .HasMaxLength(100);

        builder.Property(o => o.CustomerId);
        builder.HasIndex(o => o.CustomerId);

        builder.Property(o => o.GrandTotal)
            .HasPrecision(19, 4);

        builder.Property(o => o.BaseGrandTotal)
            .HasPrecision(19, 4);

        builder.Property(o => o.TotalQtyOrdered)
            .HasPrecision(12, 4);

        builder.Property(o => o.CreatedAt);
        builder.HasIndex(o => o.CreatedAt);

        // Configure owned entities
        builder.OwnsOne(o => o.BillingAddress, addressBuilder =>
        {
            addressBuilder.ToTable("order_billing_addresses");
            addressBuilder.WithOwner().HasForeignKey("OrderId");
            
            addressBuilder.Property(a => a.Firstname).HasMaxLength(100);
            addressBuilder.Property(a => a.Lastname).HasMaxLength(100);
            addressBuilder.Property(a => a.City).HasMaxLength(100);
            addressBuilder.Property(a => a.CountryId).HasMaxLength(10);
            addressBuilder.Property(a => a.Telephone).HasMaxLength(50);
            addressBuilder.Property(a => a.Region).HasMaxLength(100);
            addressBuilder.Property(a => a.Postcode).HasMaxLength(20);
            
            addressBuilder.Property(a => a.Street)
                .HasConversion(
                    v => string.Join(';', v),
                    v => v.Split(';', StringSplitOptions.RemoveEmptyEntries).ToList())
                .HasMaxLength(500)
                .Metadata.SetValueComparer(new ValueComparer<List<string>>(
                    (c1, c2) => c1 != null && c2 != null && c1.SequenceEqual(c2),
                    c => c != null ? c.Aggregate(0, (a, v) => HashCode.Combine(a, v.GetHashCode())) : 0,
                    c => c != null ? c.ToList() : new List<string>()));
        });

        builder.OwnsOne(o => o.ShippingAddress, addressBuilder =>
        {
            addressBuilder.ToTable("order_shipping_addresses");
            addressBuilder.WithOwner().HasForeignKey("OrderId");
            
            addressBuilder.Property(a => a.Firstname).HasMaxLength(100);
            addressBuilder.Property(a => a.Lastname).HasMaxLength(100);
            addressBuilder.Property(a => a.City).HasMaxLength(100);
            addressBuilder.Property(a => a.CountryId).HasMaxLength(10);
            addressBuilder.Property(a => a.Telephone).HasMaxLength(50);
            addressBuilder.Property(a => a.Region).HasMaxLength(100);
            addressBuilder.Property(a => a.Postcode).HasMaxLength(20);
            
            addressBuilder.Property(a => a.Street)
                .HasConversion(
                    v => string.Join(';', v),
                    v => v.Split(';', StringSplitOptions.RemoveEmptyEntries).ToList())
                .HasMaxLength(500)
                .Metadata.SetValueComparer(new ValueComparer<List<string>>(
                    (c1, c2) => c1 != null && c2 != null && c1.SequenceEqual(c2),
                    c => c != null ? c.Aggregate(0, (a, v) => HashCode.Combine(a, v.GetHashCode())) : 0,
                    c => c != null ? c.ToList() : new List<string>()));
        });

        builder.OwnsOne(o => o.Payment, paymentBuilder =>
        {
            paymentBuilder.Property(p => p.Method).HasMaxLength(100);
            paymentBuilder.Property(p => p.AmountOrdered).HasPrecision(19, 4);
            paymentBuilder.Property(p => p.BaseAmountOrdered).HasPrecision(19, 4);
        });

        builder.OwnsOne(o => o.ShippingMethod, shippingBuilder =>
        {
            shippingBuilder.Property(s => s.MethodCode).HasMaxLength(100);
            shippingBuilder.Property(s => s.CarrierCode).HasMaxLength(100);
        });

        // Configure collection
        builder.OwnsMany(o => o.Items, itemBuilder =>
        {
            itemBuilder.ToTable("order_items");
            itemBuilder.WithOwner().HasForeignKey("OrderId");
            
            itemBuilder.Property(i => i.ItemId);
            itemBuilder.HasIndex(i => i.ItemId);
            
            itemBuilder.Property(i => i.Sku)
                .IsRequired()
                .HasMaxLength(100);
            itemBuilder.HasIndex(i => i.Sku);
            
            itemBuilder.Property(i => i.Name)
                .IsRequired()
                .HasMaxLength(255);
            
            itemBuilder.Property(i => i.ProductType)
                .HasMaxLength(100);
            
            itemBuilder.Property(i => i.Qty).HasPrecision(12, 4);
            itemBuilder.Property(i => i.Price).HasPrecision(19, 4);
            itemBuilder.Property(i => i.BasePrice).HasPrecision(19, 4);
            itemBuilder.Property(i => i.RowTotal).HasPrecision(19, 4);
            itemBuilder.Property(i => i.BaseRowTotal).HasPrecision(19, 4);
        });

        // Composite indexes
        builder.HasIndex(o => new { o.CustomerId, o.CreatedAt })
            .HasDatabaseName("IX_Orders_CustomerId_CreatedAt");
            
        // Covering index for customer order history queries
        builder.HasIndex(o => new { o.CustomerId, o.Status })
            .HasDatabaseName("IX_Orders_CustomerId_Status")
            .IncludeProperties(o => new { o.IncrementId, o.GrandTotal, o.CreatedAt });
            
        // Covering index for order status queries
        builder.HasIndex(o => new { o.Status, o.CreatedAt })
            .HasDatabaseName("IX_Orders_Status_CreatedAt_Desc")
            .IsDescending(false, true)
            .IncludeProperties(o => new { o.IncrementId, o.CustomerId, o.GrandTotal });
            
        // Covering index for recent orders
        builder.HasIndex(o => o.CreatedAt)
            .HasDatabaseName("IX_Orders_CreatedAt_Desc")
            .IsDescending()
            .IncludeProperties(o => new { o.IncrementId, o.Status, o.CustomerId, o.GrandTotal });
    }
}