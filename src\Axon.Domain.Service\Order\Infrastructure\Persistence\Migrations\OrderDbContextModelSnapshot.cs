﻿// <auto-generated />
using System;
using Axon.Domain.Service.Order.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Axon.Domain.Service.Order.Infrastructure.Persistence.Migrations
{
    [DbContext(typeof(OrderDbContext))]
    partial class OrderDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasDefaultSchema("order")
                .HasAnnotation("ProductVersion", "9.0.1")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("Axon.Domain.Service.Order.Domain.Order", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<decimal>("BaseGrandTotal")
                        .HasPrecision(19, 4)
                        .HasColumnType("numeric(19,4)");

                    b.Property<DateTimeOffset?>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CustomerEmail")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("CustomerFirstname")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int?>("CustomerId")
                        .HasColumnType("integer");

                    b.Property<string>("CustomerLastname")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<decimal>("GrandTotal")
                        .HasPrecision(19, 4)
                        .HasColumnType("numeric(19,4)");

                    b.Property<string>("IncrementId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<decimal>("TotalQtyOrdered")
                        .HasPrecision(12, 4)
                        .HasColumnType("numeric(12,4)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt")
                        .IsDescending()
                        .HasDatabaseName("IX_Orders_CreatedAt_Desc");

                    NpgsqlIndexBuilderExtensions.IncludeProperties(b.HasIndex("CreatedAt"), new[] { "IncrementId", "Status", "CustomerId", "GrandTotal" });

                    b.HasIndex("CustomerEmail");

                    b.HasIndex("CustomerId");

                    b.HasIndex("IncrementId")
                        .IsUnique();

                    b.HasIndex("CustomerId", "CreatedAt")
                        .HasDatabaseName("IX_Orders_CustomerId_CreatedAt");

                    b.HasIndex("CustomerId", "Status")
                        .HasDatabaseName("IX_Orders_CustomerId_Status");

                    NpgsqlIndexBuilderExtensions.IncludeProperties(b.HasIndex("CustomerId", "Status"), new[] { "IncrementId", "GrandTotal", "CreatedAt" });

                    b.HasIndex("Status", "CreatedAt")
                        .IsDescending(false, true)
                        .HasDatabaseName("IX_Orders_Status_CreatedAt_Desc");

                    NpgsqlIndexBuilderExtensions.IncludeProperties(b.HasIndex("Status", "CreatedAt"), new[] { "IncrementId", "CustomerId", "GrandTotal" });

                    b.ToTable("orders", "order");
                });

            modelBuilder.Entity("Axon.Domain.Service.Order.Domain.Order", b =>
                {
                    b.OwnsOne("Axon.Domain.Service.Order.Domain.Address", "BillingAddress", b1 =>
                        {
                            b1.Property<Guid>("OrderId")
                                .HasColumnType("uuid");

                            b1.Property<string>("City")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.Property<string>("CountryId")
                                .IsRequired()
                                .HasMaxLength(10)
                                .HasColumnType("character varying(10)");

                            b1.Property<string>("Firstname")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.Property<string>("Lastname")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.Property<string>("Postcode")
                                .HasMaxLength(20)
                                .HasColumnType("character varying(20)");

                            b1.Property<string>("Region")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.Property<string>("Street")
                                .IsRequired()
                                .HasMaxLength(500)
                                .HasColumnType("character varying(500)");

                            b1.Property<string>("Telephone")
                                .IsRequired()
                                .HasMaxLength(50)
                                .HasColumnType("character varying(50)");

                            b1.HasKey("OrderId");

                            b1.ToTable("order_billing_addresses", "order");

                            b1.WithOwner()
                                .HasForeignKey("OrderId");
                        });

                    b.OwnsOne("Axon.Domain.Service.Order.Domain.Address", "ShippingAddress", b1 =>
                        {
                            b1.Property<Guid>("OrderId")
                                .HasColumnType("uuid");

                            b1.Property<string>("City")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.Property<string>("CountryId")
                                .IsRequired()
                                .HasMaxLength(10)
                                .HasColumnType("character varying(10)");

                            b1.Property<string>("Firstname")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.Property<string>("Lastname")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.Property<string>("Postcode")
                                .HasMaxLength(20)
                                .HasColumnType("character varying(20)");

                            b1.Property<string>("Region")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.Property<string>("Street")
                                .IsRequired()
                                .HasMaxLength(500)
                                .HasColumnType("character varying(500)");

                            b1.Property<string>("Telephone")
                                .IsRequired()
                                .HasMaxLength(50)
                                .HasColumnType("character varying(50)");

                            b1.HasKey("OrderId");

                            b1.ToTable("order_shipping_addresses", "order");

                            b1.WithOwner()
                                .HasForeignKey("OrderId");
                        });

                    b.OwnsMany("Axon.Domain.Service.Order.Domain.OrderItem", "Items", b1 =>
                        {
                            b1.Property<Guid>("OrderId")
                                .HasColumnType("uuid");

                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer");

                            NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b1.Property<int>("Id"));

                            b1.Property<decimal>("BasePrice")
                                .HasPrecision(19, 4)
                                .HasColumnType("numeric(19,4)");

                            b1.Property<decimal>("BaseRowTotal")
                                .HasPrecision(19, 4)
                                .HasColumnType("numeric(19,4)");

                            b1.Property<int>("ItemId")
                                .HasColumnType("integer");

                            b1.Property<string>("Name")
                                .IsRequired()
                                .HasMaxLength(255)
                                .HasColumnType("character varying(255)");

                            b1.Property<decimal>("Price")
                                .HasPrecision(19, 4)
                                .HasColumnType("numeric(19,4)");

                            b1.Property<string>("ProductType")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.Property<decimal>("Qty")
                                .HasPrecision(12, 4)
                                .HasColumnType("numeric(12,4)");

                            b1.Property<decimal>("RowTotal")
                                .HasPrecision(19, 4)
                                .HasColumnType("numeric(19,4)");

                            b1.Property<string>("Sku")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.HasKey("OrderId", "Id");

                            b1.HasIndex("ItemId");

                            b1.HasIndex("Sku");

                            b1.ToTable("order_items", "order");

                            b1.WithOwner()
                                .HasForeignKey("OrderId");
                        });

                    b.OwnsOne("Axon.Domain.Service.Order.Domain.Payment", "Payment", b1 =>
                        {
                            b1.Property<Guid>("OrderId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("AmountOrdered")
                                .HasPrecision(19, 4)
                                .HasColumnType("numeric(19,4)");

                            b1.Property<decimal>("BaseAmountOrdered")
                                .HasPrecision(19, 4)
                                .HasColumnType("numeric(19,4)");

                            b1.Property<string>("Method")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.HasKey("OrderId");

                            b1.ToTable("orders", "order");

                            b1.WithOwner()
                                .HasForeignKey("OrderId");
                        });

                    b.OwnsOne("Axon.Domain.Service.Order.Domain.ShippingMethod", "ShippingMethod", b1 =>
                        {
                            b1.Property<Guid>("OrderId")
                                .HasColumnType("uuid");

                            b1.Property<string>("CarrierCode")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.Property<string>("MethodCode")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.HasKey("OrderId");

                            b1.ToTable("orders", "order");

                            b1.WithOwner()
                                .HasForeignKey("OrderId");
                        });

                    b.Navigation("BillingAddress")
                        .IsRequired();

                    b.Navigation("Items");

                    b.Navigation("Payment")
                        .IsRequired();

                    b.Navigation("ShippingAddress");

                    b.Navigation("ShippingMethod");
                });
#pragma warning restore 612, 618
        }
    }
}
