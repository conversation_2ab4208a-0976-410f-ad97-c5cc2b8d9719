using Microsoft.EntityFrameworkCore;

namespace Axon.Domain.Service.Order.Infrastructure.Persistence;

public class OrderDbContext : DbContext
{
    public OrderDbContext(DbContextOptions<OrderDbContext> options) : base(options)
    {
    }

    public DbSet<Domain.Order> Orders { get; set; } = null!;

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.HasDefaultSchema("order");
        
        // Apply all configurations from the current assembly
        modelBuilder.ApplyConfigurationsFromAssembly(typeof(OrderDbContext).Assembly, 
            t => t.Namespace != null && t.Namespace.Contains("Order.Infrastructure.Persistence.Configurations"));

        base.OnModelCreating(modelBuilder);
    }
}