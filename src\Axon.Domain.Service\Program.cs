using Axon.Core.MassTransit.Options;
using Axon.Core.MassTransit.ServiceCollectionExtensions;
using Axon.Core.ServiceCollectionExtensions;
using Axon.Domain.Service.Cart.Application;
using Axon.Domain.Service.Cart.Consumers;
using Axon.Domain.Service.Cart.Domain;
using Axon.Domain.Service.Cart.Infrastructure;
using Axon.Domain.Service.Shared.Infrastructure.Persistence;
using Axon.Domain.Service.Shared.Infrastructure.HealthChecks;
using Axon.Domain.Service.Order.Consumers;
using Axon.Domain.Service.Order.Application;
using Axon.Domain.Service.Order.Domain;
using Axon.Domain.Service.Order.Infrastructure;

string[] apiCorsOrigins = [];

var host = Host.CreateDefaultBuilder(args)
    .ConfigureAxonDefaults()
    .ConfigureWebHostDefaults(webBuilder =>
    {
        webBuilder.ConfigureServices((context, services) =>
        {
            services.AddAxonCoreServices(apiCorsOrigins);

            var environment = context.HostingEnvironment.EnvironmentName;
            var isAwsEnvironment = environment.StartsWith("AWS-", StringComparison.OrdinalIgnoreCase);

            if (!isAwsEnvironment)
            {
                // Only validate RabbitMQ options in Development environment
                services.AddOptions<RabbitMqOptions>()
                    .Bind(context.Configuration.GetSection(RabbitMqOptions.Key))
                    .ValidateDataAnnotations()
                    .ValidateOnStart();
            }
            else
            {
                // Only validate AWS options in AWS environments
                services.AddOptions<AwsOptions>()
                    .Bind(context.Configuration.GetSection(AwsOptions.Key))
                    .ValidateDataAnnotations()
                    .ValidateOnStart();
            }

            services.AddMassTransitWithBroker(x =>
            {
                x.AddConsumer<RecordOrderCreatedCommandConsumer>();
                x.AddConsumer<SapSalesOrderCreatedEventConsumer>();
                x.AddConsumer<GetOrderByIdQueryConsumer>();
                x.AddConsumer<RecordCartCreatedCommandConsumer>();
                x.AddConsumer<RecordCartUpdatedCommandConsumer>();
            });

            // Add persistence services (EF Core with PostgreSQL)
            services.AddPersistence(context.Configuration);

            // Remove in-memory repositories as they are now replaced by EF Core repositories
            // services.AddSingleton<IOrderRepository, OrderRepository>();
            // services.AddSingleton<ICartRepository, CartRepository>();

            services.AddScoped<IRecordOrderCreatedHandler, RecordOrderCreatedHandler>();
            services.AddScoped<IGetOrderByIdHandler, GetOrderByIdHandler>();
            services.AddScoped<IRecordCartCreatedHandler, RecordCartCreatedHandler>();
            services.AddScoped<IRecordCartUpdatedHandler, RecordCartUpdatedHandler>();
        });

        webBuilder.Configure((context, app) => 
        { 
            app.UseAxonCoreMiddleware(apiCorsOrigins.Length > 0);
            
            // Map health check endpoints
            app.UseEndpoints(endpoints =>
            {
                // Primary health check endpoint for load balancer
                // Returns 200 OK if all databases are healthy, 503 Service Unavailable otherwise
                endpoints.MapHealthChecks("/healthz");
                
                // Optional: Detailed health check endpoint for debugging/monitoring
                // Same checks but with detailed JSON output
                endpoints.MapHealthChecks("/healthz/details", new Microsoft.AspNetCore.Diagnostics.HealthChecks.HealthCheckOptions
                {
                    ResponseWriter = HealthCheckResponseWriter.WriteDetailedResponse
                });
            });
        });
    })
    .Build();

await host.RunAsync();