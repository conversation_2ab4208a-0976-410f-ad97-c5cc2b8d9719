using Axon.Domain.Service.Cart.Infrastructure.Persistence;
using Axon.Domain.Service.Order.Infrastructure.Persistence;
using Axon.Domain.Service.Shared.Infrastructure.Persistence;
using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace Axon.Domain.Service.Shared.Infrastructure.HealthChecks;

/// <summary>
/// Composite health check that verifies all database contexts are accessible
/// </summary>
public class AllDatabasesHealthCheck : IHealthCheck
{
    private readonly CartDbContext _cartContext;
    private readonly OrderDbContext _orderContext;
    private readonly SharedDbContext _sharedContext;
    private readonly ILogger<AllDatabasesHealthCheck> _logger;

    public AllDatabasesHealthCheck(
        CartDbContext cartContext,
        OrderDbContext orderContext,
        SharedDbContext sharedContext,
        ILogger<AllDatabasesHealthCheck> logger)
    {
        _cartContext = cartContext;
        _orderContext = orderContext;
        _sharedContext = sharedContext;
        _logger = logger;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        var results = new Dictionary<string, object>();
        var unhealthyDatabases = new List<string>();
        var errors = new List<string>();

        // Check Cart database
        try
        {
            var canConnectCart = await _cartContext.Database.CanConnectAsync(cancellationToken);
            results["cart_database"] = canConnectCart ? "Connected" : "Disconnected";
            if (!canConnectCart) unhealthyDatabases.Add("Cart");
        }
        catch (Exception ex)
        {
            results["cart_database"] = "Error";
            results["cart_error"] = ex.Message;
            unhealthyDatabases.Add("Cart");
            errors.Add($"Cart: {ex.Message}");
        }

        // Check Order database
        try
        {
            var canConnectOrder = await _orderContext.Database.CanConnectAsync(cancellationToken);
            results["order_database"] = canConnectOrder ? "Connected" : "Disconnected";
            if (!canConnectOrder) unhealthyDatabases.Add("Order");
        }
        catch (Exception ex)
        {
            results["order_database"] = "Error";
            results["order_error"] = ex.Message;
            unhealthyDatabases.Add("Order");
            errors.Add($"Order: {ex.Message}");
        }

        // Check Shared database
        try
        {
            var canConnectShared = await _sharedContext.Database.CanConnectAsync(cancellationToken);
            results["shared_database"] = canConnectShared ? "Connected" : "Disconnected";
            if (!canConnectShared) unhealthyDatabases.Add("Shared");
        }
        catch (Exception ex)
        {
            results["shared_database"] = "Error";
            results["shared_error"] = ex.Message;
            unhealthyDatabases.Add("Shared");
            errors.Add($"Shared: {ex.Message}");
        }

        results["total_databases"] = 3;
        results["healthy_databases"] = 3 - unhealthyDatabases.Count;

        if (unhealthyDatabases.Count == 0)
        {
            _logger.LogInformation("All databases health check passed");
            return HealthCheckResult.Healthy("All databases are accessible", results);
        }
        else if (unhealthyDatabases.Count < 3)
        {
            var message = $"Some databases are unhealthy: {string.Join(", ", unhealthyDatabases)}";
            _logger.LogWarning(message);
            
            return HealthCheckResult.Degraded(
                message,
                null,
                results);
        }
        else
        {
            var message = "All databases are unhealthy";
            _logger.LogError(message);
            
            return HealthCheckResult.Unhealthy(
                message,
                errors.Count > 0 ? new AggregateException(errors.Select(e => new Exception(e))) : null,
                results);
        }
    }
}