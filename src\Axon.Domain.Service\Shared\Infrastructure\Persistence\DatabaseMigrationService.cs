using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace Axon.Domain.Service.Shared.Infrastructure.Persistence;

/// <summary>
/// Service responsible for automatically applying database migrations on startup
/// </summary>
public class DatabaseMigrationService(IServiceProvider serviceProvider, ILogger<DatabaseMigrationService> logger) : IHostedService
{
    private readonly IServiceProvider _serviceProvider = serviceProvider;
    private readonly ILogger<DatabaseMigrationService> _logger = logger;

    public async Task StartAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Starting database migration service");

        using var scope = _serviceProvider.CreateScope();
        var services = scope.ServiceProvider;

        try
        {
            await MigrateDbContext<Order.Infrastructure.Persistence.OrderDbContext>(services, cancellationToken);
            await MigrateDbContext<Cart.Infrastructure.Persistence.CartDbContext>(services, cancellationToken);
            await MigrateDbContext<SharedDbContext>(services, cancellationToken);

            _logger.LogInformation("Database migration completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An error occurred while migrating the database");
            throw;
        }
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Database migration service is stopping");
        return Task.CompletedTask;
    }

    private async Task MigrateDbContext<TContext>(IServiceProvider services, CancellationToken cancellationToken)
        where TContext : DbContext
    {
        var contextName = typeof(TContext).Name;
        _logger.LogInformation("Migrating database context: {ContextName}", contextName);

        try
        {
            var context = services.GetRequiredService<TContext>();
            
            // Check if database exists
            var canConnect = await context.Database.CanConnectAsync(cancellationToken);
            if (!canConnect)
            {
                _logger.LogInformation("Database does not exist. Creating database for context: {ContextName}", contextName);
                await context.Database.EnsureCreatedAsync(cancellationToken);
            }

            // Apply pending migrations
            var pendingMigrations = await context.Database.GetPendingMigrationsAsync(cancellationToken);
            if (pendingMigrations.Any())
            {
                _logger.LogInformation("Applying {Count} pending migrations for context: {ContextName}", 
                    pendingMigrations.Count(), contextName);
                
                await context.Database.MigrateAsync(cancellationToken);
                
                _logger.LogInformation("Migrations applied successfully for context: {ContextName}", contextName);
            }
            else
            {
                _logger.LogInformation("No pending migrations for context: {ContextName}", contextName);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error migrating database context: {ContextName}", contextName);
            throw;
        }
    }
}