using Axon.Domain.Service.Shared.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;

namespace Axon.Domain.Service.Shared.Infrastructure.Persistence.DesignTime;

/// <summary>
/// Design-time factory for SharedDbContext to support EF Core tools and migrations
/// </summary>
public class SharedDbContextFactory : IDesignTimeDbContextFactory<SharedDbContext>
{
    public SharedDbContext CreateDbContext(string[] args)
    {
        // Build configuration
        var configuration = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json", optional: false)
            .AddJsonFile("appsettings.Development.json", optional: true)
            .AddEnvironmentVariables()
            .Build();

        // Create options
        var optionsBuilder = new DbContextOptionsBuilder<SharedDbContext>();
        var connectionString = configuration.GetConnectionString("SharedDb");
        
        optionsBuilder.UseNpgsql(connectionString);

        return new SharedDbContext(optionsBuilder.Options);
    }
}