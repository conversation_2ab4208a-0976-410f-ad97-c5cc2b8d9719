using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;

namespace Axon.Domain.Service.Shared.Infrastructure.Persistence;

public abstract class EfRepositoryBase<TAggregate, TKey, TContext> 
    where TAggregate : class
    where TContext : DbContext
{
    protected readonly TContext Context;
    protected readonly DbSet<TAggregate> DbSet;

    protected EfRepositoryBase(TContext context)
    {
        Context = context;
        DbSet = context.Set<TAggregate>();
    }

    public virtual async Task<TAggregate?> GetByIdAsync(TKey id, CancellationToken cancellationToken = default)
    {
        return await DbSet.FindAsync(new object[] { id! }, cancellationToken);
    }

    public virtual async Task<IEnumerable<TAggregate>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        return await DbSet.ToListAsync(cancellationToken);
    }

    public virtual async Task<IEnumerable<TAggregate>> FindAsync(
        Expression<Func<TAggregate, bool>> predicate, 
        CancellationToken cancellationToken = default)
    {
        return await DbSet.Where(predicate).ToListAsync(cancellationToken);
    }

    public virtual async Task AddAsync(TAggregate entity, CancellationToken cancellationToken = default)
    {
        await DbSet.AddAsync(entity, cancellationToken);
    }

    public virtual void Update(TAggregate entity)
    {
        DbSet.Update(entity);
    }

    public virtual void Remove(TAggregate entity)
    {
        DbSet.Remove(entity);
    }

    public virtual async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        return await Context.SaveChangesAsync(cancellationToken);
    }

    public virtual async Task<bool> ExistsAsync(TKey id, CancellationToken cancellationToken = default)
    {
        var entity = await GetByIdAsync(id, cancellationToken);
        return entity != null;
    }

    protected IQueryable<TAggregate> GetQueryable()
    {
        return DbSet.AsQueryable();
    }
}