using System.ComponentModel.DataAnnotations;

namespace Axon.Domain.Service.Shared.Infrastructure.Persistence;

/// <summary>
/// Configuration options for persistence
/// </summary>
public class PersistenceOptions : IValidatableObject
{
    public const string Key = "Persistence";

    /// <summary>
    /// Type of persistence to use: InMemory or PostgreSQL
    /// </summary>
    [Required(ErrorMessage = "PersistenceType is required")]
    [RegularExpression("^(InMemory|PostgreSQL)$", ErrorMessage = "PersistenceType must be either 'InMemory' or 'PostgreSQL'")]
    public string Type { get; set; } = "InMemory";
    
    /// <summary>
    /// Connection strings for each bounded context
    /// </summary>
    public ConnectionStrings ConnectionStrings { get; set; } = new();
    
    /// <summary>
    /// Custom validation to ensure connection strings are provided when using PostgreSQL
    /// </summary>
    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        if (Type.Equals("PostgreSQL", StringComparison.OrdinalIgnoreCase))
        {
            if (string.IsNullOrWhiteSpace(ConnectionStrings?.OrderDb))
            {
                yield return new ValidationResult(
                    "OrderDb connection string is required when PersistenceType is set to PostgreSQL",
                    new[] { nameof(ConnectionStrings.OrderDb) });
            }
            
            if (string.IsNullOrWhiteSpace(ConnectionStrings?.CartDb))
            {
                yield return new ValidationResult(
                    "CartDb connection string is required when PersistenceType is set to PostgreSQL",
                    new[] { nameof(ConnectionStrings.CartDb) });
            }
            
            if (string.IsNullOrWhiteSpace(ConnectionStrings?.SharedDb))
            {
                yield return new ValidationResult(
                    "SharedDb connection string is required when PersistenceType is set to PostgreSQL",
                    new[] { nameof(ConnectionStrings.SharedDb) });
            }
        }
    }
}

/// <summary>
/// Connection strings for each bounded context
/// </summary>
public class ConnectionStrings
{
    public string? OrderDb { get; set; }
    public string? CartDb { get; set; }
    public string? SharedDb { get; set; }
}