using Axon.Domain.Service.Cart.Domain;
using Axon.Domain.Service.Cart.Infrastructure;
using Axon.Domain.Service.Cart.Infrastructure.HealthChecks;
using Axon.Domain.Service.Cart.Infrastructure.Persistence;
using Axon.Domain.Service.Order.Domain;
using Axon.Domain.Service.Order.Infrastructure;
using Axon.Domain.Service.Order.Infrastructure.HealthChecks;
using Axon.Domain.Service.Order.Infrastructure.Persistence;
using Axon.Domain.Service.Shared.Infrastructure.HealthChecks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Axon.Domain.Service.Shared.Infrastructure.Persistence;

public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Adds persistence services to the service collection based on configuration
    /// </summary>
    public static IServiceCollection AddPersistence(this IServiceCollection services, IConfiguration configuration)
    {
        // Configure persistence options with validation
        services.AddOptions<PersistenceOptions>()
            .Bind(configuration.GetSection(PersistenceOptions.Key))
            .Configure(options =>
            {
                // Bind connection strings from the ConnectionStrings section
                var connectionStrings = configuration.GetSection("ConnectionStrings");
                if (connectionStrings.Exists())
                {
                    options.ConnectionStrings.OrderDb = connectionStrings["OrderDb"];
                    options.ConnectionStrings.CartDb = connectionStrings["CartDb"];
                    options.ConnectionStrings.SharedDb = connectionStrings["SharedDb"];
                }
            })
            .ValidateDataAnnotations()
            .ValidateOnStart();

        // Build service provider to get the validated options
        using var serviceProvider = services.BuildServiceProvider();
        var options = serviceProvider.GetRequiredService<IOptions<PersistenceOptions>>().Value;
        var logger = serviceProvider.GetService<ILogger<DatabaseMigrationService>>()
            ?? serviceProvider.GetService<ILoggerFactory>()?.CreateLogger("Persistence");

        logger?.LogInformation("Configuring persistence type: {PersistenceType}", options.Type);

        switch (options.Type.ToLowerInvariant())
        {
            case "postgresql":
                logger?.LogInformation("Using PostgreSQL persistence with Entity Framework Core");
                return services.AddPostgreSqlPersistence(options);

            case "inmemory":
            default:
                logger?.LogInformation("Using in-memory persistence with Entity Framework Core");
                return services.AddInMemoryPersistence();
        }
    }

    /// <summary>
    /// Adds PostgreSQL persistence with Entity Framework Core
    /// </summary>
    private static IServiceCollection AddPostgreSqlPersistence(this IServiceCollection services, PersistenceOptions options)
    {
        // Connection strings are already validated by PersistenceOptions
        var orderDbConnection = options.ConnectionStrings.OrderDb!;
        var cartDbConnection = options.ConnectionStrings.CartDb!;
        var sharedDbConnection = options.ConnectionStrings.SharedDb!;

        // Add DbContexts
        services.AddDbContext<OrderDbContext>(dbOptions =>
        {
            dbOptions.UseNpgsql(orderDbConnection, npgsqlOptions =>
            {
                npgsqlOptions.MigrationsHistoryTable("__EFMigrationsHistory", "order");
            });
        });

        services.AddDbContext<CartDbContext>(dbOptions =>
        {
            dbOptions.UseNpgsql(cartDbConnection, npgsqlOptions =>
            {
                npgsqlOptions.MigrationsHistoryTable("__EFMigrationsHistory", "cart");
            });
        });

        services.AddDbContext<SharedDbContext>(dbOptions =>
        {
            dbOptions.UseNpgsql(sharedDbConnection, npgsqlOptions =>
            {
                npgsqlOptions.MigrationsHistoryTable("__EFMigrationsHistory", "shared");
            });
        });

        // Add EF Core repositories
        services.AddScoped<IOrderRepository, OrderRepository>();
        services.AddScoped<ICartRepository, CartRepository>();

        // Add migration service
        services.AddHostedService<DatabaseMigrationService>();

        // Add health checks
        services.AddHealthChecks()
            .AddTypeActivatedCheck<CartDatabaseHealthCheck>(
                "cart-database",
                HealthStatus.Unhealthy,
                tags: new[] { "database", "cart", "postgresql" })
            .AddTypeActivatedCheck<OrderDatabaseHealthCheck>(
                "order-database",
                HealthStatus.Unhealthy,
                tags: new[] { "database", "order", "postgresql" })
            .AddTypeActivatedCheck<AllDatabasesHealthCheck>(
                "all-databases",
                HealthStatus.Unhealthy,
                tags: new[] { "database", "postgresql" });

        return services;
    }

    /// <summary>
    /// Adds in-memory persistence
    /// </summary>
    private static IServiceCollection AddInMemoryPersistence(this IServiceCollection services)
    {
        // Add DbContexts with in-memory database
        services.AddDbContext<OrderDbContext>(options =>
        {
            options.UseInMemoryDatabase("OrderDb");
        });

        services.AddDbContext<CartDbContext>(options =>
        {
            options.UseInMemoryDatabase("CartDb");
        });

        services.AddDbContext<SharedDbContext>(options =>
        {
            options.UseInMemoryDatabase("SharedDb");
        });

        // Add EF Core repositories (they work with in-memory database too)
        services.AddScoped<IOrderRepository, OrderRepository>();
        services.AddScoped<ICartRepository, CartRepository>();

        // Add health checks for in-memory databases
        services.AddHealthChecks()
            .AddTypeActivatedCheck<CartDatabaseHealthCheck>(
                "cart-database",
                HealthStatus.Unhealthy,
                tags: new[] { "database", "cart", "inmemory" })
            .AddTypeActivatedCheck<OrderDatabaseHealthCheck>(
                "order-database",
                HealthStatus.Unhealthy,
                tags: new[] { "database", "order", "inmemory" })
            .AddTypeActivatedCheck<AllDatabasesHealthCheck>(
                "all-databases",
                HealthStatus.Unhealthy,
                tags: new[] { "database", "inmemory" });

        return services;
    }
}