using Microsoft.EntityFrameworkCore;

namespace Axon.Domain.Service.Shared.Infrastructure.Persistence;

public class SharedDbContext : DbContext
{
    public SharedDbContext(DbContextOptions<SharedDbContext> options) : base(options)
    {
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.HasDefaultSchema("shared");
        
        // Apply configurations for shared entities
        modelBuilder.ApplyConfigurationsFromAssembly(typeof(SharedDbContext).Assembly,
            t => t.Namespace != null && t.Namespace.Contains("Shared.Infrastructure.Persistence.Configurations"));

        base.OnModelCreating(modelBuilder);
    }
}