{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "Serilog": {"MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Information", "Microsoft.Hosting.Lifetime": "Information", "System": "Information", "Microsoft.AspNetCore": "Information", "MassTransit": "Debug"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"theme": "Serilog.Sinks.SystemConsole.Themes.AnsiConsoleTheme::Code, Serilog.Sinks.Console", "outputTemplate": "⚡ [{Timestamp:HH:mm:ss} {Level:u3}] 🏗️  {SourceContext:l} {Message:lj}{NewLine}{Exception}"}}]}}