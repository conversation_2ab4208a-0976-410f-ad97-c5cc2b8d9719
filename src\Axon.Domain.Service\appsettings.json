{"Serilog": {"Using": ["Serilog.Sinks.Console"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "System": "Warning", "Microsoft.AspNetCore": "Warning", "MassTransit": "Debug", "MassTransit.AmazonSqsTransport": "Debug", "MassTransit.Monitoring": "Debug"}}, "Enrich": ["FromLogContext", "WithEnvironmentName", "WithMachineName", "WithProcessId", "WithThreadId"]}, "RabbitMQ": {"Host": "rabbitmq", "VirtualHost": "/", "Username": "guest", "Password": "guest"}, "AllowedHosts": "*", "SapApiOptions": {"BaseAddress": "http://localhost:7600", "SystemNumber": "00", "Client": "100", "User": "RFC_USER", "Password": "RFC_PASSWORD", "Language": "EN", "SystemId": "DEV"}, "Aws": {"Region": "us-east-2"}}