var builder = WebApplication.CreateBuilder(args);

// Enable debug logging
builder.Logging.ClearProviders();
builder.Logging.AddConsole();
builder.Logging.SetMinimumLevel(LogLevel.Debug);

var app = builder.Build();

app.MapGet("/", () => "Hello World!");

app.MapPost("/sap/orders", async (HttpRequest request, ILogger<Program> logger) =>
{
    logger.LogDebug("/sap/orders endpoint hit");

    var body = await new StreamReader(request.Body).ReadToEndAsync();
    Console.WriteLine($"Received create-sales-order: {body}");

    // Generate a random 6-digit order number
    var random = new Random();
    var orderNumber = $"ORD{random.Next(100000, 999999)}";

    var response = new
    {
        ORDER_NUMBER = orderNumber,
        RESULTS = new[]
        {
            new {
                TYPE = "S",
                ID = "SUCCESS",
                NUMBER = "000",
                MESSAGE = "Order created successfully",
                LOG_NO = "",
                LOG_MSG_NO = "000000",
                MESSAGE_V1 = "",
                MESSAGE_V2 = "",
                MESSAGE_V3 = "",
                MESSAGE_V4 = "",
                PARAMETER = "",
                ROW = 0,
                FIELD = "",
                SYSTEM = ""
            }
        }
    };
    
    Console.WriteLine($"Sending response with ORDER_NUMBER: {orderNumber}");
    
    return Results.Ok(response);
});

app.Urls.Clear();
app.Urls.Add("http://0.0.0.0:7600");

app.Run();
